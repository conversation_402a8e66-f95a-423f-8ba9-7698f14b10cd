{"test_run_info": {"timestamp": "2025-06-10T22:32:36.178216", "total_tests": 187, "duration_seconds": 217.80607175827026, "api_calls": 108, "api_errors": 8}, "summary": {"passed": 182, "failed": 5, "partial": 0, "errors": 0, "skipped": 0}, "test_results": [{"test_name": "Packager Config - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "Y4xTK1"}}, {"test_name": "Packager Config - label", "status": "PASSED", "message": "Required field 'label' exists", "expected": null, "actual": null, "details": {"field_value": "Enhanced Test Script Packager"}}, {"test_name": "Packager Config - packager_schedule_type", "status": "PASSED", "message": "Required field 'packager_schedule_type' exists", "expected": null, "actual": null, "details": {"field_value": "MANUAL"}}, {"test_name": "Packager Config - document_status_filter", "status": "PASSED", "message": "Required field 'document_status_filter' exists", "expected": null, "actual": null, "details": {"field_value": ["VERIFIED"]}}, {"test_name": "Packager Config - eb_status_filter", "status": "PASSED", "message": "Required field 'eb_status_filter' exists", "expected": null, "actual": null, "details": {"field_value": ["VERIFIED"]}}, {"test_name": "Packager Config - output_format", "status": "PASSED", "message": "Required field 'output_format' exists", "expected": null, "actual": null, "details": {"field_value": "json"}}, {"test_name": "Packager Config - delivery_data_grouping_strategy", "status": "PASSED", "message": "Required field 'delivery_data_grouping_strategy' exists", "expected": null, "actual": null, "details": {"field_value": "ALL_IN_ONE"}}, {"test_name": "Packager Config - Label", "status": "PASSED", "message": "label has correct value: Enhanced Test Script Packager", "expected": "Enhanced Test Script Packager", "actual": "Enhanced Test Script Packager", "details": {}}, {"test_name": "Packager Config - Schedule Type", "status": "PASSED", "message": "packager_schedule_type has correct value: MANUAL", "expected": "MANUAL", "actual": "MANUAL", "details": {}}, {"test_name": "Packager Config - Output Format", "status": "PASSED", "message": "output_format has correct value: json", "expected": "json", "actual": "json", "details": {}}, {"test_name": "Packager Config - Document Status Filter", "status": "PASSED", "message": "Document status filter correct: ['VERIFIED']", "expected": ["VERIFIED"], "actual": ["VERIFIED"], "details": {}}, {"test_name": "Packager Config - Deduplication Fields", "status": "PASSED", "message": "Deduplication fields correct: []", "expected": [], "actual": [], "details": {}}, {"test_name": "Packager Config - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "Y4xTK1"}}, {"test_name": "Packager Config - label", "status": "PASSED", "message": "Required field 'label' exists", "expected": null, "actual": null, "details": {"field_value": "Enhanced Test Script Packager"}}, {"test_name": "Packager Config - packager_schedule_type", "status": "PASSED", "message": "Required field 'packager_schedule_type' exists", "expected": null, "actual": null, "details": {"field_value": "MANUAL"}}, {"test_name": "Packager Config - document_status_filter", "status": "PASSED", "message": "Required field 'document_status_filter' exists", "expected": null, "actual": null, "details": {"field_value": ["VERIFIED"]}}, {"test_name": "Packager Config - eb_status_filter", "status": "PASSED", "message": "Required field 'eb_status_filter' exists", "expected": null, "actual": null, "details": {"field_value": ["VERIFIED"]}}, {"test_name": "Packager Config - output_format", "status": "PASSED", "message": "Required field 'output_format' exists", "expected": null, "actual": null, "details": {"field_value": "json"}}, {"test_name": "Packager Config - delivery_data_grouping_strategy", "status": "PASSED", "message": "Required field 'delivery_data_grouping_strategy' exists", "expected": null, "actual": null, "details": {"field_value": "ALL_IN_ONE"}}, {"test_name": "Packager Config - Label", "status": "PASSED", "message": "label has correct value: Enhanced Test Script Packager", "expected": "Enhanced Test Script Packager", "actual": "Enhanced Test Script Packager", "details": {}}, {"test_name": "Packager Config - Schedule Type", "status": "PASSED", "message": "packager_schedule_type has correct value: MANUAL", "expected": "MANUAL", "actual": "MANUAL", "details": {}}, {"test_name": "Packager Config - Output Format", "status": "PASSED", "message": "output_format has correct value: json", "expected": "json", "actual": "json", "details": {}}, {"test_name": "Packager Config - Document Status Filter", "status": "PASSED", "message": "Document status filter correct: ['VERIFIED']", "expected": ["VERIFIED"], "actual": ["VERIFIED"], "details": {}}, {"test_name": "Packager Config - Deduplication Fields", "status": "PASSED", "message": "Deduplication fields correct: []", "expected": [], "actual": [], "details": {}}, {"test_name": "Invalid Config - packager_schedule_type", "status": "PASSED", "message": "Invalid configuration properly rejected for field: packager_schedule_type", "expected": null, "actual": null, "details": {}}, {"test_name": "Invalid Config - output_format", "status": "PASSED", "message": "Invalid configuration properly rejected for field: output_format", "expected": null, "actual": null, "details": {}}, {"test_name": "Invalid Config - label", "status": "FAILED", "message": "Invalid configuration was accepted (should have been rejected): label", "expected": null, "actual": null, "details": {"created_packager_id": "7qWhk1"}}, {"test_name": "Document Upload - COPY_doc2_dupe1.pdf - Success", "status": "PASSED", "message": "Document uploaded successfully with ID: fhkIB2", "expected": null, "actual": null, "details": {"document_id": "fhkIB2", "file_name": "COPY_doc2_dupe1.pdf"}}, {"test_name": "Document Upload - COPY_doc2_dupe1.pdf - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "fhkIB2"}}, {"test_name": "Document Upload - COPY_doc2_dupe1.pdf - label", "status": "PASSED", "message": "Required field 'label' exists", "expected": null, "actual": null, "details": {"field_value": "COPY_doc2_dupe1.pdf"}}, {"test_name": "Document Upload - COPY_doc2_dupe1.pdf - content_type", "status": "PASSED", "message": "Required field 'content_type' exists", "expected": null, "actual": null, "details": {"field_value": "application/pdf"}}, {"test_name": "Document Upload - COPY_doc2_dupe1.pdf - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T03:29:59.524347Z"}}, {"test_name": "Document Upload - COPY_doc2_dupe1.pdf - Label", "status": "PASSED", "message": "label has correct value: COPY_doc2_dupe1.pdf", "expected": "COPY_doc2_dupe1.pdf", "actual": "COPY_doc2_dupe1.pdf", "details": {}}, {"test_name": "Document Upload - COPY_doc2_dupe1.pdf - Content Type", "status": "PASSED", "message": "content_type has correct value: application/pdf", "expected": "application/pdf", "actual": "application/pdf", "details": {}}, {"test_name": "DocumentData for fhkIB2 - Existence", "status": "PASSED", "message": "DocumentData found for document fhkIB2", "expected": null, "actual": null, "details": {"doc_data_id": "LHyvE1"}}, {"test_name": "DocumentData for fhkIB2 - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "LHyvE1"}}, {"test_name": "DocumentData for fhkIB2 - document", "status": "PASSED", "message": "Required field 'document' exists", "expected": null, "actual": null, "details": {"field_value": "https://api-sandbox.glynt.ai/v6/data-pools/Fj8uL1/documents/fhkIB2/"}}, {"test_name": "DocumentData for fhkIB2 - status", "status": "PASSED", "message": "Required field 'status' exists", "expected": null, "actual": null, "details": {"field_value": "PENDING_CREATION"}}, {"test_name": "DocumentData for fhkIB2 - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T03:29:59.535238Z"}}, {"test_name": "DocumentData for fhkIB2 - updated_at", "status": "PASSED", "message": "Required field 'updated_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T03:29:59.535281Z"}}, {"test_name": "DocumentData for fhkIB2 - ID Type", "status": "PASSED", "message": "Field 'id' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for fhkIB2 - Status Type", "status": "PASSED", "message": "Field 'status' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for fhkIB2 - Status Valid", "status": "PASSED", "message": "DocumentData has valid status: PENDING_CREATION", "expected": null, "actual": null, "details": {}}, {"test_name": "Document Upload - COPY_doc3_dupe1.pdf - Success", "status": "PASSED", "message": "Document uploaded successfully with ID: ymQqV1", "expected": null, "actual": null, "details": {"document_id": "ymQqV1", "file_name": "COPY_doc3_dupe1.pdf"}}, {"test_name": "Document Upload - COPY_doc3_dupe1.pdf - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "ymQqV1"}}, {"test_name": "Document Upload - COPY_doc3_dupe1.pdf - label", "status": "PASSED", "message": "Required field 'label' exists", "expected": null, "actual": null, "details": {"field_value": "COPY_doc3_dupe1.pdf"}}, {"test_name": "Document Upload - COPY_doc3_dupe1.pdf - content_type", "status": "PASSED", "message": "Required field 'content_type' exists", "expected": null, "actual": null, "details": {"field_value": "application/pdf"}}, {"test_name": "Document Upload - COPY_doc3_dupe1.pdf - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T03:30:02.992077Z"}}, {"test_name": "Document Upload - COPY_doc3_dupe1.pdf - Label", "status": "PASSED", "message": "label has correct value: COPY_doc3_dupe1.pdf", "expected": "COPY_doc3_dupe1.pdf", "actual": "COPY_doc3_dupe1.pdf", "details": {}}, {"test_name": "Document Upload - COPY_doc3_dupe1.pdf - Content Type", "status": "PASSED", "message": "content_type has correct value: application/pdf", "expected": "application/pdf", "actual": "application/pdf", "details": {}}, {"test_name": "DocumentData for ymQqV1 - Existence", "status": "PASSED", "message": "DocumentData found for document ymQqV1", "expected": null, "actual": null, "details": {"doc_data_id": "u2Y9f1"}}, {"test_name": "DocumentData for ymQqV1 - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "u2Y9f1"}}, {"test_name": "DocumentData for ymQqV1 - document", "status": "PASSED", "message": "Required field 'document' exists", "expected": null, "actual": null, "details": {"field_value": "https://api-sandbox.glynt.ai/v6/data-pools/Fj8uL1/documents/ymQqV1/"}}, {"test_name": "DocumentData for ymQqV1 - status", "status": "PASSED", "message": "Required field 'status' exists", "expected": null, "actual": null, "details": {"field_value": "PENDING_CREATION"}}, {"test_name": "DocumentData for ymQqV1 - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T03:30:03.007154Z"}}, {"test_name": "DocumentData for ymQqV1 - updated_at", "status": "PASSED", "message": "Required field 'updated_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T03:30:03.007199Z"}}, {"test_name": "DocumentData for ymQqV1 - ID Type", "status": "PASSED", "message": "Field 'id' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for ymQqV1 - Status Type", "status": "PASSED", "message": "Field 'status' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for ymQqV1 - Status Valid", "status": "PASSED", "message": "DocumentData has valid status: PENDING_CREATION", "expected": null, "actual": null, "details": {}}, {"test_name": "Document Upload - COPY_doc3_dupe2.pdf - Success", "status": "PASSED", "message": "Document uploaded successfully with ID: XY04w1", "expected": null, "actual": null, "details": {"document_id": "XY04w1", "file_name": "COPY_doc3_dupe2.pdf"}}, {"test_name": "Document Upload - COPY_doc3_dupe2.pdf - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "XY04w1"}}, {"test_name": "Document Upload - COPY_doc3_dupe2.pdf - label", "status": "PASSED", "message": "Required field 'label' exists", "expected": null, "actual": null, "details": {"field_value": "COPY_doc3_dupe2.pdf"}}, {"test_name": "Document Upload - COPY_doc3_dupe2.pdf - content_type", "status": "PASSED", "message": "Required field 'content_type' exists", "expected": null, "actual": null, "details": {"field_value": "application/pdf"}}, {"test_name": "Document Upload - COPY_doc3_dupe2.pdf - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T03:30:06.377375Z"}}, {"test_name": "Document Upload - COPY_doc3_dupe2.pdf - Label", "status": "PASSED", "message": "label has correct value: COPY_doc3_dupe2.pdf", "expected": "COPY_doc3_dupe2.pdf", "actual": "COPY_doc3_dupe2.pdf", "details": {}}, {"test_name": "Document Upload - COPY_doc3_dupe2.pdf - Content Type", "status": "PASSED", "message": "content_type has correct value: application/pdf", "expected": "application/pdf", "actual": "application/pdf", "details": {}}, {"test_name": "DocumentData for XY04w1 - Existence", "status": "PASSED", "message": "DocumentData found for document XY04w1", "expected": null, "actual": null, "details": {"doc_data_id": "To7N52"}}, {"test_name": "DocumentData for XY04w1 - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "To7N52"}}, {"test_name": "DocumentData for XY04w1 - document", "status": "PASSED", "message": "Required field 'document' exists", "expected": null, "actual": null, "details": {"field_value": "https://api-sandbox.glynt.ai/v6/data-pools/Fj8uL1/documents/XY04w1/"}}, {"test_name": "DocumentData for XY04w1 - status", "status": "PASSED", "message": "Required field 'status' exists", "expected": null, "actual": null, "details": {"field_value": "PENDING_CREATION"}}, {"test_name": "DocumentData for XY04w1 - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T03:30:06.389448Z"}}, {"test_name": "DocumentData for XY04w1 - updated_at", "status": "PASSED", "message": "Required field 'updated_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T03:30:06.389490Z"}}, {"test_name": "DocumentData for XY04w1 - ID Type", "status": "PASSED", "message": "Field 'id' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for XY04w1 - Status Type", "status": "PASSED", "message": "Field 'status' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for XY04w1 - Status Valid", "status": "PASSED", "message": "DocumentData has valid status: PENDING_CREATION", "expected": null, "actual": null, "details": {}}, {"test_name": "Document Upload - orig_doc_1.pdf - Success", "status": "PASSED", "message": "Document uploaded successfully with ID: qdgbG1", "expected": null, "actual": null, "details": {"document_id": "qdgbG1", "file_name": "orig_doc_1.pdf"}}, {"test_name": "Document Upload - orig_doc_1.pdf - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "qdgbG1"}}, {"test_name": "Document Upload - orig_doc_1.pdf - label", "status": "PASSED", "message": "Required field 'label' exists", "expected": null, "actual": null, "details": {"field_value": "orig_doc_1.pdf"}}, {"test_name": "Document Upload - orig_doc_1.pdf - content_type", "status": "PASSED", "message": "Required field 'content_type' exists", "expected": null, "actual": null, "details": {"field_value": "application/pdf"}}, {"test_name": "Document Upload - orig_doc_1.pdf - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T03:30:09.697639Z"}}, {"test_name": "Document Upload - orig_doc_1.pdf - Label", "status": "PASSED", "message": "label has correct value: orig_doc_1.pdf", "expected": "orig_doc_1.pdf", "actual": "orig_doc_1.pdf", "details": {}}, {"test_name": "Document Upload - orig_doc_1.pdf - Content Type", "status": "PASSED", "message": "content_type has correct value: application/pdf", "expected": "application/pdf", "actual": "application/pdf", "details": {}}, {"test_name": "DocumentData for qdgbG1 - Existence", "status": "PASSED", "message": "DocumentData found for document qdgbG1", "expected": null, "actual": null, "details": {"doc_data_id": "mtnuP1"}}, {"test_name": "DocumentData for qdgbG1 - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "mtnuP1"}}, {"test_name": "DocumentData for qdgbG1 - document", "status": "PASSED", "message": "Required field 'document' exists", "expected": null, "actual": null, "details": {"field_value": "https://api-sandbox.glynt.ai/v6/data-pools/Fj8uL1/documents/qdgbG1/"}}, {"test_name": "DocumentData for qdgbG1 - status", "status": "PASSED", "message": "Required field 'status' exists", "expected": null, "actual": null, "details": {"field_value": "PENDING_CREATION"}}, {"test_name": "DocumentData for qdgbG1 - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T03:30:09.709900Z"}}, {"test_name": "DocumentData for qdgbG1 - updated_at", "status": "PASSED", "message": "Required field 'updated_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T03:30:09.709943Z"}}, {"test_name": "DocumentData for qdgbG1 - ID Type", "status": "PASSED", "message": "Field 'id' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for qdgbG1 - Status Type", "status": "PASSED", "message": "Field 'status' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for qdgbG1 - Status Valid", "status": "PASSED", "message": "DocumentData has valid status: PENDING_CREATION", "expected": null, "actual": null, "details": {}}, {"test_name": "Document Upload - origin_doc_2.pdf - Success", "status": "PASSED", "message": "Document uploaded successfully with ID: PPGpg1", "expected": null, "actual": null, "details": {"document_id": "PPGpg1", "file_name": "origin_doc_2.pdf"}}, {"test_name": "Document Upload - origin_doc_2.pdf - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "PPGpg1"}}, {"test_name": "Document Upload - origin_doc_2.pdf - label", "status": "PASSED", "message": "Required field 'label' exists", "expected": null, "actual": null, "details": {"field_value": "origin_doc_2.pdf"}}, {"test_name": "Document Upload - origin_doc_2.pdf - content_type", "status": "PASSED", "message": "Required field 'content_type' exists", "expected": null, "actual": null, "details": {"field_value": "application/pdf"}}, {"test_name": "Document Upload - origin_doc_2.pdf - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T03:30:12.956301Z"}}, {"test_name": "Document Upload - origin_doc_2.pdf - Label", "status": "PASSED", "message": "label has correct value: origin_doc_2.pdf", "expected": "origin_doc_2.pdf", "actual": "origin_doc_2.pdf", "details": {}}, {"test_name": "Document Upload - origin_doc_2.pdf - Content Type", "status": "PASSED", "message": "content_type has correct value: application/pdf", "expected": "application/pdf", "actual": "application/pdf", "details": {}}, {"test_name": "DocumentData for PPGpg1 - Existence", "status": "PASSED", "message": "DocumentData found for document PPGpg1", "expected": null, "actual": null, "details": {"doc_data_id": "LfN8q1"}}, {"test_name": "DocumentData for PPGpg1 - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "LfN8q1"}}, {"test_name": "DocumentData for PPGpg1 - document", "status": "PASSED", "message": "Required field 'document' exists", "expected": null, "actual": null, "details": {"field_value": "https://api-sandbox.glynt.ai/v6/data-pools/Fj8uL1/documents/PPGpg1/"}}, {"test_name": "DocumentData for PPGpg1 - status", "status": "PASSED", "message": "Required field 'status' exists", "expected": null, "actual": null, "details": {"field_value": "PENDING_CREATION"}}, {"test_name": "DocumentData for PPGpg1 - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T03:30:12.966332Z"}}, {"test_name": "DocumentData for PPGpg1 - updated_at", "status": "PASSED", "message": "Required field 'updated_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T03:30:12.966377Z"}}, {"test_name": "DocumentData for PPGpg1 - ID Type", "status": "PASSED", "message": "Field 'id' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for PPGpg1 - Status Type", "status": "PASSED", "message": "Field 'status' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for PPGpg1 - Status Valid", "status": "PASSED", "message": "DocumentData has valid status: PENDING_CREATION", "expected": null, "actual": null, "details": {}}, {"test_name": "Document Upload - orig_doc_3.pdf - Success", "status": "PASSED", "message": "Document uploaded successfully with ID: yAq272", "expected": null, "actual": null, "details": {"document_id": "yAq272", "file_name": "orig_doc_3.pdf"}}, {"test_name": "Document Upload - orig_doc_3.pdf - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "yAq272"}}, {"test_name": "Document Upload - orig_doc_3.pdf - label", "status": "PASSED", "message": "Required field 'label' exists", "expected": null, "actual": null, "details": {"field_value": "orig_doc_3.pdf"}}, {"test_name": "Document Upload - orig_doc_3.pdf - content_type", "status": "PASSED", "message": "Required field 'content_type' exists", "expected": null, "actual": null, "details": {"field_value": "application/pdf"}}, {"test_name": "Document Upload - orig_doc_3.pdf - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T03:30:16.294629Z"}}, {"test_name": "Document Upload - orig_doc_3.pdf - Label", "status": "PASSED", "message": "label has correct value: orig_doc_3.pdf", "expected": "orig_doc_3.pdf", "actual": "orig_doc_3.pdf", "details": {}}, {"test_name": "Document Upload - orig_doc_3.pdf - Content Type", "status": "PASSED", "message": "content_type has correct value: application/pdf", "expected": "application/pdf", "actual": "application/pdf", "details": {}}, {"test_name": "DocumentData for yAq272 - Existence", "status": "PASSED", "message": "DocumentData found for document yAq272", "expected": null, "actual": null, "details": {"doc_data_id": "ek3gA1"}}, {"test_name": "DocumentData for yAq272 - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "ek3gA1"}}, {"test_name": "DocumentData for yAq272 - document", "status": "PASSED", "message": "Required field 'document' exists", "expected": null, "actual": null, "details": {"field_value": "https://api-sandbox.glynt.ai/v6/data-pools/Fj8uL1/documents/yAq272/"}}, {"test_name": "DocumentData for yAq272 - status", "status": "PASSED", "message": "Required field 'status' exists", "expected": null, "actual": null, "details": {"field_value": "PENDING_CREATION"}}, {"test_name": "DocumentData for yAq272 - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T03:30:16.307667Z"}}, {"test_name": "DocumentData for yAq272 - updated_at", "status": "PASSED", "message": "Required field 'updated_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T03:30:16.307716Z"}}, {"test_name": "DocumentData for yAq272 - ID Type", "status": "PASSED", "message": "Field 'id' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for yAq272 - Status Type", "status": "PASSED", "message": "Field 'status' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for yAq272 - Status Valid", "status": "PASSED", "message": "DocumentData has valid status: PENDING_CREATION", "expected": null, "actual": null, "details": {}}, {"test_name": "Package kF9Ku1 Entries - Entry Count", "status": "PASSED", "message": "Found 6 package entries", "expected": null, "actual": null, "details": {"entry_count": 6}}, {"test_name": "Package kF9Ku1 Entries - Duplicate Exclusion", "status": "PASSED", "message": "Duplicate exclusion working: 4 documents excluded as duplicates", "expected": null, "actual": null, "details": {"excluded_count": 4}}, {"test_name": "Package kF9Ku1 Config Compliance - Configuration Recorded", "status": "PASSED", "message": "Package configuration recorded for compliance verification", "expected": null, "actual": null, "details": {"include_source_files": true, "include_relationship_details": true, "output_format": "json", "grouping_strategy": "ALL_IN_ONE", "dedup_fields": ["CustomerName", "<PERSON><PERSON><PERSON><PERSON>", "StatementDate"]}}, {"test_name": "Simulation Format", "status": "PASSED", "message": "Response follows pure delivery format correctly", "expected": null, "actual": null, "details": {"data_items_count": 1}}, {"test_name": "Simulation Content - Item 1 - Filename Format", "status": "PASSED", "message": "Filename format correct: DOCDATA_7517_fRh16p.json", "expected": null, "actual": null, "details": {}}, {"test_name": "Simulation Content - Item 1 - DocumentID Field", "status": "PASSED", "message": "DocumentID field present: fhkIB2", "expected": null, "actual": null, "details": {}}, {"test_name": "Simulation Content - Item 1 - JSON Structure", "status": "PASSED", "message": "Valid JSON with 2 documents", "expected": null, "actual": null, "details": {}}, {"test_name": "Duplicate Filtering Scenarios - Exclude ON Format", "status": "PASSED", "message": "Response follows pure delivery format correctly", "expected": null, "actual": null, "details": {"data_items_count": 1}}, {"test_name": "Duplicate Filtering Scenarios - Exclude OFF Format", "status": "PASSED", "message": "Response follows pure delivery format correctly", "expected": null, "actual": null, "details": {"data_items_count": 1}}, {"test_name": "Duplicate Filtering Scenarios - Duplicate Filtering Logic", "status": "PASSED", "message": "Exclude OFF (1) >= Exclude ON (1) as expected", "expected": null, "actual": null, "details": {"exclude_on_count": 1, "exclude_off_count": 1}}, {"test_name": "Package kF9Ku1 Entries - Entry Count", "status": "PASSED", "message": "Found 6 package entries", "expected": null, "actual": null, "details": {"entry_count": 6}}, {"test_name": "Package kF9Ku1 Entries - Duplicate Exclusion", "status": "PASSED", "message": "Duplicate exclusion working: 4 documents excluded as duplicates", "expected": null, "actual": null, "details": {"excluded_count": 4}}, {"test_name": "DocumentData Fields - source_file_id", "status": "PASSED", "message": "All documents have source_file_id populated", "expected": null, "actual": null, "details": {}}, {"test_name": "DocumentData Fields - source_file_name", "status": "PASSED", "message": "All documents have source_file_name populated", "expected": null, "actual": null, "details": {}}, {"test_name": "DocumentData Fields - source_file_md5", "status": "PASSED", "message": "All documents have source_file_md5 populated", "expected": null, "actual": null, "details": {}}, {"test_name": "DocumentData Fields - received_at", "status": "PASSED", "message": "All documents have received_at populated", "expected": null, "actual": null, "details": {}}, {"test_name": "DocumentData Fields - source_file_num_pages", "status": "PASSED", "message": "All documents have source_file_num_pages populated", "expected": null, "actual": null, "details": {}}, {"test_name": "DocumentData Fields - source_file_size", "status": "PASSED", "message": "All documents have source_file_size populated", "expected": null, "actual": null, "details": {}}, {"test_name": "DocumentData Fields - source_file_detected_language", "status": "FAILED", "message": "No documents have source_file_detected_language populated", "expected": null, "actual": null, "details": {}}, {"test_name": "DocumentData Fields - canonical_document_data", "status": "PASSED", "message": "4 documents have canonical_document_data populated (indicates duplicate relationships)", "expected": null, "actual": null, "details": {}}, {"test_name": "Package kF9Ku1 Config Compliance - Configuration Recorded", "status": "PASSED", "message": "Package configuration recorded for compliance verification", "expected": null, "actual": null, "details": {"include_source_files": true, "include_relationship_details": true, "output_format": "json", "grouping_strategy": "ALL_IN_ONE", "dedup_fields": ["CustomerName", "<PERSON><PERSON><PERSON><PERSON>", "StatementDate"]}}, {"test_name": "Package kF9Ku1 Verification - Status Updated", "status": "PASSED", "message": "Package status successfully updated from PROCESSING_SUCCESSFUL to VERIFIED", "expected": null, "actual": null, "details": {"initial_status": "PROCESSING_SUCCESSFUL", "final_status": "VERIFIED"}}, {"test_name": "Package kF9Ku1 Final Delivery - Preview Format", "status": "PASSED", "message": "Response follows pure delivery format correctly", "expected": null, "actual": null, "details": {"data_items_count": 1}}, {"test_name": "Package kF9Ku1 Final Delivery - Preview Content - Item 1 - Filename Format", "status": "PASSED", "message": "Filename format correct: DOCDATA_7517_EEqjbn.json", "expected": null, "actual": null, "details": {}}, {"test_name": "Package kF9Ku1 Final Delivery - Preview Content - Item 1 - DocumentID Field", "status": "PASSED", "message": "DocumentID field present: fhkIB2", "expected": null, "actual": null, "details": {}}, {"test_name": "Package kF9Ku1 Final Delivery - Preview Content - Item 1 - JSON Structure", "status": "PASSED", "message": "Valid JSON with 2 documents", "expected": null, "actual": null, "details": {}}, {"test_name": "Package kF9Ku1 Final Delivery - Preview Count", "status": "PASSED", "message": "Preview shows correct number of documents: 1", "expected": null, "actual": null, "details": {"preview_count": 1, "expected": 1}}, {"test_name": "Package kF9Ku1 Final Delivery - Preview Deduplication", "status": "PASSED", "message": "No duplicate content found in preview data", "expected": null, "actual": null, "details": {"unique_items": 1}}, {"test_name": "Package kF9Ku1 Final Delivery - Download Available", "status": "PASSED", "message": "Package download endpoint is accessible", "expected": null, "actual": null, "details": {"download_available": true}}, {"test_name": "Flag Variations - Full CoC + Duplicates ON + Relationships ON Creation", "status": "FAILED", "message": "Failed to create test packager for Full CoC + Duplicates ON + Relationships ON", "expected": null, "actual": null, "details": {}}, {"test_name": "Flag Variations - Essential CoC + Duplicates OFF + Relationships ON Creation", "status": "FAILED", "message": "Failed to create test packager for Essential CoC + Duplicates OFF + Relationships ON", "expected": null, "actual": null, "details": {}}, {"test_name": "Flag Variations - No CoC + All Flags OFF Creation", "status": "FAILED", "message": "Failed to create test packager for No CoC + All Flags OFF", "expected": null, "actual": null, "details": {}}, {"test_name": "Debug API - CoC Fields Present", "status": "PASSED", "message": "All expected CoC fields are available in DocumentData API", "expected": null, "actual": null, "details": {}}, {"test_name": "Debug API - Minimal Config Creation", "status": "PASSED", "message": "Successfully created packager with Minimal Config", "expected": null, "actual": null, "details": {}}, {"test_name": "Debug API - Basic CoC Config Creation", "status": "PASSED", "message": "Successfully created packager with Basic CoC Config", "expected": null, "actual": null, "details": {}}, {"test_name": "Debug API - Full CoC Config Creation", "status": "PASSED", "message": "Successfully created packager with Full CoC Config", "expected": null, "actual": null, "details": {}}, {"test_name": "Document Upload - tmpo98lt7qr.pdf - Success", "status": "PASSED", "message": "Document uploaded successfully with ID: HGWaR1", "expected": null, "actual": null, "details": {"document_id": "HGWaR1", "file_name": "tmpo98lt7qr.pdf"}}, {"test_name": "Document Upload - tmpo98lt7qr.pdf - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "HGWaR1"}}, {"test_name": "Document Upload - tmpo98lt7qr.pdf - label", "status": "PASSED", "message": "Required field 'label' exists", "expected": null, "actual": null, "details": {"field_value": "tmpo98lt7qr.pdf"}}, {"test_name": "Document Upload - tmpo98lt7qr.pdf - content_type", "status": "PASSED", "message": "Required field 'content_type' exists", "expected": null, "actual": null, "details": {"field_value": "application/pdf"}}, {"test_name": "Document Upload - tmpo98lt7qr.pdf - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T03:32:21.167054Z"}}, {"test_name": "Document Upload - tmpo98lt7qr.pdf - Label", "status": "PASSED", "message": "label has correct value: tmpo98lt7qr.pdf", "expected": "tmpo98lt7qr.pdf", "actual": "tmpo98lt7qr.pdf", "details": {}}, {"test_name": "Document Upload - tmpo98lt7qr.pdf - Content Type", "status": "PASSED", "message": "content_type has correct value: application/pdf", "expected": "application/pdf", "actual": "application/pdf", "details": {}}, {"test_name": "Document Upload - tmppu373_qq.pdf - Success", "status": "PASSED", "message": "Document uploaded successfully with ID: q16or1", "expected": null, "actual": null, "details": {"document_id": "q16or1", "file_name": "tmppu373_qq.pdf"}}, {"test_name": "Document Upload - tmppu373_qq.pdf - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "q16or1"}}, {"test_name": "Document Upload - tmppu373_qq.pdf - label", "status": "PASSED", "message": "Required field 'label' exists", "expected": null, "actual": null, "details": {"field_value": "tmppu373_qq.pdf"}}, {"test_name": "Document Upload - tmppu373_qq.pdf - content_type", "status": "PASSED", "message": "Required field 'content_type' exists", "expected": null, "actual": null, "details": {"field_value": "application/pdf"}}, {"test_name": "Document Upload - tmppu373_qq.pdf - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T03:32:21.954332Z"}}, {"test_name": "Document Upload - tmppu373_qq.pdf - Label", "status": "PASSED", "message": "label has correct value: tmppu373_qq.pdf", "expected": "tmppu373_qq.pdf", "actual": "tmppu373_qq.pdf", "details": {}}, {"test_name": "Document Upload - tmppu373_qq.pdf - Content Type", "status": "PASSED", "message": "content_type has correct value: application/pdf", "expected": "application/pdf", "actual": "application/pdf", "details": {}}, {"test_name": "Document Upload - tmpg_q1jsmm.pdf - Success", "status": "PASSED", "message": "Document uploaded successfully with ID: 97mLC1", "expected": null, "actual": null, "details": {"document_id": "97mLC1", "file_name": "tmpg_q1jsmm.pdf"}}, {"test_name": "Document Upload - tmpg_q1jsmm.pdf - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "97mLC1"}}, {"test_name": "Document Upload - tmpg_q1jsmm.pdf - label", "status": "PASSED", "message": "Required field 'label' exists", "expected": null, "actual": null, "details": {"field_value": "tmpg_q1jsmm.pdf"}}, {"test_name": "Document Upload - tmpg_q1jsmm.pdf - content_type", "status": "PASSED", "message": "Required field 'content_type' exists", "expected": null, "actual": null, "details": {"field_value": "application/pdf"}}, {"test_name": "Document Upload - tmpg_q1jsmm.pdf - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T03:32:22.719136Z"}}, {"test_name": "Document Upload - tmpg_q1jsmm.pdf - Label", "status": "PASSED", "message": "label has correct value: tmpg_q1jsmm.pdf", "expected": "tmpg_q1jsmm.pdf", "actual": "tmpg_q1jsmm.pdf", "details": {}}, {"test_name": "Document Upload - tmpg_q1jsmm.pdf - Content Type", "status": "PASSED", "message": "content_type has correct value: application/pdf", "expected": "application/pdf", "actual": "application/pdf", "details": {}}, {"test_name": "Document Upload - tmpxigg9kn_.pdf - Success", "status": "PASSED", "message": "Document uploaded successfully with ID: isLZc1", "expected": null, "actual": null, "details": {"document_id": "isLZc1", "file_name": "tmpxigg9kn_.pdf"}}, {"test_name": "Document Upload - tmpxigg9kn_.pdf - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "isLZc1"}}, {"test_name": "Document Upload - tmpxigg9kn_.pdf - label", "status": "PASSED", "message": "Required field 'label' exists", "expected": null, "actual": null, "details": {"field_value": "tmpxigg9kn_.pdf"}}, {"test_name": "Document Upload - tmpxigg9kn_.pdf - content_type", "status": "PASSED", "message": "Required field 'content_type' exists", "expected": null, "actual": null, "details": {"field_value": "application/pdf"}}, {"test_name": "Document Upload - tmpxigg9kn_.pdf - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T03:32:23.480949Z"}}, {"test_name": "Document Upload - tmpxigg9kn_.pdf - Label", "status": "PASSED", "message": "label has correct value: tmpxigg9kn_.pdf", "expected": "tmpxigg9kn_.pdf", "actual": "tmpxigg9kn_.pdf", "details": {}}, {"test_name": "Document Upload - tmpxigg9kn_.pdf - Content Type", "status": "PASSED", "message": "content_type has correct value: application/pdf", "expected": "application/pdf", "actual": "application/pdf", "details": {}}]}