Timestamp,Step,Status,Details
2025-06-10T02:20:38.880399,<PERSON><PERSON><PERSON> Start,Completed,
2025-06-10T02:20:45.069764,Get Access Token,Completed,
2025-06-10T02:20:45.070181,Data Pool Cleanup,Skipped by user,
2025-06-10T02:20:45.070380,Packager Lifecycle Test,Skipped by user,
2025-06-10T02:20:45.369659,Main Packager Creation,Completed,Created main packager ID: tYlWQ1
2025-06-10T02:20:45.369929,Upload Documents,Skipped by user,
2025-06-10T02:20:45.370375,Document Processing Status Check,Skipped by user,
2025-06-10T02:20:45.370592,Create Extraction Batch,Skipped by user,
2025-06-10T02:20:45.370840,Simulate Content Duplication,Skipped by user,
2025-06-10T02:20:45.371071,Verify Extraction Batch,Skipped by user,
2025-06-10T02:20:45.371262,Change Packager Schedule to CRON,Skipped by user,
2025-06-10T02:20:45.371448,Val<PERSON>te CRON Package,Skipped by user,
2025-06-10T02:20:45.371639,Test Package Reuse,Skipped by user,
2025-06-10T02:20:45.371823,Test Delete Package and Re-run CRON,Skipped by user,
2025-06-10T02:20:45.690938,Reset Packager to MA<PERSON>AL,Completed,Packager schedule set to MANUAL
2025-06-10T02:20:45.691211,Simulate Package,Skipped by user,
2025-06-10T02:20:45.691646,Create Package,Skipped by user,
2025-06-10T02:20:45.691860,Trigger Delivery,Skipped by user,
2025-06-10T02:20:45.692057,Flag Variation Testing,Skipped by user,
2025-06-10T02:20:45.692249,Generate CoC Report,Skipped by user,
2025-06-10T02:20:45.692449,Script End,Completed,
