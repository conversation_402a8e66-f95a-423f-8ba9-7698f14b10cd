Timestamp,Step,Status,Details
2025-06-11T01:06:07.528400,<PERSON><PERSON><PERSON> Start,Completed,
2025-06-11T01:06:18.117618,Get Access Token,Completed,
2025-06-11T01:06:35.337620,Data Pool Cleanup - Delete extractionbatchdata,Completed,Successfully deleted all 1 extractionbatchdata
2025-06-11T01:06:44.237819,Data Pool Cleanup - Delete extractionbatches,Completed,Successfully deleted all 1 extractionbatches
2025-06-11T01:06:48.316372,Data Pool Cleanup - Delete packages,Completed,Successfully deleted all 1 packages
2025-06-11T01:06:50.119480,Data Pool Cleanup - Delete packagers,Completed,Successfully deleted all 1 packagers
2025-06-11T01:06:54.106471,Data Pool Cleanup - Delete extractions,Completed,Successfully deleted all 6 extractions
2025-06-11T01:07:00.282811,Data Pool Cleanup - Delete documents,Completed,Successfully deleted all 10 documents
2025-06-11T01:07:05.387580,Data Pool Cleanup - Delete documentdata,Completed,Successfully deleted all 10 documentdata
2025-06-11T01:07:05.387903,Data Pool Cleanup,Completed,
2025-06-11T01:07:16.185521,Packager Lifecycle Test,Completed,Created packager jmhBc1 with 12/12 validations passed. Verified packager exists with 12/12 field validations passed. Deleted temp packager jmhBc1. Verified temp packager jmhBc1 is deleted. 
2025-06-11T01:07:24.386781,Main Packager Creation,Completed,Created main packager ID: bdxwM1
2025-06-11T01:07:48.041215,Enhanced Document Upload,Completed,Uploaded 6/6 documents with 96/96 validations passed
2025-06-11T01:07:57.383080,Document Processing Status Check,Completed,All 6 documents still processing. 
2025-06-11T01:08:46.813480,Create Extraction Batch,Completed,Created extraction batch ID: 70cbr1. Batch processing completed. 
2025-06-11T01:09:06.687154,Simulate Content Duplication,Completed,Created identical content in 3 documents for duplicate testing. Created 3 corrections for orig_doc_1.pdf. Created 3 corrections for origin_doc_2.pdf. Created 3 corrections for orig_doc_3.pdf. 
2025-06-11T01:09:22.912714,Verify Extraction Batch,Completed,"Verified extraction batch 70cbr1 on attempt 1. Found 4 duplicate relationships (0 MD5, 0 label, 4 content). "
2025-06-11T01:10:07.563805,Change Packager Schedule to CRON,Completed,Changed packager bdxwM1 schedule to CRON. CRON schedule successfully created package 3LprE1 after 30s. Package processing completed. CRON schedule working correctly. 
2025-06-11T01:10:22.370196,Validate CRON Package,Partial Success, 4 duplicate relationships found. CRON package validation: 5/6 checks passed.
2025-06-11T01:10:22.370484,Test Package Reuse,Skipped by user,
2025-06-11T01:10:22.370919,Test Delete Package and Re-run CRON,Skipped by user,
2025-06-11T01:10:22.704041,Reset Packager to MANUAL,Completed,Packager schedule set to MANUAL
2025-06-11T01:11:07.435218,Simulate Package,Completed,"Simulation successful: 1 data items, 0 errors. "
2025-06-11T01:12:12.550228,Duplicate Filtering Test,Completed,All 3 duplicate filtering tests passed. 
