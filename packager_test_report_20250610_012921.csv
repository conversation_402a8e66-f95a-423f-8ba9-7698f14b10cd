Timestamp,Step,Status,Details
2025-06-10T01:29:21.670361,<PERSON><PERSON><PERSON> Start,Completed,
2025-06-10T01:29:27.444175,Get Access Token,Completed,
2025-06-10T01:29:51.384287,Data Pool Cleanup - Delete extractionbatchdata,Completed,Successfully deleted all 1 extractionbatchdata
2025-06-10T01:29:52.360821,Data Pool Cleanup - Delete extractionbatches,Completed,Successfully deleted all 1 extractionbatches
2025-06-10T01:29:53.486806,Data Pool Cleanup - Delete packages,Completed,Successfully deleted all 1 packages
2025-06-10T01:29:56.247899,Data Pool Cleanup - Delete packagers,Completed,Successfully deleted all 1 packagers
2025-06-10T01:30:00.844864,Data Pool Cleanup - Delete extractions,Completed,Successfully deleted all 6 extractions
2025-06-10T01:30:03.835854,Data Pool Cleanup - Delete documents,Completed,Successfully deleted all 6 documents
2025-06-10T01:30:06.709330,Data Pool Cleanup - Delete documentdata,Completed,Successfully deleted all 6 documentdata
2025-06-10T01:30:06.709530,Data Pool Cleanup,Completed,
2025-06-10T01:30:12.357525,Packager Lifecycle Test,Completed,Created packager PlKHo1 with 12/12 validations passed. Verified packager exists with 12/12 field validations passed. Deleted temp packager PlKHo1. Verified temp packager PlKHo1 is deleted. 
2025-06-10T01:30:21.938655,Main Packager Creation,Completed,Created main packager ID: Hca2Z1
2025-06-10T01:30:44.252034,Enhanced Document Upload,Completed,Uploaded 6/6 documents with 96/96 validations passed
2025-06-10T01:30:52.459813,Document Processing Status Check,Completed,All 6 documents still processing. 
2025-06-10T01:31:26.846956,Create Extraction Batch,Completed,Created extraction batch ID: vLAX61. Batch processing completed. 
2025-06-10T01:31:51.684461,Simulate Content Duplication,Completed,Created identical content in 3 documents for duplicate testing. Created 3 corrections for orig_doc_1.pdf. Created 3 corrections for origin_doc_2.pdf. Created 3 corrections for orig_doc_3.pdf. 
2025-06-10T01:32:50.890977,Verify Extraction Batch,Completed,"Verified extraction batch vLAX61 on attempt 1. Found 4 duplicate relationships (0 MD5, 0 label, 4 content). "
2025-06-10T01:34:01.202133,Change Packager Schedule to CRON,Completed,Changed packager Hca2Z1 schedule to CRON. CRON schedule successfully created package s0UMY1 after 60s. Package processing completed. CRON schedule working correctly. 
2025-06-10T01:34:10.533966,Validate CRON Package,Completed, 4 duplicate relationships found. CRON package validation: 3/3 checks passed.
2025-06-10T01:34:10.534199,Test Package Reuse,Skipped by user,
2025-06-10T01:34:10.534660,Test Delete Package and Re-run CRON,Skipped by user,
2025-06-10T01:34:10.853221,Reset Packager to MANUAL,Completed,Packager schedule set to MANUAL
2025-06-10T01:34:39.193995,Simulate Package,Completed,"Simulation successful: 1 data items, 0 errors. "
2025-06-10T01:35:12.263406,Create Package,Partial Success,"Using CRON package ID: s0UMY1.  4 duplicate relationships found (0 MD5, 4 content). Package validation: 5/13 checks passed."
2025-06-10T01:35:12.263682,Trigger Delivery,Skipped by user,
2025-06-10T01:35:34.092893,Flag Variation Testing,Partial Success,Only 0/3 flag variation tests passed. 
2025-06-10T01:35:42.848680,Generate CoC Report,Completed,CoC report response received. 
2025-06-10T01:35:42.848992,Script End,Completed,
