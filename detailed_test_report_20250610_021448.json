{"test_run_info": {"timestamp": "2025-06-10T02:14:48.351799", "total_tests": 143, "duration_seconds": 675.6086640357971, "api_calls": 77, "api_errors": 6}, "summary": {"passed": 137, "failed": 6, "partial": 0, "errors": 0, "skipped": 0}, "test_results": [{"test_name": "Packager Config - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "9rF0v1"}}, {"test_name": "Packager Config - label", "status": "PASSED", "message": "Required field 'label' exists", "expected": null, "actual": null, "details": {"field_value": "Enhanced Test Script Packager"}}, {"test_name": "Packager Config - packager_schedule_type", "status": "PASSED", "message": "Required field 'packager_schedule_type' exists", "expected": null, "actual": null, "details": {"field_value": "MANUAL"}}, {"test_name": "Packager Config - document_status_filter", "status": "PASSED", "message": "Required field 'document_status_filter' exists", "expected": null, "actual": null, "details": {"field_value": ["VERIFIED"]}}, {"test_name": "Packager Config - eb_status_filter", "status": "PASSED", "message": "Required field 'eb_status_filter' exists", "expected": null, "actual": null, "details": {"field_value": ["VERIFIED"]}}, {"test_name": "Packager Config - output_format", "status": "PASSED", "message": "Required field 'output_format' exists", "expected": null, "actual": null, "details": {"field_value": "json"}}, {"test_name": "Packager Config - delivery_data_grouping_strategy", "status": "PASSED", "message": "Required field 'delivery_data_grouping_strategy' exists", "expected": null, "actual": null, "details": {"field_value": "ALL_IN_ONE"}}, {"test_name": "Packager Config - Label", "status": "PASSED", "message": "label has correct value: Enhanced Test Script Packager", "expected": "Enhanced Test Script Packager", "actual": "Enhanced Test Script Packager", "details": {}}, {"test_name": "Packager Config - Schedule Type", "status": "PASSED", "message": "packager_schedule_type has correct value: MANUAL", "expected": "MANUAL", "actual": "MANUAL", "details": {}}, {"test_name": "Packager Config - Output Format", "status": "PASSED", "message": "output_format has correct value: json", "expected": "json", "actual": "json", "details": {}}, {"test_name": "Packager Config - Document Status Filter", "status": "PASSED", "message": "Document status filter correct: ['VERIFIED']", "expected": ["VERIFIED"], "actual": ["VERIFIED"], "details": {}}, {"test_name": "Packager Config - Deduplication Fields", "status": "PASSED", "message": "Deduplication fields correct: []", "expected": [], "actual": [], "details": {}}, {"test_name": "Packager Config - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "9rF0v1"}}, {"test_name": "Packager Config - label", "status": "PASSED", "message": "Required field 'label' exists", "expected": null, "actual": null, "details": {"field_value": "Enhanced Test Script Packager"}}, {"test_name": "Packager Config - packager_schedule_type", "status": "PASSED", "message": "Required field 'packager_schedule_type' exists", "expected": null, "actual": null, "details": {"field_value": "MANUAL"}}, {"test_name": "Packager Config - document_status_filter", "status": "PASSED", "message": "Required field 'document_status_filter' exists", "expected": null, "actual": null, "details": {"field_value": ["VERIFIED"]}}, {"test_name": "Packager Config - eb_status_filter", "status": "PASSED", "message": "Required field 'eb_status_filter' exists", "expected": null, "actual": null, "details": {"field_value": ["VERIFIED"]}}, {"test_name": "Packager Config - output_format", "status": "PASSED", "message": "Required field 'output_format' exists", "expected": null, "actual": null, "details": {"field_value": "json"}}, {"test_name": "Packager Config - delivery_data_grouping_strategy", "status": "PASSED", "message": "Required field 'delivery_data_grouping_strategy' exists", "expected": null, "actual": null, "details": {"field_value": "ALL_IN_ONE"}}, {"test_name": "Packager Config - Label", "status": "PASSED", "message": "label has correct value: Enhanced Test Script Packager", "expected": "Enhanced Test Script Packager", "actual": "Enhanced Test Script Packager", "details": {}}, {"test_name": "Packager Config - Schedule Type", "status": "PASSED", "message": "packager_schedule_type has correct value: MANUAL", "expected": "MANUAL", "actual": "MANUAL", "details": {}}, {"test_name": "Packager Config - Output Format", "status": "PASSED", "message": "output_format has correct value: json", "expected": "json", "actual": "json", "details": {}}, {"test_name": "Packager Config - Document Status Filter", "status": "PASSED", "message": "Document status filter correct: ['VERIFIED']", "expected": ["VERIFIED"], "actual": ["VERIFIED"], "details": {}}, {"test_name": "Packager Config - Deduplication Fields", "status": "PASSED", "message": "Deduplication fields correct: []", "expected": [], "actual": [], "details": {}}, {"test_name": "Invalid Config - packager_schedule_type", "status": "PASSED", "message": "Invalid configuration properly rejected for field: packager_schedule_type", "expected": null, "actual": null, "details": {}}, {"test_name": "Invalid Config - output_format", "status": "PASSED", "message": "Invalid configuration properly rejected for field: output_format", "expected": null, "actual": null, "details": {}}, {"test_name": "Invalid Config - label", "status": "FAILED", "message": "Invalid configuration was accepted (should have been rejected): label", "expected": null, "actual": null, "details": {"created_packager_id": "SwvXF1"}}, {"test_name": "Document Upload - COPY_doc2_dupe1.pdf - Success", "status": "PASSED", "message": "Document uploaded successfully with ID: 1tB5N1", "expected": null, "actual": null, "details": {"document_id": "1tB5N1", "file_name": "COPY_doc2_dupe1.pdf"}}, {"test_name": "Document Upload - COPY_doc2_dupe1.pdf - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "1tB5N1"}}, {"test_name": "Document Upload - COPY_doc2_dupe1.pdf - label", "status": "PASSED", "message": "Required field 'label' exists", "expected": null, "actual": null, "details": {"field_value": "COPY_doc2_dupe1.pdf"}}, {"test_name": "Document Upload - COPY_doc2_dupe1.pdf - content_type", "status": "PASSED", "message": "Required field 'content_type' exists", "expected": null, "actual": null, "details": {"field_value": "application/pdf"}}, {"test_name": "Document Upload - COPY_doc2_dupe1.pdf - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-10T07:04:08.321951Z"}}, {"test_name": "Document Upload - COPY_doc2_dupe1.pdf - Label", "status": "PASSED", "message": "label has correct value: COPY_doc2_dupe1.pdf", "expected": "COPY_doc2_dupe1.pdf", "actual": "COPY_doc2_dupe1.pdf", "details": {}}, {"test_name": "Document Upload - COPY_doc2_dupe1.pdf - Content Type", "status": "PASSED", "message": "content_type has correct value: application/pdf", "expected": "application/pdf", "actual": "application/pdf", "details": {}}, {"test_name": "DocumentData for 1tB5N1 - Existence", "status": "PASSED", "message": "DocumentData found for document 1tB5N1", "expected": null, "actual": null, "details": {"doc_data_id": "x8JOW1"}}, {"test_name": "DocumentData for 1tB5N1 - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "x8JOW1"}}, {"test_name": "DocumentData for 1tB5N1 - document", "status": "PASSED", "message": "Required field 'document' exists", "expected": null, "actual": null, "details": {"field_value": "https://api-sandbox.glynt.ai/v6/data-pools/Fj8uL1/documents/1tB5N1/"}}, {"test_name": "DocumentData for 1tB5N1 - status", "status": "PASSED", "message": "Required field 'status' exists", "expected": null, "actual": null, "details": {"field_value": "PENDING_CREATION"}}, {"test_name": "DocumentData for 1tB5N1 - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-10T07:04:08.334711Z"}}, {"test_name": "DocumentData for 1tB5N1 - updated_at", "status": "PASSED", "message": "Required field 'updated_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-10T07:04:08.334767Z"}}, {"test_name": "DocumentData for 1tB5N1 - ID Type", "status": "PASSED", "message": "Field 'id' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for 1tB5N1 - Status Type", "status": "PASSED", "message": "Field 'status' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for 1tB5N1 - Status Valid", "status": "PASSED", "message": "DocumentData has valid status: PENDING_CREATION", "expected": null, "actual": null, "details": {}}, {"test_name": "Document Upload - COPY_doc3_dupe1.pdf - Success", "status": "PASSED", "message": "Document uploaded successfully with ID: 1HbHy1", "expected": null, "actual": null, "details": {"document_id": "1HbHy1", "file_name": "COPY_doc3_dupe1.pdf"}}, {"test_name": "Document Upload - COPY_doc3_dupe1.pdf - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "1HbHy1"}}, {"test_name": "Document Upload - COPY_doc3_dupe1.pdf - label", "status": "PASSED", "message": "Required field 'label' exists", "expected": null, "actual": null, "details": {"field_value": "COPY_doc3_dupe1.pdf"}}, {"test_name": "Document Upload - COPY_doc3_dupe1.pdf - content_type", "status": "PASSED", "message": "Required field 'content_type' exists", "expected": null, "actual": null, "details": {"field_value": "application/pdf"}}, {"test_name": "Document Upload - COPY_doc3_dupe1.pdf - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-10T07:04:11.420869Z"}}, {"test_name": "Document Upload - COPY_doc3_dupe1.pdf - Label", "status": "PASSED", "message": "label has correct value: COPY_doc3_dupe1.pdf", "expected": "COPY_doc3_dupe1.pdf", "actual": "COPY_doc3_dupe1.pdf", "details": {}}, {"test_name": "Document Upload - COPY_doc3_dupe1.pdf - Content Type", "status": "PASSED", "message": "content_type has correct value: application/pdf", "expected": "application/pdf", "actual": "application/pdf", "details": {}}, {"test_name": "DocumentData for 1HbHy1 - Existence", "status": "PASSED", "message": "DocumentData found for document 1HbHy1", "expected": null, "actual": null, "details": {"doc_data_id": "xWia72"}}, {"test_name": "DocumentData for 1HbHy1 - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "xWia72"}}, {"test_name": "DocumentData for 1HbHy1 - document", "status": "PASSED", "message": "Required field 'document' exists", "expected": null, "actual": null, "details": {"field_value": "https://api-sandbox.glynt.ai/v6/data-pools/Fj8uL1/documents/1HbHy1/"}}, {"test_name": "DocumentData for 1HbHy1 - status", "status": "PASSED", "message": "Required field 'status' exists", "expected": null, "actual": null, "details": {"field_value": "PENDING_CREATION"}}, {"test_name": "DocumentData for 1HbHy1 - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-10T07:04:11.431557Z"}}, {"test_name": "DocumentData for 1HbHy1 - updated_at", "status": "PASSED", "message": "Required field 'updated_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-10T07:04:11.431598Z"}}, {"test_name": "DocumentData for 1HbHy1 - ID Type", "status": "PASSED", "message": "Field 'id' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for 1HbHy1 - Status Type", "status": "PASSED", "message": "Field 'status' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for 1HbHy1 - Status Valid", "status": "PASSED", "message": "DocumentData has valid status: PENDING_CREATION", "expected": null, "actual": null, "details": {}}, {"test_name": "Document Upload - COPY_doc3_dupe2.pdf - Success", "status": "PASSED", "message": "Document uploaded successfully with ID: StQG92", "expected": null, "actual": null, "details": {"document_id": "StQG92", "file_name": "COPY_doc3_dupe2.pdf"}}, {"test_name": "Document Upload - COPY_doc3_dupe2.pdf - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "StQG92"}}, {"test_name": "Document Upload - COPY_doc3_dupe2.pdf - label", "status": "PASSED", "message": "Required field 'label' exists", "expected": null, "actual": null, "details": {"field_value": "COPY_doc3_dupe2.pdf"}}, {"test_name": "Document Upload - COPY_doc3_dupe2.pdf - content_type", "status": "PASSED", "message": "Required field 'content_type' exists", "expected": null, "actual": null, "details": {"field_value": "application/pdf"}}, {"test_name": "Document Upload - COPY_doc3_dupe2.pdf - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-10T07:04:14.554059Z"}}, {"test_name": "Document Upload - COPY_doc3_dupe2.pdf - Label", "status": "PASSED", "message": "label has correct value: COPY_doc3_dupe2.pdf", "expected": "COPY_doc3_dupe2.pdf", "actual": "COPY_doc3_dupe2.pdf", "details": {}}, {"test_name": "Document Upload - COPY_doc3_dupe2.pdf - Content Type", "status": "PASSED", "message": "content_type has correct value: application/pdf", "expected": "application/pdf", "actual": "application/pdf", "details": {}}, {"test_name": "DocumentData for StQG92 - Existence", "status": "PASSED", "message": "DocumentData found for document StQG92", "expected": null, "actual": null, "details": {"doc_data_id": "8TetC1"}}, {"test_name": "DocumentData for StQG92 - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "8TetC1"}}, {"test_name": "DocumentData for StQG92 - document", "status": "PASSED", "message": "Required field 'document' exists", "expected": null, "actual": null, "details": {"field_value": "https://api-sandbox.glynt.ai/v6/data-pools/Fj8uL1/documents/StQG92/"}}, {"test_name": "DocumentData for StQG92 - status", "status": "PASSED", "message": "Required field 'status' exists", "expected": null, "actual": null, "details": {"field_value": "PENDING_CREATION"}}, {"test_name": "DocumentData for StQG92 - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-10T07:04:14.566647Z"}}, {"test_name": "DocumentData for StQG92 - updated_at", "status": "PASSED", "message": "Required field 'updated_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-10T07:04:14.566688Z"}}, {"test_name": "DocumentData for StQG92 - ID Type", "status": "PASSED", "message": "Field 'id' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for StQG92 - Status Type", "status": "PASSED", "message": "Field 'status' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for StQG92 - Status Valid", "status": "PASSED", "message": "DocumentData has valid status: PENDING_CREATION", "expected": null, "actual": null, "details": {}}, {"test_name": "Document Upload - orig_doc_1.pdf - Success", "status": "PASSED", "message": "Document uploaded successfully with ID: ly6oT1", "expected": null, "actual": null, "details": {"document_id": "ly6oT1", "file_name": "orig_doc_1.pdf"}}, {"test_name": "Document Upload - orig_doc_1.pdf - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "ly6oT1"}}, {"test_name": "Document Upload - orig_doc_1.pdf - label", "status": "PASSED", "message": "Required field 'label' exists", "expected": null, "actual": null, "details": {"field_value": "orig_doc_1.pdf"}}, {"test_name": "Document Upload - orig_doc_1.pdf - content_type", "status": "PASSED", "message": "Required field 'content_type' exists", "expected": null, "actual": null, "details": {"field_value": "application/pdf"}}, {"test_name": "Document Upload - orig_doc_1.pdf - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-10T07:04:17.668952Z"}}, {"test_name": "Document Upload - orig_doc_1.pdf - Label", "status": "PASSED", "message": "label has correct value: orig_doc_1.pdf", "expected": "orig_doc_1.pdf", "actual": "orig_doc_1.pdf", "details": {}}, {"test_name": "Document Upload - orig_doc_1.pdf - Content Type", "status": "PASSED", "message": "content_type has correct value: application/pdf", "expected": "application/pdf", "actual": "application/pdf", "details": {}}, {"test_name": "DocumentData for ly6oT1 - Existence", "status": "PASSED", "message": "DocumentData found for document ly6oT1", "expected": null, "actual": null, "details": {"doc_data_id": "hEE7d1"}}, {"test_name": "DocumentData for ly6oT1 - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "hEE7d1"}}, {"test_name": "DocumentData for ly6oT1 - document", "status": "PASSED", "message": "Required field 'document' exists", "expected": null, "actual": null, "details": {"field_value": "https://api-sandbox.glynt.ai/v6/data-pools/Fj8uL1/documents/ly6oT1/"}}, {"test_name": "DocumentData for ly6oT1 - status", "status": "PASSED", "message": "Required field 'status' exists", "expected": null, "actual": null, "details": {"field_value": "PENDING_CREATION"}}, {"test_name": "DocumentData for ly6oT1 - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-10T07:04:17.687762Z"}}, {"test_name": "DocumentData for ly6oT1 - updated_at", "status": "PASSED", "message": "Required field 'updated_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-10T07:04:17.687808Z"}}, {"test_name": "DocumentData for ly6oT1 - ID Type", "status": "PASSED", "message": "Field 'id' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for ly6oT1 - Status Type", "status": "PASSED", "message": "Field 'status' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for ly6oT1 - Status Valid", "status": "PASSED", "message": "DocumentData has valid status: PENDING_CREATION", "expected": null, "actual": null, "details": {}}, {"test_name": "Document Upload - origin_doc_2.pdf - Success", "status": "PASSED", "message": "Document uploaded successfully with ID: Kkg1u1", "expected": null, "actual": null, "details": {"document_id": "Kkg1u1", "file_name": "origin_doc_2.pdf"}}, {"test_name": "Document Upload - origin_doc_2.pdf - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "Kkg1u1"}}, {"test_name": "Document Upload - origin_doc_2.pdf - label", "status": "PASSED", "message": "Required field 'label' exists", "expected": null, "actual": null, "details": {"field_value": "origin_doc_2.pdf"}}, {"test_name": "Document Upload - origin_doc_2.pdf - content_type", "status": "PASSED", "message": "Required field 'content_type' exists", "expected": null, "actual": null, "details": {"field_value": "application/pdf"}}, {"test_name": "Document Upload - origin_doc_2.pdf - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-10T07:04:20.907618Z"}}, {"test_name": "Document Upload - origin_doc_2.pdf - Label", "status": "PASSED", "message": "label has correct value: origin_doc_2.pdf", "expected": "origin_doc_2.pdf", "actual": "origin_doc_2.pdf", "details": {}}, {"test_name": "Document Upload - origin_doc_2.pdf - Content Type", "status": "PASSED", "message": "content_type has correct value: application/pdf", "expected": "application/pdf", "actual": "application/pdf", "details": {}}, {"test_name": "DocumentData for Kkg1u1 - Existence", "status": "PASSED", "message": "DocumentData found for document Kkg1u1", "expected": null, "actual": null, "details": {"doc_data_id": "G0oK32"}}, {"test_name": "DocumentData for Kkg1u1 - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "G0oK32"}}, {"test_name": "DocumentData for Kkg1u1 - document", "status": "PASSED", "message": "Required field 'document' exists", "expected": null, "actual": null, "details": {"field_value": "https://api-sandbox.glynt.ai/v6/data-pools/Fj8uL1/documents/Kkg1u1/"}}, {"test_name": "DocumentData for Kkg1u1 - status", "status": "PASSED", "message": "Required field 'status' exists", "expected": null, "actual": null, "details": {"field_value": "PENDING_CREATION"}}, {"test_name": "DocumentData for Kkg1u1 - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-10T07:04:20.917728Z"}}, {"test_name": "DocumentData for Kkg1u1 - updated_at", "status": "PASSED", "message": "Required field 'updated_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-10T07:04:20.917768Z"}}, {"test_name": "DocumentData for Kkg1u1 - ID Type", "status": "PASSED", "message": "Field 'id' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for Kkg1u1 - Status Type", "status": "PASSED", "message": "Field 'status' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for Kkg1u1 - Status Valid", "status": "PASSED", "message": "DocumentData has valid status: PENDING_CREATION", "expected": null, "actual": null, "details": {}}, {"test_name": "Document Upload - orig_doc_3.pdf - Success", "status": "PASSED", "message": "Document uploaded successfully with ID: dpMZE1", "expected": null, "actual": null, "details": {"document_id": "dpMZE1", "file_name": "orig_doc_3.pdf"}}, {"test_name": "Document Upload - orig_doc_3.pdf - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "dpMZE1"}}, {"test_name": "Document Upload - orig_doc_3.pdf - label", "status": "PASSED", "message": "Required field 'label' exists", "expected": null, "actual": null, "details": {"field_value": "orig_doc_3.pdf"}}, {"test_name": "Document Upload - orig_doc_3.pdf - content_type", "status": "PASSED", "message": "Required field 'content_type' exists", "expected": null, "actual": null, "details": {"field_value": "application/pdf"}}, {"test_name": "Document Upload - orig_doc_3.pdf - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-10T07:04:24.141859Z"}}, {"test_name": "Document Upload - orig_doc_3.pdf - Label", "status": "PASSED", "message": "label has correct value: orig_doc_3.pdf", "expected": "orig_doc_3.pdf", "actual": "orig_doc_3.pdf", "details": {}}, {"test_name": "Document Upload - orig_doc_3.pdf - Content Type", "status": "PASSED", "message": "content_type has correct value: application/pdf", "expected": "application/pdf", "actual": "application/pdf", "details": {}}, {"test_name": "DocumentData for dpMZE1 - Existence", "status": "PASSED", "message": "DocumentData found for document dpMZE1", "expected": null, "actual": null, "details": {"doc_data_id": "Z5UsN1"}}, {"test_name": "DocumentData for dpMZE1 - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "Z5UsN1"}}, {"test_name": "DocumentData for dpMZE1 - document", "status": "PASSED", "message": "Required field 'document' exists", "expected": null, "actual": null, "details": {"field_value": "https://api-sandbox.glynt.ai/v6/data-pools/Fj8uL1/documents/dpMZE1/"}}, {"test_name": "DocumentData for dpMZE1 - status", "status": "PASSED", "message": "Required field 'status' exists", "expected": null, "actual": null, "details": {"field_value": "PENDING_CREATION"}}, {"test_name": "DocumentData for dpMZE1 - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-10T07:04:24.152563Z"}}, {"test_name": "DocumentData for dpMZE1 - updated_at", "status": "PASSED", "message": "Required field 'updated_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-10T07:04:24.152604Z"}}, {"test_name": "DocumentData for dpMZE1 - ID Type", "status": "PASSED", "message": "Field 'id' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for dpMZE1 - Status Type", "status": "PASSED", "message": "Field 'status' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for dpMZE1 - Status Valid", "status": "PASSED", "message": "DocumentData has valid status: PENDING_CREATION", "expected": null, "actual": null, "details": {}}, {"test_name": "Package krj7J1 Entries - Entry Count", "status": "PASSED", "message": "Found 6 package entries", "expected": null, "actual": null, "details": {"entry_count": 6}}, {"test_name": "Package krj7J1 Entries - Duplicate Exclusion", "status": "PASSED", "message": "Duplicate exclusion working: 4 documents excluded as duplicates", "expected": null, "actual": null, "details": {"excluded_count": 4}}, {"test_name": "Package krj7J1 Config Compliance - Configuration Recorded", "status": "PASSED", "message": "Package configuration recorded for compliance verification", "expected": null, "actual": null, "details": {"include_source_files": true, "include_relationship_details": true, "output_format": "json", "grouping_strategy": "ALL_IN_ONE", "dedup_fields": ["CustomerName", "<PERSON><PERSON><PERSON><PERSON>", "StatementDate"]}}, {"test_name": "Package krj7J1 Entries - Entry Count", "status": "PASSED", "message": "Found 6 package entries", "expected": null, "actual": null, "details": {"entry_count": 6}}, {"test_name": "Package krj7J1 Entries - Duplicate Exclusion", "status": "PASSED", "message": "Duplicate exclusion working: 4 documents excluded as duplicates", "expected": null, "actual": null, "details": {"excluded_count": 4}}, {"test_name": "DocumentData Fields - source_file_id", "status": "PASSED", "message": "All documents have source_file_id populated", "expected": null, "actual": null, "details": {}}, {"test_name": "DocumentData Fields - source_file_name", "status": "PASSED", "message": "All documents have source_file_name populated", "expected": null, "actual": null, "details": {}}, {"test_name": "DocumentData Fields - source_file_md5", "status": "PASSED", "message": "All documents have source_file_md5 populated", "expected": null, "actual": null, "details": {}}, {"test_name": "DocumentData Fields - received_at", "status": "PASSED", "message": "All documents have received_at populated", "expected": null, "actual": null, "details": {}}, {"test_name": "DocumentData Fields - source_file_num_pages", "status": "PASSED", "message": "All documents have source_file_num_pages populated", "expected": null, "actual": null, "details": {}}, {"test_name": "DocumentData Fields - source_file_size", "status": "PASSED", "message": "All documents have source_file_size populated", "expected": null, "actual": null, "details": {}}, {"test_name": "DocumentData Fields - source_file_detected_language", "status": "FAILED", "message": "No documents have source_file_detected_language populated", "expected": null, "actual": null, "details": {}}, {"test_name": "DocumentData Fields - canonical_document_data", "status": "PASSED", "message": "4 documents have canonical_document_data populated (indicates duplicate relationships)", "expected": null, "actual": null, "details": {}}, {"test_name": "Package krj7J1 Config Compliance - Configuration Recorded", "status": "PASSED", "message": "Package configuration recorded for compliance verification", "expected": null, "actual": null, "details": {"include_source_files": true, "include_relationship_details": true, "output_format": "json", "grouping_strategy": "ALL_IN_ONE", "dedup_fields": ["CustomerName", "<PERSON><PERSON><PERSON><PERSON>", "StatementDate"]}}, {"test_name": "Package krj7J1 Verification - Status Updated", "status": "PASSED", "message": "Package status successfully updated from PROCESSING_SUCCESSFUL to VERIFIED", "expected": null, "actual": null, "details": {"initial_status": "PROCESSING_SUCCESSFUL", "final_status": "VERIFIED"}}, {"test_name": "Package krj7J1 Final Delivery - Preview Count", "status": "FAILED", "message": "Preview shows 0 documents, expected 1", "expected": null, "actual": null, "details": {"preview_count": 0, "expected": 1}}, {"test_name": "Package krj7J1 Final Delivery - Download Available", "status": "PASSED", "message": "Package download endpoint is accessible", "expected": null, "actual": null, "details": {"download_available": true}}, {"test_name": "Flag Variations - Full CoC + Duplicates ON + Relationships ON Creation", "status": "FAILED", "message": "Failed to create test packager for Full CoC + Duplicates ON + Relationships ON", "expected": null, "actual": null, "details": {}}, {"test_name": "Flag Variations - Essential CoC + Duplicates OFF + Relationships ON Creation", "status": "FAILED", "message": "Failed to create test packager for Essential CoC + Duplicates OFF + Relationships ON", "expected": null, "actual": null, "details": {}}, {"test_name": "Flag Variations - No CoC + All Flags OFF Creation", "status": "FAILED", "message": "Failed to create test packager for No CoC + All Flags OFF", "expected": null, "actual": null, "details": {}}]}