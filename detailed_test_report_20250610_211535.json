{"test_run_info": {"timestamp": "2025-06-10T21:15:35.750238", "total_tests": 188, "duration_seconds": 197.3273937702179, "api_calls": 95, "api_errors": 7}, "summary": {"passed": 182, "failed": 6, "partial": 0, "errors": 0, "skipped": 0}, "test_results": [{"test_name": "Packager Config - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "VtLDt1"}}, {"test_name": "Packager Config - label", "status": "PASSED", "message": "Required field 'label' exists", "expected": null, "actual": null, "details": {"field_value": "Enhanced Test Script Packager"}}, {"test_name": "Packager Config - packager_schedule_type", "status": "PASSED", "message": "Required field 'packager_schedule_type' exists", "expected": null, "actual": null, "details": {"field_value": "MANUAL"}}, {"test_name": "Packager Config - document_status_filter", "status": "PASSED", "message": "Required field 'document_status_filter' exists", "expected": null, "actual": null, "details": {"field_value": ["VERIFIED"]}}, {"test_name": "Packager Config - eb_status_filter", "status": "PASSED", "message": "Required field 'eb_status_filter' exists", "expected": null, "actual": null, "details": {"field_value": ["VERIFIED"]}}, {"test_name": "Packager Config - output_format", "status": "PASSED", "message": "Required field 'output_format' exists", "expected": null, "actual": null, "details": {"field_value": "json"}}, {"test_name": "Packager Config - delivery_data_grouping_strategy", "status": "PASSED", "message": "Required field 'delivery_data_grouping_strategy' exists", "expected": null, "actual": null, "details": {"field_value": "ALL_IN_ONE"}}, {"test_name": "Packager Config - Label", "status": "PASSED", "message": "label has correct value: Enhanced Test Script Packager", "expected": "Enhanced Test Script Packager", "actual": "Enhanced Test Script Packager", "details": {}}, {"test_name": "Packager Config - Schedule Type", "status": "PASSED", "message": "packager_schedule_type has correct value: MANUAL", "expected": "MANUAL", "actual": "MANUAL", "details": {}}, {"test_name": "Packager Config - Output Format", "status": "PASSED", "message": "output_format has correct value: json", "expected": "json", "actual": "json", "details": {}}, {"test_name": "Packager Config - Document Status Filter", "status": "PASSED", "message": "Document status filter correct: ['VERIFIED']", "expected": ["VERIFIED"], "actual": ["VERIFIED"], "details": {}}, {"test_name": "Packager Config - Deduplication Fields", "status": "PASSED", "message": "Deduplication fields correct: []", "expected": [], "actual": [], "details": {}}, {"test_name": "Packager Config - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "VtLDt1"}}, {"test_name": "Packager Config - label", "status": "PASSED", "message": "Required field 'label' exists", "expected": null, "actual": null, "details": {"field_value": "Enhanced Test Script Packager"}}, {"test_name": "Packager Config - packager_schedule_type", "status": "PASSED", "message": "Required field 'packager_schedule_type' exists", "expected": null, "actual": null, "details": {"field_value": "MANUAL"}}, {"test_name": "Packager Config - document_status_filter", "status": "PASSED", "message": "Required field 'document_status_filter' exists", "expected": null, "actual": null, "details": {"field_value": ["VERIFIED"]}}, {"test_name": "Packager Config - eb_status_filter", "status": "PASSED", "message": "Required field 'eb_status_filter' exists", "expected": null, "actual": null, "details": {"field_value": ["VERIFIED"]}}, {"test_name": "Packager Config - output_format", "status": "PASSED", "message": "Required field 'output_format' exists", "expected": null, "actual": null, "details": {"field_value": "json"}}, {"test_name": "Packager Config - delivery_data_grouping_strategy", "status": "PASSED", "message": "Required field 'delivery_data_grouping_strategy' exists", "expected": null, "actual": null, "details": {"field_value": "ALL_IN_ONE"}}, {"test_name": "Packager Config - Label", "status": "PASSED", "message": "label has correct value: Enhanced Test Script Packager", "expected": "Enhanced Test Script Packager", "actual": "Enhanced Test Script Packager", "details": {}}, {"test_name": "Packager Config - Schedule Type", "status": "PASSED", "message": "packager_schedule_type has correct value: MANUAL", "expected": "MANUAL", "actual": "MANUAL", "details": {}}, {"test_name": "Packager Config - Output Format", "status": "PASSED", "message": "output_format has correct value: json", "expected": "json", "actual": "json", "details": {}}, {"test_name": "Packager Config - Document Status Filter", "status": "PASSED", "message": "Document status filter correct: ['VERIFIED']", "expected": ["VERIFIED"], "actual": ["VERIFIED"], "details": {}}, {"test_name": "Packager Config - Deduplication Fields", "status": "PASSED", "message": "Deduplication fields correct: []", "expected": [], "actual": [], "details": {}}, {"test_name": "Invalid Config - packager_schedule_type", "status": "PASSED", "message": "Invalid configuration properly rejected for field: packager_schedule_type", "expected": null, "actual": null, "details": {}}, {"test_name": "Invalid Config - output_format", "status": "PASSED", "message": "Invalid configuration properly rejected for field: output_format", "expected": null, "actual": null, "details": {}}, {"test_name": "Invalid Config - label", "status": "FAILED", "message": "Invalid configuration was accepted (should have been rejected): label", "expected": null, "actual": null, "details": {"created_packager_id": "oy1lD1"}}, {"test_name": "Document Upload - COPY_doc2_dupe1.pdf - Success", "status": "PASSED", "message": "Document uploaded successfully with ID: n4g8E1", "expected": null, "actual": null, "details": {"document_id": "n4g8E1", "file_name": "COPY_doc2_dupe1.pdf"}}, {"test_name": "Document Upload - COPY_doc2_dupe1.pdf - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "n4g8E1"}}, {"test_name": "Document Upload - COPY_doc2_dupe1.pdf - label", "status": "PASSED", "message": "Required field 'label' exists", "expected": null, "actual": null, "details": {"field_value": "COPY_doc2_dupe1.pdf"}}, {"test_name": "Document Upload - COPY_doc2_dupe1.pdf - content_type", "status": "PASSED", "message": "Required field 'content_type' exists", "expected": null, "actual": null, "details": {"field_value": "application/pdf"}}, {"test_name": "Document Upload - COPY_doc2_dupe1.pdf - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T02:12:49.873391Z"}}, {"test_name": "Document Upload - COPY_doc2_dupe1.pdf - Label", "status": "PASSED", "message": "label has correct value: COPY_doc2_dupe1.pdf", "expected": "COPY_doc2_dupe1.pdf", "actual": "COPY_doc2_dupe1.pdf", "details": {}}, {"test_name": "Document Upload - COPY_doc2_dupe1.pdf - Content Type", "status": "PASSED", "message": "content_type has correct value: application/pdf", "expected": "application/pdf", "actual": "application/pdf", "details": {}}, {"test_name": "DocumentData for n4g8E1 - Existence", "status": "PASSED", "message": "DocumentData found for document n4g8E1", "expected": null, "actual": null, "details": {"doc_data_id": "jKnRN1"}}, {"test_name": "DocumentData for n4g8E1 - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "jKnRN1"}}, {"test_name": "DocumentData for n4g8E1 - document", "status": "PASSED", "message": "Required field 'document' exists", "expected": null, "actual": null, "details": {"field_value": "https://api-sandbox.glynt.ai/v6/data-pools/Fj8uL1/documents/n4g8E1/"}}, {"test_name": "DocumentData for n4g8E1 - status", "status": "PASSED", "message": "Required field 'status' exists", "expected": null, "actual": null, "details": {"field_value": "PENDING_CREATION"}}, {"test_name": "DocumentData for n4g8E1 - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T02:12:49.884864Z"}}, {"test_name": "DocumentData for n4g8E1 - updated_at", "status": "PASSED", "message": "Required field 'updated_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T02:12:49.884906Z"}}, {"test_name": "DocumentData for n4g8E1 - ID Type", "status": "PASSED", "message": "Field 'id' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for n4g8E1 - Status Type", "status": "PASSED", "message": "Field 'status' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for n4g8E1 - Status Valid", "status": "PASSED", "message": "DocumentData has valid status: PENDING_CREATION", "expected": null, "actual": null, "details": {}}, {"test_name": "Document Upload - COPY_doc3_dupe1.pdf - Success", "status": "PASSED", "message": "Document uploaded successfully with ID: MqFMe1", "expected": null, "actual": null, "details": {"document_id": "MqFMe1", "file_name": "COPY_doc3_dupe1.pdf"}}, {"test_name": "Document Upload - COPY_doc3_dupe1.pdf - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "MqFMe1"}}, {"test_name": "Document Upload - COPY_doc3_dupe1.pdf - label", "status": "PASSED", "message": "Required field 'label' exists", "expected": null, "actual": null, "details": {"field_value": "COPY_doc3_dupe1.pdf"}}, {"test_name": "Document Upload - COPY_doc3_dupe1.pdf - content_type", "status": "PASSED", "message": "Required field 'content_type' exists", "expected": null, "actual": null, "details": {"field_value": "application/pdf"}}, {"test_name": "Document Upload - COPY_doc3_dupe1.pdf - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T02:12:53.276359Z"}}, {"test_name": "Document Upload - COPY_doc3_dupe1.pdf - Label", "status": "PASSED", "message": "label has correct value: COPY_doc3_dupe1.pdf", "expected": "COPY_doc3_dupe1.pdf", "actual": "COPY_doc3_dupe1.pdf", "details": {}}, {"test_name": "Document Upload - COPY_doc3_dupe1.pdf - Content Type", "status": "PASSED", "message": "content_type has correct value: application/pdf", "expected": "application/pdf", "actual": "application/pdf", "details": {}}, {"test_name": "DocumentData for MqFMe1 - Existence", "status": "PASSED", "message": "DocumentData found for document MqFMe1", "expected": null, "actual": null, "details": {"doc_data_id": "I6Nfn1"}}, {"test_name": "DocumentData for MqFMe1 - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "I6Nfn1"}}, {"test_name": "DocumentData for MqFMe1 - document", "status": "PASSED", "message": "Required field 'document' exists", "expected": null, "actual": null, "details": {"field_value": "https://api-sandbox.glynt.ai/v6/data-pools/Fj8uL1/documents/MqFMe1/"}}, {"test_name": "DocumentData for MqFMe1 - status", "status": "PASSED", "message": "Required field 'status' exists", "expected": null, "actual": null, "details": {"field_value": "PENDING_CREATION"}}, {"test_name": "DocumentData for MqFMe1 - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T02:12:53.287911Z"}}, {"test_name": "DocumentData for MqFMe1 - updated_at", "status": "PASSED", "message": "Required field 'updated_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T02:12:53.287956Z"}}, {"test_name": "DocumentData for MqFMe1 - ID Type", "status": "PASSED", "message": "Field 'id' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for MqFMe1 - Status Type", "status": "PASSED", "message": "Field 'status' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for MqFMe1 - Status Valid", "status": "PASSED", "message": "DocumentData has valid status: PENDING_CREATION", "expected": null, "actual": null, "details": {}}, {"test_name": "Document Upload - COPY_doc3_dupe2.pdf - Success", "status": "PASSED", "message": "Document uploaded successfully with ID: vbpZ42", "expected": null, "actual": null, "details": {"document_id": "vbpZ42", "file_name": "COPY_doc3_dupe2.pdf"}}, {"test_name": "Document Upload - COPY_doc3_dupe2.pdf - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "vbpZ42"}}, {"test_name": "Document Upload - COPY_doc3_dupe2.pdf - label", "status": "PASSED", "message": "Required field 'label' exists", "expected": null, "actual": null, "details": {"field_value": "COPY_doc3_dupe2.pdf"}}, {"test_name": "Document Upload - COPY_doc3_dupe2.pdf - content_type", "status": "PASSED", "message": "Required field 'content_type' exists", "expected": null, "actual": null, "details": {"field_value": "application/pdf"}}, {"test_name": "Document Upload - COPY_doc3_dupe2.pdf - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T02:12:56.654154Z"}}, {"test_name": "Document Upload - COPY_doc3_dupe2.pdf - Label", "status": "PASSED", "message": "label has correct value: COPY_doc3_dupe2.pdf", "expected": "COPY_doc3_dupe2.pdf", "actual": "COPY_doc3_dupe2.pdf", "details": {}}, {"test_name": "Document Upload - COPY_doc3_dupe2.pdf - Content Type", "status": "PASSED", "message": "content_type has correct value: application/pdf", "expected": "application/pdf", "actual": "application/pdf", "details": {}}, {"test_name": "DocumentData for vbpZ42 - Existence", "status": "PASSED", "message": "DocumentData found for document vbpZ42", "expected": null, "actual": null, "details": {"doc_data_id": "bB3D81"}}, {"test_name": "DocumentData for vbpZ42 - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "bB3D81"}}, {"test_name": "DocumentData for vbpZ42 - document", "status": "PASSED", "message": "Required field 'document' exists", "expected": null, "actual": null, "details": {"field_value": "https://api-sandbox.glynt.ai/v6/data-pools/Fj8uL1/documents/vbpZ42/"}}, {"test_name": "DocumentData for vbpZ42 - status", "status": "PASSED", "message": "Required field 'status' exists", "expected": null, "actual": null, "details": {"field_value": "PENDING_CREATION"}}, {"test_name": "DocumentData for vbpZ42 - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T02:12:56.668610Z"}}, {"test_name": "DocumentData for vbpZ42 - updated_at", "status": "PASSED", "message": "Required field 'updated_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T02:12:56.668664Z"}}, {"test_name": "DocumentData for vbpZ42 - ID Type", "status": "PASSED", "message": "Field 'id' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for vbpZ42 - Status Type", "status": "PASSED", "message": "Field 'status' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for vbpZ42 - Status Valid", "status": "PASSED", "message": "DocumentData has valid status: PENDING_CREATION", "expected": null, "actual": null, "details": {}}, {"test_name": "Document Upload - orig_doc_1.pdf - Success", "status": "PASSED", "message": "Document uploaded successfully with ID: EhV7P1", "expected": null, "actual": null, "details": {"document_id": "EhV7P1", "file_name": "orig_doc_1.pdf"}}, {"test_name": "Document Upload - orig_doc_1.pdf - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "EhV7P1"}}, {"test_name": "Document Upload - orig_doc_1.pdf - label", "status": "PASSED", "message": "Required field 'label' exists", "expected": null, "actual": null, "details": {"field_value": "orig_doc_1.pdf"}}, {"test_name": "Document Upload - orig_doc_1.pdf - content_type", "status": "PASSED", "message": "Required field 'content_type' exists", "expected": null, "actual": null, "details": {"field_value": "application/pdf"}}, {"test_name": "Document Upload - orig_doc_1.pdf - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T02:13:00.111623Z"}}, {"test_name": "Document Upload - orig_doc_1.pdf - Label", "status": "PASSED", "message": "label has correct value: orig_doc_1.pdf", "expected": "orig_doc_1.pdf", "actual": "orig_doc_1.pdf", "details": {}}, {"test_name": "Document Upload - orig_doc_1.pdf - Content Type", "status": "PASSED", "message": "content_type has correct value: application/pdf", "expected": "application/pdf", "actual": "application/pdf", "details": {}}, {"test_name": "DocumentData for EhV7P1 - Existence", "status": "PASSED", "message": "DocumentData found for document EhV7P1", "expected": null, "actual": null, "details": {"doc_data_id": "AxcQY1"}}, {"test_name": "DocumentData for EhV7P1 - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "AxcQY1"}}, {"test_name": "DocumentData for EhV7P1 - document", "status": "PASSED", "message": "Required field 'document' exists", "expected": null, "actual": null, "details": {"field_value": "https://api-sandbox.glynt.ai/v6/data-pools/Fj8uL1/documents/EhV7P1/"}}, {"test_name": "DocumentData for EhV7P1 - status", "status": "PASSED", "message": "Required field 'status' exists", "expected": null, "actual": null, "details": {"field_value": "PENDING_CREATION"}}, {"test_name": "DocumentData for EhV7P1 - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T02:13:00.130815Z"}}, {"test_name": "DocumentData for EhV7P1 - updated_at", "status": "PASSED", "message": "Required field 'updated_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T02:13:00.130861Z"}}, {"test_name": "DocumentData for EhV7P1 - ID Type", "status": "PASSED", "message": "Field 'id' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for EhV7P1 - Status Type", "status": "PASSED", "message": "Field 'status' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for EhV7P1 - Status Valid", "status": "PASSED", "message": "DocumentData has valid status: PENDING_CREATION", "expected": null, "actual": null, "details": {}}, {"test_name": "Document Upload - origin_doc_2.pdf - Success", "status": "PASSED", "message": "Document uploaded successfully with ID: nS5Lp1", "expected": null, "actual": null, "details": {"document_id": "nS5Lp1", "file_name": "origin_doc_2.pdf"}}, {"test_name": "Document Upload - origin_doc_2.pdf - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "nS5Lp1"}}, {"test_name": "Document Upload - origin_doc_2.pdf - label", "status": "PASSED", "message": "Required field 'label' exists", "expected": null, "actual": null, "details": {"field_value": "origin_doc_2.pdf"}}, {"test_name": "Document Upload - origin_doc_2.pdf - content_type", "status": "PASSED", "message": "Required field 'content_type' exists", "expected": null, "actual": null, "details": {"field_value": "application/pdf"}}, {"test_name": "Document Upload - origin_doc_2.pdf - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T02:13:03.525639Z"}}, {"test_name": "Document Upload - origin_doc_2.pdf - Label", "status": "PASSED", "message": "label has correct value: origin_doc_2.pdf", "expected": "origin_doc_2.pdf", "actual": "origin_doc_2.pdf", "details": {}}, {"test_name": "Document Upload - origin_doc_2.pdf - Content Type", "status": "PASSED", "message": "content_type has correct value: application/pdf", "expected": "application/pdf", "actual": "application/pdf", "details": {}}, {"test_name": "DocumentData for nS5Lp1 - Existence", "status": "PASSED", "message": "DocumentData found for document nS5Lp1", "expected": null, "actual": null, "details": {"doc_data_id": "jiCey1"}}, {"test_name": "DocumentData for nS5Lp1 - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "jiCey1"}}, {"test_name": "DocumentData for nS5Lp1 - document", "status": "PASSED", "message": "Required field 'document' exists", "expected": null, "actual": null, "details": {"field_value": "https://api-sandbox.glynt.ai/v6/data-pools/Fj8uL1/documents/nS5Lp1/"}}, {"test_name": "DocumentData for nS5Lp1 - status", "status": "PASSED", "message": "Required field 'status' exists", "expected": null, "actual": null, "details": {"field_value": "PENDING_CREATION"}}, {"test_name": "DocumentData for nS5Lp1 - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T02:13:03.540695Z"}}, {"test_name": "DocumentData for nS5Lp1 - updated_at", "status": "PASSED", "message": "Required field 'updated_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T02:13:03.540737Z"}}, {"test_name": "DocumentData for nS5Lp1 - ID Type", "status": "PASSED", "message": "Field 'id' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for nS5Lp1 - Status Type", "status": "PASSED", "message": "Field 'status' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for nS5Lp1 - Status Valid", "status": "PASSED", "message": "DocumentData has valid status: PENDING_CREATION", "expected": null, "actual": null, "details": {}}, {"test_name": "Document Upload - orig_doc_3.pdf - Success", "status": "PASSED", "message": "Document uploaded successfully with ID: 6Yls91", "expected": null, "actual": null, "details": {"document_id": "6Yls91", "file_name": "orig_doc_3.pdf"}}, {"test_name": "Document Upload - orig_doc_3.pdf - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "6Yls91"}}, {"test_name": "Document Upload - orig_doc_3.pdf - label", "status": "PASSED", "message": "Required field 'label' exists", "expected": null, "actual": null, "details": {"field_value": "orig_doc_3.pdf"}}, {"test_name": "Document Upload - orig_doc_3.pdf - content_type", "status": "PASSED", "message": "Required field 'content_type' exists", "expected": null, "actual": null, "details": {"field_value": "application/pdf"}}, {"test_name": "Document Upload - orig_doc_3.pdf - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T02:13:06.977180Z"}}, {"test_name": "Document Upload - orig_doc_3.pdf - Label", "status": "PASSED", "message": "label has correct value: orig_doc_3.pdf", "expected": "orig_doc_3.pdf", "actual": "orig_doc_3.pdf", "details": {}}, {"test_name": "Document Upload - orig_doc_3.pdf - Content Type", "status": "PASSED", "message": "content_type has correct value: application/pdf", "expected": "application/pdf", "actual": "application/pdf", "details": {}}, {"test_name": "DocumentData for 6Yls91 - Existence", "status": "PASSED", "message": "DocumentData found for document 6Yls91", "expected": null, "actual": null, "details": {"doc_data_id": "2osBJ1"}}, {"test_name": "DocumentData for 6Yls91 - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "2osBJ1"}}, {"test_name": "DocumentData for 6Yls91 - document", "status": "PASSED", "message": "Required field 'document' exists", "expected": null, "actual": null, "details": {"field_value": "https://api-sandbox.glynt.ai/v6/data-pools/Fj8uL1/documents/6Yls91/"}}, {"test_name": "DocumentData for 6Yls91 - status", "status": "PASSED", "message": "Required field 'status' exists", "expected": null, "actual": null, "details": {"field_value": "PENDING_CREATION"}}, {"test_name": "DocumentData for 6Yls91 - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T02:13:06.994881Z"}}, {"test_name": "DocumentData for 6Yls91 - updated_at", "status": "PASSED", "message": "Required field 'updated_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T02:13:06.994936Z"}}, {"test_name": "DocumentData for 6Yls91 - ID Type", "status": "PASSED", "message": "Field 'id' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for 6Yls91 - Status Type", "status": "PASSED", "message": "Field 'status' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for 6Yls91 - Status Valid", "status": "PASSED", "message": "DocumentData has valid status: PENDING_CREATION", "expected": null, "actual": null, "details": {}}, {"test_name": "Package BUZ6U1 Entries - Entry Count", "status": "PASSED", "message": "Found 6 package entries", "expected": null, "actual": null, "details": {"entry_count": 6}}, {"test_name": "Package BUZ6U1 Entries - Duplicate Exclusion", "status": "PASSED", "message": "Duplicate exclusion working: 4 documents excluded as duplicates", "expected": null, "actual": null, "details": {"excluded_count": 4}}, {"test_name": "Package BUZ6U1 Config Compliance - Configuration Recorded", "status": "PASSED", "message": "Package configuration recorded for compliance verification", "expected": null, "actual": null, "details": {"include_source_files": true, "include_relationship_details": true, "output_format": "json", "grouping_strategy": "ALL_IN_ONE", "dedup_fields": ["CustomerName", "<PERSON><PERSON><PERSON><PERSON>", "StatementDate"]}}, {"test_name": "Simulation Format", "status": "PASSED", "message": "Response follows pure delivery format correctly", "expected": null, "actual": null, "details": {"data_items_count": 1}}, {"test_name": "Simulation Content - Item 1 - Filename Format", "status": "PASSED", "message": "Filename format correct: DOCDATA_7517_SYtooI.json", "expected": null, "actual": null, "details": {}}, {"test_name": "Simulation Content - Item 1 - DocumentID Field", "status": "PASSED", "message": "DocumentID field present: n4g8E1", "expected": null, "actual": null, "details": {}}, {"test_name": "Simulation Content - Item 1 - JSON Structure", "status": "PASSED", "message": "Valid JSON with 2 documents", "expected": null, "actual": null, "details": {}}, {"test_name": "Duplicate Filtering Scenarios - Exclude ON Format", "status": "PASSED", "message": "Response follows pure delivery format correctly", "expected": null, "actual": null, "details": {"data_items_count": 1}}, {"test_name": "Duplicate Filtering Scenarios - Exclude OFF Format", "status": "PASSED", "message": "Response follows pure delivery format correctly", "expected": null, "actual": null, "details": {"data_items_count": 1}}, {"test_name": "Duplicate Filtering Scenarios - Duplicate Filtering Logic", "status": "PASSED", "message": "Exclude OFF (1) >= Exclude ON (1) as expected", "expected": null, "actual": null, "details": {"exclude_on_count": 1, "exclude_off_count": 1}}, {"test_name": "Package BUZ6U1 Entries - Entry Count", "status": "PASSED", "message": "Found 6 package entries", "expected": null, "actual": null, "details": {"entry_count": 6}}, {"test_name": "Package BUZ6U1 Entries - Duplicate Exclusion", "status": "PASSED", "message": "Duplicate exclusion working: 4 documents excluded as duplicates", "expected": null, "actual": null, "details": {"excluded_count": 4}}, {"test_name": "DocumentData Fields - source_file_id", "status": "PASSED", "message": "All documents have source_file_id populated", "expected": null, "actual": null, "details": {}}, {"test_name": "DocumentData Fields - source_file_name", "status": "PASSED", "message": "All documents have source_file_name populated", "expected": null, "actual": null, "details": {}}, {"test_name": "DocumentData Fields - source_file_md5", "status": "PASSED", "message": "All documents have source_file_md5 populated", "expected": null, "actual": null, "details": {}}, {"test_name": "DocumentData Fields - received_at", "status": "PASSED", "message": "All documents have received_at populated", "expected": null, "actual": null, "details": {}}, {"test_name": "DocumentData Fields - source_file_num_pages", "status": "PASSED", "message": "All documents have source_file_num_pages populated", "expected": null, "actual": null, "details": {}}, {"test_name": "DocumentData Fields - source_file_size", "status": "PASSED", "message": "All documents have source_file_size populated", "expected": null, "actual": null, "details": {}}, {"test_name": "DocumentData Fields - source_file_detected_language", "status": "FAILED", "message": "No documents have source_file_detected_language populated", "expected": null, "actual": null, "details": {}}, {"test_name": "DocumentData Fields - canonical_document_data", "status": "PASSED", "message": "4 documents have canonical_document_data populated (indicates duplicate relationships)", "expected": null, "actual": null, "details": {}}, {"test_name": "Package BUZ6U1 Config Compliance - Configuration Recorded", "status": "PASSED", "message": "Package configuration recorded for compliance verification", "expected": null, "actual": null, "details": {"include_source_files": true, "include_relationship_details": true, "output_format": "json", "grouping_strategy": "ALL_IN_ONE", "dedup_fields": ["CustomerName", "<PERSON><PERSON><PERSON><PERSON>", "StatementDate"]}}, {"test_name": "Package BUZ6U1 Verification - Status Updated", "status": "PASSED", "message": "Package status successfully updated from PROCESSING_SUCCESSFUL to VERIFIED", "expected": null, "actual": null, "details": {"initial_status": "PROCESSING_SUCCESSFUL", "final_status": "VERIFIED"}}, {"test_name": "Package BUZ6U1 Final Delivery - Preview Format", "status": "PASSED", "message": "Response follows pure delivery format correctly", "expected": null, "actual": null, "details": {"data_items_count": 1}}, {"test_name": "Package BUZ6U1 Final Delivery - Preview Content - Item 1 - Filename Format", "status": "PASSED", "message": "Filename format correct: DOCDATA_7517_bxPRkV.json", "expected": null, "actual": null, "details": {}}, {"test_name": "Package BUZ6U1 Final Delivery - Preview Content - Item 1 - DocumentID Field", "status": "PASSED", "message": "DocumentID field present: n4g8E1", "expected": null, "actual": null, "details": {}}, {"test_name": "Package BUZ6U1 Final Delivery - Preview Content - Item 1 - JSON Structure", "status": "PASSED", "message": "Valid JSON with 2 documents", "expected": null, "actual": null, "details": {}}, {"test_name": "Package BUZ6U1 Final Delivery - Preview Count", "status": "PASSED", "message": "Preview shows correct number of documents: 1", "expected": null, "actual": null, "details": {"preview_count": 1, "expected": 1}}, {"test_name": "Package BUZ6U1 Final Delivery - Preview Deduplication", "status": "PASSED", "message": "No duplicate content found in preview data", "expected": null, "actual": null, "details": {"unique_items": 1}}, {"test_name": "Package BUZ6U1 Final Delivery - Download Available", "status": "PASSED", "message": "Package download endpoint is accessible", "expected": null, "actual": null, "details": {"download_available": true}}, {"test_name": "Flag Variations - Full CoC + Duplicates ON + Relationships ON Creation", "status": "FAILED", "message": "Failed to create test packager for Full CoC + Duplicates ON + Relationships ON", "expected": null, "actual": null, "details": {}}, {"test_name": "Flag Variations - Essential CoC + Duplicates OFF + Relationships ON Creation", "status": "FAILED", "message": "Failed to create test packager for Essential CoC + Duplicates OFF + Relationships ON", "expected": null, "actual": null, "details": {}}, {"test_name": "Flag Variations - No CoC + All Flags OFF Creation", "status": "FAILED", "message": "Failed to create test packager for No CoC + All Flags OFF", "expected": null, "actual": null, "details": {}}, {"test_name": "Debug API - CoC Fields Present", "status": "PASSED", "message": "All expected CoC fields are available in DocumentData API", "expected": null, "actual": null, "details": {}}, {"test_name": "Debug API - Minimal Config Creation", "status": "PASSED", "message": "Successfully created packager with Minimal Config", "expected": null, "actual": null, "details": {}}, {"test_name": "Debug API - Basic CoC Config Creation", "status": "PASSED", "message": "Successfully created packager with Basic CoC Config", "expected": null, "actual": null, "details": {}}, {"test_name": "Debug API - Full CoC Config Creation", "status": "PASSED", "message": "Successfully created packager with Full CoC Config", "expected": null, "actual": null, "details": {}}, {"test_name": "Document Upload - tmpilgevh5n.pdf - Success", "status": "PASSED", "message": "Document uploaded successfully with ID: fJL6a1", "expected": null, "actual": null, "details": {"document_id": "fJL6a1", "file_name": "tmpilgevh5n.pdf"}}, {"test_name": "Document Upload - tmpilgevh5n.pdf - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "fJL6a1"}}, {"test_name": "Document Upload - tmpilgevh5n.pdf - label", "status": "PASSED", "message": "Required field 'label' exists", "expected": null, "actual": null, "details": {"field_value": "tmpilgevh5n.pdf"}}, {"test_name": "Document Upload - tmpilgevh5n.pdf - content_type", "status": "PASSED", "message": "Required field 'content_type' exists", "expected": null, "actual": null, "details": {"field_value": "application/pdf"}}, {"test_name": "Document Upload - tmpilgevh5n.pdf - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T02:15:21.915977Z"}}, {"test_name": "Document Upload - tmpilgevh5n.pdf - Label", "status": "PASSED", "message": "label has correct value: tmpilgevh5n.pdf", "expected": "tmpilgevh5n.pdf", "actual": "tmpilgevh5n.pdf", "details": {}}, {"test_name": "Document Upload - tmpilgevh5n.pdf - Content Type", "status": "PASSED", "message": "content_type has correct value: application/pdf", "expected": "application/pdf", "actual": "application/pdf", "details": {}}, {"test_name": "Document Upload - tmp8_waywlq.pdf - Success", "status": "PASSED", "message": "Document uploaded successfully with ID: E5vJ02", "expected": null, "actual": null, "details": {"document_id": "E5vJ02", "file_name": "tmp8_waywlq.pdf"}}, {"test_name": "Document Upload - tmp8_waywlq.pdf - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "E5vJ02"}}, {"test_name": "Document Upload - tmp8_waywlq.pdf - label", "status": "PASSED", "message": "Required field 'label' exists", "expected": null, "actual": null, "details": {"field_value": "tmp8_waywlq.pdf"}}, {"test_name": "Document Upload - tmp8_waywlq.pdf - content_type", "status": "PASSED", "message": "Required field 'content_type' exists", "expected": null, "actual": null, "details": {"field_value": "application/pdf"}}, {"test_name": "Document Upload - tmp8_waywlq.pdf - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T02:15:22.803453Z"}}, {"test_name": "Document Upload - tmp8_waywlq.pdf - Label", "status": "PASSED", "message": "label has correct value: tmp8_waywlq.pdf", "expected": "tmp8_waywlq.pdf", "actual": "tmp8_waywlq.pdf", "details": {}}, {"test_name": "Document Upload - tmp8_waywlq.pdf - Content Type", "status": "PASSED", "message": "content_type has correct value: application/pdf", "expected": "application/pdf", "actual": "application/pdf", "details": {}}, {"test_name": "Document Upload - tmpbqu2hq61.pdf - Success", "status": "PASSED", "message": "Document uploaded successfully with ID: XAbrK1", "expected": null, "actual": null, "details": {"document_id": "XAbrK1", "file_name": "tmpbqu2hq61.pdf"}}, {"test_name": "Document Upload - tmpbqu2hq61.pdf - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "XAbrK1"}}, {"test_name": "Document Upload - tmpbqu2hq61.pdf - label", "status": "PASSED", "message": "Required field 'label' exists", "expected": null, "actual": null, "details": {"field_value": "tmpbqu2hq61.pdf"}}, {"test_name": "Document Upload - tmpbqu2hq61.pdf - content_type", "status": "PASSED", "message": "Required field 'content_type' exists", "expected": null, "actual": null, "details": {"field_value": "application/pdf"}}, {"test_name": "Document Upload - tmpbqu2hq61.pdf - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T02:15:23.993018Z"}}, {"test_name": "Document Upload - tmpbqu2hq61.pdf - Label", "status": "PASSED", "message": "label has correct value: tmpbqu2hq61.pdf", "expected": "tmpbqu2hq61.pdf", "actual": "tmpbqu2hq61.pdf", "details": {}}, {"test_name": "Document Upload - tmpbqu2hq61.pdf - Content Type", "status": "PASSED", "message": "content_type has correct value: application/pdf", "expected": "application/pdf", "actual": "application/pdf", "details": {}}, {"test_name": "Document Upload - tmpvt38q5ms.pdf - Success", "status": "PASSED", "message": "Document uploaded successfully with ID: 6wA5l1", "expected": null, "actual": null, "details": {"document_id": "6wA5l1", "file_name": "tmpvt38q5ms.pdf"}}, {"test_name": "Document Upload - tmpvt38q5ms.pdf - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "6wA5l1"}}, {"test_name": "Document Upload - tmpvt38q5ms.pdf - label", "status": "PASSED", "message": "Required field 'label' exists", "expected": null, "actual": null, "details": {"field_value": "tmpvt38q5ms.pdf"}}, {"test_name": "Document Upload - tmpvt38q5ms.pdf - content_type", "status": "PASSED", "message": "Required field 'content_type' exists", "expected": null, "actual": null, "details": {"field_value": "application/pdf"}}, {"test_name": "Document Upload - tmpvt38q5ms.pdf - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T02:15:24.763125Z"}}, {"test_name": "Document Upload - tmpvt38q5ms.pdf - Label", "status": "PASSED", "message": "label has correct value: tmpvt38q5ms.pdf", "expected": "tmpvt38q5ms.pdf", "actual": "tmpvt38q5ms.pdf", "details": {}}, {"test_name": "Document Upload - tmpvt38q5ms.pdf - Content Type", "status": "PASSED", "message": "content_type has correct value: application/pdf", "expected": "application/pdf", "actual": "application/pdf", "details": {}}, {"test_name": "Delivery Scenarios - Incremental Packager Creation", "status": "FAILED", "message": "Failed to create incremental delivery packager", "expected": null, "actual": null, "details": {}}]}