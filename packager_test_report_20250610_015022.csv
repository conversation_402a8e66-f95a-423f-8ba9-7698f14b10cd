Timestamp,Step,Status,Details
2025-06-10T01:50:22.933503,<PERSON><PERSON><PERSON> Start,Completed,
2025-06-10T01:50:25.837486,Get Access Token,Completed,
2025-06-10T01:50:53.201474,Data Pool Cleanup - Delete extractionbatchdata,Completed,Successfully deleted all 1 extractionbatchdata
2025-06-10T01:50:54.229688,Data Pool Cleanup - Delete extractionbatches,Completed,Successfully deleted all 1 extractionbatches
2025-06-10T01:50:55.045883,Data Pool Cleanup - Delete packages,Completed,Successfully deleted all 1 packages
2025-06-10T01:50:55.829019,Data Pool Cleanup - Delete packagers,Completed,Successfully deleted all 1 packagers
2025-06-10T01:50:58.934785,Data Pool Cleanup - Delete extractions,Completed,Successfully deleted all 6 extractions
2025-06-10T01:51:04.095212,Data Pool Cleanup - Delete documents,Completed,Successfully deleted all 6 documents
2025-06-10T01:51:08.552551,Data Pool Cleanup - Delete documentdata,Completed,Successfully deleted all 6 documentdata
2025-06-10T01:51:08.552829,Data Pool Cleanup,Completed,
2025-06-10T01:51:08.553479,Packager Lifecycle Test,Skipped by user,
2025-06-10T01:51:08.553698,Main Packager Creation,Skipped by user,
2025-06-10T01:51:08.553924,Upload Documents,Skipped by user,
2025-06-10T01:51:08.554122,Document Processing Status Check,Skipped by user,
2025-06-10T01:51:08.554349,Create Extraction Batch,Skipped by user,
2025-06-10T01:51:08.554640,Simulate Content Duplication,Skipped by user,
2025-06-10T01:51:08.554907,Verify Extraction Batch,Skipped by user,
2025-06-10T01:51:08.555119,Change Packager Schedule to CRON,Skipped by user,
2025-06-10T01:51:08.555412,Validate CRON Package,Skipped by user,
2025-06-10T01:51:08.555600,Test Package Reuse,Skipped by user,
2025-06-10T01:51:08.555852,Test Delete Package and Re-run CRON,Skipped by user,
2025-06-10T01:51:08.556072,Simulate Package,Skipped by user,
2025-06-10T01:51:08.556266,Create Package,Skipped by user,
2025-06-10T01:51:08.556450,Trigger Delivery,Skipped by user,
2025-06-10T01:51:08.556630,Flag Variation Testing,Skipped by user,
2025-06-10T01:51:08.556785,Generate CoC Report,Skipped by user,
2025-06-10T01:51:08.556955,Script End,Completed,
