Timestamp,Step,Status,Details
2025-06-10T01:16:03.423172,<PERSON><PERSON><PERSON> Start,Completed,
2025-06-10T01:16:07.091281,Get Access Token,Completed,
2025-06-10T01:16:14.569555,Data Pool Cleanup - Delete extractionbatchdata,Completed,Successfully deleted all 1 extractionbatchdata
2025-06-10T01:16:15.663197,Data Pool Cleanup - Delete extractionbatches,Completed,Successfully deleted all 1 extractionbatches
2025-06-10T01:16:16.880125,Data Pool Cleanup - Delete packages,Completed,Successfully deleted all 1 packages
2025-06-10T01:16:18.093518,Data Pool Cleanup - Delete packagers,Completed,Successfully deleted all 1 packagers
2025-06-10T01:16:21.857801,Data Pool Cleanup - Delete extractions,Completed,Successfully deleted all 6 extractions
2025-06-10T01:16:24.443651,Data Pool Cleanup - Delete documents,Completed,Successfully deleted all 6 documents
2025-06-10T01:16:27.195152,Data Pool Cleanup - Delete documentdata,Completed,Successfully deleted all 6 documentdata
2025-06-10T01:16:27.195447,Data Pool Cleanup,Completed,
2025-06-10T01:16:29.565846,Packager Lifecycle Test,Completed,Created packager ffPYh1 with 12/12 validations passed. Verified packager exists with 12/12 field validations passed. Deleted temp packager ffPYh1. Verified temp packager ffPYh1 is deleted. 
2025-06-10T01:16:29.869784,Main Packager Creation,Completed,Created main packager ID: XWfJS1
2025-06-10T01:16:49.548299,Enhanced Document Upload,Completed,Uploaded 6/6 documents with 96/96 validations passed
2025-06-10T01:16:51.449099,Document Processing Status Check,Completed,All 6 documents still processing. 
2025-06-10T01:17:23.519366,Create Extraction Batch,Completed,Created extraction batch ID: 3VulL1. Batch processing completed. 
2025-06-10T01:17:26.912427,Simulate Content Duplication,Completed,Created identical content in 3 documents for duplicate testing. Created 3 corrections for orig_doc_1.pdf. Created 3 corrections for origin_doc_2.pdf. Created 3 corrections for orig_doc_3.pdf. 
2025-06-10T01:17:48.893764,Verify Extraction Batch,Completed,"Verified extraction batch 3VulL1 on attempt 1. Found 4 duplicate relationships (0 MD5, 0 label, 4 content). "
2025-06-10T01:18:10.477543,Change Packager Schedule to CRON,Completed,Changed packager XWfJS1 schedule to CRON. CRON schedule successfully created package 0AEbn1 after 20s. Package processing completed. CRON schedule working correctly. 
2025-06-10T01:18:11.710422,Validate CRON Package,Completed, 4 duplicate relationships found. CRON package validation: 3/3 checks passed.
2025-06-10T01:18:11.710691,Test Package Reuse,Skipped by user,
2025-06-10T01:18:11.711133,Test Delete Package and Re-run CRON,Skipped by user,
2025-06-10T01:18:12.057585,Reset Packager to MANUAL,Completed,Packager schedule set to MANUAL
2025-06-10T01:18:16.354467,Simulate Package,Completed,"Simulation successful: 1 data items, 0 errors. "
2025-06-10T01:18:21.511386,Create Package,Partial Success,"Using CRON package ID: 0AEbn1.  4 duplicate relationships found (0 MD5, 4 content). Package validation: 5/13 checks passed."
2025-06-10T01:18:21.511659,Trigger Delivery,Skipped by user,
2025-06-10T01:18:23.930865,Flag Variation Testing,Partial Success,Only 0/3 flag variation tests passed. 
2025-06-10T01:18:24.261141,Generate CoC Report,Completed,CoC report response received. 
2025-06-10T01:18:24.261418,Script End,Completed,
