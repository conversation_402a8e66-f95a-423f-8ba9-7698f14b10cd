Timestamp,Step,Status,Details
2025-06-10T02:03:32.741548,<PERSON><PERSON><PERSON> Start,Completed,
2025-06-10T02:03:36.866000,Get Access Token,Completed,
2025-06-10T02:03:44.941123,Data Pool Cleanup - Delete extractionbatchdata,Completed,Successfully deleted all 1 extractionbatchdata
2025-06-10T02:03:45.952617,Data Pool Cleanup - Delete extractionbatches,Completed,Successfully deleted all 1 extractionbatches
2025-06-10T02:03:47.059012,Data Pool Cleanup - Delete packages,Completed,Successfully deleted all 1 packages
2025-06-10T02:03:48.223158,Data Pool Cleanup - Delete packagers,Completed,Successfully deleted all 1 packagers
2025-06-10T02:03:51.826492,Data Pool Cleanup - Delete extractions,Completed,Successfully deleted all 6 extractions
2025-06-10T02:03:54.277861,Data Pool Cleanup - Delete documents,Completed,Successfully deleted all 6 documents
2025-06-10T02:03:56.519798,Data Pool Cleanup - Delete documentdata,Completed,Successfully deleted all 6 documentdata
2025-06-10T02:03:56.520119,Data Pool Cleanup,Completed,
2025-06-10T02:04:00.326903,Packager Lifecycle Test,Completed,Created packager 9rF0v1 with 12/12 validations passed. Verified packager exists with 12/12 field validations passed. Deleted temp packager 9rF0v1. Verified temp packager 9rF0v1 is deleted. 
2025-06-10T02:04:03.752933,Main Packager Creation,Completed,Created main packager ID: 1iVlf1
2025-06-10T02:04:26.732526,Enhanced Document Upload,Completed,Uploaded 6/6 documents with 96/96 validations passed
2025-06-10T02:04:39.764142,Document Processing Status Check,Completed,All 6 documents still processing. 
2025-06-10T02:05:14.039960,Create Extraction Batch,Completed,Created extraction batch ID: PX0zJ1. Batch processing completed. 
2025-06-10T02:05:22.543016,Simulate Content Duplication,Completed,Created identical content in 3 documents for duplicate testing. Created 3 corrections for orig_doc_1.pdf. Created 3 corrections for origin_doc_2.pdf. Created 3 corrections for orig_doc_3.pdf. 
2025-06-10T02:05:40.002416,Verify Extraction Batch,Completed,"Verified extraction batch PX0zJ1 on attempt 1. Found 4 duplicate relationships (0 MD5, 0 label, 4 content). "
2025-06-10T02:07:02.411493,Change Packager Schedule to CRON,Completed,Changed packager 1iVlf1 schedule to CRON. CRON schedule successfully created package krj7J1 after 40s. Package processing completed. CRON schedule working correctly. 
2025-06-10T02:07:09.941007,Validate CRON Package,Completed, 4 duplicate relationships found. CRON package validation: 3/3 checks passed.
2025-06-10T02:07:09.941296,Test Package Reuse,Skipped by user,
2025-06-10T02:07:09.941679,Test Delete Package and Re-run CRON,Skipped by user,
2025-06-10T02:07:10.267893,Reset Packager to MANUAL,Completed,Packager schedule set to MANUAL
2025-06-10T02:07:42.613018,Simulate Package,Completed,"Simulation successful: 1 data items, 0 errors. "
2025-06-10T02:07:52.080083,Create Package,Partial Success,"Using CRON package ID: krj7J1.  4 duplicate relationships found (0 MD5, 4 content). Package validation: 12/14 checks passed."
2025-06-10T02:07:52.080390,Trigger Delivery,Skipped by user,
2025-06-10T02:13:27.414088,Flag Variation Testing,Partial Success,Only 0/3 flag variation tests passed. 
2025-06-10T02:14:48.348729,Generate CoC Report,Completed,CoC report response received. 
2025-06-10T02:14:48.349562,Script End,Completed,
