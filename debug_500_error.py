#!/usr/bin/env python3
"""
Debug script to isolate the 500 error when creating packagers with CoC fields
"""

import os
import requests
import json

# Configuration - Use same as packager test script
API_BASE_URL = os.getenv('API_BASE_URL', 'https://api-sandbox.glynt.ai/v6')
USERNAME = os.getenv('USERNAME', 'rashan-glynt')
PASSWORD = os.getenv('PASSWORD', 'Rashan123!')
DATA_POOL_ID = os.getenv('DATA_POOL_ID', 'Fj8uL1')
TOKEN_CACHE_FILE = "access_token_cache.json"

def load_token_from_cache():
    """Loads access token from the cache file."""
    if os.path.exists(TOKEN_CACHE_FILE):
        try:
            with open(TOKEN_CACHE_FILE, 'r') as f:
                data = json.load(f)
                return data["access_token"]
        except (json.JSONDecodeError, KeyError):
            print("Error reading token cache file.")
            return None
    return None

def save_token_to_cache(token):
    """Saves access token to the cache file."""
    try:
        with open(TOKEN_CACHE_FILE, 'w') as f:
            json.dump({"access_token": token}, f)
        print("Access token saved to cache file.")
    except IOError:
        print("Error writing token cache file.")

def test_token_validity(token):
    """Tests if the provided token is valid by fetching data pools."""
    url = f"{API_BASE_URL}/data-pools/"
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        return True
    except requests.exceptions.RequestException as e:
        print(f"Token validation failed: {e}")
        return False

def get_access_token():
    """Obtains and caches an access token from the API."""
    # Attempt to read token from cache file
    cached_token = load_token_from_cache()
    if cached_token:
        print("Attempting to use cached access token.")
        # Validate cached token by trying to fetch data pools
        if test_token_validity(cached_token):
            print("Cached token is valid.")
            return cached_token
        else:
            print("Cached token is invalid or expired. Obtaining a new token.")

    # If cached token is not valid or not found, obtain a new one
    auth_url = f"{API_BASE_URL}/auth/get-token/"
    payload = json.dumps({
      "username": USERNAME,
      "password": PASSWORD
    })
    headers = {
      'Content-Type': 'application/json'
    }
    try:
        response = requests.post(auth_url, headers=headers, data=payload)
        response.raise_for_status()
        token = response.json()["access_token"]
        save_token_to_cache(token)
        print("Obtained and cached new access token.")
        return token
    except requests.exceptions.RequestException as e:
        print(f"Error obtaining access token: {e}")
        return None

def test_packager_creation(config, test_name):
    """Test creating a packager with specific configuration"""
    access_token = get_access_token()
    if not access_token:
        return False
    
    url = f"{API_BASE_URL}/data-pools/{DATA_POOL_ID}/packagers/"
    headers = {
        'Authorization': f'Bearer {access_token}',
        'Content-Type': 'application/json'
    }
    
    print(f"\n--- Testing: {test_name} ---")
    print(f"📋 Config: {json.dumps(config, indent=2)}")
    
    try:
        response = requests.post(url, headers=headers, json=config)
        print(f"📊 Status: {response.status_code}")
        
        if response.status_code == 201:
            result = response.json()
            packager_id = result.get('id')
            print(f"✅ Success! Created packager: {packager_id}")
            
            # Clean up
            delete_url = f"{API_BASE_URL}/data-pools/{DATA_POOL_ID}/packagers/{packager_id}/"
            delete_response = requests.delete(delete_url, headers=headers)
            if delete_response.status_code == 204:
                print(f"🗑️ Cleaned up packager {packager_id}")
            
            return True
        else:
            print(f"❌ Failed with status {response.status_code}")
            try:
                error_data = response.json()
                print(f"📋 Error details: {json.dumps(error_data, indent=2)}")
            except:
                print(f"📋 Error text: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return False

def main():
    """Test different CoC field configurations to isolate the 500 error"""
    
    # Base configuration that works
    base_config = {
        "label": "Debug Test",
        "output_format": "json",
        "delivery_data_grouping_strategy": "ALL_IN_ONE",
        "s3_bucket": "glynt-sandbox-packager",
        "s3_prefix": "debug-test",
        "region": "us-west-2"
    }
    
    # Test configurations
    test_configs = [
        {
            "name": "1. Base Config (Should Work)",
            "config": base_config.copy()
        },
        {
            "name": "2. Add append_coc_fields_to_data",
            "config": {
                **base_config,
                "append_coc_fields_to_data": True
            }
        },
        {
            "name": "3. Add empty coc_field_config",
            "config": {
                **base_config,
                "append_coc_fields_to_data": True,
                "coc_field_config": []
            }
        },
        {
            "name": "4. Add single CoC field",
            "config": {
                **base_config,
                "append_coc_fields_to_data": True,
                "coc_field_config": ["SourceFileID"]
            }
        },
        {
            "name": "5. Add multiple CoC fields",
            "config": {
                **base_config,
                "append_coc_fields_to_data": True,
                "coc_field_config": ["SourceFileID", "SourceFileOriginalName", "PackageID"]
            }
        },
        {
            "name": "6. Add coc_report_fields_config",
            "config": {
                **base_config,
                "append_coc_fields_to_data": True,
                "coc_field_config": ["SourceFileID"],
                "coc_report_fields_config": ["SourceFileID", "SourceFileOriginalName"]
            }
        },
        {
            "name": "7. Full CoC config (like test script)",
            "config": {
                **base_config,
                "append_coc_fields_to_data": True,
                "coc_field_config": [
                    "ChainOfCustodyStartDate",
                    "ChainOfCustodyEndDate", 
                    "SourceFileReceivedAt",
                    "SourceFileID",
                    "SourceFileOriginalName",
                    "SourceFileOriginalPath",
                    "SourceFileFinalName",
                    "SourceFileStatus",
                    "ExclusionReason",
                    "ParentFileID",
                    "RelationshipToParent",
                    "PackageID",
                    "DeliveredAt"
                ],
                "coc_report_fields_config": [
                    "ChainOfCustodyStartDate",
                    "ChainOfCustodyEndDate",
                    "SourceFileReceivedAt",
                    "SourceFileID",
                    "SourceFileOriginalName",
                    "SourceFileOriginalPath",
                    "SourceFileFinalName",
                    "SourceFileStatus",
                    "ExclusionReason",
                    "ParentFileID",
                    "RelationshipToParent",
                    "PackageID",
                    "DeliveredAt",
                    "SourceFileNumPages",
                    "SourceFileDetectedLanguage",
                    "SourceFileSize"
                ]
            }
        }
    ]
    
    print("🧪 Testing CoC Field Configurations to Isolate 500 Error")
    print("=" * 60)
    
    results = []
    for test_config in test_configs:
        success = test_packager_creation(test_config["config"], test_config["name"])
        results.append((test_config["name"], success))
    
    print("\n" + "=" * 60)
    print("📊 RESULTS SUMMARY:")
    print("=" * 60)
    
    for name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {name}")
    
    # Find the breaking point
    last_success = None
    first_failure = None
    
    for name, success in results:
        if success:
            last_success = name
        elif first_failure is None:
            first_failure = name
            break
    
    if last_success and first_failure:
        print(f"\n🔍 BREAKING POINT ANALYSIS:")
        print(f"   Last Success: {last_success}")
        print(f"   First Failure: {first_failure}")
    
    print("\n🎯 This should help identify what's causing the 500 error!")

if __name__ == "__main__":
    main()
