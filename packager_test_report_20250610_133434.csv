Timestamp,Step,Status,Details
2025-06-10T13:34:34.292179,<PERSON><PERSON><PERSON> Start,Completed,
2025-06-10T13:34:44.900873,Get Access Token,Completed,
2025-06-10T13:34:56.672520,Data Pool Cleanup - Delete extractionbatchdata,Completed,Successfully deleted all 1 extractionbatchdata
2025-06-10T13:34:59.307738,Data Pool Cleanup - Delete extractionbatches,Completed,Successfully deleted all 1 extractionbatches
2025-06-10T13:35:00.557182,Data Pool Cleanup - Delete packages,Completed,Successfully deleted all 1 packages
2025-06-10T13:35:01.711648,Data Pool Cleanup - Delete packagers,Completed,Successfully deleted all 1 packagers
2025-06-10T13:35:05.372354,Data Pool Cleanup - Delete extractions,Completed,Successfully deleted all 6 extractions
2025-06-10T13:35:10.861391,Data Pool Cleanup - Delete documents,Completed,Successfully deleted all 11 documents
2025-06-10T13:35:17.528576,Data Pool Cleanup - Delete documentdata,Completed,Successfully deleted all 11 documentdata
2025-06-10T13:35:17.528784,Data Pool Cleanup,Completed,
2025-06-10T13:35:24.709000,Packager Lifecycle Test,Completed,Created packager 4HWEi1 with 12/12 validations passed. Verified packager exists with 12/12 field validations passed. Deleted temp packager 4HWEi1. Verified temp packager 4HWEi1 is deleted. 
2025-06-10T13:35:31.652349,Main Packager Creation,Completed,Created main packager ID: w7mzS1
2025-06-10T13:35:53.036066,Enhanced Document Upload,Completed,Uploaded 6/6 documents with 96/96 validations passed
2025-06-10T13:36:41.909574,Document Processing Status Check,Completed,All 6 documents still processing. 
2025-06-10T13:37:16.891220,Create Extraction Batch,Completed,Created extraction batch ID: gNmcg1. Batch processing completed. 
2025-06-10T13:37:29.807827,Simulate Content Duplication,Completed,Created identical content in 3 documents for duplicate testing. Created 3 corrections for orig_doc_1.pdf. Created 3 corrections for origin_doc_2.pdf. Created 3 corrections for orig_doc_3.pdf. 
2025-06-10T13:37:51.126152,Verify Extraction Batch,Completed,"Verified extraction batch gNmcg1 on attempt 1. Found 4 duplicate relationships (0 MD5, 0 label, 4 content). "
2025-06-10T13:38:05.117866,Change Packager Schedule to CRON,Completed,Changed packager w7mzS1 schedule to CRON. CRON schedule successfully created package sOtY92 after 10s. Package processing completed. CRON schedule working correctly. 
2025-06-10T13:38:34.952432,Validate CRON Package,Completed, 4 duplicate relationships found. CRON package validation: 3/3 checks passed.
2025-06-10T13:38:34.952979,Test Package Reuse,Skipped by user,
2025-06-10T13:38:34.953148,Test Delete Package and Re-run CRON,Skipped by user,
2025-06-10T13:38:35.298719,Reset Packager to MANUAL,Completed,Packager schedule set to MANUAL
2025-06-10T13:38:41.721487,Simulate Package,Completed,"Simulation successful: 1 data items, 0 errors. "
2025-06-10T13:38:50.621364,Duplicate Filtering Test,Partial Success,Only 0/1 duplicate filtering tests passed. 
