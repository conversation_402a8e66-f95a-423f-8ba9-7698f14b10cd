{"test_run_info": {"timestamp": "2025-06-11T02:27:39.560858", "total_tests": 193, "duration_seconds": 142.6395571231842, "api_calls": 70, "api_errors": 9}, "summary": {"passed": 188, "failed": 5, "partial": 0, "errors": 0, "skipped": 0}, "test_results": [{"test_name": "Packager Config - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "Tscui1"}}, {"test_name": "Packager Config - label", "status": "PASSED", "message": "Required field 'label' exists", "expected": null, "actual": null, "details": {"field_value": "Enhanced Test Script Packager"}}, {"test_name": "Packager Config - packager_schedule_type", "status": "PASSED", "message": "Required field 'packager_schedule_type' exists", "expected": null, "actual": null, "details": {"field_value": "MANUAL"}}, {"test_name": "Packager Config - document_status_filter", "status": "PASSED", "message": "Required field 'document_status_filter' exists", "expected": null, "actual": null, "details": {"field_value": ["VERIFIED"]}}, {"test_name": "Packager Config - eb_status_filter", "status": "PASSED", "message": "Required field 'eb_status_filter' exists", "expected": null, "actual": null, "details": {"field_value": ["VERIFIED"]}}, {"test_name": "Packager Config - output_format", "status": "PASSED", "message": "Required field 'output_format' exists", "expected": null, "actual": null, "details": {"field_value": "json"}}, {"test_name": "Packager Config - delivery_data_grouping_strategy", "status": "PASSED", "message": "Required field 'delivery_data_grouping_strategy' exists", "expected": null, "actual": null, "details": {"field_value": "ALL_IN_ONE"}}, {"test_name": "Packager Config - Label", "status": "PASSED", "message": "label has correct value: Enhanced Test Script Packager", "expected": "Enhanced Test Script Packager", "actual": "Enhanced Test Script Packager", "details": {}}, {"test_name": "Packager Config - Schedule Type", "status": "PASSED", "message": "packager_schedule_type has correct value: MANUAL", "expected": "MANUAL", "actual": "MANUAL", "details": {}}, {"test_name": "Packager Config - Output Format", "status": "PASSED", "message": "output_format has correct value: json", "expected": "json", "actual": "json", "details": {}}, {"test_name": "Packager Config - Document Status Filter", "status": "PASSED", "message": "Document status filter correct: ['VERIFIED']", "expected": ["VERIFIED"], "actual": ["VERIFIED"], "details": {}}, {"test_name": "Packager Config - Deduplication Fields", "status": "PASSED", "message": "Deduplication fields correct: []", "expected": [], "actual": [], "details": {}}, {"test_name": "Packager Config - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "Tscui1"}}, {"test_name": "Packager Config - label", "status": "PASSED", "message": "Required field 'label' exists", "expected": null, "actual": null, "details": {"field_value": "Enhanced Test Script Packager"}}, {"test_name": "Packager Config - packager_schedule_type", "status": "PASSED", "message": "Required field 'packager_schedule_type' exists", "expected": null, "actual": null, "details": {"field_value": "MANUAL"}}, {"test_name": "Packager Config - document_status_filter", "status": "PASSED", "message": "Required field 'document_status_filter' exists", "expected": null, "actual": null, "details": {"field_value": ["VERIFIED"]}}, {"test_name": "Packager Config - eb_status_filter", "status": "PASSED", "message": "Required field 'eb_status_filter' exists", "expected": null, "actual": null, "details": {"field_value": ["VERIFIED"]}}, {"test_name": "Packager Config - output_format", "status": "PASSED", "message": "Required field 'output_format' exists", "expected": null, "actual": null, "details": {"field_value": "json"}}, {"test_name": "Packager Config - delivery_data_grouping_strategy", "status": "PASSED", "message": "Required field 'delivery_data_grouping_strategy' exists", "expected": null, "actual": null, "details": {"field_value": "ALL_IN_ONE"}}, {"test_name": "Packager Config - Label", "status": "PASSED", "message": "label has correct value: Enhanced Test Script Packager", "expected": "Enhanced Test Script Packager", "actual": "Enhanced Test Script Packager", "details": {}}, {"test_name": "Packager Config - Schedule Type", "status": "PASSED", "message": "packager_schedule_type has correct value: MANUAL", "expected": "MANUAL", "actual": "MANUAL", "details": {}}, {"test_name": "Packager Config - Output Format", "status": "PASSED", "message": "output_format has correct value: json", "expected": "json", "actual": "json", "details": {}}, {"test_name": "Packager Config - Document Status Filter", "status": "PASSED", "message": "Document status filter correct: ['VERIFIED']", "expected": ["VERIFIED"], "actual": ["VERIFIED"], "details": {}}, {"test_name": "Packager Config - Deduplication Fields", "status": "PASSED", "message": "Deduplication fields correct: []", "expected": [], "actual": [], "details": {}}, {"test_name": "Invalid Config - packager_schedule_type", "status": "PASSED", "message": "Invalid configuration properly rejected for field: packager_schedule_type", "expected": null, "actual": null, "details": {}}, {"test_name": "Invalid Config - output_format", "status": "PASSED", "message": "Invalid configuration properly rejected for field: output_format", "expected": null, "actual": null, "details": {}}, {"test_name": "Invalid Config - label", "status": "FAILED", "message": "Invalid configuration was accepted (should have been rejected): label", "expected": null, "actual": null, "details": {"created_packager_id": "2eC892"}}, {"test_name": "Document Upload - COPY_doc2_dupe1.pdf - Success", "status": "PASSED", "message": "Document uploaded successfully with ID: ZjPQG1", "expected": null, "actual": null, "details": {"document_id": "ZjPQG1", "file_name": "COPY_doc2_dupe1.pdf"}}, {"test_name": "Document Upload - COPY_doc2_dupe1.pdf - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "ZjPQG1"}}, {"test_name": "Document Upload - COPY_doc2_dupe1.pdf - label", "status": "PASSED", "message": "Required field 'label' exists", "expected": null, "actual": null, "details": {"field_value": "COPY_doc2_dupe1.pdf"}}, {"test_name": "Document Upload - COPY_doc2_dupe1.pdf - content_type", "status": "PASSED", "message": "Required field 'content_type' exists", "expected": null, "actual": null, "details": {"field_value": "application/pdf"}}, {"test_name": "Document Upload - COPY_doc2_dupe1.pdf - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T07:25:28.328408Z"}}, {"test_name": "Document Upload - COPY_doc2_dupe1.pdf - Label", "status": "PASSED", "message": "label has correct value: COPY_doc2_dupe1.pdf", "expected": "COPY_doc2_dupe1.pdf", "actual": "COPY_doc2_dupe1.pdf", "details": {}}, {"test_name": "Document Upload - COPY_doc2_dupe1.pdf - Content Type", "status": "PASSED", "message": "content_type has correct value: application/pdf", "expected": "application/pdf", "actual": "application/pdf", "details": {}}, {"test_name": "DocumentData for ZjPQG1 - Existence", "status": "PASSED", "message": "DocumentData found for document ZjPQG1", "expected": null, "actual": null, "details": {"doc_data_id": "VzWjP1"}}, {"test_name": "DocumentData for ZjPQG1 - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "VzWjP1"}}, {"test_name": "DocumentData for ZjPQG1 - document", "status": "PASSED", "message": "Required field 'document' exists", "expected": null, "actual": null, "details": {"field_value": "https://api-sandbox.glynt.ai/v6/data-pools/Fj8uL1/documents/ZjPQG1/"}}, {"test_name": "DocumentData for ZjPQG1 - status", "status": "PASSED", "message": "Required field 'status' exists", "expected": null, "actual": null, "details": {"field_value": "PENDING_CREATION"}}, {"test_name": "DocumentData for ZjPQG1 - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T07:25:28.345131Z"}}, {"test_name": "DocumentData for ZjPQG1 - updated_at", "status": "PASSED", "message": "Required field 'updated_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T07:25:28.345178Z"}}, {"test_name": "DocumentData for ZjPQG1 - ID Type", "status": "PASSED", "message": "Field 'id' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for ZjPQG1 - Status Type", "status": "PASSED", "message": "Field 'status' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for ZjPQG1 - Status Valid", "status": "PASSED", "message": "DocumentData has valid status: PENDING_CREATION", "expected": null, "actual": null, "details": {}}, {"test_name": "Document Upload - COPY_doc3_dupe1.pdf - Success", "status": "PASSED", "message": "Document uploaded successfully with ID: 8Vzdg1", "expected": null, "actual": null, "details": {"document_id": "8Vzdg1", "file_name": "COPY_doc3_dupe1.pdf"}}, {"test_name": "Document Upload - COPY_doc3_dupe1.pdf - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "8Vzdg1"}}, {"test_name": "Document Upload - COPY_doc3_dupe1.pdf - label", "status": "PASSED", "message": "Required field 'label' exists", "expected": null, "actual": null, "details": {"field_value": "COPY_doc3_dupe1.pdf"}}, {"test_name": "Document Upload - COPY_doc3_dupe1.pdf - content_type", "status": "PASSED", "message": "Required field 'content_type' exists", "expected": null, "actual": null, "details": {"field_value": "application/pdf"}}, {"test_name": "Document Upload - COPY_doc3_dupe1.pdf - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T07:25:31.614954Z"}}, {"test_name": "Document Upload - COPY_doc3_dupe1.pdf - Label", "status": "PASSED", "message": "label has correct value: COPY_doc3_dupe1.pdf", "expected": "COPY_doc3_dupe1.pdf", "actual": "COPY_doc3_dupe1.pdf", "details": {}}, {"test_name": "Document Upload - COPY_doc3_dupe1.pdf - Content Type", "status": "PASSED", "message": "content_type has correct value: application/pdf", "expected": "application/pdf", "actual": "application/pdf", "details": {}}, {"test_name": "DocumentData for 8Vzdg1 - Existence", "status": "PASSED", "message": "DocumentData found for document 8Vzdg1", "expected": null, "actual": null, "details": {"doc_data_id": "4l6xp1"}}, {"test_name": "DocumentData for 8Vzdg1 - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "4l6xp1"}}, {"test_name": "DocumentData for 8Vzdg1 - document", "status": "PASSED", "message": "Required field 'document' exists", "expected": null, "actual": null, "details": {"field_value": "https://api-sandbox.glynt.ai/v6/data-pools/Fj8uL1/documents/8Vzdg1/"}}, {"test_name": "DocumentData for 8Vzdg1 - status", "status": "PASSED", "message": "Required field 'status' exists", "expected": null, "actual": null, "details": {"field_value": "PENDING_CREATION"}}, {"test_name": "DocumentData for 8Vzdg1 - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T07:25:31.627110Z"}}, {"test_name": "DocumentData for 8Vzdg1 - updated_at", "status": "PASSED", "message": "Required field 'updated_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T07:25:31.627156Z"}}, {"test_name": "DocumentData for 8Vzdg1 - ID Type", "status": "PASSED", "message": "Field 'id' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for 8Vzdg1 - Status Type", "status": "PASSED", "message": "Field 'status' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for 8Vzdg1 - Status Valid", "status": "PASSED", "message": "DocumentData has valid status: PENDING_CREATION", "expected": null, "actual": null, "details": {}}, {"test_name": "Document Upload - COPY_doc3_dupe2.pdf - Success", "status": "PASSED", "message": "Document uploaded successfully with ID: hGZr62", "expected": null, "actual": null, "details": {"document_id": "hGZr62", "file_name": "COPY_doc3_dupe2.pdf"}}, {"test_name": "Document Upload - COPY_doc3_dupe2.pdf - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "hGZr62"}}, {"test_name": "Document Upload - COPY_doc3_dupe2.pdf - label", "status": "PASSED", "message": "Required field 'label' exists", "expected": null, "actual": null, "details": {"field_value": "COPY_doc3_dupe2.pdf"}}, {"test_name": "Document Upload - COPY_doc3_dupe2.pdf - content_type", "status": "PASSED", "message": "Required field 'content_type' exists", "expected": null, "actual": null, "details": {"field_value": "application/pdf"}}, {"test_name": "Document Upload - COPY_doc3_dupe2.pdf - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T07:25:34.842388Z"}}, {"test_name": "Document Upload - COPY_doc3_dupe2.pdf - Label", "status": "PASSED", "message": "label has correct value: COPY_doc3_dupe2.pdf", "expected": "COPY_doc3_dupe2.pdf", "actual": "COPY_doc3_dupe2.pdf", "details": {}}, {"test_name": "Document Upload - COPY_doc3_dupe2.pdf - Content Type", "status": "PASSED", "message": "content_type has correct value: application/pdf", "expected": "application/pdf", "actual": "application/pdf", "details": {}}, {"test_name": "DocumentData for hGZr62 - Existence", "status": "PASSED", "message": "DocumentData found for document hGZr62", "expected": null, "actual": null, "details": {"doc_data_id": "NqmUA1"}}, {"test_name": "DocumentData for hGZr62 - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "NqmUA1"}}, {"test_name": "DocumentData for hGZr62 - document", "status": "PASSED", "message": "Required field 'document' exists", "expected": null, "actual": null, "details": {"field_value": "https://api-sandbox.glynt.ai/v6/data-pools/Fj8uL1/documents/hGZr62/"}}, {"test_name": "DocumentData for hGZr62 - status", "status": "PASSED", "message": "Required field 'status' exists", "expected": null, "actual": null, "details": {"field_value": "PENDING_CREATION"}}, {"test_name": "DocumentData for hGZr62 - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T07:25:34.853518Z"}}, {"test_name": "DocumentData for hGZr62 - updated_at", "status": "PASSED", "message": "Required field 'updated_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T07:25:34.853561Z"}}, {"test_name": "DocumentData for hGZr62 - ID Type", "status": "PASSED", "message": "Field 'id' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for hGZr62 - Status Type", "status": "PASSED", "message": "Field 'status' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for hGZr62 - Status Valid", "status": "PASSED", "message": "DocumentData has valid status: PENDING_CREATION", "expected": null, "actual": null, "details": {}}, {"test_name": "Document Upload - orig_doc_1.pdf - Success", "status": "PASSED", "message": "Document uploaded successfully with ID: 0MFPR1", "expected": null, "actual": null, "details": {"document_id": "0MFPR1", "file_name": "orig_doc_1.pdf"}}, {"test_name": "Document Upload - orig_doc_1.pdf - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "0MFPR1"}}, {"test_name": "Document Upload - orig_doc_1.pdf - label", "status": "PASSED", "message": "Required field 'label' exists", "expected": null, "actual": null, "details": {"field_value": "orig_doc_1.pdf"}}, {"test_name": "Document Upload - orig_doc_1.pdf - content_type", "status": "PASSED", "message": "Required field 'content_type' exists", "expected": null, "actual": null, "details": {"field_value": "application/pdf"}}, {"test_name": "Document Upload - orig_doc_1.pdf - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T07:25:38.014934Z"}}, {"test_name": "Document Upload - orig_doc_1.pdf - Label", "status": "PASSED", "message": "label has correct value: orig_doc_1.pdf", "expected": "orig_doc_1.pdf", "actual": "orig_doc_1.pdf", "details": {}}, {"test_name": "Document Upload - orig_doc_1.pdf - Content Type", "status": "PASSED", "message": "content_type has correct value: application/pdf", "expected": "application/pdf", "actual": "application/pdf", "details": {}}, {"test_name": "DocumentData for 0MFPR1 - Existence", "status": "PASSED", "message": "DocumentData found for document 0MFPR1", "expected": null, "actual": null, "details": {"doc_data_id": "wbMia1"}}, {"test_name": "DocumentData for 0MFPR1 - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "wbMia1"}}, {"test_name": "DocumentData for 0MFPR1 - document", "status": "PASSED", "message": "Required field 'document' exists", "expected": null, "actual": null, "details": {"field_value": "https://api-sandbox.glynt.ai/v6/data-pools/Fj8uL1/documents/0MFPR1/"}}, {"test_name": "DocumentData for 0MFPR1 - status", "status": "PASSED", "message": "Required field 'status' exists", "expected": null, "actual": null, "details": {"field_value": "PENDING_CREATION"}}, {"test_name": "DocumentData for 0MFPR1 - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T07:25:38.031371Z"}}, {"test_name": "DocumentData for 0MFPR1 - updated_at", "status": "PASSED", "message": "Required field 'updated_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T07:25:38.031417Z"}}, {"test_name": "DocumentData for 0MFPR1 - ID Type", "status": "PASSED", "message": "Field 'id' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for 0MFPR1 - Status Type", "status": "PASSED", "message": "Field 'status' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for 0MFPR1 - Status Valid", "status": "PASSED", "message": "DocumentData has valid status: PENDING_CREATION", "expected": null, "actual": null, "details": {}}, {"test_name": "Document Upload - origin_doc_2.pdf - Success", "status": "PASSED", "message": "Document uploaded successfully with ID: Z7pcr1", "expected": null, "actual": null, "details": {"document_id": "Z7pcr1", "file_name": "origin_doc_2.pdf"}}, {"test_name": "Document Upload - origin_doc_2.pdf - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "Z7pcr1"}}, {"test_name": "Document Upload - origin_doc_2.pdf - label", "status": "PASSED", "message": "Required field 'label' exists", "expected": null, "actual": null, "details": {"field_value": "origin_doc_2.pdf"}}, {"test_name": "Document Upload - origin_doc_2.pdf - content_type", "status": "PASSED", "message": "Required field 'content_type' exists", "expected": null, "actual": null, "details": {"field_value": "application/pdf"}}, {"test_name": "Document Upload - origin_doc_2.pdf - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T07:25:41.324760Z"}}, {"test_name": "Document Upload - origin_doc_2.pdf - Label", "status": "PASSED", "message": "label has correct value: origin_doc_2.pdf", "expected": "origin_doc_2.pdf", "actual": "origin_doc_2.pdf", "details": {}}, {"test_name": "Document Upload - origin_doc_2.pdf - Content Type", "status": "PASSED", "message": "content_type has correct value: application/pdf", "expected": "application/pdf", "actual": "application/pdf", "details": {}}, {"test_name": "DocumentData for Z7pcr1 - Existence", "status": "PASSED", "message": "DocumentData found for document Z7pcr1", "expected": null, "actual": null, "details": {"doc_data_id": "VNwv02"}}, {"test_name": "DocumentData for Z7pcr1 - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "VNwv02"}}, {"test_name": "DocumentData for Z7pcr1 - document", "status": "PASSED", "message": "Required field 'document' exists", "expected": null, "actual": null, "details": {"field_value": "https://api-sandbox.glynt.ai/v6/data-pools/Fj8uL1/documents/Z7pcr1/"}}, {"test_name": "DocumentData for Z7pcr1 - status", "status": "PASSED", "message": "Required field 'status' exists", "expected": null, "actual": null, "details": {"field_value": "PENDING_CREATION"}}, {"test_name": "DocumentData for Z7pcr1 - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T07:25:41.336245Z"}}, {"test_name": "DocumentData for Z7pcr1 - updated_at", "status": "PASSED", "message": "Required field 'updated_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T07:25:41.336288Z"}}, {"test_name": "DocumentData for Z7pcr1 - ID Type", "status": "PASSED", "message": "Field 'id' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for Z7pcr1 - Status Type", "status": "PASSED", "message": "Field 'status' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for Z7pcr1 - Status Valid", "status": "PASSED", "message": "DocumentData has valid status: PENDING_CREATION", "expected": null, "actual": null, "details": {}}, {"test_name": "Document Upload - orig_doc_3.pdf - Success", "status": "PASSED", "message": "Document uploaded successfully with ID: sCVAC1", "expected": null, "actual": null, "details": {"document_id": "sCVAC1", "file_name": "orig_doc_3.pdf"}}, {"test_name": "Document Upload - orig_doc_3.pdf - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "sCVAC1"}}, {"test_name": "Document Upload - orig_doc_3.pdf - label", "status": "PASSED", "message": "Required field 'label' exists", "expected": null, "actual": null, "details": {"field_value": "orig_doc_3.pdf"}}, {"test_name": "Document Upload - orig_doc_3.pdf - content_type", "status": "PASSED", "message": "Required field 'content_type' exists", "expected": null, "actual": null, "details": {"field_value": "application/pdf"}}, {"test_name": "Document Upload - orig_doc_3.pdf - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T07:25:44.561267Z"}}, {"test_name": "Document Upload - orig_doc_3.pdf - Label", "status": "PASSED", "message": "label has correct value: orig_doc_3.pdf", "expected": "orig_doc_3.pdf", "actual": "orig_doc_3.pdf", "details": {}}, {"test_name": "Document Upload - orig_doc_3.pdf - Content Type", "status": "PASSED", "message": "content_type has correct value: application/pdf", "expected": "application/pdf", "actual": "application/pdf", "details": {}}, {"test_name": "DocumentData for sCVAC1 - Existence", "status": "PASSED", "message": "DocumentData found for document sCVAC1", "expected": null, "actual": null, "details": {"doc_data_id": "oScTL1"}}, {"test_name": "DocumentData for sCVAC1 - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "oScTL1"}}, {"test_name": "DocumentData for sCVAC1 - document", "status": "PASSED", "message": "Required field 'document' exists", "expected": null, "actual": null, "details": {"field_value": "https://api-sandbox.glynt.ai/v6/data-pools/Fj8uL1/documents/sCVAC1/"}}, {"test_name": "DocumentData for sCVAC1 - status", "status": "PASSED", "message": "Required field 'status' exists", "expected": null, "actual": null, "details": {"field_value": "PENDING_CREATION"}}, {"test_name": "DocumentData for sCVAC1 - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T07:25:44.574280Z"}}, {"test_name": "DocumentData for sCVAC1 - updated_at", "status": "PASSED", "message": "Required field 'updated_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T07:25:44.574325Z"}}, {"test_name": "DocumentData for sCVAC1 - ID Type", "status": "PASSED", "message": "Field 'id' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for sCVAC1 - Status Type", "status": "PASSED", "message": "Field 'status' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for sCVAC1 - Status Valid", "status": "PASSED", "message": "DocumentData has valid status: PENDING_CREATION", "expected": null, "actual": null, "details": {}}, {"test_name": "Package c6P5f1 Entries - Entry Count", "status": "PASSED", "message": "Found 6 package entries", "expected": null, "actual": null, "details": {"entry_count": 6}}, {"test_name": "Package c6P5f1 Entries - Duplicate Exclusion", "status": "PASSED", "message": "Duplicate exclusion working: 5 documents excluded as duplicates", "expected": null, "actual": null, "details": {"excluded_count": 5}}, {"test_name": "Package c6P5f1 Config Compliance - Configuration Recorded", "status": "PASSED", "message": "Package configuration recorded for compliance verification", "expected": null, "actual": null, "details": {"include_source_files": true, "include_relationship_details": true, "output_format": "json", "grouping_strategy": "ALL_IN_ONE", "dedup_fields": ["CustomerName", "<PERSON><PERSON><PERSON><PERSON>", "StatementDate"]}}, {"test_name": "Package c6P5f1 Business Logic - Duplicate Exclusion", "status": "PASSED", "message": "Found 5 entries excluded as duplicates (exclude_duplicates_from_delivery=True)", "expected": null, "actual": null, "details": {}}, {"test_name": "Package c6P5f1 Business Logic - Included Entries", "status": "PASSED", "message": "Package has 1 included entries", "expected": null, "actual": null, "details": {}}, {"test_name": "Package c6P5f1 Business Logic - Entry Structure Compliance", "status": "PASSED", "message": "All 6 package entries have required fields", "expected": null, "actual": null, "details": {}}, {"test_name": "Package c6P5f1 Business Logic - Package Status", "status": "PASSED", "message": "Package status 'PROCESSING_SUCCESSFUL' is valid", "expected": null, "actual": null, "details": {}}, {"test_name": "Duplicate Detection Business Logic - Total Relationships", "status": "PASSED", "message": "Found 5 document relationships (0 MD5, 0 Label, 0 Content)", "expected": null, "actual": null, "details": {}}, {"test_name": "Duplicate Detection Business Logic - Relationship Structure", "status": "PASSED", "message": "All 5 relationships have required fields", "expected": null, "actual": null, "details": {}}, {"test_name": "Simulation Format", "status": "PASSED", "message": "Response follows pure delivery format correctly", "expected": null, "actual": null, "details": {"data_items_count": 1}}, {"test_name": "Simulation Content - Item 1 - Filename Format", "status": "PASSED", "message": "Filename format correct: DOCDATA_7517_XrTDvR.json", "expected": null, "actual": null, "details": {}}, {"test_name": "Simulation Content - Item 1 - DocumentID Field", "status": "PASSED", "message": "DocumentID field present: ZjPQG1", "expected": null, "actual": null, "details": {}}, {"test_name": "Simulation Content - Item 1 - JSON Structure", "status": "PASSED", "message": "Valid JSON with 1 documents", "expected": null, "actual": null, "details": {}}, {"test_name": "Duplicate Filtering Scenarios - Exclude ON Format", "status": "PASSED", "message": "Response follows pure delivery format correctly", "expected": null, "actual": null, "details": {"data_items_count": 1}}, {"test_name": "Duplicate Filtering Scenarios - Exclude OFF Format", "status": "PASSED", "message": "Response follows pure delivery format correctly", "expected": null, "actual": null, "details": {"data_items_count": 1}}, {"test_name": "Duplicate Filtering Scenarios - Duplicate Filtering Logic", "status": "PASSED", "message": "Exclude OFF (1) >= Exclude ON (1) as expected", "expected": null, "actual": null, "details": {"exclude_on_count": 1, "exclude_off_count": 1}}, {"test_name": "Package c6P5f1 Entries - Entry Count", "status": "PASSED", "message": "Found 6 package entries", "expected": null, "actual": null, "details": {"entry_count": 6}}, {"test_name": "Package c6P5f1 Entries - Duplicate Exclusion", "status": "PASSED", "message": "Duplicate exclusion working: 5 documents excluded as duplicates", "expected": null, "actual": null, "details": {"excluded_count": 5}}, {"test_name": "DocumentData Fields - source_file_id", "status": "PASSED", "message": "All documents have source_file_id populated", "expected": null, "actual": null, "details": {}}, {"test_name": "DocumentData Fields - source_file_name", "status": "PASSED", "message": "All documents have source_file_name populated", "expected": null, "actual": null, "details": {}}, {"test_name": "DocumentData Fields - source_file_md5", "status": "PASSED", "message": "All documents have source_file_md5 populated", "expected": null, "actual": null, "details": {}}, {"test_name": "DocumentData Fields - received_at", "status": "PASSED", "message": "All documents have received_at populated", "expected": null, "actual": null, "details": {}}, {"test_name": "DocumentData Fields - source_file_num_pages", "status": "PASSED", "message": "All documents have source_file_num_pages populated", "expected": null, "actual": null, "details": {}}, {"test_name": "DocumentData Fields - source_file_size", "status": "PASSED", "message": "All documents have source_file_size populated", "expected": null, "actual": null, "details": {}}, {"test_name": "DocumentData Fields - source_file_detected_language", "status": "FAILED", "message": "No documents have source_file_detected_language populated", "expected": null, "actual": null, "details": {}}, {"test_name": "DocumentData Fields - canonical_document_data", "status": "PASSED", "message": "5 documents have canonical_document_data populated (indicates duplicate relationships)", "expected": null, "actual": null, "details": {}}, {"test_name": "Package c6P5f1 Config Compliance - Configuration Recorded", "status": "PASSED", "message": "Package configuration recorded for compliance verification", "expected": null, "actual": null, "details": {"include_source_files": true, "include_relationship_details": true, "output_format": "json", "grouping_strategy": "ALL_IN_ONE", "dedup_fields": ["CustomerName", "<PERSON><PERSON><PERSON><PERSON>", "StatementDate"]}}, {"test_name": "Package c6P5f1 Verification - Status Updated", "status": "PASSED", "message": "Package status successfully updated from PROCESSING_SUCCESSFUL to VERIFIED", "expected": null, "actual": null, "details": {"initial_status": "PROCESSING_SUCCESSFUL", "final_status": "VERIFIED"}}, {"test_name": "Package c6P5f1 Final Delivery - Preview Format", "status": "PASSED", "message": "Response follows pure delivery format correctly", "expected": null, "actual": null, "details": {"data_items_count": 1}}, {"test_name": "Package c6P5f1 Final Delivery - Preview Content - Item 1 - Filename Format", "status": "PASSED", "message": "Filename format correct: DOCDATA_7517_0l7XeI.json", "expected": null, "actual": null, "details": {}}, {"test_name": "Package c6P5f1 Final Delivery - Preview Content - Item 1 - DocumentID Field", "status": "PASSED", "message": "DocumentID field present: ZjPQG1", "expected": null, "actual": null, "details": {}}, {"test_name": "Package c6P5f1 Final Delivery - Preview Content - Item 1 - JSON Structure", "status": "PASSED", "message": "Valid JSON with 1 documents", "expected": null, "actual": null, "details": {}}, {"test_name": "Package c6P5f1 Final Delivery - Preview Count", "status": "PASSED", "message": "Preview shows correct number of documents: 1", "expected": null, "actual": null, "details": {"preview_count": 1, "expected": 1}}, {"test_name": "Package c6P5f1 Final Delivery - Preview Deduplication", "status": "PASSED", "message": "No duplicate content found in preview data", "expected": null, "actual": null, "details": {"unique_items": 1}}, {"test_name": "Package c6P5f1 Final Delivery - Download Available", "status": "PASSED", "message": "Package download endpoint is accessible", "expected": null, "actual": null, "details": {"download_available": true}}, {"test_name": "Flag Variations - Full CoC + Duplicates ON + Relationships ON Creation", "status": "FAILED", "message": "Failed to create test packager for Full CoC + Duplicates ON + Relationships ON", "expected": null, "actual": null, "details": {}}, {"test_name": "Flag Variations - Essential CoC + Duplicates OFF + Relationships ON Creation", "status": "FAILED", "message": "Failed to create test packager for Essential CoC + Duplicates OFF + Relationships ON", "expected": null, "actual": null, "details": {}}, {"test_name": "Flag Variations - No CoC + All Flags OFF Creation", "status": "FAILED", "message": "Failed to create test packager for No CoC + All Flags OFF", "expected": null, "actual": null, "details": {}}, {"test_name": "Debug API - CoC Fields Present", "status": "PASSED", "message": "All expected CoC fields are available in DocumentData API", "expected": null, "actual": null, "details": {}}, {"test_name": "Debug API - Minimal Config Creation", "status": "PASSED", "message": "Successfully created packager with Minimal Config", "expected": null, "actual": null, "details": {}}, {"test_name": "Debug API - Basic CoC Config Creation", "status": "PASSED", "message": "Successfully created packager with Basic CoC Config", "expected": null, "actual": null, "details": {}}, {"test_name": "Debug API - Full CoC Config Creation", "status": "PASSED", "message": "Successfully created packager with Full CoC Config", "expected": null, "actual": null, "details": {}}, {"test_name": "Document Upload - tmpprantcc1.pdf - Success", "status": "PASSED", "message": "Document uploaded successfully with ID: Ry4Oc1", "expected": null, "actual": null, "details": {"document_id": "Ry4Oc1", "file_name": "tmpprantcc1.pdf"}}, {"test_name": "Document Upload - tmpprantcc1.pdf - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "Ry4Oc1"}}, {"test_name": "Document Upload - tmpprantcc1.pdf - label", "status": "PASSED", "message": "Required field 'label' exists", "expected": null, "actual": null, "details": {"field_value": "tmpprantcc1.pdf"}}, {"test_name": "Document Upload - tmpprantcc1.pdf - content_type", "status": "PASSED", "message": "Required field 'content_type' exists", "expected": null, "actual": null, "details": {"field_value": "application/pdf"}}, {"test_name": "Document Upload - tmpprantcc1.pdf - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T07:27:24.861192Z"}}, {"test_name": "Document Upload - tmpprantcc1.pdf - Label", "status": "PASSED", "message": "label has correct value: tmpprantcc1.pdf", "expected": "tmpprantcc1.pdf", "actual": "tmpprantcc1.pdf", "details": {}}, {"test_name": "Document Upload - tmpprantcc1.pdf - Content Type", "status": "PASSED", "message": "content_type has correct value: application/pdf", "expected": "application/pdf", "actual": "application/pdf", "details": {}}, {"test_name": "Document Upload - tmpxz50unvq.pdf - Success", "status": "PASSED", "message": "Document uploaded successfully with ID: 0keb22", "expected": null, "actual": null, "details": {"document_id": "0keb22", "file_name": "tmpxz50unvq.pdf"}}, {"test_name": "Document Upload - tmpxz50unvq.pdf - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "0keb22"}}, {"test_name": "Document Upload - tmpxz50unvq.pdf - label", "status": "PASSED", "message": "Required field 'label' exists", "expected": null, "actual": null, "details": {"field_value": "tmpxz50unvq.pdf"}}, {"test_name": "Document Upload - tmpxz50unvq.pdf - content_type", "status": "PASSED", "message": "Required field 'content_type' exists", "expected": null, "actual": null, "details": {"field_value": "application/pdf"}}, {"test_name": "Document Upload - tmpxz50unvq.pdf - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T07:27:25.554934Z"}}, {"test_name": "Document Upload - tmpxz50unvq.pdf - Label", "status": "PASSED", "message": "label has correct value: tmpxz50unvq.pdf", "expected": "tmpxz50unvq.pdf", "actual": "tmpxz50unvq.pdf", "details": {}}, {"test_name": "Document Upload - tmpxz50unvq.pdf - Content Type", "status": "PASSED", "message": "content_type has correct value: application/pdf", "expected": "application/pdf", "actual": "application/pdf", "details": {}}, {"test_name": "Document Upload - tmpkihq74p9.pdf - Success", "status": "PASSED", "message": "Document uploaded successfully with ID: JpK9N1", "expected": null, "actual": null, "details": {"document_id": "JpK9N1", "file_name": "tmpkihq74p9.pdf"}}, {"test_name": "Document Upload - tmpkihq74p9.pdf - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "JpK9N1"}}, {"test_name": "Document Upload - tmpkihq74p9.pdf - label", "status": "PASSED", "message": "Required field 'label' exists", "expected": null, "actual": null, "details": {"field_value": "tmpkihq74p9.pdf"}}, {"test_name": "Document Upload - tmpkihq74p9.pdf - content_type", "status": "PASSED", "message": "Required field 'content_type' exists", "expected": null, "actual": null, "details": {"field_value": "application/pdf"}}, {"test_name": "Document Upload - tmpkihq74p9.pdf - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T07:27:26.332158Z"}}, {"test_name": "Document Upload - tmpkihq74p9.pdf - Label", "status": "PASSED", "message": "label has correct value: tmpkihq74p9.pdf", "expected": "tmpkihq74p9.pdf", "actual": "tmpkihq74p9.pdf", "details": {}}, {"test_name": "Document Upload - tmpkihq74p9.pdf - Content Type", "status": "PASSED", "message": "content_type has correct value: application/pdf", "expected": "application/pdf", "actual": "application/pdf", "details": {}}, {"test_name": "Document Upload - tmplk8j5i_z.pdf - Success", "status": "PASSED", "message": "Document uploaded successfully with ID: sauMn1", "expected": null, "actual": null, "details": {"document_id": "sauMn1", "file_name": "tmplk8j5i_z.pdf"}}, {"test_name": "Document Upload - tmplk8j5i_z.pdf - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "sauMn1"}}, {"test_name": "Document Upload - tmplk8j5i_z.pdf - label", "status": "PASSED", "message": "Required field 'label' exists", "expected": null, "actual": null, "details": {"field_value": "tmplk8j5i_z.pdf"}}, {"test_name": "Document Upload - tmplk8j5i_z.pdf - content_type", "status": "PASSED", "message": "Required field 'content_type' exists", "expected": null, "actual": null, "details": {"field_value": "application/pdf"}}, {"test_name": "Document Upload - tmplk8j5i_z.pdf - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-11T07:27:27.046088Z"}}, {"test_name": "Document Upload - tmplk8j5i_z.pdf - Label", "status": "PASSED", "message": "label has correct value: tmplk8j5i_z.pdf", "expected": "tmplk8j5i_z.pdf", "actual": "tmplk8j5i_z.pdf", "details": {}}, {"test_name": "Document Upload - tmplk8j5i_z.pdf - Content Type", "status": "PASSED", "message": "content_type has correct value: application/pdf", "expected": "application/pdf", "actual": "application/pdf", "details": {}}]}