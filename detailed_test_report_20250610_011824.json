{"test_run_info": {"timestamp": "2025-06-10T01:18:24.264377", "total_tests": 142, "duration_seconds": 140.8409309387207, "api_calls": 77, "api_errors": 6}, "summary": {"passed": 130, "failed": 12, "partial": 0, "errors": 0, "skipped": 0}, "test_results": [{"test_name": "Packager Config - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "ffPYh1"}}, {"test_name": "Packager Config - label", "status": "PASSED", "message": "Required field 'label' exists", "expected": null, "actual": null, "details": {"field_value": "Enhanced Test Script Packager"}}, {"test_name": "Packager Config - packager_schedule_type", "status": "PASSED", "message": "Required field 'packager_schedule_type' exists", "expected": null, "actual": null, "details": {"field_value": "MANUAL"}}, {"test_name": "Packager Config - document_status_filter", "status": "PASSED", "message": "Required field 'document_status_filter' exists", "expected": null, "actual": null, "details": {"field_value": ["VERIFIED"]}}, {"test_name": "Packager Config - eb_status_filter", "status": "PASSED", "message": "Required field 'eb_status_filter' exists", "expected": null, "actual": null, "details": {"field_value": ["VERIFIED"]}}, {"test_name": "Packager Config - output_format", "status": "PASSED", "message": "Required field 'output_format' exists", "expected": null, "actual": null, "details": {"field_value": "json"}}, {"test_name": "Packager Config - delivery_data_grouping_strategy", "status": "PASSED", "message": "Required field 'delivery_data_grouping_strategy' exists", "expected": null, "actual": null, "details": {"field_value": "ALL_IN_ONE"}}, {"test_name": "Packager Config - Label", "status": "PASSED", "message": "label has correct value: Enhanced Test Script Packager", "expected": "Enhanced Test Script Packager", "actual": "Enhanced Test Script Packager", "details": {}}, {"test_name": "Packager Config - Schedule Type", "status": "PASSED", "message": "packager_schedule_type has correct value: MANUAL", "expected": "MANUAL", "actual": "MANUAL", "details": {}}, {"test_name": "Packager Config - Output Format", "status": "PASSED", "message": "output_format has correct value: json", "expected": "json", "actual": "json", "details": {}}, {"test_name": "Packager Config - Document Status Filter", "status": "PASSED", "message": "Document status filter correct: ['VERIFIED']", "expected": ["VERIFIED"], "actual": ["VERIFIED"], "details": {}}, {"test_name": "Packager Config - Deduplication Fields", "status": "PASSED", "message": "Deduplication fields correct: []", "expected": [], "actual": [], "details": {}}, {"test_name": "Packager Config - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "ffPYh1"}}, {"test_name": "Packager Config - label", "status": "PASSED", "message": "Required field 'label' exists", "expected": null, "actual": null, "details": {"field_value": "Enhanced Test Script Packager"}}, {"test_name": "Packager Config - packager_schedule_type", "status": "PASSED", "message": "Required field 'packager_schedule_type' exists", "expected": null, "actual": null, "details": {"field_value": "MANUAL"}}, {"test_name": "Packager Config - document_status_filter", "status": "PASSED", "message": "Required field 'document_status_filter' exists", "expected": null, "actual": null, "details": {"field_value": ["VERIFIED"]}}, {"test_name": "Packager Config - eb_status_filter", "status": "PASSED", "message": "Required field 'eb_status_filter' exists", "expected": null, "actual": null, "details": {"field_value": ["VERIFIED"]}}, {"test_name": "Packager Config - output_format", "status": "PASSED", "message": "Required field 'output_format' exists", "expected": null, "actual": null, "details": {"field_value": "json"}}, {"test_name": "Packager Config - delivery_data_grouping_strategy", "status": "PASSED", "message": "Required field 'delivery_data_grouping_strategy' exists", "expected": null, "actual": null, "details": {"field_value": "ALL_IN_ONE"}}, {"test_name": "Packager Config - Label", "status": "PASSED", "message": "label has correct value: Enhanced Test Script Packager", "expected": "Enhanced Test Script Packager", "actual": "Enhanced Test Script Packager", "details": {}}, {"test_name": "Packager Config - Schedule Type", "status": "PASSED", "message": "packager_schedule_type has correct value: MANUAL", "expected": "MANUAL", "actual": "MANUAL", "details": {}}, {"test_name": "Packager Config - Output Format", "status": "PASSED", "message": "output_format has correct value: json", "expected": "json", "actual": "json", "details": {}}, {"test_name": "Packager Config - Document Status Filter", "status": "PASSED", "message": "Document status filter correct: ['VERIFIED']", "expected": ["VERIFIED"], "actual": ["VERIFIED"], "details": {}}, {"test_name": "Packager Config - Deduplication Fields", "status": "PASSED", "message": "Deduplication fields correct: []", "expected": [], "actual": [], "details": {}}, {"test_name": "Invalid Config - packager_schedule_type", "status": "PASSED", "message": "Invalid configuration properly rejected for field: packager_schedule_type", "expected": null, "actual": null, "details": {}}, {"test_name": "Invalid Config - output_format", "status": "PASSED", "message": "Invalid configuration properly rejected for field: output_format", "expected": null, "actual": null, "details": {}}, {"test_name": "Invalid Config - label", "status": "FAILED", "message": "Invalid configuration was accepted (should have been rejected): label", "expected": null, "actual": null, "details": {"created_packager_id": "ERzl72"}}, {"test_name": "Document Upload - COPY_doc2_dupe1.pdf - Success", "status": "PASSED", "message": "Document uploaded successfully with ID: RUUfU1", "expected": null, "actual": null, "details": {"document_id": "RUUfU1", "file_name": "COPY_doc2_dupe1.pdf"}}, {"test_name": "Document Upload - COPY_doc2_dupe1.pdf - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "RUUfU1"}}, {"test_name": "Document Upload - COPY_doc2_dupe1.pdf - label", "status": "PASSED", "message": "Required field 'label' exists", "expected": null, "actual": null, "details": {"field_value": "COPY_doc2_dupe1.pdf"}}, {"test_name": "Document Upload - COPY_doc2_dupe1.pdf - content_type", "status": "PASSED", "message": "Required field 'content_type' exists", "expected": null, "actual": null, "details": {"field_value": "application/pdf"}}, {"test_name": "Document Upload - COPY_doc2_dupe1.pdf - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-10T06:16:30.466040Z"}}, {"test_name": "Document Upload - COPY_doc2_dupe1.pdf - Label", "status": "PASSED", "message": "label has correct value: COPY_doc2_dupe1.pdf", "expected": "COPY_doc2_dupe1.pdf", "actual": "COPY_doc2_dupe1.pdf", "details": {}}, {"test_name": "Document Upload - COPY_doc2_dupe1.pdf - Content Type", "status": "PASSED", "message": "content_type has correct value: application/pdf", "expected": "application/pdf", "actual": "application/pdf", "details": {}}, {"test_name": "DocumentData for RUUfU1 - Existence", "status": "PASSED", "message": "DocumentData found for document RUUfU1", "expected": null, "actual": null, "details": {"doc_data_id": "Nkbyd1"}}, {"test_name": "DocumentData for RUUfU1 - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "Nkbyd1"}}, {"test_name": "DocumentData for RUUfU1 - document", "status": "PASSED", "message": "Required field 'document' exists", "expected": null, "actual": null, "details": {"field_value": "https://api-sandbox.glynt.ai/v6/data-pools/Fj8uL1/documents/RUUfU1/"}}, {"test_name": "DocumentData for RUUfU1 - status", "status": "PASSED", "message": "Required field 'status' exists", "expected": null, "actual": null, "details": {"field_value": "PENDING_CREATION"}}, {"test_name": "DocumentData for RUUfU1 - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-10T06:16:30.479657Z"}}, {"test_name": "DocumentData for RUUfU1 - updated_at", "status": "PASSED", "message": "Required field 'updated_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-10T06:16:30.479698Z"}}, {"test_name": "DocumentData for RUUfU1 - ID Type", "status": "PASSED", "message": "Field 'id' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for RUUfU1 - Status Type", "status": "PASSED", "message": "Field 'status' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for RUUfU1 - Status Valid", "status": "PASSED", "message": "DocumentData has valid status: PENDING_CREATION", "expected": null, "actual": null, "details": {}}, {"test_name": "Document Upload - COPY_doc3_dupe1.pdf - Success", "status": "PASSED", "message": "Document uploaded successfully with ID: 0G4tu1", "expected": null, "actual": null, "details": {"document_id": "0G4tu1", "file_name": "COPY_doc3_dupe1.pdf"}}, {"test_name": "Document Upload - COPY_doc3_dupe1.pdf - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "0G4tu1"}}, {"test_name": "Document Upload - COPY_doc3_dupe1.pdf - label", "status": "PASSED", "message": "Required field 'label' exists", "expected": null, "actual": null, "details": {"field_value": "COPY_doc3_dupe1.pdf"}}, {"test_name": "Document Upload - COPY_doc3_dupe1.pdf - content_type", "status": "PASSED", "message": "Required field 'content_type' exists", "expected": null, "actual": null, "details": {"field_value": "application/pdf"}}, {"test_name": "Document Upload - COPY_doc3_dupe1.pdf - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-10T06:16:33.894066Z"}}, {"test_name": "Document Upload - COPY_doc3_dupe1.pdf - Label", "status": "PASSED", "message": "label has correct value: COPY_doc3_dupe1.pdf", "expected": "COPY_doc3_dupe1.pdf", "actual": "COPY_doc3_dupe1.pdf", "details": {}}, {"test_name": "Document Upload - COPY_doc3_dupe1.pdf - Content Type", "status": "PASSED", "message": "content_type has correct value: application/pdf", "expected": "application/pdf", "actual": "application/pdf", "details": {}}, {"test_name": "DocumentData for 0G4tu1 - Existence", "status": "PASSED", "message": "DocumentData found for document 0G4tu1", "expected": null, "actual": null, "details": {"doc_data_id": "wVBC42"}}, {"test_name": "DocumentData for 0G4tu1 - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "wVBC42"}}, {"test_name": "DocumentData for 0G4tu1 - document", "status": "PASSED", "message": "Required field 'document' exists", "expected": null, "actual": null, "details": {"field_value": "https://api-sandbox.glynt.ai/v6/data-pools/Fj8uL1/documents/0G4tu1/"}}, {"test_name": "DocumentData for 0G4tu1 - status", "status": "PASSED", "message": "Required field 'status' exists", "expected": null, "actual": null, "details": {"field_value": "PENDING_CREATION"}}, {"test_name": "DocumentData for 0G4tu1 - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-10T06:16:33.904559Z"}}, {"test_name": "DocumentData for 0G4tu1 - updated_at", "status": "PASSED", "message": "Required field 'updated_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-10T06:16:33.904600Z"}}, {"test_name": "DocumentData for 0G4tu1 - ID Type", "status": "PASSED", "message": "Field 'id' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for 0G4tu1 - Status Type", "status": "PASSED", "message": "Field 'status' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for 0G4tu1 - Status Valid", "status": "PASSED", "message": "DocumentData has valid status: PENDING_CREATION", "expected": null, "actual": null, "details": {}}, {"test_name": "Document Upload - COPY_doc3_dupe2.pdf - Success", "status": "PASSED", "message": "Document uploaded successfully with ID: JLkQF1", "expected": null, "actual": null, "details": {"document_id": "JLkQF1", "file_name": "COPY_doc3_dupe2.pdf"}}, {"test_name": "Document Upload - COPY_doc3_dupe2.pdf - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "JLkQF1"}}, {"test_name": "Document Upload - COPY_doc3_dupe2.pdf - label", "status": "PASSED", "message": "Required field 'label' exists", "expected": null, "actual": null, "details": {"field_value": "COPY_doc3_dupe2.pdf"}}, {"test_name": "Document Upload - COPY_doc3_dupe2.pdf - content_type", "status": "PASSED", "message": "Required field 'content_type' exists", "expected": null, "actual": null, "details": {"field_value": "application/pdf"}}, {"test_name": "Document Upload - COPY_doc3_dupe2.pdf - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-10T06:16:37.114556Z"}}, {"test_name": "Document Upload - COPY_doc3_dupe2.pdf - Label", "status": "PASSED", "message": "label has correct value: COPY_doc3_dupe2.pdf", "expected": "COPY_doc3_dupe2.pdf", "actual": "COPY_doc3_dupe2.pdf", "details": {}}, {"test_name": "Document Upload - COPY_doc3_dupe2.pdf - Content Type", "status": "PASSED", "message": "content_type has correct value: application/pdf", "expected": "application/pdf", "actual": "application/pdf", "details": {}}, {"test_name": "DocumentData for JLkQF1 - Existence", "status": "PASSED", "message": "DocumentData found for document JLkQF1", "expected": null, "actual": null, "details": {"doc_data_id": "FbrjO1"}}, {"test_name": "DocumentData for JLkQF1 - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "FbrjO1"}}, {"test_name": "DocumentData for JLkQF1 - document", "status": "PASSED", "message": "Required field 'document' exists", "expected": null, "actual": null, "details": {"field_value": "https://api-sandbox.glynt.ai/v6/data-pools/Fj8uL1/documents/JLkQF1/"}}, {"test_name": "DocumentData for JLkQF1 - status", "status": "PASSED", "message": "Required field 'status' exists", "expected": null, "actual": null, "details": {"field_value": "PENDING_CREATION"}}, {"test_name": "DocumentData for JLkQF1 - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-10T06:16:37.126507Z"}}, {"test_name": "DocumentData for JLkQF1 - updated_at", "status": "PASSED", "message": "Required field 'updated_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-10T06:16:37.126549Z"}}, {"test_name": "DocumentData for JLkQF1 - ID Type", "status": "PASSED", "message": "Field 'id' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for JLkQF1 - Status Type", "status": "PASSED", "message": "Field 'status' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for JLkQF1 - Status Valid", "status": "PASSED", "message": "DocumentData has valid status: PENDING_CREATION", "expected": null, "actual": null, "details": {}}, {"test_name": "Document Upload - orig_doc_1.pdf - Success", "status": "PASSED", "message": "Document uploaded successfully with ID: s6Kef1", "expected": null, "actual": null, "details": {"document_id": "s6Kef1", "file_name": "orig_doc_1.pdf"}}, {"test_name": "Document Upload - orig_doc_1.pdf - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "s6Kef1"}}, {"test_name": "Document Upload - orig_doc_1.pdf - label", "status": "PASSED", "message": "Required field 'label' exists", "expected": null, "actual": null, "details": {"field_value": "orig_doc_1.pdf"}}, {"test_name": "Document Upload - orig_doc_1.pdf - content_type", "status": "PASSED", "message": "Required field 'content_type' exists", "expected": null, "actual": null, "details": {"field_value": "application/pdf"}}, {"test_name": "Document Upload - orig_doc_1.pdf - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-10T06:16:40.353299Z"}}, {"test_name": "Document Upload - orig_doc_1.pdf - Label", "status": "PASSED", "message": "label has correct value: orig_doc_1.pdf", "expected": "orig_doc_1.pdf", "actual": "orig_doc_1.pdf", "details": {}}, {"test_name": "Document Upload - orig_doc_1.pdf - Content Type", "status": "PASSED", "message": "content_type has correct value: application/pdf", "expected": "application/pdf", "actual": "application/pdf", "details": {}}, {"test_name": "DocumentData for s6Kef1 - Existence", "status": "PASSED", "message": "DocumentData found for document s6Kef1", "expected": null, "actual": null, "details": {"doc_data_id": "oMRxo1"}}, {"test_name": "DocumentData for s6Kef1 - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "oMRxo1"}}, {"test_name": "DocumentData for s6Kef1 - document", "status": "PASSED", "message": "Required field 'document' exists", "expected": null, "actual": null, "details": {"field_value": "https://api-sandbox.glynt.ai/v6/data-pools/Fj8uL1/documents/s6Kef1/"}}, {"test_name": "DocumentData for s6Kef1 - status", "status": "PASSED", "message": "Required field 'status' exists", "expected": null, "actual": null, "details": {"field_value": "PENDING_CREATION"}}, {"test_name": "DocumentData for s6Kef1 - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-10T06:16:40.380922Z"}}, {"test_name": "DocumentData for s6Kef1 - updated_at", "status": "PASSED", "message": "Required field 'updated_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-10T06:16:40.380971Z"}}, {"test_name": "DocumentData for s6Kef1 - ID Type", "status": "PASSED", "message": "Field 'id' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for s6Kef1 - Status Type", "status": "PASSED", "message": "Field 'status' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for s6Kef1 - Status Valid", "status": "PASSED", "message": "DocumentData has valid status: PENDING_CREATION", "expected": null, "actual": null, "details": {}}, {"test_name": "Document Upload - origin_doc_2.pdf - Success", "status": "PASSED", "message": "Document uploaded successfully with ID: Rstr52", "expected": null, "actual": null, "details": {"document_id": "Rstr52", "file_name": "origin_doc_2.pdf"}}, {"test_name": "Document Upload - origin_doc_2.pdf - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "Rstr52"}}, {"test_name": "Document Upload - origin_doc_2.pdf - label", "status": "PASSED", "message": "Required field 'label' exists", "expected": null, "actual": null, "details": {"field_value": "origin_doc_2.pdf"}}, {"test_name": "Document Upload - origin_doc_2.pdf - content_type", "status": "PASSED", "message": "Required field 'content_type' exists", "expected": null, "actual": null, "details": {"field_value": "application/pdf"}}, {"test_name": "Document Upload - origin_doc_2.pdf - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-10T06:16:43.666488Z"}}, {"test_name": "Document Upload - origin_doc_2.pdf - Label", "status": "PASSED", "message": "label has correct value: origin_doc_2.pdf", "expected": "origin_doc_2.pdf", "actual": "origin_doc_2.pdf", "details": {}}, {"test_name": "Document Upload - origin_doc_2.pdf - Content Type", "status": "PASSED", "message": "content_type has correct value: application/pdf", "expected": "application/pdf", "actual": "application/pdf", "details": {}}, {"test_name": "DocumentData for Rstr52 - Existence", "status": "PASSED", "message": "DocumentData found for document Rstr52", "expected": null, "actual": null, "details": {"doc_data_id": "7S7V91"}}, {"test_name": "DocumentData for Rstr52 - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "7S7V91"}}, {"test_name": "DocumentData for Rstr52 - document", "status": "PASSED", "message": "Required field 'document' exists", "expected": null, "actual": null, "details": {"field_value": "https://api-sandbox.glynt.ai/v6/data-pools/Fj8uL1/documents/Rstr52/"}}, {"test_name": "DocumentData for Rstr52 - status", "status": "PASSED", "message": "Required field 'status' exists", "expected": null, "actual": null, "details": {"field_value": "PENDING_CREATION"}}, {"test_name": "DocumentData for Rstr52 - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-10T06:16:43.678751Z"}}, {"test_name": "DocumentData for Rstr52 - updated_at", "status": "PASSED", "message": "Required field 'updated_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-10T06:16:43.678793Z"}}, {"test_name": "DocumentData for Rstr52 - ID Type", "status": "PASSED", "message": "Field 'id' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for Rstr52 - Status Type", "status": "PASSED", "message": "Field 'status' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for Rstr52 - Status Valid", "status": "PASSED", "message": "DocumentData has valid status: PENDING_CREATION", "expected": null, "actual": null, "details": {}}, {"test_name": "Document Upload - orig_doc_3.pdf - Success", "status": "PASSED", "message": "Document uploaded successfully with ID: kxZPQ1", "expected": null, "actual": null, "details": {"document_id": "kxZPQ1", "file_name": "orig_doc_3.pdf"}}, {"test_name": "Document Upload - orig_doc_3.pdf - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "kxZPQ1"}}, {"test_name": "Document Upload - orig_doc_3.pdf - label", "status": "PASSED", "message": "Required field 'label' exists", "expected": null, "actual": null, "details": {"field_value": "orig_doc_3.pdf"}}, {"test_name": "Document Upload - orig_doc_3.pdf - content_type", "status": "PASSED", "message": "Required field 'content_type' exists", "expected": null, "actual": null, "details": {"field_value": "application/pdf"}}, {"test_name": "Document Upload - orig_doc_3.pdf - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-10T06:16:46.904456Z"}}, {"test_name": "Document Upload - orig_doc_3.pdf - Label", "status": "PASSED", "message": "label has correct value: orig_doc_3.pdf", "expected": "orig_doc_3.pdf", "actual": "orig_doc_3.pdf", "details": {}}, {"test_name": "Document Upload - orig_doc_3.pdf - Content Type", "status": "PASSED", "message": "content_type has correct value: application/pdf", "expected": "application/pdf", "actual": "application/pdf", "details": {}}, {"test_name": "DocumentData for kxZPQ1 - Existence", "status": "PASSED", "message": "DocumentData found for document kxZPQ1", "expected": null, "actual": null, "details": {"doc_data_id": "gDhiZ1"}}, {"test_name": "DocumentData for kxZPQ1 - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "gDhiZ1"}}, {"test_name": "DocumentData for kxZPQ1 - document", "status": "PASSED", "message": "Required field 'document' exists", "expected": null, "actual": null, "details": {"field_value": "https://api-sandbox.glynt.ai/v6/data-pools/Fj8uL1/documents/kxZPQ1/"}}, {"test_name": "DocumentData for kxZPQ1 - status", "status": "PASSED", "message": "Required field 'status' exists", "expected": null, "actual": null, "details": {"field_value": "PENDING_CREATION"}}, {"test_name": "DocumentData for kxZPQ1 - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-10T06:16:46.915752Z"}}, {"test_name": "DocumentData for kxZPQ1 - updated_at", "status": "PASSED", "message": "Required field 'updated_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-10T06:16:46.915795Z"}}, {"test_name": "DocumentData for kxZPQ1 - ID Type", "status": "PASSED", "message": "Field 'id' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for kxZPQ1 - Status Type", "status": "PASSED", "message": "Field 'status' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for kxZPQ1 - Status Valid", "status": "PASSED", "message": "DocumentData has valid status: PENDING_CREATION", "expected": null, "actual": null, "details": {}}, {"test_name": "Package 0AEbn1 Entries - Entry Count", "status": "PASSED", "message": "Found 6 package entries", "expected": null, "actual": null, "details": {"entry_count": 6}}, {"test_name": "Package 0AEbn1 Entries - Duplicate Exclusion", "status": "PASSED", "message": "Duplicate exclusion working: 4 documents excluded as duplicates", "expected": null, "actual": null, "details": {"excluded_count": 4}}, {"test_name": "Package 0AEbn1 Config Compliance - Configuration Recorded", "status": "PASSED", "message": "Package configuration recorded for compliance verification", "expected": null, "actual": null, "details": {"include_source_files": true, "include_relationship_details": true, "output_format": "json", "grouping_strategy": "ALL_IN_ONE", "dedup_fields": ["CustomerName", "<PERSON><PERSON><PERSON><PERSON>", "StatementDate"]}}, {"test_name": "Package 0AEbn1 Entries - Entry Count", "status": "PASSED", "message": "Found 6 package entries", "expected": null, "actual": null, "details": {"entry_count": 6}}, {"test_name": "Package 0AEbn1 Entries - Duplicate Exclusion", "status": "PASSED", "message": "Duplicate exclusion working: 4 documents excluded as duplicates", "expected": null, "actual": null, "details": {"excluded_count": 4}}, {"test_name": "DocumentData Fields - source_file_id", "status": "FAILED", "message": "No documents have source_file_id populated", "expected": null, "actual": null, "details": {}}, {"test_name": "DocumentData Fields - source_file_name", "status": "FAILED", "message": "No documents have source_file_name populated", "expected": null, "actual": null, "details": {}}, {"test_name": "DocumentData Fields - source_file_md5", "status": "FAILED", "message": "No documents have source_file_md5 populated", "expected": null, "actual": null, "details": {}}, {"test_name": "DocumentData Fields - received_at", "status": "FAILED", "message": "No documents have received_at populated", "expected": null, "actual": null, "details": {}}, {"test_name": "DocumentData Fields - source_file_num_pages", "status": "FAILED", "message": "No documents have source_file_num_pages populated", "expected": null, "actual": null, "details": {}}, {"test_name": "DocumentData Fields - source_file_size", "status": "FAILED", "message": "No documents have source_file_size populated", "expected": null, "actual": null, "details": {}}, {"test_name": "DocumentData Fields - source_file_detected_language", "status": "FAILED", "message": "No documents have source_file_detected_language populated", "expected": null, "actual": null, "details": {}}, {"test_name": "Package 0AEbn1 Config Compliance - Configuration Recorded", "status": "PASSED", "message": "Package configuration recorded for compliance verification", "expected": null, "actual": null, "details": {"include_source_files": true, "include_relationship_details": true, "output_format": "json", "grouping_strategy": "ALL_IN_ONE", "dedup_fields": ["CustomerName", "<PERSON><PERSON><PERSON><PERSON>", "StatementDate"]}}, {"test_name": "Package 0AEbn1 Verification - Status Updated", "status": "PASSED", "message": "Package status successfully updated from PROCESSING_SUCCESSFUL to VERIFIED", "expected": null, "actual": null, "details": {"initial_status": "PROCESSING_SUCCESSFUL", "final_status": "VERIFIED"}}, {"test_name": "Package 0AEbn1 Final Delivery - Preview Count", "status": "FAILED", "message": "Preview shows 0 documents, expected 1", "expected": null, "actual": null, "details": {"preview_count": 0, "expected": 1}}, {"test_name": "Package 0AEbn1 Final Delivery - Download Available", "status": "PASSED", "message": "Package download endpoint is accessible", "expected": null, "actual": null, "details": {"download_available": true}}, {"test_name": "Flag Variations - Full CoC + Duplicates ON + Relationships ON Creation", "status": "FAILED", "message": "Failed to create test packager for Full CoC + Duplicates ON + Relationships ON", "expected": null, "actual": null, "details": {}}, {"test_name": "Flag Variations - Essential CoC + Duplicates OFF + Relationships ON Creation", "status": "FAILED", "message": "Failed to create test packager for Essential CoC + Duplicates OFF + Relationships ON", "expected": null, "actual": null, "details": {}}, {"test_name": "Flag Variations - No CoC + All Flags OFF Creation", "status": "FAILED", "message": "Failed to create test packager for No CoC + All Flags OFF", "expected": null, "actual": null, "details": {}}]}