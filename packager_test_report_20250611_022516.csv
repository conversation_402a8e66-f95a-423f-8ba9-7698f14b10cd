Timestamp,Step,Status,Details
2025-06-11T02:25:16.920732,<PERSON><PERSON><PERSON> Start,Completed,
2025-06-11T02:25:20.999290,Get Access Token,Completed,
2025-06-11T02:25:25.085969,Data Pool Cleanup,Cancelled by user,
2025-06-11T02:25:27.561230,Packager Lifecycle Test,Completed,Created packager Tscui1 with 12/12 validations passed. Verified packager exists with 12/12 field validations passed. Deleted temp packager Tscui1. Verified temp packager Tscui1 is deleted. 
2025-06-11T02:25:27.883579,Main Packager Creation,Completed,Created main packager ID: LjsfT1
2025-06-11T02:25:47.258564,Enhanced Document Upload,Completed,Uploaded 6/6 documents with 96/96 validations passed
2025-06-11T02:25:49.138501,Document Processing Status Check,Completed,All 6 documents still processing. 
2025-06-11T02:26:31.664517,Create Extraction Batch,Completed,Created extraction batch ID: uG8bP1. Batch processing completed. 
2025-06-11T02:26:32.006569,Simulate Content Duplication,Failed,Error during corrections: name 'packager_details' is not defined
2025-06-11T02:26:54.170520,Verify Extraction Batch,Failed,"Verified extraction batch uG8bP1 on attempt 1. Found 5 duplicate relationships (0 MD5, 0 label, 5 content). Error verifying extraction batch: name 'target_documents' is not defined. "
2025-06-11T02:27:05.412712,Change Packager Schedule to CRON,Completed,Changed packager LjsfT1 schedule to CRON. CRON schedule successfully created package c6P5f1 after 10s. Package processing completed. CRON schedule working correctly. 
2025-06-11T02:27:07.569421,Validate CRON Package,Completed, 5 duplicate relationships found. CRON package validation: 9/9 checks passed.
2025-06-11T02:27:07.569775,Test Package Reuse,Skipped by user,
2025-06-11T02:27:07.569987,Test Delete Package and Re-run CRON,Skipped by user,
2025-06-11T02:27:07.898825,Reset Packager to MANUAL,Completed,Packager schedule set to MANUAL
2025-06-11T02:27:10.886802,Simulate Package,Completed,"Simulation successful: 1 data items, 0 errors. "
2025-06-11T02:27:14.915593,Duplicate Filtering Test,Completed,All 3 duplicate filtering tests passed. 
2025-06-11T02:27:19.836097,Create Package,Partial Success,"Using CRON package ID: c6P5f1.  5 duplicate relationships found (0 MD5, 5 content). Package validation: 18/19 checks passed."
2025-06-11T02:27:19.836409,Trigger Delivery,Skipped by user,
2025-06-11T02:27:22.131278,Flag Variation Testing,Partial Success,Only 0/3 flag variation tests passed. 
2025-06-11T02:27:24.571213,Debug API Issues,Completed,All 4 debug tests passed
2025-06-11T02:27:39.282750,Incremental vs Cumulative Delivery Testing,Completed,All 0 delivery scenario tests passed
2025-06-11T02:27:39.559308,Generate CoC Report,Failed,Failed to generate CoC report - no response received. 
2025-06-11T02:27:39.559588,Script End,Completed,
