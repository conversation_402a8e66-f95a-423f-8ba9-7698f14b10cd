#!/bin/sh

APP_ENV=$1
BRANCH=$2

### bypassed (these packages are in the image)
### apt-get -y update
###
### # Set the Locale to en_US.UTF-8
### #
### # https://github.com/pypa/pip/issues/10219 suggests this is a problem with
### # a UTF-8 path that was recently added to the ansible tarball.
### #
### # If/when the locale gets set to en_US.UTF-8 in the Docker image, this bit
### # can go away.
### apt-get -y install locales
### locale-gen en_US.UTF-8
### export LANG=en_US.UTF-8
### export LC_ALL=en_US.UTF-8
###
### apt-get -y install openssh-client rsync
###
### # Install Python 3.8 so that we can get a newer Ansible (and not
### # get deprecation warnings about Python 3.6.9)
### apt-get -y install python3.8 python3.8-venv python3-pip
###
### # Activate a virtual environment and install dependencies
### python3.8 -m venv venv
### . venv/bin/activate
### pip install -q --upgrade pip
### pip install -q boto3==1.22.13  # current on 5/13/2022
### pip install -q ansible==5.7.1  # current on 5/13/2022

# activate the ansible venv from the deployment image
. /venv/bin/activate

# Set EC2 credentials
. aws_credentials/aws_credentials.sh

# Exports shared information such as aws region and shared rds creds
. shared_env/${APP_ENV}_shared_env.sh

cp -r ec2_deployment_key/glynt-api-${APP_ENV}.pem ssh_key.pem
chmod 0600 ssh_key.pem

# Copying the vault password file
cp ansible_vault/vault_pass.txt vault_pass.txt

# Ansible execution with all needed resources
echo "Executing ansible-playbook"
cd source_code/api/__provision__/ansible && ansible-playbook main.yml \
	-e branch_name=${BRANCH} \
	--private-key=../../../../ssh_key.pem \
	--inventory=inventory/${APP_ENV} \
	--vault-password-file=../../../../vault_pass.txt

