#!/bin/sh

APP_ENV=$1

# Setting AWS credentials
. aws_credentials/aws_credentials.sh

. shared_env/${APP_ENV}_shared_env.sh
export TF_VAR_aws_region=${AWS_REGION}

# Running terraform with all needed parameters
cd source_code/api/__provision__/terraform/environments/${APP_ENV} && \
	terraform init -upgrade && \
	terraform plan -out=current_tfplan && \
	echo "Applying plan in 30 seconds..." && \
	sleep 30 && \
	terraform apply current_tfplan && \
	rm current_tfplan

