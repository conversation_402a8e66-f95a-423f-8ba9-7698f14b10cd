---
resources:
  - name: source_code
    type: git
    source:
      uri: **************:PaceWebApp/glynt-api.git
      branch: deploy/stage
      private_key: ((glynt_api_git_key))

  - name: aws_credentials
    type: s3
    source:
      bucket: secrets.baja.wattzon
      versioned_file: aws_credentials.sh
      region_name: us-west-2
      access_key_id: ((aws_access_key))
      secret_access_key: ((aws_secret_key))

  - name: shared_env
    type: s3
    source:
      bucket: secrets.baja.wattzon
      versioned_file: stage_shared_env.sh
      region_name: us-west-2
      access_key_id: ((aws_access_key))
      secret_access_key: ((aws_secret_key))

  - name: ec2_deployment_key
    type: s3
    source:
      bucket: secrets.baja.wattzon
      versioned_file: glynt_api/glynt-api-stage.pem
      region_name: us-west-2
      access_key_id: ((aws_access_key))
      secret_access_key: ((aws_secret_key))

  - name: ansible_vault
    type: s3
    source:
      bucket: secrets.baja.wattzon
      versioned_file: glynt_api/vault_pass.txt
      region_name: us-west-2
      access_key_id: ((aws_access_key))
      secret_access_key: ((aws_secret_key))

jobs:
  - name: create_infrastructure
    plan:
      - get: source_code
      - get: aws_credentials
      - get: shared_env
      - task: run_terraform
        config:
          platform: linux
          image_resource:
            type: docker-image
            source: 
              repository: 700095865679.dkr.ecr.us-west-1.amazonaws.com/glynt/terraform-custom
              aws_access_key_id: ((aws_access_key_ecr_downloads_production))
              aws_secret_access_key:  ((aws_secret_key_ecr_downloads_production))
              tag: 0.12.21
          inputs:
            - name: shared_env
            - name: source_code
            - name: aws_credentials
          run:
            path: source_code/api/__provision__/ci/scripts/run_terraform.sh
            args: [stage]

  - name: configure_infrastructure
    plan:
      - get: source_code
        passed: [create_infrastructure]
        trigger: true
      - get: aws_credentials
      - get: shared_env
      - get: ec2_deployment_key
      - get: ansible_vault
      - task: run_ansible
        config:
          platform: linux
          image_resource:
            type: docker-image
            source:
              repository: 700095865679.dkr.ecr.us-west-1.amazonaws.com/glynt/deployment
              aws_access_key_id: ((aws_access_key_ecr_downloads_production))
              aws_secret_access_key:  ((aws_secret_key_ecr_downloads_production))
              tag: latest
          inputs:
            - name: ec2_deployment_key
            - name: shared_env
            - name: ansible_vault
            - name: source_code
            - name: aws_credentials
          run:
            path: source_code/api/__provision__/ci/scripts/run_ansible.sh

            args: [stage, deploy/stage]
