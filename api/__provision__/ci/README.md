# Glynt API Deploy Pipelines

This directory contains the files necessary to configure Concourse to deploy
the Glynt API project. Any time the pipeline files in the /pipelines/
directory are changed, the pipelines will need to be updated on Concourse as
well.

By convention, we only update each environment's pipeline as we deploy the
environment. So if you make changes to an environment which you will not deploy
right away (for example stage or production), then when these changes are
merged to `master`, you MUST add a note to the corresponding release ticket on
JIRA so that the person deploying those other environments knows that the
pipelines need to be updated. Similarly, as these pipelines are updated on those
environments, the JIRA ticket should be updated with a note that the pipeline
has been updated, so that if that environment is deployed again, the person
doing the deploy isn't confused when there are no detected changes in the
pipeline.

### Initial Setup

These setup instructions assume the target AWS account is created and the
control box for that account with Con<PERSON> is provisioned and functioning, and
that you have access to it and the 'glynt' team that should exist on it. It also
assumes that the tfstate bucket (`tfstate.<account_name>.wattzon`) and secrets
bucket (`secrets.<account_name>.wattzon`) for that account have already been
created.

#### Step 1
Create and populate an S3 bucket in the target AWS account with the secrets
concourse needs for deployment.

1. In the S3 secrets bucket, create a `glynt_api` directory if it does not already
   exist.
2. Create a file called `glynt_api/vault_pass.txt` in the bucket. The
   file should be a single line of text with the Ansible Vault password for
   the project. This password is stored in LastPass.
3. If it does not already exist, create a `glynt-api-<environment>` AWS key pair and
   upload the private key to last pass. If it does already exist, get the
   private key file from LastPass. Upload the `glynt_api/glynt-api-<environment>.pem`
   private key file to the s3 bucket. TODO: automate this step via terraform

#### Step 2
Visit the Concourse webpage and download the `fly` command line application.

#### Step 3
Login to the correct Concourse instance and team using fly. The team we use for
all of the glynt api deploy pipelines is called "glynt". You should have been given
credentials for logging into that team via LastPass.

```
$ fly -t <some_id_of_your_choice> login -n glynt -c https://<account_name>-ci.wattzon.com

```

#### Step 4

Copy ci/vars.dist.yml and fill in the copy with the needed secrets.

#### Step 5

Register the pipelines in Concourse. If you are updating a pipeline, be sure the name matches the target pipeline exactly. Carefully review the changes it will make before saying yes. If you do not fully understand every change this step will make, consult with the rest of the team so that we get a full understanding of what is being updated.

```
$ fly -t <id_chosen_above> set-pipeline -p <some_name_for_pipeline> -c <path_to_pipeline_yaml> --load-vars-from <path_to_vars_file>

```

#### Step 6

If you just created a pipeline for the first time, login to the Concourse
webapp and unpause the pipeline so that jobs are available for building.

### Deploying

To deploy, first use the following command to force-push some branch to the
intended deploy branch.

```
$ git push origin +<some_branch>:deploy/<env>
```

At present, the three deploy branches are:

- deploy/sandbox
- deploy/stage
- deploy/production

Then, login to the Concourse webapp and click the "run" button on the first
task in the pipeline you created. This will begin a deployment using the source
code in the appropriate deploy branch.
