# API Provisioning

The API is provisioned by a combination of Terraform building the infrastructure,
and Ansible configuring that infrastructure. The `/ci/` directory contains the
scripts to allow Concourse to manage running those tools in the proper
sequence. When in doubt, the steps followed by the CI scripts are the
cannonical way to deploy the API, and can be referred to when confused about how
something works or if there appears to be a discrepancy in this documentation.
