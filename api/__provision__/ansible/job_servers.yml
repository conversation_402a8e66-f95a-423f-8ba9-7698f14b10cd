---
- name: Provision and deployment playbook for job servers
  hosts: job_servers
  become: yes
  roles:
    - role: docker
      docker_users: ['ubuntu', '{{ app_user }}']
      tags: ['docker']
      when: development_provider|default() != "docker"

    - role: glynt_api
      configure_job_server: yes
      tags: ['glynt_api']

    - role: cloudwatch-agent
      tags: ['cloudwatch_agent']
      aws_cwa_region: "{{ aws_default_region }}"
      aws_cwa_namespace: glynt-api-{{ environment_name }}
      aws_cwa_cfgs: group_files/awscwa_cfg/job_servers
      aws_cwa_logfiles:
        - file_path: /var/log/chrony/statistics.log
          log_group_name: /glynt/api/{{ environment_name }}/job_servers/chrony
          log_group_tags:
            - environment: "{{ environment_name }}"
            - server_type: "job_server"
          log_stream_name: "{instance_id}"
          log_retention: "{{ log_retention_in_days }}"
          timestamp_format: "%Y-%m-%d %H:%M:%S"
          timezone: UTC
        - file_path: /var/log/chrony/measurements.log
          log_group_name: /glynt/api/{{ environment_name }}/job_servers/chrony
          log_group_tags:
            - environment: "{{ environment_name }}"
            - server_type: "job_server"
          log_stream_name: "{instance_id}"
          log_retention: "{{ log_retention_in_days }}"
          timestamp_format: "%Y-%m-%d %H:%M:%S"
          timezone: UTC
        - file_path: /var/log/chrony/tracking.log
          log_group_name: /glynt/api/{{ environment_name }}/job_servers/chrony
          log_group_tags:
            - environment: "{{ environment_name }}"
            - server_type: "job_server"
          log_stream_name: "{instance_id}"
          log_retention: "{{ log_retention_in_days }}"
          timestamp_format: "%Y-%m-%d %H:%M:%S"
          timezone: UTC
        - file_path: /var/log/celery/celery_transformer.log
          log_group_name: /glynt/api/{{ environment_name }}/job_servers/celery
          log_group_tags:
            - environment: "{{ environment_name }}"
            - server_type: "job_server"
          log_stream_name: "transformer-{instance_id}"
          log_retention: "{{ log_retention_in_days }}"
          timestamp_format: "[%Y-%m-%d %H:%M:%S"
          timezone: UTC
        - file_path: /var/log/celery/celery_miscellaneous-io.log
          log_group_name: /glynt/api/{{ environment_name }}/job_servers/celery
          log_group_tags:
            - environment: "{{ environment_name }}"
            - server_type: "job_server"
          log_stream_name: "miscellaneous-io-{instance_id}"
          log_retention: "{{ log_retention_in_days }}"
          timestamp_format: "[%Y-%m-%d %H:%M:%S"
          timezone: UTC
        - file_path: /var/log/celery/celery_miscellaneous-cpu.log
          log_group_name: /glynt/api/{{ environment_name }}/job_servers/celery
          log_group_tags:
            - environment: "{{ environment_name }}"
            - server_type: "job_server"
          log_stream_name: "miscellaneous-cpu-{instance_id}"
          log_retention: "{{ log_retention_in_days }}"
          timestamp_format: "[%Y-%m-%d %H:%M:%S"
          timezone: UTC
        - file_path: /var/log/celery/celery_asyncio.log
          log_group_name: /glynt/api/{{ environment_name }}/job_servers/celery
          log_group_tags:
            - environment: "{{ environment_name }}"
            - server_type: "job_server"
          log_stream_name: "asyncio-{instance_id}"
          log_retention: "{{ log_retention_in_days }}"
          timestamp_format: "[%Y-%m-%d %H:%M:%S"
          timezone: UTC
        - file_path: /var/log/celery/celery_classify.log
          log_group_name: /glynt/api/{{ environment_name }}/job_servers/celery
          log_group_tags:
            - environment: "{{ environment_name }}"
            - server_type: "job_server"
          log_stream_name: "classify-{instance_id}"
          log_retention: "{{ log_retention_in_days }}"
          timestamp_format: "[%Y-%m-%d %H:%M:%S"
          timezone: UTC
        - file_path: /var/log/celery/celery_email.log
          log_group_name: /glynt/api/{{ environment_name }}/job_servers/celery
          log_group_tags:
            - environment: "{{ environment_name }}"
            - server_type: "job_server"
          log_stream_name: "email-{instance_id}"
          log_retention: "{{ log_retention_in_days }}"
          timestamp_format: "[%Y-%m-%d %H:%M:%S"
          timezone: UTC
        - file_path: /var/log/celery/celery_core_extract.log
          log_group_name: /glynt/api/{{ environment_name }}/job_servers/celery
          log_group_tags:
            - environment: "{{ environment_name }}"
            - server_type: "job_server"
          log_stream_name: "glynt_core_extract-{instance_id}"
          log_retention: "{{ log_retention_in_days }}"
          timestamp_format: "[%Y-%m-%d %H:%M:%S"
          timezone: UTC
        - file_path: /var/log/celery/celery_core_extract_finish.log
          log_group_name: /glynt/api/{{ environment_name }}/job_servers/celery
          log_group_tags:
            - environment: "{{ environment_name }}"
            - server_type: "job_server"
          log_stream_name: "glynt_core_extract_finish-{instance_id}"
          log_retention: "{{ log_retention_in_days }}"
          timestamp_format: "[%Y-%m-%d %H:%M:%S"
          timezone: UTC
        - file_path: /var/log/celery/celery_core_train.log
          log_group_name: /glynt/api/{{ environment_name }}/job_servers/celery
          log_group_tags:
            - environment: "{{ environment_name }}"
            - server_type: "job_server"
          log_stream_name: "glynt_core_train-{instance_id}"
          log_retention: "{{ log_retention_in_days }}"
          timestamp_format: "[%Y-%m-%d %H:%M:%S"
          timezone: UTC
        - file_path: /var/log/celery/celery_pages.log
          log_group_name: /glynt/api/{{ environment_name }}/job_servers/celery
          log_group_tags:
            - environment: "{{ environment_name }}"
            - server_type: "job_server"
          log_stream_name: "pages-{instance_id}"
          log_retention: "{{ log_retention_in_days }}"
          timestamp_format: "[%Y-%m-%d %H:%M:%S"
          timezone: UTC
        - file_path: /var/log/celery/celery_parse.log
          log_group_name: /glynt/api/{{ environment_name }}/job_servers/celery
          log_group_tags:
            - environment: "{{ environment_name }}"
            - server_type: "job_server"
          log_stream_name: "parse-{instance_id}"
          log_retention: "{{ log_retention_in_days }}"
          timestamp_format: "[%Y-%m-%d %H:%M:%S"
          timezone: UTC
        - file_path: /var/log/celery/celery_storage.log
          log_group_name: /glynt/api/{{ environment_name }}/job_servers/celery
          log_group_tags:
            - environment: "{{ environment_name }}"
            - server_type: "job_server"
          log_stream_name: "storage-{instance_id}"
          log_retention: "{{ log_retention_in_days }}"
          timestamp_format: "[%Y-%m-%d %H:%M:%S"
          timezone: UTC
        - file_path: /var/log/celery/celery_packager.log
          log_group_name: /glynt/api/{{ environment_name }}/job_servers/celery
          log_group_tags:
          - environment: "{{ environment_name }}"
          - server_type: "job_server"
          log_stream_name: "packager-{instance_id}"
          log_retention: "{{ log_retention_in_days }}"
          timestamp_format: "[%Y-%m-%d %H:%M:%S"
          timezone: UTC
      when: environment_name != "development"
