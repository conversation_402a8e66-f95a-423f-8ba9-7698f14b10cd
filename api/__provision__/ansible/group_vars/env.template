# Template for Glynt API group vars. Make a copy and save it to <env>/vars.yml.
# Make sure the environment name matches the name used in the corresponding
# inventory groups file. Fill out for new environment and encrypt using the
# ansible-var-vault tool. Be sure to store db credentials in a
# LastPass shared folder.

variables_to_encrypt:
          - deployment_private_key
          - app_secret_key
          - app_auth0_client_secret
          - app_db_password
          - rabbitmq_user_password
          - ssl_server_pem
          - ssl_server_key
          - flower_oauth2_secret
          - redis_password


environment_name:

deployment_public_key:  # the git ssh public key used to pull glynt repo. Must be installed in github by an admin in the Deploy Keys section of the github console and should be stored in LastPass
deployment_private_key:  # the git ssh private key portion

app_git_branch:  # the desired git branch this environment will be deployed from (i.e., deploy/production)
app_glynt_api_domain:  # domain name of api (i.e., api.glynt.ai)
app_secret_key:  # key used by django for internal crypto. Can be generated using django.core.management.utils.get_random_secret_key.

app_db_password:  #  min 40 chars, create and store in LastPass, avoid special chars, particularly @
app_db_readonly_password:  # password for the readonly user, 40 char min, create and store in LastPass


# Auth0
# Follow instructions in the main README to set up a new Auth0 tenant which
# will generate the following vars
app_auth0_api_identifier:
app_auth0_client_id:
app_auth0_client_secret:
app_auth0_domain:


# ui

glynt_ui_url:  # example, "https://ui.glynt.ai"


# redis

redis_password:  # min 50 char, avoid special chars, particularly @


# Rabbit MQ

rabbitmq_user_password:  # min 50 char, avoid special chars, particularly @


# Flower
# Follow instructions in the main README to set up flower github oauth2 app
flower_server_name:  # the flower domain name registered in DNS
flower_oauth2_key:
flower_oauth2_secret:


# Slack

slack_monitoring_channel:  # slack channel the api will use to send alerts
slack_customer_metrics_channel: # slack channel the api will use to send metrics reports

# UI Path

uipath_base_url: # base api url for uipath 
uipath_auth_url: # url for uipath authentication

# SSL
# SSL cert and key or *.glynt.ai.  Can be found in LastPass shared folder or
# copied from another environment group vars
ssl_server_pem:
ssl_server_key:
