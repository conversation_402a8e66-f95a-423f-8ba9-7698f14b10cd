variables_to_encrypt:
          - vault_confirmation
          - sendgrid_api_key
          - slack_token
          - rabbitmq_api_user_password
          - mapquest_api_consumer_key

ansible_python_interpreter: "/usr/bin/python3"

aws_access_key_id: "{{ lookup('env', 'A<PERSON>_ACCESS_KEY_ID') }}"
aws_secret_access_key: "{{ lookup('env', 'AWS_SECRET_ACCESS_KEY') }}"

glynt_pypi_aws_account: "************"
glynt_pypi_aws_region: "us-west-2"

redis_bind_addresses: "0.0.0.0"

# TODO: delete when finished with transition to AWS-managed redis
redis_uri: "redis://:{{ redis_password }}@{{ queue_server_host }}:6379"

vault_confirmation: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          33346333373132646363363430366635373435623863366337376235343265663863393738313331
          3763333766373232383138636338383066383939333733650a616239656661623666653738653232
          63303763366331363162393663666562386662663963316633386330333235326664393235663932
          6133373665393564300a316234623264386362323065666333633338323835356335373935346433
          33363032613866363838393330623865393739353062643564306134383963626337

app_user: "glynt_api"

sendgrid_api_key: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          65393865333231303961343161303939626232366363656265333937663432333762333636313736
          3531343830373861633934343061353532343639636461660a636265656662353562643136303931
          39303336303666663432366536343734313865323132343939613037633936316463643138383064
          3234633037363232650a373936346164373164353033353034343961653864333030646565313331
          30386531393665343266343261343232326637343861303262663562353632633334396632656435
          33393537636164656136626566386431333032356230373239633339353861346661353866343461
          33663065633139363037616536323830323731356139626163643034336265343437363331343339
          33643737343662306433

slack_token: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          35303164386136636132363135666561306662636563616365356466613034633235643365366630
          3663643635623536666438306535383738393431623132320a343439663364353763323762353533
          32376531373237353638373562336430343732313463303730343931376433636265313864363961
          3063376661326137630a663539646331383962363434646231386563633533316263383736353138
          38666361343835323131346131313039633039353232613237313166343535626466343662306364
          39636139333636363833626531376665663336656533656133313530646633653938323634396465
          383231356230393664656133373738363130

rabbitmq_api_user_name: "rabbitmq_mgmt"

rabbitmq_api_user_password: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          32333636343832663465643866656231366462613439363063393934373666616636393837643534
          3235383030333737323931666334616236343636626465300a643237376234313038393038343434
          63663131626532353733336232613765306462363631363330663365623064663438353534323161
          3536346532666337610a646638313636616461633931333735393931336332646636386336656365
          30653961623139376237636361323863653134366163323065613438376434306632393364643139
          62636261346263663235386466343039663430366262306432393364613131636365613939343433
          373361666235393561666230393836366534

log_retention_in_days: 120

mapquest_api_consumer_key: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          62653561386132333135663333656665363263623037633632346633663036356462336238646133
          3166326630303666393738303733376163623437373532380a353736336561656366393431363661
          38306662633464386364306163366638316664366532376138616530623561623132363331393833
          3135636331336436620a633836363362343636623564326161383839326165636132333564666437
          66326135396138663765333736396137336563303733333336303363656237336335656136643538
          3131326439666332663964346636373630303862343933626165
