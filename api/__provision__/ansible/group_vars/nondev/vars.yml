---
app_server_ips: "{{ groups['app_servers'] | map('extract', hostvars, 'private_ip_address') | list }}"
queue_server_ips: "{{ groups['queue_servers'] | map('extract', hostvars, 'private_ip_address') | list }}"
job_server_ips: "{{ groups['job_servers'] | map('extract', hostvars, 'private_ip_address') | list }}"
flower_server_ips: "{{ groups['flower_servers'] | map('extract', hostvars, 'private_ip_address') | list }}"

aws_default_region: "{{ lookup('env','AWS_REGION') }}"
aws_cwa_key_access: "{{ aws_access_key_id }}"
aws_cwa_key_secret: "{{ aws_secret_access_key }}"
# The access key which grants access to the glynt.shared bucket on bacalar,
# which contains common libs and such.
aws_shared_access_key_id: "{{ lookup('env', 'AWS_SHARED_ACCESS_KEY_ID') }}"
aws_shared_secret_access_key: "{{ lookup('env', 'AWS_SHARED_SECRET_ACCESS_KEY') }}"

app_allowed_hosts: "{{ app_glynt_api_domain }}"
app_db_region: "{{ aws_default_region }}"
app_db_root_username: "{{ lookup('env','DB_ROOT_USERNAME') }}"
app_db_root_password: "{{ lookup('env','DB_ROOT_PASSWORD') }}"
app_db_name: "glynt_api"
app_db_username: "glynt_api"
app_file_storage_region: "{{ aws_default_region }}"
app_secrets_directory: "{{ app_directory }}/secrets"

rabbitmq_user_name: "glynt_api"

queue_server_host: "{{ queue_server_ips[0] }}"

# flower requires celery; for Glynt, this is a job or application server
flower_host: "{{ flower_server_ips[0] }}"
flower_host_private_url: "http://{{ flower_host }}:{{ flower_port }}"
flower_oauth2_redirect_url: "http://{{ flower_server_name }}/login"
# To allow users to access flower, add their github email here
flower_authorized_emails:
  - "<EMAIL>"
  - "<EMAIL>"
  - "<EMAIL>"
  - "<EMAIL>"
  - "<EMAIL>"
  - "<EMAIL>"
  - "<EMAIL>"
  - "<EMAIL>"

celery_broker_uri: "pyamqp://{{ rabbitmq_user_name }}:{{ rabbitmq_user_password }}@{{ queue_server_host }}//"

# the following vars are highly tunable and change in response to many
# different conditions including our max concurrency settings per job type, the
# worker pool configuration (gevent vs prefork), and the specific tasks
# involved in each job type. Consider carefully. 
celery_worker_concurrency_parse: 8  # polling worker; use moderate capacity
celery_worker_concurrency_pages: 4  # pages are a higher cpu task (invoking heavy libraries) but are snappy and low demand
celery_worker_concurrency_glynt_core_train: 20  # TODO: evaluate lower number for this polling worker
celery_worker_concurrency_glynt_core_extract: 4
celery_worker_concurrency_glynt_core_extract_finish: 4
celery_worker_concurrency_miscellaneous_io: 20  # the majority of these tasks are simple I/O, mostly to publish cloudwatch metrics
celery_worker_concurrency_miscellaneous_cpu: 4
celery_worker_concurrency_storage: 10
celery_worker_concurrency_classify: 10
celery_worker_concurrency_transform: 10

ecs_environment: "{{ environment_name }}"

boxcars_api_url: "http://boxcars-app-server.{{ environment_name }}.private.glynt.ai/api/v1"

core_api_url: "http://{{ core_api_host }}.{{ environment_name }}.private.glynt.ai/api/v1"

extra_ssh_keys:
          - ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQDMiMZ249wqxirUhIrariGQswRYbKr0CxfsnZSXpOCDAMxV+NAr223CAvOlMAUQ1u9rgZTBRKJRC1VfCccXySHDkW7WWVEbcGhTdOtGSare34KIK8EQXHtXrFsi3/HMF2hCjVczS+VjDzhqGUiC7zeJSnbBXBcVmP8DQypPQaHTtpt7sicZGbADi/ddgxvdkFSQByIA7+hF4BcvTsmi7jG9XBpTeVV651eS/BZTFc5hXRQDEcciG8TR+Nr6NBzGu3dr+xKTL7Ex8QTiQli1a8BmKCbWcgdno5kjUC4M6LRJW526jybk5eArJzCV8JOSPYWeCTlvDX2IR1wqDn/rLjRskwVOlYU8e76/7wLLYbcA+doD8KM74Jrhvla5XGOKdnE85QeggZrt9rKWFn+iSNuWyWSF73bA2iAmxateqGvCNjT//yP/YWjle3ZKDa7alwLHypTA4G39EYcdHBqvg91DDn6aRuhPbVcX2LQD7hZ8OW4XjJuBFWrrQaDxQghI3nc= <EMAIL>
          - ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAACAQDsLfTg0WXJjWP/4n5PoFxB94FnK5A8X7ux+IXD6FlSW9vLfF4R2bSC0kALS08Bl15aGtp4fJwVfcIec67/K8JqJ/0zkKrJwzodf5/LvPv2RR8njLmC2pfvckpQI4Rej7wLvC+pze7BYLJCganOAvTBwBq3tURonuJNtBn1HKpZAvYjswBniRMVoe6SSvEehV7fHlFL9pceYbTP8ZuVGt3+Ed570UU310Glbd8dmQS1boVmJCqHn7OjPsyQmR79igTFX+mGEYQE8whF9x40ddRdcUGlyND2Sz/6pU3KJscenLaiTlxM1Q27Brgn8OQCEajbnDnmPKICpJXGtzco7meviukvI+2HZ5XuxA2eA9wmkKN1jzT2VcuZ/TWhArajyKxOAyMcYuG85/23pZgcpQSo7AHOdt4P8hwc+rr8HqU85Y4260ekl4olk3zyIYGrDn1J5BGg14TlyL5gZe3sb29wBWpM+J/XSL61hLs1p9pt+rB8rSxjBkii/SdG65MSMBS9xgCpMSh+yyz6qSL4O7TkEB25qF8JkioiRfEuwvsD7bC2quvpBdVeDlj74/9IDZDZnx4RxsZzePnym5hIXkWiUoETKTRFd4ATiMbxzncbgDxzn+R1J7wyJxJtK5ecYwrBb53lQ+pT8B7xryJrdlZbYaLdkYH0kxzfuRLufEygdw== <EMAIL>
          - ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAACAQDWIKe6nDnMLsPooWUdrCRCVDqbb0laD5pGRtTbyBRF2vNyHa4Gzz+89EIX/WwDIIYhvcgLC1WZ1p1NzyoeqDS+RtDIieQyaLiR3exz9qVksakCO8ePfJFtHRDl5sGMSJQ0t002UG47Bbk9EIhWtAN9mC4oICERtZPLN4G2syP6wsaQy5KsKIC70UkVuj7ERYchictVW2qphF4IXN7FITpN+OxlrtyozXTNQcnKCqgDTnMlP/9S4CYCsfW746JDQ81dKLCgxFlnEoLkK0xCmuWUQaL+ocN+dSXAZfvFyHYYhTXNt4bSOQj+zMZ5zOYZz17SM/8G3IcSBjLIS9w0JveG9c43Yb89pWVRjGLLl4eZ56pL3cCmO36oiI0HlVoKNfNANSZf5CpyaENaOOWJXdNqPmML/DLEwPxld2BVZmo7lhPh8IfuifPAHGTR20PnxUiDV1P2lwtxus2I2LA2STVeKfp3bgdmtVYMDgIJhDvqy/C1xyDol0j69HIrcXAkzt2lS20+hWnlUv0a91oZUO/iegOg8VN9MTYTAbU5yTW1CH6f6xEamw8brK2e/pM6eKY8Rvn1gXvVM66zhMrJdaGhC/lWTB2mNCpDAJlU+r3eDD4nnIpWJgmeV4EoQdf8C2v/8Olw3rsBDwCvs+CLPE62Yws9N34NWjljCeWLpq6iQQ== <EMAIL>
          - ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQC8l1RxC5Q6Q6RZtkP+TRs+aq/lfIRqtqfWAVy00BLwWSo/sZzyji9EShNxXUn6Zu0pmfNsvhN8Y71ZQDJ3FKMWW0dWys+WBYURdlSCn4QCsS9xTPtfbMKiDXZ+lS1B8XAIdgy0jbw818h2a3kjq2LpYfqKBtmw1A7GrTAeGbDwD4bgWjTBE2jKOezIskZsOJ3tzYbE2oYS7ATLL7bOxa5bp5DYehDu1BBTd6KQqydA80ZjQKgqTo76kHHslUQWOedgslL8vSfM4FFEg2k6DTS9jvfyjbsUjU4/5QaD8sDvxYK8GXJGMZQAm9iqzwjOnVXvMkQazyaiSeyi/JOQ+v0YC51H7zcKnlMxKFzAj4g7NUiE6jdiIitEvbouwbVhygr3VcFAFI6dNJU1lnP8WHpBrhW30umnGULFWposcOXduYYog0WBz+EqQWY72cAv2lhb0P/fq6B/Z3qT8YPhC24VJD4PrxOLsPF2B2T1Ngyzw+bMiT3R6k2BxatOK7MOKm0= <EMAIL>
          - ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAACAQDAmJ4gR8pkZWSN3eiUO8BQx5SQ448tMsZ7v1hA1WgPP67W9TEIkMNkFq+FsU79hr1KA0u6wuJEbI0DdbeIy0TR84w1rJ4M1qLjvKTx5xndFGCTPj5sicqLge+xvUfPuLrnmzUcW3ZmeG+t/XtsOgH6HFWWL1ykPBRTcfpR/IzhyLmh/ZNJLzp7/vxBinz8OilEJwIrRgjdD6SAfH0N0zkz7DwrHJrw/OI/0xau7JPAsZwZnDkP8LPL86xR6J5FW6mVXZ9xOGT6nz/qsRbczoNxCIbfY3nz92tVhz2NEorF3M5ymh/arSfCkoWD1WzruWV7wEGlIDFpRCZ+sjq6A9ozgwIiDGAZ24ZjS3vLHT1NeSwjcLrCuRWLm72yJRo46St7E1La9ncBGnLdoiO6GpQnPJHFcXmqJmFrZyChATH/oYcoNxFCRXY/XZuHpBY3PHI2tkapcTq1wiIBWj4Da+ug95PUUSyqg05efeLRxRDoMTbAJrwwggpaJknCO67fpzy6BmOg81x98MKlgAMyT+0WHpEHwYhlVZdomOhOf8hXjrUARWuB5sOD/57cFPZ1igBGdzc/jo73KFPFo/T8NQyGpx/yPksAMPQ0vr9UMoFPvp3Nw23fPCKXnXTHM2I1CVb6pBHXvICcXd6QoPToVZtXJnhYyfbqAyD8TKFg+9B9KQ== <EMAIL>
          - ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIBuXzfMB+OOo+jcMmHhms8LCN6ZZvHift23aQplGx8w1 <EMAIL>
          - ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIDWtjJS5EzMjwczq5VAx3A1F836kczJ+uz7T7WWNoLcP <EMAIL>
          - ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIMgN1JZN1NNdhsX+gbARb777g45kJmasUCrSSZvAq67L <EMAIL>

stale_ssh_keys:
          - ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIEVaEtJ0+6F/zkCOBr3Y1BbUP9jZfsC1Q/gkmPiyMvHx <EMAIL>
          - ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIF8jSQ+uGMzJMUUlPXBCjoEy/JsU/71ETpeDVxjO9ZhS <EMAIL>
