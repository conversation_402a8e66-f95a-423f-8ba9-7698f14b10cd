variables_to_encrypt:
          - aws_shared_secret_access_key

# Global

environment_name: "development"
appserver_ips:
          - **************
queueserver_ips:
          - **************
jobserver_ips:
          - **************

queue_server_host: "{{ queueserver_ips[0] }}"
redis_host: "{{ queue_server_host }}"

# App

upgrade_apt_packages: false

aws_shared_access_key_id: "AKIA2GAHXENHTIP7Q3E7"
aws_shared_secret_access_key: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          33373632343539336331333934376431396238393833303137336164633762366138366432386437
          3031346161333431666433663437626639363535386631340a313766346366376439653939346565
          ********************************************************************************
          ********************************************************************************
          ********************************************************************************
          3931363264353464636132356163363332313266346662313736

# this base_url matches the Vagrantfile's configuration
app_base_url: "http://localhost:8080"
app_glynt_api_domain: "localhost"
app_allowed_hosts: "localhost,**************"

app_secret_key: "kfinb1dt3cCVzOhA6zeuvQ1Q5eXi5K5A6pAZWRoPibMZTXIwlhmjNCDIvW2SH"
app_secrets_directory: "{{ app_directory }}/secrets"

app_storage_bucket_prefix: "glynt.api.development.rb"

app_user_db_password: "glynt_api"
app_db_name: "{{ app_user }}"
app_db_uri: "mysql://{{ app_user }}:{{ app_user_db_password }}@localhost:3306/{{ app_db_name }}"


# redis

job_server_ips:
          - 127.0.0.1
redis_password: "pass"

app_gunicorn_workers: 2

# auth0

app_auth0_api_identifier: "glynt-public-api-dev"
app_auth0_client_id: "3wJNSkQg7dW0y1oTX4XTJVlKwCBsVZnV"
app_auth0_client_secret: "****************************************************************"
app_auth0_domain: "glynt-dev.auth0.com"


# boxcars

boxcars_api_url: "http://*************/api/v1"


# core

core_api_url: "http://*************/api/v1"


# ui

glynt_ui_url: "http://ui-dev.dummy.ai"


# mysql

mysql_root_password: root_glynt_api
mysql_databases:
  - name: glynt_api
mysql_users:
  - name: "{{ app_user }}"
    password: "{{ app_user_db_password }}"
    host: "%"
    priv: "*.*:ALL"
  - name: "{{ app_user }}_readonly"
    password: "{{ app_user_db_password }}"
    host: "%"
    priv: "{{ app_db_name }}.*:SELECT"


# RabbitMQ

rabbitmq_user_name: "glynt_api"
rabbitmq_user_password: "glynt_api"


# Celery

celery_broker_uri: "pyamqp://{{ rabbitmq_user_name }}:{{ rabbitmq_user_password }}@{{ queue_server_host }}//"
celery_broker_base_url: "pyamqp://{{ queue_server_host }}//"

celery_worker_concurrency_parse: 1
celery_worker_concurrency_pages: 1
celery_worker_concurrency_glynt_core_train: 1
celery_worker_concurrency_glynt_core_extract: 1
celery_worker_concurrency_glynt_core_extract_finish: 1
celery_worker_concurrency_miscellaneous_io: 10
celery_worker_concurrency_miscellaneous_cpu: 3
celery_worker_concurrency_storage: 100
celery_worker_concurrency_classify: 1000
celery_worker_concurrency_transform: 1


# documentation settings
build_docs: "{{ lookup('ansible.builtin.env', 'BUILD_DOCS', default='false') }}"


# Slack

slack_monitoring_channel: "dev_monitoring_channel"
slack_customer_metrics_channel: "dev_customer_metrics"

# UI Path

uipath_base_url: "https://cloud.uipath.com/glynt/Sandbox"
uipath_auth_url: "https://account.uipath.com/oauth/token"


# SSL

ssl_server_pem: |
          -----BEGIN CERTIFICATE-----
          MIIEHDCCAgQCAQIwDQYJKoZIhvcNAQELBQAwgZUxCzAJBgNVBAYTAlVTMRMwEQYD
          VQQIDApDYWxpZm9ybmlhMRYwFAYDVQQHDA1TYW4gRnJhbmNpc2NvMRAwDgYDVQQK
          DAdXYXR0em9uMQ0wCwYDVQQLDARMaW5rMRcwFQYDVQQDDA5sb2NhbGhvc3Q6OTQ0
          MzEfMB0GCSqGSIb3DQEJARYQbGlua0B3YXR0em9uLmNvbTAeFw0xNjAzMjExOTA0
          MzNaFw0xOTAzMjExOTA0MzNaMIGVMQswCQYDVQQGEwJVUzETMBEGA1UECAwKQ2Fs
          aWZvcm5pYTEWMBQGA1UEBwwNU2FuIEZyYW5jaXNjbzEQMA4GA1UECgwHV2F0dHpv
          bjENMAsGA1UECwwETGluazEXMBUGA1UEAwwObG9jYWxob3N0Ojk0NDMxHzAdBgkq
          hkiG9w0BCQEWEGxpbmtAd2F0dHpvbi5jb20wgZ8wDQYJKoZIhvcNAQEBBQADgY0A
          MIGJAoGBAKkYyrOpZBd3DKAs9NSai6oWIXkRwWGKMpouArtALpldvBOGBfTxkBmi
          fBvNOY4mp6rJk59t2SbHQH7ihJBKH1kMw8I/r/+tIhBeSEVw0lkzjNFoma8mhay+
          VjMsV3BTBnhTKo2z+bxiuJlPjlXUCiOmMhLLUQc+Le9pXv7a/N89AgMBAAEwDQYJ
          KoZIhvcNAQELBQADggIBAE++4c954iBVeboYESI9WrIBO8vUYWvmfc1vnuXOvGGc
          /av2pYTWhINptIgu+nkKMG96c0ad1sAqXuOKFg8x/k7LWBJaW5wAB7wYtBEgcXhm
          4EilyNKxYWdT/oKwyfD0tSV4gyYdbuGNj0krm2Lm5jpnpiOkSyQRdAGURG9Os+nr
          1bYb9tsPI1fhFos2VLzSKE39muOlQKFy5ceu8oZ8W6t8wqrxBI+q2zc/Bc7Wi7tj
          MWvMBlVw9BPBVcvFxE1hF6785rk50h2SUAV3zaT8IWMX34ZhPE3zWTmIruhWbRkN
          M0cmSCPxbLnmyrko7VpbzWXIrpW8ixOV5p4EdPTfdkNEKPWmMQH5t2BY1FJ74DWX
          lemaPDdPIdn/0S2hvBag3wyxrrd8v4mmQ9ubtfF4AQsADdhGwSyj7GU2mrvz0g0d
          cFvwd2av2VRUEJrKjdeaEAQEZJo9Jv58kh/j9Z3P/bM9EV2U09x/Rcs+Kc5iCeUE
          LBZ5EPGhXzuOenXl7uWBjHQfrAxJUZVZTIyEp0Sx6LrTc7vkGurTGD6MlCpjQaoV
          t6T8JD/vXTndJn5yJKNAaUMp7p85tEFJXfGBKi1+nZvKT38NoFhLweWPVajNS4WB
          VLDiMGDABhJ5UfNyZhB3xZ2I9Vafc69gRbce9Nk95PPeLuFuCILK+TMLXxcLJQuF
          -----END CERTIFICATE-----
ssl_server_key: |
****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

