---
app_gunicorn_max_requests: 0
app_gunicorn_max_requests_jitter: 0

app_git_repo: **************:PaceWebApp/glynt-api.git
app_git_branch: master
app_git_directory: /home/<USER>/app

# flags for job/app server specific tasks
configure_web: no
configure_job_server: no

# Celery workers
# NOTE: This configuration is a list of dictionaries, which is tailored to
# run a full set of the workers on each job server in the Ansible inventory.
#
# This will be changed to autoscaling ECS services so that the number of
# each worker can expand upon demand. At that point, the number of workers
# will only be a starting point (and this configuration file will not be
# used at all). Additionally, we may want to reduce the number of queues.

celery_worker_config:
  - { name: 'transformer',
      queues: 'glynt_api_transformer',
      template: 'generic_worker.service.j2',
      num_workers: "{{ celery_worker_concurrency_transform }}",
      pool_type: 'prefork',
      prefetch: '1',
    }
  - { name: 'parse',
      queues: 'glynt_api_parse',
      template: 'generic_worker.service.j2',
      num_workers: "{{ celery_worker_concurrency_parse }}",
      pool_type: 'prefork',
      prefetch: '1',
      loglevel: 'INFO',
      timeout_stop: '300',
    }
  - { name: 'pages',
      queues: 'glynt_api_pages',
      template: 'generic_worker.service.j2',
      num_workers: "{{ celery_worker_concurrency_pages }}",
      pool_type: 'prefork',
      prefetch: '1',
      timeout_stop: '300',
    }
  - { name: 'core_train',
      queues: 'glynt_api_train',
      template: 'generic_worker.service.j2',
      num_workers: "{{ celery_worker_concurrency_glynt_core_train }}",
      pool_type: 'gevent',
      prefetch: '4',
      timeout_stop: '300',
    }
  - { name: 'core_extract',
      queues: 'glynt_api_extract',
      template: 'generic_worker.service.j2',
      num_workers: "{{ celery_worker_concurrency_glynt_core_extract }}",
      pool_type: 'prefork',
      prefetch: '1',
      loglevel: 'INFO',
      timeout_stop: '300',
    }
  - { name: 'core_extract_finish',
      queues: 'glynt_api_extract_finish',
      template: 'generic_worker.service.j2',
      num_workers: "{{ celery_worker_concurrency_glynt_core_extract_finish }}",
      pool_type: 'prefork',
      prefetch: '1',
      timeout_stop: '300',
    }
  - { name: 'email',
      queues: 'glynt_api_email',
      template: 'generic_worker.service.j2',
      num_workers: "1",
      pool_type: 'prefork',
      prefetch: '1',
      timeout_stop: '60',
    }
  - { name: 'miscellaneous-io',
      queues: 'glynt_api_miscellaneous_io',
      template: 'generic_worker.service.j2',
      num_workers: "{{ celery_worker_concurrency_miscellaneous_io }}",
      pool_type: 'gevent',
      prefetch: '4',
      timeout_stop: '300',
    }
  - { name: 'miscellaneous-cpu',
      queues: 'glynt_api_miscellaneous_cpu',
      template: 'generic_worker.service.j2',
      num_workers: "{{ celery_worker_concurrency_miscellaneous_cpu }}",
      pool_type: 'prefork',
      prefetch: '1',
      timeout_stop: '300',
    }
  - { name: 'storage',
      queues: 'glynt_api_storage',
      template: 'generic_worker.service.j2',
      num_workers: "{{ celery_worker_concurrency_storage }}",
      pool_type: 'gevent',
      prefetch: '1',
      timeout_stop: '10',
    }
  - { name: 'classify',
      queues: 'glynt_api_classify',
      template: 'generic_worker.service.j2',
      num_workers: "{{ celery_worker_concurrency_classify }}",
      pool_type: 'gevent',
      prefetch: '1',
      timeout_stop: '10',
    }
  - { name: 'packager',
      queues: 'glynt_api_packager',
      template: 'generic_worker.service.j2',
      num_workers: "1",
      pool_type: 'prefork',
      prefetch: '1',
      loglevel: 'INFO',
      timeout_stop: '300',
  }

app_install_directory: '{{ app_git_directory }}/api'
app_opt_directory: '/opt/glynt_api'
app_virtualenv_path: "{{ app_opt_directory }}/venv"
poetry_virtualenv_path: '/opt/poetry_venv'
# root django app directory (where manage.py is)
app_directory: '{{ app_install_directory }}/src'
app_docs_directory: '{{ app_install_directory }}/docs'
app_build_docker_directory: '{{ app_git_directory }}/common/dockerized_GS'

app_secrets_directory: '{{ app_directory }}/secrets'
app_log_dir: "/var/log/glynt_api"
app_log_file: "{{ app_log_dir }}/glynt_api.log"
app_error_log_file: "{{ app_log_dir }}/glynt_api.error.log"
app_silk_profiles_dir: "/opt/silky/silk_profiles"

app_ghostscript_path: /usr/bin/gs-9533-linux-x86_64
docker_ghostscript_path: '{{ app_build_docker_directory }}/gs-9533-linux-x86_64'

app_db_rds_global_bundle: '{{ app_install_directory }}/global-bundle.pem'
app_db_instance_name: 'glynt-shared-{{ environment_name }}'
app_db_uri: '{{ app_db_engine }}://{{ app_db_username }}:{{ app_db_password }}@{{ app_db_ip }}:{{ app_db_port }}/{{ app_db_name }}?ssl_ca={{ app_db_rds_global_bundle }}&ssl=REQUIRED'

# Directory where slate package resides (for building docs)
app_local_slate_dir: ../../../slate
# Directory where the slate package will be copied to on the remote machines
app_remote_slate_dir: /usr/local/lib

celery_log_dir: "/var/log/celery"
celery_broker_api_url: "http://{{ rabbitmq_api_user_name }}:{{ rabbitmq_api_user_password }}@{{ queue_server_host }}:15672/api/"
rabbitmq_management_url: "http://{{ queue_server_host }}:15672"

flower_version: 1.2.0
flower_user: "{{ app_user }}"
flower_app_name: glynt_api
flower_port: 5555

poetry_version: 1.5.1
