---
- name: restart glynt_api
  systemd: name=glynt_api state=restarted
  listen: "restart app services"
  when: configure_web

- name: restart celery task units
  service: name="worker_{{ item.name }}" state=restarted
  listen: "restart app services"
  loop: "{{ celery_worker_config }}"
  when: configure_job_server

- name: restart celerybeat
  systemd:
    name: 'celerybeat.service'
    state: restarted
  listen: "restart app services"
  when: configure_job_server and environment_name != 'development' and tags['celerybeat_server'] == "true"
