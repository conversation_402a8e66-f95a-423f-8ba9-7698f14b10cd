---
- name: install nginx
  apt:
    name: nginx=1.1*
    state: present
    update_cache: yes

- name: configure nginx
  template:
    src: nginx.conf.j2
    dest: /etc/nginx/nginx.conf

# NOTE: This template fixes a problem with some ngnix launchpad PPA releases:
# https://bugs.launchpad.net/nginx/+bug/1450770
# It should not be necessary with a distribution-supplied nginx, but
# we'll keep it here if it becomes necessary again.
#
# - name: configure logrotation fix
#   template:
#     src: templates/nginx_logrotation.j2
#     dest: /etc/logrotate.d/nginx
#     owner: root
#     group: root
#     mode: 0644

# see https://glynt.atlassian.net/browse/GL-3723 on why this isn't a handler
- name: ensure nginx is enabled and started
  systemd:
    name: 'nginx.service'
    enabled: yes
    state: started

- name: restart nginx
  systemd:
    name: 'nginx.service'
    state: restarted

