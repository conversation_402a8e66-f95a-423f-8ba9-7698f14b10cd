---
- name: register db uri
  rds:
    command: facts
    aws_access_key: "{{ lookup('env','AWS_ACCESS_KEY_ID') }}"
    aws_secret_key: "{{ lookup('env','AWS_SECRET_ACCESS_KEY') }}" 
    region: '{{ app_db_region }}'
    instance_name: '{{ app_db_instance_name }}'
  register: db_facts

- name: set database facts
  set_fact:
    app_db_ip: '{{ db_facts["instance"]["endpoint"] }}'
    app_db_port: '{{ db_facts["instance"]["port"] }}'
    app_db_engine: '{{ db_facts["instance"]["engine"] }}'

- name: ensure app db exists
  mysql_db:
    login_host: "{{ app_db_ip }}"
    login_user: "{{ app_db_root_username }}"
    login_password: "{{ app_db_root_password }}"
    name: "{{ app_db_name }}"
    encoding: "utf8"
    state: present

- name: ensure app db user exists
  mysql_user:
    login_host: "{{ app_db_ip }}"
    login_user: "{{ app_db_root_username }}"
    login_password: "{{ app_db_root_password }}"
    name: "{{ app_db_username }}"
    password: "{{ app_db_password }}"
    host: "10.1.1.%"
    priv: "{{ app_db_name }}.*:ALL"
    state: present

- name: ensure read-only admin db user exists
  mysql_user:
    login_host: "{{ app_db_ip }}"
    login_user: "{{ app_db_root_username }}"
    login_password: "{{ app_db_root_password }}"
    name: "{{ app_db_username }}_readonly"
    password: "{{ app_db_readonly_password }}"
    host: "10.1.1.%"
    priv: "{{ app_db_name }}.*:SELECT"
    state: present
