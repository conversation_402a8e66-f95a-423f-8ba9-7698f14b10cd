---
- name: sync glynt/slate to remote
  synchronize:
    src: '{{ app_local_slate_dir }}'
    dest: '{{ app_remote_slate_dir }}'

- name: build docs
  command: ./build.sh -t
  args:
    chdir: '{{ app_docs_directory }}'

- name: create link to html files
  file:
    src: '{{ app_docs_directory }}/{{ item.src }}/index.html'
    dest: '{{ app_directory }}/api_docs/templates/{{ item.dest }}'
    state: link
  with_items:
    - { src: 'build', dest: 'basic.html' }
    - { src: 'advanced_build', dest: 'advanced.html' }

- name: ensure static dir exists
  file:
    path: '{{ app_directory }}/api_docs/static'
    state: directory

- name: create link to static files
  file:
    src: '{{ app_docs_directory }}/build/{{ item }}'
    dest: '{{ app_directory }}/api_docs/static/{{ item }}'
    state: link
  with_items:
    - fonts
    - images
    - javascripts
    - stylesheets
