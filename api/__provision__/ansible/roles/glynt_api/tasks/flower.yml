---
- name: install flower
  pip: 
    name: flower
    version: "{{ flower_version }}"
    virtualenv: '{{ app_virtualenv_path }}'
    virtualenv_command: /usr/bin/python3 -m venv

- name: render flower systemd unit file
  template:                                                                
    src: flower.service.j2                                                    
    dest: /etc/systemd/system/flower.service                               
    owner: root                                                            
    group: root 
  when: configure_job_server

- name: enable/start flower systemd unit
  systemd:
    name: 'flower.service'
    state: restarted
    enabled: yes
    daemon_reload: yes
  when: configure_job_server

