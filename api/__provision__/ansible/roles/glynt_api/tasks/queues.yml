---
# NOTE: uncomment to test shared queue settings on local dev environment
#       (see also end of file)
# - name: temporarily switching environments
#   set_fact:
#     old_environment_name: "{{ environment_name }}"
#     environment_name: "sandbox"

- name: fetching shared usage status
  set_fact:
    use_shared_queues: "{{ false if environment_name == 'development' else true }}"

### shared RabbitMQ

- name: getting shared RabbitMQ ID
  shell: aws mq list-brokers --query="(BrokerSummaries[?BrokerName=='glynt-{{ environment_name }}-rabbitmq'].BrokerId)[0]"
  register: broker_id_json
  when: use_shared_queues == true

- name: extracting shared RabbitMQ ID
  set_fact: 
    broker_id: "{{ (broker_id_json.stdout | from_json) }}"
  when: use_shared_queues == true

- name: getting shared RabbitMQ broker information
  shell: aws mq describe-broker --broker-id {{ broker_id }} --query 'BrokerInstances[0]'
  register: broker_description_json
  when: use_shared_queues == true

- name: extracting shared RabbitMQ URL pieces (from shared broker)
  set_fact:
    celery_broker_base_url: "{{ (broker_description_json.stdout | from_json)['Endpoints'][0] }}"
    # NOTE: The JSON returned by the AWS API doesn't seem to include the
    # port for the management URL, so it's added manually here.
    rabbitmq_management_url: "{{ (broker_description_json.stdout | from_json)['ConsoleURL'] }}:15671"
  when: use_shared_queues == true

- name: set RabbitMQ username
  set_fact:
    rabbitmq_user_name: "app"
  when: use_shared_queues == true

- name: get RabbitMQ passwd from AWS
  set_fact: 
    rabbitmq_user_password: "{{ lookup('amazon.aws.aws_ssm', '/glynt/' + environment_name + '/shared/rabbitmq_password', region=aws_region) }}"
  when: use_shared_queues == true

- name: set RabbitMQ management credentials
  set_fact:
    rabbitmq_management_user_name: "{{ rabbitmq_user_name }}"
    rabbitmq_management_user_password: "{{ rabbitmq_user_password }}"
#  when: use_shared_queues == true

# NOTE: The following creates celery_broker_api_url, used only by Flower.
# It assumes that rabbitmq_management_url will not include a password.
- name: extracting RabbitMQ managemement URL details
  set_fact:
    mq_mgt_parts: "{{ rabbitmq_management_url | urlsplit }}"
#  when: use_shared_queues == true

- name: set celery_broker_api_url
  set_fact:
    celery_broker_api_url: "{{ mq_mgt_parts['scheme'] + '://' + rabbitmq_management_user_name + ':' + rabbitmq_management_user_password + '@' + mq_mgt_parts['netloc'] }}"
#  when: use_shared_queues == true

### shared Redis (used by Celery for backend result store)

- name: get Redis passwd from AWS
  set_fact:
    redis_password: "{{ lookup('amazon.aws.aws_ssm', '/glynt/' + environment_name + '/shared/redis_password', region=aws_region) }}"
  when: use_shared_queues == true

- name: getting shared Redis configuration
  shell: aws elasticache describe-replication-groups --replication-group-id glynt-{{ environment_name }}-redis --query="ReplicationGroups[0].NodeGroups[0].PrimaryEndpoint"
  register: redis_config_json
  when: use_shared_queues == true

- name: extracting shared Redis endpoint details
  set_fact:
    redis_host: "{{ (redis_config_json.stdout | from_json)['Address'] }}"
  when: use_shared_queues == true

- name: set Celery backend URL
  set_fact:
    redis_base_url: "{{ redis_scheme + '://' + redis_host + ':' + redis_port + '/' + redis_ssl_param }}"
  vars:
    redis_scheme: "{{ 'rediss' if use_shared_queues == true else 'redis' }}"
    redis_port: "{{ '6379' if use_shared_queues == false else (redis_config_json.stdout | from_json)['Port'] }}"
    redis_ssl_param: ""
    # was the following (the django-redis cache doesn't like the parameter):
    # redis_ssl_param: "{{ '' if use_shared_queues == false else '?ssl_cert_reqs=CERT_REQUIRED' }}"

# NOTE: uncomment to test shared queue settings on local dev environment
# - name: switching environment back to original
#   set_fact:
#     environment_name: "{{ old_environment_name }}"
