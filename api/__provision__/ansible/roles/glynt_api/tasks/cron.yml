---
- name: Create cron entry for rabbitmq monitor task every 60 seconds
  cron:
    name: "send rabbitmq queue count metric"
    job: >
      {{ app_virtualenv_path }}/bin/python
      {{ app_install_directory }}/src/manage.py shell <
      {{ app_install_directory }}/src/api_metrics/custom_metrics/rabbitmq_queue_size.py
  when: configure_job_server and environment_name != 'development' and tags['celery_job_reporter'] == "true"

- name: Create cron entry for celery active tasks count every 60 seconds
  cron:
    name: "send celery active tasks count metric"
    job: >
      {{ app_virtualenv_path }}/bin/python
      {{ app_install_directory }}/src/manage.py shell <
      {{ app_install_directory }}/src/api_metrics/custom_metrics/celery_active_tasks.py
  when: configure_job_server and environment_name != 'development' and tags['celery_job_reporter'] == "true"

- name: Create cron entry for database heartbeat metric
  cron:
    name: "send database heartbeat metric"
    job: >
      {{ app_virtualenv_path }}/bin/python
      {{ app_install_directory }}/src/manage.py shell <
      {{ app_install_directory }}/src/api_metrics/custom_metrics/database_heatbeat.py
  when: configure_job_server and environment_name != 'development' and tags['celery_job_reporter'] == "true"

- name: Create cron entry for boxcars health metrics
  cron:
    name: "send boxcars health metrics"
    job: >
      {{ app_virtualenv_path }}/bin/python
      {{ app_install_directory }}/src/manage.py shell <
      {{ app_install_directory }}/src/api_metrics/custom_metrics/boxcars_health.py
  when: configure_job_server and environment_name != 'development' and tags['celery_job_reporter'] == "true"
