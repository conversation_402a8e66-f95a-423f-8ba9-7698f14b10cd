---
- name: ensure required apt packages are installed
  apt:
    name: [
      'git',
      'python3',
      'python3-pip',
      'python3-venv',
      'libgmp-dev',
      'libssl-dev',
      'libffi-dev',
      'libmysqlclient-dev',
      'libmagickwand-dev',
      'poppler-utils'
    ]

- name: upgrade pip to latest version
  pip: name=pip executable=pip3 extra_args='--upgrade'

- name: install python packages
  pip:
    name: '{{ item.name }}'
    version: '{{ item.version | default(omit) }}'
  with_items:
    - name: boto
    - name: boto3
    - name: virtualenv
    - name: mysqlclient

- name: add ubuntu user to app user group
  user:
    name: ubuntu
    groups: '{{ app_user }}'
    append: yes

- name: add vagrant user to app user group
  user:
    name: vagrant
    groups: '{{ app_user }}'
    append: yes
  when: environment_name == 'development'

- name: initialize app log dir
  file:
    path: '{{ app_log_dir }}'
    owner: '{{ app_user }}'
    group: '{{ app_user }}'
    mode: 0755
    state: directory

- name: ensure app log files exist
  file:
    path: '{{ item }}'
    owner: '{{ app_user }}'
    group: '{{ app_user }}'
    mode: 0664
    state: touch
  with_items:
    - '{{ app_log_file }}'
    - '{{ app_error_log_file }}'

- name: configure glynt_api logrotation
  template:
    src: templates/glynt_api_logrotation.j2
    dest: /etc/logrotate.d/glynt_api
    owner: root
    group: root
    mode: "u=rw,g=r,o=r"

- name: configure gunicorn logrotation
  template:
    src: templates/gunicorn_logrotation.j2
    dest: /etc/logrotate.d/gunicorn
    owner: root
    group: root
    mode: "u=rw,g=r,o=r"

- name: initialize celery log dir
  file:
    path: '{{ celery_log_dir }}'
    owner: '{{ app_user }}'
    group: '{{ app_user }}'
    mode: 0755
    state: directory
    recurse: yes
  when: configure_job_server

- name: configure celery logrotation
  template:
    src: templates/celery_logrotation.j2
    dest: /etc/logrotate.d/celery
    owner: root
    group: root
    mode: "u=rw,g=r,o=r"
  when: configure_job_server

- name: ensure silk profiles dir exists
  file:
    path: '{{ app_silk_profiles_dir }}'
    owner: '{{ app_user }}'
    group: '{{ app_user }}'
    mode: 0755
    state: directory
  when: environment_name != 'production'

- name: Ensures app user ssh dir exists
  file:
    path: /home/<USER>/.ssh
    state: directory
    owner: '{{ app_user }}'
    group: '{{ app_user }}'
  # Not needed when building VMs because of sync'd folders
  when: environment_name != 'development'

- name: copy deployment keys
  copy:
    content: '{{ item.content }}'
    dest: /home/<USER>/.ssh/{{ item.name }}
    owner: '{{ app_user }}'
    group: '{{ app_user }}'
    mode: 0600
  with_items:
    - name: id_rsa
      content: '{{ deployment_private_key }}'
    - name: id_rsa.pub
      content: '{{ deployment_public_key }}'
  no_log: True
  # Not needed when building VMs because of sync'd folder
  when: environment_name != 'development'

- name: ensure that app directory exists and has correct ownership
  file:
    path: "{{ app_git_directory }}"
    state: directory
    owner: "{{ app_user }}"
    group: "{{ app_user }}"
    recurse: yes
  changed_when: false
  # Not needed when building VMs because of sync'd folders
  when: environment_name != 'development'

# Ensure that directory ownerships are correct before we got the git pull.
# This avoids falling prey to the git CVE-2022-24767 fix. (See GL-2961).
- name: ensure owner/group of git directory
  shell:
    cmd: 'chown -R {{ app_user }}:{{ app_user }} {{ app_git_directory }}'
    warn: false
  changed_when: false
  # Not needed when building VMs because of sync'd folders
  when: environment_name != 'development'

- name: update repositories
  git:
    dest: '{{ item.dest }}'
    repo: '{{ item.repo }}'
    accept_hostkey: yes
    update: yes
    version: '{{ app_git_branch }}'
    recursive: no
    track_submodules: no
    force: yes
    key_file: '{{ item.key }}'
  with_items:
    - repo: '{{ app_git_repo }}'
      dest: '{{ app_git_directory }}'
      key: /home/<USER>/.ssh/id_rsa
  become: true
  become_user: '{{ app_user }}'
  # Not needed in dev because of sync'd folders
  when: environment_name != 'development'
  notify: "restart app services"

- name: Install Poetry
  pip:
    name: poetry
    version: '{{ poetry_version }}'
    virtualenv: '{{ poetry_virtualenv_path }}'
    virtualenv_command: /usr/bin/python3 -m venv

- name: Setup glynt-pypi authentication for Poetry
  shell: |
    AWS_ACCOUNT="************"
    AWS_REGION="us-east-1"
    CA_DOMAIN="glynt-artifacts"
    CA_REPOSITORY="glynt-private-pypi"
    TOKEN=$(aws codeartifact get-authorization-token --domain ${CA_DOMAIN} --domain-owner ${AWS_ACCOUNT} --region ${AWS_REGION} --query authorizationToken --output text)
    {{ poetry_virtualenv_path }}/bin/poetry config http-basic.glynt-pypi aws "${TOKEN}"
  args:
    chdir: '{{ app_install_directory }}'
  environment:
    AWS_ACCESS_KEY_ID: '{{ aws_access_key_id }}'
    AWS_SECRET_ACCESS_KEY: '{{ aws_secret_access_key }}'

- name: Setup glynt-pypi authentication for Poetry token set from host
  shell: |
    {{ poetry_virtualenv_path }}/bin/poetry config installer.parallel false
    {{ poetry_virtualenv_path }}/bin/poetry config http-basic.glynt-pypi aws "${PRIVATE_PYPI_TOKEN}"
  args:
    chdir: '{{ app_install_directory }}'
  environment:
    PRIVATE_PYPI_TOKEN: "{{ lookup('env', 'PRIVATE_PYPI_TOKEN') }}"
  when: development_provider|default() == "docker"

- name: Ensure app opt dir exists
  file:
    path: '{{ app_opt_directory }}'
    owner: '{{ app_user }}'
    group: '{{ app_user }}'
    mode: 0755
    state: directory

- name: Ensure app venv does not exist
  shell: |
    rm -rf {{ app_virtualenv_path }}
  args:
    executable: /bin/bash


- name: Initialize app venv and upgrade pip and setuptools
  pip:
    name: '{{ item.name }}'
    extra_args: '--upgrade'
    virtualenv: '{{ app_virtualenv_path }}'
  with_items:
    - name: pip
    - name: setuptools

- name: Install app requirements via Poetry
  shell: |
    source {{ app_virtualenv_path }}/bin/activate
    {{ poetry_virtualenv_path }}/bin/poetry install --only main
  args:
    executable: /bin/bash
    chdir: '{{ app_install_directory }}'

- name: Install dev requirements via Poetry
  shell: |
    source {{ app_virtualenv_path }}/bin/activate
    {{ poetry_virtualenv_path }}/bin/poetry install --only dev
  args:
    executable: /bin/bash
    chdir: '{{ app_install_directory }}'
  when: environment_name == 'development'

- name: ensure ImageMagick policy dir exists
  file:
    path: /etc/ImageMagick-6
    state: directory
  when: configure_job_server

- name: configure ImageMagick policies
  copy:
    src: ImageMagick-policy.xml
    dest: /etc/ImageMagick-6/policy.xml
    owner: root
    group: root
    mode: 0644
  when: configure_job_server

- name: ensure additional ghostscript binary is installed
  aws_s3:
    aws_access_key: '{{ aws_shared_access_key_id }}'
    aws_secret_key: '{{ aws_shared_secret_access_key }}'
    bucket: glynt.shared
    object: /ghostscript-9.533-linux-x86_64/gs-9533-linux-x86_64
    dest: '{{ app_ghostscript_path }}'
    mode: get
    overwrite: different
  when: configure_job_server

- name: ensure ghostscript binary file permissions
  file:
    path: '{{ app_ghostscript_path }}'
    owner: root
    group: root
    mode: '0755'
  when: configure_job_server

- name: Get Ghostscript for docker
  aws_s3:
    aws_access_key: '{{ aws_shared_access_key_id }}'
    aws_secret_key: '{{ aws_shared_secret_access_key }}'
    bucket: glynt.shared
    object: /ghostscript-9.533-linux-x86_64/gs-9533-linux-x86_64
    dest: '{{ docker_ghostscript_path }}'
    mode: get
  when: configure_job_server

- name: ensure ghostscript binary file for docker permissions
  file:
    path: '{{ docker_ghostscript_path }}'
    owner: root
    group: root
    mode: '0755'
  when: configure_job_server

- name: build docker image for Ghostscript
  shell: ./build_docker_Ubuntu_GS_IM.sh &> docker_build.txt
  args:
    executable: /bin/bash
    chdir: '{{ app_build_docker_directory }}'

- name: download rds global-bundle.pem
  get_url:
    url: 'https://truststore.pki.rds.amazonaws.com/global/global-bundle.pem'
    dest: '{{ app_db_rds_global_bundle }}'
    owner: '{{ app_user }}'
    group: '{{ app_user }}'
    mode: '0440'

- name: ensure rds is configured and facts are gathered
  include_tasks: rds.yml
  when: environment_name != 'development'

- name: get shared queue and redis facts
  include_tasks:
    file: queues.yml
    # NOTE: We have to set the region in a few different places, because
    # it's inconsistent across boto3 versions and the AWS CLI, and we have
    # several floating around. The pipeline scripts set the AWS_REGION
    # environment variable, so that will be our source value. We'll have
    # to tolerate this until we move to a fully containerized setup.
    apply:
      # propagate AWS environment variables to the remotes on queues.yml
      environment:
        AWS_ACCESS_KEY_ID: "{{ lookup('ansible.builtin.env', 'AWS_ACCESS_KEY_ID', default='') }}"
        AWS_SECRET_ACCESS_KEY: "{{ lookup('ansible.builtin.env', 'AWS_SECRET_ACCESS_KEY', default='') }}"
        AWS_REGION: "{{ lookup('ansible.builtin.env', 'AWS_REGION', default='') }}"
        AWS_DEFAULT_REGION: "{{ lookup('ansible.builtin.env', 'AWS_REGION', default='') }}"
      vars:
        aws_region: "{{ lookup('ansible.builtin.env', 'AWS_REGION', default='') }}"

- name: render dotenv file (nondev)
  template:
    src: env.j2
    dest: '{{ app_directory }}/.env'
    mode: '0600'
    owner: '{{ app_user }}'
    group: '{{ app_user }}'
  notify: "restart app services"
  when: environment_name != 'development'

# Dev VMs require a looser permission, since file ownership
# can't be changed on mounted volumes
- name: render dotenv file (dev)
  template:
    src: env.j2
    dest: '{{ app_directory }}/.env'
    mode: '0644'
  notify: "restart app services"
  when: environment_name == 'development'

- name: create local storage client dir
  file:
    path: /local_storage_client
    state: directory
    mode: 0777
  when: environment_name == 'development'


# Here is an explanation of the rules below regarding workers (and followup
# rules later on). The following happens on the app server:
#
# 1. Ansible gets the status of the workers on all job servers--a
#    structure of workers running on individual servers; this is stored
#    in hostvars.
# 2. Ansible disables any workers running the job servers. This prevents
#    the workers from running into problems after a data migration.
# 3. Ansible runs the migration.
# 4. Ansible starts any workers that it stopped before.
#
# For fresh installs, such as a new dev provisioning, the job servers have
# no workers when the app server is provisioned, so there will be no
# disable/enable; Ansible skips those tasks. However, on a re-provision of
# a system that's already up, you will see the disable/enable.
#
# Job servers ignore much of this code; the provisioning that renders the
# systemd unit files and sets the workers in motion is at the end. It
# simply creates the unit files and restarts the workers.
#
# There is a potential for runtime errors in the period between when the
# app server performs the migration and the job server restarts its workers,
# because the app server could re-enable old workers, then an out-of-date
# worker could operate on data from a new migration. To prevent this,
# create/alter model fields one release ahead of actually using them.

- name: get job server service unit facts
  ansible.builtin.service_facts:
  delegate_to: "{{ item }}"
  delegate_facts: true
  when: configure_web
  loop: "{{ groups['job_servers'] }}"

- name: stop celery workers
  systemd:
    name: 'worker_{{ item[1].name }}.service'
    state: stopped
  delegate_to: '{{ item[0] }}'
  delegate_facts: true
  when: configure_web and 'worker_' + item[1].name + '.service' in hostvars[item[0]]['services']
  with_nested:
   - "{{ groups['job_servers'] }}"
   - "{{ celery_worker_config }}"

- name: disable celerybeat worker
  systemd:
    name: 'celerybeat.service'
    state: stopped
  delegate_to: '{{ item }}'
  delegate_facts: true
  with_items:
   - "{{ groups['job_servers'] }}"
  when: configure_web and environment_name != 'development' and 'celerybeat.service' in hostvars[item]['services']

- name: run django model migrations
  django_manage:
    command: migrate
    app_path: '{{ app_directory }}'
    virtualenv: '{{ app_virtualenv_path }}'
  notify: "restart app services"
  when: configure_web and (environment_name == 'development' or tags['migration_server'] == 'true')

- name: remove stale django sessions
  django_manage:
    command: clearsessions
    app_path: '{{ app_directory }}'
    virtualenv: '{{ app_virtualenv_path }}'
  when: configure_web and (environment_name == 'development' or tags['migration_server'] == 'true')

# Visit the cached_reverse app for more details about the command
# and its usage which optimizes url generation
- name: populate api url cache
  django_manage:
    command: populate_url_cache
    app_path: '{{ app_directory }}'
    virtualenv: '{{ app_virtualenv_path }}'
  when: configure_job_server

- name: start celery workers
  systemd:
    name: 'worker_{{ item[1].name }}.service'
    state: started
  delegate_to: '{{ item[0] }}'
  delegate_facts: true
  when: configure_web and 'worker_' + item[1].name + '.service' in hostvars[item[0]]['services']
  with_nested:
   - "{{ groups['job_servers'] }}"
   - "{{ celery_worker_config }}"

- name: enable/start celerybeat worker systemd unit(s)
  systemd:
    name: 'celerybeat'
    state: started
    enabled: yes
  delegate_to: '{{ item }}'
  delegate_facts: true
  with_items:
   - "{{ groups['job_servers'] }}"
  when: configure_web and environment_name != 'development' and 'celerybeat.service' in hostvars[item]['services']

- include_tasks: docs.yml
  tags: ['docs']
  when: >
    configure_web and build_docs|default() == "true"

- name: collectstatic django files
  django_manage:
    command: collectstatic
    app_path: '{{ app_directory }}'
    virtualenv: '{{ app_virtualenv_path }}'

- name: render glynt_api.service systemd file
  template:
    src: glynt_api.service.j2
    dest: /etc/systemd/system/glynt_api.service
    mode: 0644
  when: configure_web

- name: restart/enable glynt_api
  systemd:
    name: glynt_api
    enabled: yes
    state: restarted
    daemon_reload: yes
  when: configure_web

- name: render celery task systemd service unit files
  template:
    src: '{{ item.template }}'
    dest: '/etc/systemd/system/worker_{{ item.name }}.service'
    mode: 0644
  loop: "{{ celery_worker_config }}"
  when: configure_job_server

- name: restart/enable celery task systemd units
  systemd:
    name: "worker_{{ item.name }}"
    enabled: yes
    state: restarted
    daemon_reload: yes
  loop: "{{ celery_worker_config }}"
  when: configure_job_server

- name: render celerybeat systemd unit file
  template:
    src: celerybeat.service.j2
    dest: /etc/systemd/system/celerybeat.service
    owner: root
    group: root
  when: configure_job_server and environment_name != 'development' and tags['celerybeat_server'] == "true"

- name: restart/enable celerybeat worker systemd units
  systemd:
    name: 'celerybeat.service'
    enabled: yes
    state: restarted
    daemon_reload: yes
  when: configure_job_server and environment_name != 'development' and tags['celerybeat_server'] == "true"

# uncomment to debug service handlers in the dev environment
# - name: trigger app service handler
#   debug:
#     msg: "triggering app service handler"
#   notify: "restart app services"
#   changed_when: True
#   when: configure_job_server
