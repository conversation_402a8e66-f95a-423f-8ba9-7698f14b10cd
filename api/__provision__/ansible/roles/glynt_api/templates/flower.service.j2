[Unit]
Description=flower celery monitoring tool
After=network.target
StartLimitIntervalSec=0

[Service]
Type=simple
Restart=always
RestartSec=5
User={{ app_user }}
WorkingDirectory={{ app_directory }}
ExecStart={{ app_virtualenv_path }}/bin/celery -A {{ flower_app_name }} \
    flower \
    --port={{ flower_port }} \
    --persistent=True \
    --purge-offline-workers=600 \
{#    --broker={{ celery_broker_uri }} \ #}
{# NOTE: --broker is disabled because flower needs only to connect to the #}
{#       management API with --broker_api (see below) for our current usage #}
{#       pattern. Try to re-enable this when we containerize Flower. #}
    --broker_api={{ celery_broker_api_url }} \
{% if environment_name != "development" %}
    --debug=False \
    --auth_provider=flower.views.auth.GithubLoginHandler \
    --auth={{ flower_authorized_emails | join('|') }}
Environment="FLOWER_OAUTH2_KEY={{ flower_oauth2_key }}"
Environment="FLOWER_OAUTH2_SECRET={{ flower_oauth2_secret }}"
Environment="FLOWER_OAUTH2_REDIRECT_URI={{ flower_oauth2_redirect_url }}"
{% else %}
    --debug=True \
{% endif %}
SyslogIdentifier=flower.service
StandardOutput=file:/var/log/glynt_api/flower.log
StandardError=file:/var/log/glynt_api/flower.error.log

[Install]
WantedBy=multi-user.target

