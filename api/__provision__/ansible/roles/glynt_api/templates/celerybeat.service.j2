[Unit]
Description=celery beat scheduler
After=network.target
StartLimitIntervalSec=0

[Service]
Type=simple
Restart=always
RestartSec=5
User={{ app_user }}
WorkingDirectory={{ app_directory }}
ExecStart={{ app_virtualenv_path }}/bin/celery --app=glynt_api beat \
    --scheduler glynt_api.schedulers.DatabaseSchedulerWithCleanUp \
    --pidfile=/tmp/celerybeat.pid \
    --loglevel=INFO \
    --logfile={{ celery_log_dir }}/celerybeat.log

SyslogIdentifier=celerybeat.service

[Install]
WantedBy=multi-user.target

