APP_ENV={{ environment_name }}

{% if environment_name == 'development' %}
{# this MUST match the private network ip address in Vagrantfile #}
VPC_HOST=**************
{% else %}
VPC_HOST=
{% endif %}

APP_BASE_URL={{ app_base_url }}
APP_DB_URI={{ app_db_uri }}
APP_SECRET_KEY={{ app_secret_key }}
APP_SECRETS_DIR={{ app_secrets_directory }}
APP_ALLOWED_HOSTS={{ app_allowed_hosts }}
APP_LOG_FILE={{ app_log_file }}
APP_ERROR_LOG_FILE={{ app_error_log_file }}
APP_SILKY_PROFILER_RESULT_PATH={{ app_silk_profiles_dir }}

# NOTE: These are used in the django-redis reverse-URL cache
REDIS_BASE_URL="{{ redis_base_url }}"
REDIS_PASSWORD="{{ redis_password }}"

{% if app_file_storage_region is defined %}
APP_FILE_STORAGE_REGION={{ app_file_storage_region }}
{% endif %}
APP_STORAGE_BUCKET_PREFIX={{ app_storage_bucket_prefix }}

BOXCARS_API_URL={{ boxcars_api_url }}

CORE_API_URL={{ core_api_url }}

GLYNT_UI_URL={{ glynt_ui_url }}

CELERY_BROKER_BASE_URL="{{ celery_broker_base_url }}"
CELERY_BROKER_USERNAME="{{ rabbitmq_user_name }}"
CELERY_BROKER_PASSWORD="{{ rabbitmq_user_password }}"
CELERY_RESULT_BACKEND_BASE_URL="{{ redis_base_url }}"
CELERY_RESULT_BACKEND_PASSWORD="{{ redis_password }}"

RABBITMQ_MANAGEMENT_URL="{{ rabbitmq_management_url }}"
RABBITMQ_MANAGEMENT_USER_NAME="{{ rabbitmq_user_name }}"
RABBITMQ_MANAGEMENT_USER_PASSWORD="{{ rabbitmq_user_password }}"

AUTH0_API_IDENTIFIER={{ app_auth0_api_identifier }}
AUTH0_DOMAIN={{ app_auth0_domain }}
AUTH0_CLIENT_ID={{ app_auth0_client_id }}
AUTH0_CLIENT_SECRET={{ app_auth0_client_secret }}

{% if environment_name != 'development' %}
AWS_DEFAULT_REGION={{ aws_default_region }}
{% endif %}

SENDGRID_API_KEY={{ sendgrid_api_key }}

SLACK_TOKEN={{ slack_token }}
SLACK_MONITORING_CHANNEL={{ slack_monitoring_channel }}
SLACK_CUSTOMER_METRICS_CHANNEL={{ slack_customer_metrics_channel }}

UIPATH_BASE_URL={{ uipath_base_url }}
UIPATH_AUTH_URL={{ uipath_auth_url }}

MAPQUEST_API_CONSUMER_KEY={{ mapquest_api_consumer_key }}
