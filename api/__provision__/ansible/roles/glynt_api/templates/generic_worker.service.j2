[Unit]
Description=Glynt {{ item.name }} celery worker(s)
After=network.target
StartLimitIntervalSec=0

[Service]
Type=simple
Restart=always
RestartSec=1
User={{ app_user }}
LimitNOFILE=65536
WorkingDirectory={{ app_directory }}
ExecStart={{ app_virtualenv_path }}/bin/celery --app=glynt_api worker \
    --hostname={{ item.queues }}@%H \
    --queues={{ item.queues }} \
    --pool={{ item.pool_type }} \
    --concurrency={{ item.num_workers }} \
    --prefetch-multiplier={{ item.prefetch }} \
    --max-tasks-per-child=1024 \
    --without-mingle \
    --without-gossip \
    --loglevel={{ item.loglevel|default("DEBUG", true) }} \
    --logfile={{ celery_log_dir }}/celery_{{ item.name }}.log

SyslogIdentifier=worker_{{ item.name }}.service

{% if item.timeout_stop is defined %}
TimeoutStopSec={{ item.timeout_stop }}
{% endif %}

[Install]
WantedBy=multi-user.target

