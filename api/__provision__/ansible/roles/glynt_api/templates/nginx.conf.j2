user www-data;
worker_processes 2;
pid /run/nginx.pid;

events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    server_tokens off;

    access_log /var/log/nginx/access.log;
    error_log /var/log/nginx/error.log;

    error_page 495 496 =403 /403.html;

    log_format time_format '[TIME=$time_local] [REQUEST=$request] [STATUS=$status] [BYTES=$body_bytes_sent]'
                           '[USER_AGENT=$http_user_agent] [REQUEST_TIME=$request_time] [UPSTREAM_TIME=$upstream_response_time]';

    sendfile off;
    keepalive_timeout 65;

    gzip on;
    gzip_disable "msie6";

    # Load modular configuration files from the /etc/nginx/conf.d directory.
    # See http://nginx.org/en/docs/ngx_core_module.html#include
    # for more information.
    include /etc/nginx/conf.d/*.conf;

    upstream  glynt_api {
        server 127.0.0.1:8000;
    }

    {% if ('development' not in group_names) %}
    server {
        listen 80;
        server_name {{ flower_server_name }};

        location / {
            proxy_pass {{ flower_host_private_url }};
            proxy_set_header Host $host;
        }
    }
    {% endif %}


    server {
        client_max_body_size 50M;
        listen 80 default_server;

        location /static {
            root {{ app_directory }};
            autoindex on;
        }

        location / {
	    # adds a trailing slash, if missing, to all URIs that don't contain
	    # a dot or don't begin with '/auth'.  This is because the auth0 login
	    # URI is a special case that accepts the name of the auth backend (auth0)
	    # at the terminus of the URI as if it were a filename.
	    rewrite ^(?!\/auth)([^\.]*$(?<!\/)) $1/ last;

            if ($request_method = 'OPTIONS') {
                add_header 'Access-Control-Allow-Origin' '*' always;
                add_header 'Access-Control-Allow-Methods' 'DELETE, GET, PATCH, POST, PUT, OPTIONS' always;
                #
                # Custom headers and headers various browsers *should* be OK with but aren't
                # The "Authorization" header allows the passing of the Bearer tokens
                #
                add_header 'Access-Control-Allow-Headers' 'Authorization,DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Content-MD5' always;
                #
                # Tell client that this pre-flight info is valid for 20 days
                #
                add_header 'Access-Control-Max-Age' 1728000;
                add_header 'Content-Type' 'text/plain; charset=utf-8';
                add_header 'Content-Length' 0;
                return 204;
            }

	    # overriding host allows the ELB health check to be accounted for by
	    # django as an allowed host. There are other ways to handle this
	    # but this is safe as long as we trust the ELB to send traffic only
	    # from the api url hostname (which is the only way to ping it from
	    # the open internet) or from the health checker (which sends pings
	    # directly to this machine's private ip, hence the ip pattern check
	    # below). Since we can't necessarily know the private IP (specially
	    # when app servers become completely elastic) we can trust that, if the
	    # ELB is sending pings with an IP address as the host name, that it
	    # is an allowed host.
	    set $override_host $http_host;
	    if ($http_host ~ "\d+\.\d+\.\d+\.\d+") {
		set $override_host {{ app_glynt_api_domain }};
	    }

            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header Host $override_host;
            proxy_redirect off;
            proxy_pass http://glynt_api;
            proxy_read_timeout 600;

	    {% if ('development' not in group_names) %}
	    # nginx is behind a load balancer which handles https. This setting
	    # allows django to return https urls in the HyperlinkedRelatedField
	    # class. Only set when you are sure https has been verified before
	    # arriving at this proxy.
	    proxy_set_header X-Forwarded-Proto https;
	    {% else %}
	    proxy_set_header X-Forwarded-Proto http;
	    {% endif %}

            if ($request_method = 'DELETE') {
               add_header 'Access-Control-Allow-Origin' '*' always;
               add_header 'Access-Control-Allow-Methods' 'DELETE, GET, PATCH, POST, PUT, OPTIONS' always;
               add_header 'Access-Control-Allow-Headers' 'Authorization,DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Content-MD5' always;
               add_header 'Access-Control-Expose-Headers' 'Content-Length,Content-Range' always;
               add_header 'Cache-Control' 'no-store';
               add_header 'Pragma' 'no-cache';
            } 
            if ($request_method = 'GET') {
               add_header 'Access-Control-Allow-Origin' '*' always;
               add_header 'Access-Control-Allow-Methods' 'DELETE, GET, PATCH, POST, PUT, OPTIONS' always;
               add_header 'Access-Control-Allow-Headers' 'Authorization,DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Content-MD5' always;
               add_header 'Access-Control-Expose-Headers' 'Content-Length,Content-Range' always;
               add_header 'Cache-Control' 'no-store';
               add_header 'Pragma' 'no-cache';
            } 
            if ($request_method = 'PATCH') {
               add_header 'Access-Control-Allow-Origin' '*' always;
               add_header 'Access-Control-Allow-Methods' 'DELETE, GET, PATCH, POST, PUT, OPTIONS' always;
               add_header 'Access-Control-Allow-Headers' 'Authorization,DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Content-MD5' always;
               add_header 'Access-Control-Expose-Headers' 'Content-Length,Content-Range' always;
               add_header 'Cache-Control' 'no-store';
               add_header 'Pragma' 'no-cache';
            }
            if ($request_method = 'POST') {
               add_header 'Access-Control-Allow-Origin' '*' always;
               add_header 'Access-Control-Allow-Methods' 'DELETE, GET, PATCH, POST, PUT, OPTIONS' always;
               add_header 'Access-Control-Allow-Headers' 'Authorization,DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Content-MD5' always;
               add_header 'Access-Control-Expose-Headers' 'Content-Length,Content-Range' always;
               add_header 'Cache-Control' 'no-store';
               add_header 'Pragma' 'no-cache';
            }
            if ($request_method = 'PUT') {
               add_header 'Access-Control-Allow-Origin' '*' always;
               add_header 'Access-Control-Allow-Methods' 'DELETE, GET, PATCH, POST, PUT, OPTIONS' always;
               add_header 'Access-Control-Allow-Headers' 'Authorization,DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Content-MD5' always;
               add_header 'Access-Control-Expose-Headers' 'Content-Length,Content-Range' always;
               add_header 'Cache-Control' 'no-store';
               add_header 'Pragma' 'no-cache';
            }
        }
    }
}
