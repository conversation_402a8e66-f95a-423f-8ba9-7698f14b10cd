[Unit]
Description=Glynt API service (glynt_api)
After=network.target
StartLimitIntervalSec=0

[Service]
Type=simple
Restart=always
RestartSec=15s
User={{ app_user }}
WorkingDirectory={{ app_directory }}
ExecStart={{ app_virtualenv_path }}/bin/gunicorn \
    --workers={{ app_gunicorn_workers }} \
    --bind=127.0.0.1:8000 \
    --timeout=600 \
    --max-requests={{ app_gunicorn_max_requests }} \
    --max-requests-jitter={{ app_gunicorn_max_requests_jitter }} \
    --log-level=info \
    --access-logfile=/var/log/glynt_api/gunicorn.log \
    --error-logfile=/var/log/glynt_api/gunicorn.error.log \
    glynt_api.wsgi
SyslogIdentifier=glynt_api.service

[Install]
WantedBy=multi-user.target

