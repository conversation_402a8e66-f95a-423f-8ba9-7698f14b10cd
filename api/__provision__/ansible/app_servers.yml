---
- name: Provision and deployment playbook for application servers
  hosts: app_servers
  become: yes
  vars:
  roles:
    - role: docker
      docker_users: ['ubuntu', 'vagrant','{{ app_user }}']
      tags: ['docker']
      when: development_provider|default() != "docker"

    - role: glynt_api
      configure_web: yes
      tags: ['glynt_api']

    - role: cloudwatch-agent
      aws_cwa_region: "{{ aws_default_region }}"
      aws_cwa_namespace: glynt-api-{{ environment_name }}
      aws_cwa_cfgs: group_files/awscwa_cfg/app_servers
      aws_cwa_logfiles:
        - file_path: /var/log/chrony/statistics.log
          log_group_name: /glynt/api/{{ environment_name }}/app_servers/chrony
          log_group_tags:
            - environment: "{{ environment_name }}"
            - server_type: "app_server"
          log_stream_name: "{instance_id}"
          log_retention: "{{ log_retention_in_days }}"
          timestamp_format: "%Y-%m-%d %H:%M:%S"
          timezone: UTC
        - file_path: /var/log/chrony/measurements.log
          log_group_name: /glynt/api/{{ environment_name }}/app_servers/chrony
          log_group_tags:
            - environment: "{{ environment_name }}"
            - server_type: "app_server"
          log_stream_name: "{instance_id}"
          log_retention: "{{ log_retention_in_days }}"
          timestamp_format: "%Y-%m-%d %H:%M:%S"
          timezone: UTC
        - file_path: /var/log/chrony/tracking.log
          log_group_name: /glynt/api/{{ environment_name }}/app_servers/chrony
          log_group_tags:
            - environment: "{{ environment_name }}"
            - server_type: "app_server"
          log_stream_name: "{instance_id}"
          log_retention: "{{ log_retention_in_days }}"
          timestamp_format: "%Y-%m-%d %H:%M:%S"
          timezone: UTC
        - file_path: /var/log/glynt_api/glynt_api.log
          log_group_name: /glynt/api/{{ environment_name }}/app_servers/glynt_api
          log_group_tags:
            - environment: "{{ environment_name }}"
            - server_type: "app_server"
          log_stream_name: "{instance_id}"
          log_retention: "{{ log_retention_in_days }}"
          timestamp_format: "%Y-%m-%d %H:%M:%S"
          timezone: UTC
        - file_path: /var/log/glynt_api/gunicorn.error.log
          log_group_name: /glynt/api/{{ environment_name }}/app_servers/gunicorn
          log_group_tags:
            - environment: "{{ environment_name }}"
            - server_type: "app_server"
          log_stream_name: "{instance_id}"
          log_retention: "{{ log_retention_in_days }}"
          timestamp_format: "[%Y-%m-%d %H:%M:%S %z]"
          timezone: UTC
        - file_path: /var/log/nginx/access.log
          log_group_name: /glynt/api/{{ environment_name }}/app_servers/nginx
          log_group_tags:
            - environment: "{{ environment_name }}"
            - server_type: "app_server"
          log_stream_name: "{instance_id}/access"
          log_retention: "{{ log_retention_in_days }}"
          timestamp_format: "%d/%b/%Y:%H:%M:%S %z"
          timezone: UTC
        - file_path: /var/log/nginx/error.log
          log_group_name: /glynt/api/{{ environment_name }}/app_servers/nginx
          log_group_tags:
            - environment: "{{ environment_name }}"
            - server_type: "app_server"
          log_stream_name: "{instance_id}/error"
          log_retention: "{{ log_retention_in_days }}"
          timestamp_format: "%Y/%m/%d %H:%M:%S"
          timezone: UTC
      when: environment_name != "development"
