---
- name: Provision shared roles
  hosts: development:production:sandbox:stage
  become: yes

  pre_tasks:
    - name: check vault pass is valid
      debug:
        msg: '{{ vault_confirmation }}'

    - name: create app user group
      group:
        name: '{{ app_user }}'
        state: present
        system: yes

    - name: create app user
      user:
        name: '{{ app_user }}'
        state: present
        system: yes
        create_home: yes
        group: '{{ app_user }}'
        generate_ssh_key: yes
        shell: /bin/bash

  roles:
    - role: system
      tags: ['system']
      when: development_provider|default() != "docker"

    - role: aws_cli
      tags: ['aws-cli']

    - role: mysql
      when: environment_name == 'development'
