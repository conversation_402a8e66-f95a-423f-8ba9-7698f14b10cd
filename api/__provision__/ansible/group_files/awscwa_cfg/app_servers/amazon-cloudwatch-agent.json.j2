{
    "agent": {
        "metrics_collection_interval": 60,
        "region": "{{ aws_cwa_region }}",
        "logfile": "/opt/aws/amazon-cloudwatch-agent/logs/amazon-cloudwatch-agent.log",
        "debug": false
    },
{% if aws_cwa_logfiles|length > 0 %}
    "logs": {
        "logs_collected": {
            "files": {
                "collect_list": [
{% for logfile in aws_cwa_logfiles %}
                    {
                        "file_path": "{{ logfile.file_path }}",
                        "log_group_name": "{{ logfile.log_group_name }}",
                        "log_stream_name": "{{ logfile.log_stream_name }}",
                        "timestamp_format": "{{ logfile.timestamp_format }}",
                        "multi_line_start_pattern": "{timestamp_format}",
                        "timezone": "{{ logfile.timezone }}"
                    }{% if not loop.last %},{% endif %}

{% endfor %}
                ]
            }
        }
    },
{% endif %}
    "metrics": {
        "namespace": "{{ aws_cwa_namespace }}-app-server",
        "metrics_collected": {
            "cpu": {
                "measurement": [
		    "cpu_usage_idle",
		    "cpu_usage_active"
                ],
                "metrics_collection_interval": 60,
                "totalcpu": true
            },
            "disk": {
                "measurement": [
                    "used_percent"
                ],
                "metrics_collection_interval": 60,
                "resources": [{% for p in aws_cwa_disk_monitor_paths %}"{{ p }}"{% if not loop.last %}, {% endif %}{% endfor %}]
            },
            "mem": {
                "measurement": [
                    "mem_used_percent"
                ],
                "metrics_collection_interval": 60
            }
        }
    },
    "append_dimensions": {
        "ImageId": "${aws:ImageId}",
        "InstanceId": "${aws:InstanceId}",
        "InstanceType": "${aws:InstanceType}",
        "AutoScalingGroupName": "${aws:AutoScalingGroupName}"
    }
}
