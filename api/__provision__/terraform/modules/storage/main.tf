# The API is responsible for creating the transformers repository, simply to
# save the overhead of having to introduce terraform into the transformers
# provision process. When Transformers moves out of beta, this can move to a
# more appropriate location. This name is hardcoded in the transformers
# provisioning as well. If this name changes, the corresponding name in the
# Transformers repo will need to change as well. DRY this up when we move out
# of beta.
resource "aws_ecr_repository" "transformers-beta" {
  name = "glynt/${var.environment}/transformers-beta"
}

resource "aws_ecr_lifecycle_policy" "transformers-beta-policy" {
  repository = aws_ecr_repository.transformers-beta.name

  policy = <<EOF
{
    "rules": [
        {
            "rulePriority": 1,
            "description": "Expire images in excess of 10 images",
            "selection": {
                "tagStatus": "untagged",
                "countType": "imageCountMoreThan",
                "countNumber": 10
            },
            "action": {
                "type": "expire"
            }
        }
    ]
}
EOF
}
