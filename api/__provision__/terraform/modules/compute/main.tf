data "aws_ami" "os" {
  most_recent = true

  filter {
    name   = "name"
    values = ["${var.ami_name}"]
  }

  filter {
    name   = "virtualization-type"
    values = ["hvm"]
  }

  owners = ["************"] # Canonical
}

data "aws_security_group" "db_client" {
  name = "Glynt ${var.environment} db client" # security group provided by shared infrastructure
}

data "aws_region" "current" {}

data "aws_caller_identity" "current" {}

data "aws_elb_service_account" "main" {}

locals {
  account_id = data.aws_caller_identity.current.account_id
}

# Load balancer requires two subnets in different AZs although we are only using one atm
data "aws_subnet" "public_c" {
  filter {
    name   = "tag:Name"
    values = ["glynt-${var.environment}-public-subnet-c"]
  }
}

resource "aws_s3_bucket" "elb_access_logs" {
  bucket        = "glynt-api-${var.environment}-lb-access-logs"
  acl           = "private"
  force_destroy = true
  policy        = <<POLICY
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "AWS": "arn:aws:iam::${data.aws_elb_service_account.main.id}:root"
      },
      "Action": "s3:PutObject",
      "Resource": "arn:aws:s3:::glynt-api-${var.environment}-lb-access-logs/AWSLogs/${local.account_id}/*"
    },
    {
      "Effect": "Allow",
      "Principal": {
        "Service": "delivery.logs.amazonaws.com"
      },
      "Action": "s3:PutObject",
      "Resource": "arn:aws:s3:::glynt-api-${var.environment}-lb-access-logs/AWSLogs/${local.account_id}/*",
      "Condition": {
        "StringEquals": {
          "s3:x-amz-acl": "bucket-owner-full-control"
        }
      }
    },
    {
      "Effect": "Allow",
      "Principal": {
        "Service": "delivery.logs.amazonaws.com"
      },
      "Action": "s3:GetBucketAcl",
      "Resource": "arn:aws:s3:::glynt-api-${var.environment}-lb-access-logs"
    },
    {
            "Effect": "Deny",
            "Principal": "*",
            "Action": "*",
            "Resource": [
                "arn:aws:s3:::glynt-api-${var.environment}-lb-access-logs",
                "arn:aws:s3:::glynt-api-${var.environment}-lb-access-logs/*"
            ],
            "Condition": {
                "Bool": {
                    "aws:SecureTransport": "false"
                }
            }
        }
    ]
}
POLICY
}

resource "aws_lb" "app_servers_lb" {
  name               = "glynt-api-${var.environment}-app-lb"
  internal           = false
  load_balancer_type = "application"
  security_groups    = [var.lb_sg_id]
  subnets            = [var.public_subnet_id, data.aws_subnet.public_c.id]
  idle_timeout       = 500

  access_logs {
    bucket  = aws_s3_bucket.elb_access_logs.id
    enabled = true
  }
}

resource "aws_instance" "app_server" {
  count                  = var.app_server_count
  ami                    = data.aws_ami.os.id
  instance_type          = var.app_server_instance_type
  iam_instance_profile   = var.glynt_api_instance_profile
  key_name               = var.aws_key_name
  vpc_security_group_ids = [var.app_server_sg_id, data.aws_security_group.db_client.id]
  subnet_id              = var.public_subnet_id

  root_block_device {
    volume_size = var.app_server_root_volume_size
    encrypted   = true
  }

  tags = {
    Name                     = "GlyntAPI app server ${count.index} (${var.environment})"
    classification           = "${var.environment}_glyntapi_app_server"
    monitor_client           = "true"
    monitor_classification   = "glyntapi_app_server"
    server_id                = "glyntapi.${var.environment}.app_server"
    "scheduler:ebs-snapshot" = var.ebs_snapshot_schedule
    migration_server         = count.index == 0 ? "true" : "false"
  }
}

resource "aws_lb_target_group" "app_servers_tg" {
  name     = "glynt-api-${var.environment}-app-tg"
  port     = 80
  protocol = "HTTP"
  vpc_id   = var.vpc_id

  health_check {
    interval = 30
    path     = "/v6"
    matcher  = "401"
  }
}

resource "aws_lb_target_group_attachment" "app_servers_http_tg_attachment" {
  count            = length(aws_instance.app_server[*].id)
  target_group_arn = aws_lb_target_group.app_servers_tg.arn
  target_id        = aws_instance.app_server[count.index].id
  port             = 80
}

resource "aws_lb_listener" "http_listener" {
  load_balancer_arn = aws_lb.app_servers_lb.arn
  port              = "80"
  protocol          = "HTTP"

  default_action {
    type = "redirect"
    redirect {
      port        = "443"
      protocol    = "HTTPS"
      status_code = "HTTP_301"
    }
  }
}

data "aws_acm_certificate" "glynt_ai_ssl" { # must be uploaded as part of terraform initial setup, see README.
  domain   = "*.glynt.ai"
  statuses = ["ISSUED"]
}

resource "aws_lb_listener" "https_listener" {
  load_balancer_arn = aws_lb.app_servers_lb.arn
  port              = "443"
  protocol          = "HTTPS"
  ssl_policy        = "ELBSecurityPolicy-TLS13-1-2-2021-06"
  certificate_arn   = data.aws_acm_certificate.glynt_ai_ssl.arn

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.app_servers_tg.arn
  }
}

resource "aws_instance" "job_server" {
  count                       = var.job_server_count
  ami                         = data.aws_ami.os.id
  instance_type               = var.job_server_instance_type
  iam_instance_profile        = var.glynt_api_instance_profile
  key_name                    = var.aws_key_name
  vpc_security_group_ids      = [var.job_server_sg_id, data.aws_security_group.db_client.id]
  subnet_id                   = var.public_subnet_id
  associate_public_ip_address = true

  root_block_device {
    volume_size = var.job_server_root_volume_size
    encrypted   = true
  }


  tags = {
    Name                   = "GlyntAPI job server ${count.index} (${var.environment})"
    classification         = "${var.environment}_glyntapi_job_server"
    ip_blocking            = "yes"
    monitor_client         = "true"
    monitor_classification = "glyntapi_job_server"
    server_id              = "glyntapi.${var.environment}.job_server"
    flower_server          = count.index == 0 ? "true" : "false"
    celerybeat_server      = count.index == 0 ? "true" : "false"
    celery_job_reporter    = count.index == 0 ? "true" : "false"
  }
}
