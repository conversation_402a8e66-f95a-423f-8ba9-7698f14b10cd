output "app_server_availability_zone" {
  value = "${aws_instance.app_server[0].availability_zone}"
}

output "app_server_id" {
  value = "${aws_instance.app_server[0].id}"
}

output "app_server_private_ip" {
  value = "${aws_instance.app_server[0].private_ip}"
}

output "job_server_private_ip" {
  value = "${aws_instance.job_server[0].private_ip}"
}

output "app_server_private_dns" {
  value = "${aws_instance.app_server[0].private_dns}"
}

output "job_server_id" {
  value = "${aws_instance.job_server[0].id}"
}

output "job_server_private_dns" {
  value = "${aws_instance.job_server[0].private_dns}"
}

output "elb_public_dns_name" {
  value = aws_lb.app_servers_lb.dns_name
}
