variable "environment" {
  type = string
}

variable "vpc_id" {
  type = string
}

variable "aws_key_name" {
  type = string
}

variable "app_server_count" {
  type = string
}

variable "app_server_sg_id" {
  type = string
}

variable "public_subnet_id" {
  type = string
}

variable "lb_sg_id" {
  type = string
}

variable "app_server_root_volume_size" {
  type = string
}

variable "app_server_instance_type" {
  type = string
}

variable "ebs_snapshot_schedule" {
  type = string
}

variable "job_server_count" {
  type = string
}

variable "job_server_sg_id" {
  type = string
}

variable "job_server_instance_type" {
  type = string
}

variable "job_server_root_volume_size" {
  type = string
}

variable "ami_name" {
  type = string
}

variable "glynt_api_instance_profile" {
  type = string
}
