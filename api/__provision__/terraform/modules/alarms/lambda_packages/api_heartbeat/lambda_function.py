import logging
import os

import boto3
from botocore.vendored import requests

logger = logging.getLogger()
logger.setLevel(logging.INFO)

ENV = os.environ['Environment']
METRIC_NAME = os.environ['MetricName']
NAMESPACE = os.environ['Namespace']

API_BASE_URLS = {
    'sandbox': 'https://api-sandbox.glynt.ai/v6',
    'stage': 'https://api-stage.glynt.ai/v6',
    'production': 'https://api.glynt.ai/v6'
}


def lambda_handler(event, context):
    """Makes a call to the glynt api and publishes a heartbeat metric. Metric
    fails if either the request fails outright or the endpoint returns a 5xx
    status code."""
    logger.info(event)
    metric_value = get_api_heartbeat_metric()
    publish_api_heartbeat_metric(metric_value)


def get_api_heartbeat_metric():
    logger.info("Pinging glynt api at: {}".format(API_BASE_URLS[ENV]))
    try:
        resp = requests.get(API_BASE_URLS[ENV], verify=False)
    except Exception as err:
        logger.error("Exception raised while making request: {}".format(err))
        value = 0
    else:
        logger.info("Api responded with a {} status code".format(resp.status_code))
        if 499 < resp.status_code < 600:
            value = 0
        else:
            value = 1
    return value


def publish_api_heartbeat_metric(value):
    metric_data = [
        {
            'MetricName': METRIC_NAME,
            'Unit': 'Count',
            'Value': value
        }
    ]
    logger.info("Publishing metric data: {}".format(metric_data))
    boto3.client('cloudwatch').put_metric_data(
        MetricData=metric_data,
        Namespace=NAMESPACE
    )
