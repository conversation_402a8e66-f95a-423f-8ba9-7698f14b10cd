# App Server Alarms

data "aws_sns_topic" "alarm_status_change" {
  name = "${var.environment}-trigger-notification-when-alarm-state-change" # from shared infrastructure
}

resource "aws_cloudwatch_metric_alarm" "app_server_cpu" {
  alarm_name          = "Glynt Api App Server (${var.environment}) - cpu usage"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "1"
  metric_name         = "cpu_usage_active"
  namespace           = "glynt-api-${var.environment}-app-server"
  period              = "300"
  statistic           = "Average"
  threshold           = "90"
  alarm_description   = "Triggers when cpu average usage goes over 90% for one period of 5 minutes."
  ok_actions          = [data.aws_sns_topic.alarm_status_change.arn]
  treat_missing_data  = "notBreaching"

  dimensions = {
    cpu  = "cpu-total"
    host = "${element(split(".", var.app_server_private_dns), 0)}"
  }
}

resource "aws_cloudwatch_metric_alarm" "app_server_disk" {
  alarm_name          = "Glynt Api App Server (${var.environment}) - disk space"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "1"
  metric_name         = "disk_used_percent"
  namespace           = "glynt-api-${var.environment}-app-server"
  period              = "60"
  statistic           = "Average"
  threshold           = "90"
  alarm_description   = "Triggers when disk usage goes over 90% for one period of 1 minute."
  ok_actions          = [data.aws_sns_topic.alarm_status_change.arn]
  treat_missing_data  = "notBreaching"

  dimensions = {
    path   = "/"
    host   = "${element(split(".", var.app_server_private_dns), 0)}"
    device = "xvda1" # could change if the ami changes
    fstype = "ext4"
  }
}

resource "aws_cloudwatch_metric_alarm" "app_server_system_status" {
  alarm_name          = "Glynt Api App Server (${var.environment}) - system status"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "1"
  metric_name         = "StatusCheckFailed_System"
  namespace           = "AWS/EC2"
  period              = "60"
  statistic           = "Minimum"
  threshold           = "0.0"
  alarm_description   = "Triggers when the aws system check fails. Attempts auto-recovery when triggered."
  ok_actions          = [data.aws_sns_topic.alarm_status_change.arn]
  alarm_actions       = ["arn:aws:automate:${var.aws_region}:ec2:recover"]
  treat_missing_data  = "notBreaching"

  dimensions = {
    InstanceId = var.app_server_id
  }
}

# Jobs Server Alarms

resource "aws_cloudwatch_metric_alarm" "job_server_cpu" {
  alarm_name          = "Glynt Api Job Server (${var.environment}) - cpu usage"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "1"
  metric_name         = "cpu_usage_active"
  namespace           = "glynt-api-${var.environment}-job-server"
  period              = "300"
  statistic           = "Average"
  threshold           = "90"
  alarm_description   = "Triggers when cpu usage goes over 90% for one period of 5 minutes."
  ok_actions          = [data.aws_sns_topic.alarm_status_change.arn]
  treat_missing_data  = "notBreaching"

  dimensions = {
    cpu  = "cpu-total"
    host = "${element(split(".", var.job_server_private_dns), 0)}"
  }
}

resource "aws_cloudwatch_metric_alarm" "job_server_disk" {
  alarm_name          = "Glynt Api Job Server (${var.environment}) - disk space"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "1"
  metric_name         = "disk_used_percent"
  namespace           = "glynt-api-${var.environment}-job-server"
  period              = "60"
  statistic           = "Average"
  threshold           = "90"
  alarm_description   = "Triggers when disk usage goes over 90% for one period of 5 minutes."
  ok_actions          = [data.aws_sns_topic.alarm_status_change.arn]
  treat_missing_data  = "notBreaching"

  dimensions = {
    path   = "/"
    host   = "${element(split(".", var.job_server_private_dns), 0)}"
    device = "nvme0n1p1" # could change if the ami changes
    fstype = "ext4"
  }
}

resource "aws_cloudwatch_metric_alarm" "job_server_system_status" {
  alarm_name          = "Glynt Api Job Server (${var.environment}) - system status"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "1"
  metric_name         = "StatusCheckFailed_System"
  namespace           = "AWS/EC2"
  period              = "60"
  statistic           = "Minimum"
  threshold           = "0.0"
  alarm_description   = "Triggers when the aws system check fails. Attempts auto-recovery when triggered."
  ok_actions          = [data.aws_sns_topic.alarm_status_change.arn]
  alarm_actions       = ["arn:aws:automate:${var.aws_region}:ec2:recover"]
  treat_missing_data  = "notBreaching"

  dimensions = {
    InstanceId = var.job_server_id
  }
}


# Api Alarms

resource "aws_cloudwatch_metric_alarm" "tokenized_job_failure_rate_spike" {
  alarm_name          = "Tokenized Job Failures Spike (${var.environment})"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "1"
  metric_name         = "TokenizedStatusCount-Failed"
  namespace           = "glynt-api-${var.environment}"
  period              = "180"
  statistic           = "Sum"
  threshold           = "1"
  alarm_description   = "Triggers when tokenized job failures spike to above the threshold during the last three minute period."
  treat_missing_data  = "notBreaching"
}

resource "aws_cloudwatch_metric_alarm" "tokenized_job_failure_rate_sustained" {
  alarm_name          = "Tokenized Job Failures Sustained (${var.environment})"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "5"
  datapoints_to_alarm = "4"
  threshold           = ".10"
  alarm_description   = "Triggers when tokenized jobs fail at a rate of 10 percent for the majority of the last 5 periods of 3 minutes."
  treat_missing_data  = "notBreaching"

  metric_query {
    id = "failed"
    metric {
      metric_name = "TokenizedStatusCount-Failed"
      namespace   = "glynt-api-${var.environment}"
      period      = "180"
      stat        = "Sum"
    }
  }

  metric_query {
    id = "succeeded"
    metric {
      metric_name = "TokenizedStatusCount-Success"
      namespace   = "glynt-api-${var.environment}"
      period      = "180"
      stat        = "Sum"
    }
  }

  metric_query {
    id          = "failure_rate"
    expression  = "failed / (failed + succeeded)"
    return_data = "true"
  }
}

resource "aws_cloudwatch_metric_alarm" "extraction_job_failure_rate_spike" {
  alarm_name          = "Extraction Job Failures Spike (${var.environment})"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "1"
  metric_name         = "ExtractionStatusCount-Failed"
  namespace           = "glynt-api-${var.environment}"
  period              = "180"
  statistic           = "Sum"
  threshold           = "1"
  alarm_description   = "Triggers when job failures spike to above the threshold during the last three minute period."
  treat_missing_data  = "notBreaching"
}

resource "aws_cloudwatch_metric_alarm" "extraction_job_failure_rate_sustained" {
  alarm_name          = "Extraction Job Failures Sustained (${var.environment})"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "5"
  datapoints_to_alarm = "4"
  threshold           = ".10"
  alarm_description   = "Triggers when extraction jobs fail at a rate of 10 percent for the majority of the last 5 periods of 3 minutes."
  treat_missing_data  = "notBreaching"

  metric_query {
    id = "failed"
    metric {
      metric_name = "ExtractionStatusCount-Failed"
      namespace   = "glynt-api-${var.environment}"
      period      = "180"
      stat        = "Sum"
    }
  }

  metric_query {
    id = "succeeded"
    metric {
      metric_name = "ExtractionStatusCount-Success"
      namespace   = "glynt-api-${var.environment}"
      period      = "180"
      stat        = "Sum"
    }
  }

  metric_query {
    id          = "failure_rate"
    expression  = "failed / (failed + succeeded)"
    return_data = "true"
  }
}

resource "aws_cloudwatch_metric_alarm" "celery_extraction_job_queue_size" {
  alarm_name          = "Extraction Job Queue Size (${var.environment})"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "1"
  metric_name         = "glynt_core_extract-QueueCount"
  namespace           = "glynt-api-${var.environment}"
  period              = "60"
  statistic           = "Average"
  threshold           = "10000"
  alarm_description   = "Triggers when the extractions jobs queue gets slammed with over 10000 jobs at once."
  ok_actions          = [data.aws_sns_topic.alarm_status_change.arn]
  treat_missing_data  = "notBreaching"
}

resource "aws_cloudwatch_metric_alarm" "extraction_runtime" {
  alarm_name          = "Extraction Job Runtime (${var.environment})"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "1"
  metric_name         = "ExtractionRuntime"
  namespace           = "glynt-api-${var.environment}"
  period              = "180"
  statistic           = "Average"
  threshold           = "9000"
  alarm_description   = "Triggers when extraction jobs start taking more than fifteen minutes during the last three minute period."
  treat_missing_data  = "notBreaching"
  ok_actions          = [data.aws_sns_topic.alarm_status_change.arn]
}

resource "aws_cloudwatch_metric_alarm" "training_revision_job_failure_rate_spike" {
  alarm_name          = "Training Revision Job Failures Spike (${var.environment})"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "1"
  metric_name         = "TrainingRevisionStatusCount-Failed"
  namespace           = "glynt-api-${var.environment}"
  period              = "180"
  statistic           = "Sum"
  threshold           = "1"
  alarm_description   = "Triggers when job failures spike to above the threshold during the last three minute period."
  treat_missing_data  = "notBreaching"
}

resource "aws_cloudwatch_metric_alarm" "training_job_failure_rate_sustained" {
  alarm_name          = "Training Job Failures Sustained (${var.environment})"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "5"
  datapoints_to_alarm = "4"
  threshold           = ".10"
  alarm_description   = "Triggers when training jobs fail at a rate of 10 percent for the majority of the last 5 periods of 3 minutes."
  treat_missing_data  = "notBreaching"

  metric_query {
    id = "failed"
    metric {
      metric_name = "TrainingRevisionStatusCount-Failed"
      namespace   = "glynt-api-${var.environment}"
      period      = "180"
      stat        = "Sum"
    }
  }

  metric_query {
    id = "succeeded"
    metric {
      metric_name = "TrainingRevisionStatusCount-Success"
      namespace   = "glynt-api-${var.environment}"
      period      = "180"
      stat        = "Sum"
    }
  }

  metric_query {
    id          = "failure_rate"
    expression  = "failed / (failed + succeeded)"
    return_data = "true"
  }
}

resource "aws_cloudwatch_metric_alarm" "training_revision_runtime" {
  alarm_name          = "Training Revision Runtime (${var.environment})"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "1"
  metric_name         = "TrainingRevisionRuntime"
  namespace           = "glynt-api-${var.environment}"
  period              = "180"
  statistic           = "Average"
  threshold           = "3000"
  alarm_description   = "Triggers when training revision jobs start taking more that the threshold for one period of five minutes."
  treat_missing_data  = "notBreaching"
  ok_actions          = [data.aws_sns_topic.alarm_status_change.arn]
}

resource "aws_cloudwatch_metric_alarm" "database_heartbeat" {
  alarm_name          = "Glynt DB Heartbeat (${var.environment})"
  comparison_operator = "LessThanThreshold"
  evaluation_periods  = "1"
  metric_name         = "database-available"
  namespace           = "glynt-api-${var.environment}"
  period              = "60"
  statistic           = "Maximum"
  threshold           = "1"
  alarm_description   = "Triggers when the glynt db is unreachable for one minute."
  treat_missing_data  = "notBreaching"
  ok_actions          = [data.aws_sns_topic.alarm_status_change.arn]
}

resource "aws_cloudwatch_metric_alarm" "boxcars_heartbeat" {
  alarm_name          = "Boxcars Heartbeat (${var.environment})"
  comparison_operator = "LessThanThreshold"
  evaluation_periods  = "1"
  metric_name         = "boxcars-${var.environment}-heartbeat"
  namespace           = "glynt-api-${var.environment}"
  period              = "60"
  statistic           = "Maximum"
  threshold           = "1"
  alarm_description   = "Triggers when boxcars app server is unreachable for one minute."
  treat_missing_data  = "notBreaching"
  ok_actions          = [data.aws_sns_topic.alarm_status_change.arn]
}

resource "aws_cloudwatch_metric_alarm" "api_heartbeat" {
  alarm_name          = "Glynt Api Heartbeat (${var.environment})"
  comparison_operator = "LessThanThreshold"
  evaluation_periods  = "1"
  metric_name         = "glynt_api_${var.environment}_heartbeat"
  namespace           = "glynt-api-${var.environment}"
  period              = "60"
  statistic           = "Maximum"
  threshold           = "1"
  alarm_description   = "Triggers when the glynt api is unreachable for one minute."
  treat_missing_data  = "notBreaching"
  ok_actions          = [data.aws_sns_topic.alarm_status_change.arn]
}

resource "aws_cloudwatch_metric_alarm" "request_rate_5xx" {
  alarm_name          = "Glynt Api (${var.environment}) - 5xx request rate"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "1"
  metric_name         = "5xxRequests"
  namespace           = "glynt-api-${var.environment}"
  period              = "60"
  statistic           = "Average"
  threshold           = "1"
  alarm_description   = "Triggers when the rate of 5xx responses reaches above 1"
  treat_missing_data  = "notBreaching"
  ok_actions          = [data.aws_sns_topic.alarm_status_change.arn]
}


# Log filter metric alarms

resource "aws_cloudwatch_log_group" "api_job_server_celery" {
  name              = "/glynt/api/${var.environment}/job_servers/celery"
  retention_in_days = 14
}

resource "aws_cloudwatch_log_metric_filter" "celery_worker_connection_errors" {
  name           = "Celery Worker Throwing Connection Related Exceptions"
  pattern        = "?ConnectionResetError ?ConnectionError"
  log_group_name = aws_cloudwatch_log_group.api_job_server_celery.name

  metric_transformation {
    name      = "celery-worker-connection-errors"
    namespace = "glynt-api-${var.environment}"
    value     = "1"
  }
}

resource "aws_cloudwatch_metric_alarm" "celery_worker_connection_errors" {
  alarm_name          = "Glynt Api (${var.environment}) - Celery Worker Connection Errors - Sustaining"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "3"
  datapoints_to_alarm = "1"
  metric_name         = aws_cloudwatch_log_metric_filter.celery_worker_connection_errors.name
  namespace           = "glynt-api-${var.environment}"
  period              = "3600"
  statistic           = "Sum"
  threshold           = "1"
  alarm_description   = "A celery worker has an elevated level of connection related errors and needs immediate attention. -- tag: skip_nagging_notifier"
  treat_missing_data  = "notBreaching"
  ok_actions          = [data.aws_sns_topic.alarm_status_change.arn]
  alarm_actions       = [data.aws_sns_topic.alarm_status_change.arn]
}

resource "aws_cloudwatch_log_metric_filter" "celery_worker_reboot" {
  name           = "Celery Worker Reboot"
  pattern        = "\"Worker: Preparing bootsteps.\""
  log_group_name = aws_cloudwatch_log_group.api_job_server_celery.name

  metric_transformation {
    name      = "celery-worker-reboot"
    namespace = "glynt-api-${var.environment}"
    value     = "1"
  }
}

resource "aws_cloudwatch_metric_alarm" "celery_worker_reboot_sustaining" {
  alarm_name          = "Glynt Api (${var.environment}) - Celery Worker Reboot - Sustaining"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "1440"
  datapoints_to_alarm = "144"
  metric_name         = aws_cloudwatch_log_metric_filter.celery_worker_reboot.name
  namespace           = "glynt-api-${var.environment}"
  period              = "60"
  statistic           = "Sum"
  threshold           = "1"
  alarm_description   = "Alarm triggers when any of the Glynt API celery worker logs show signs of excessive and automatic rebooting. A manual restart and an investigation should be carried out whenever this state is reached by any worker. You may have to use log insights to figure out exactly which worker is exhibiting this symptom. -- tag: skip_nagging_notifier"
  treat_missing_data  = "notBreaching"
  ok_actions          = [data.aws_sns_topic.alarm_status_change.arn]
  alarm_actions       = [data.aws_sns_topic.alarm_status_change.arn]
}


# Lambda

resource "aws_iam_policy" "put_metric_data" {
  name = "glynt_${var.environment}_cloudwatch_put_metric_data"
  path = "/"

  policy = <<EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": "cloudwatch:PutMetricData",
            "Resource": "*"
        }
    ]
}
EOF
}

data "aws_iam_role" "lambda_exec" {
  name = "glynt_${var.environment}_lambda_function_execution_role"
}

resource "aws_iam_role_policy_attachment" "publish_metric_policy" {
  role       = data.aws_iam_role.lambda_exec.name
  policy_arn = aws_iam_policy.put_metric_data.arn
}

# This is changing to a data block, resource has been imported into the glynt-infrastructure repo
/*
data "archive_file" "api_heartbeat_lambda_archive" {
  type        = "zip"
  source_file = "${path.module}/lambda_packages/api_heartbeat/lambda_function.py"
  output_path = "api_heartbeat_lambda_package.zip"
}

resource "aws_lambda_function" "api_heartbeat" {
  function_name    = "glynt_api_${var.environment}_heartbeat"
  filename         = data.archive_file.api_heartbeat_lambda_archive.output_path
  handler          = "lambda_function.lambda_handler"
  role             = data.aws_iam_role.lambda_exec.arn
  source_code_hash = data.archive_file.api_heartbeat_lambda_archive.output_base64sha256
  runtime          = "python3.7"

  environment {
    variables = {
      Environment = "${var.environment}",
      MetricName  = "glynt_api_${var.environment}_heartbeat",
      Namespace   = "glynt-api-${var.environment}"
    }
  }
  depends_on = [aws_cloudwatch_log_group.api_heartbeat_log]
}
*/

data "aws_lambda_function" "api_heartbeat" {
  function_name    = "glynt_api_${var.environment}_heartbeat"
}

resource "aws_cloudwatch_log_group" "api_heartbeat_log" {
  name              = "/aws/lambda/glynt_api_${var.environment}_heartbeat"
  retention_in_days = 14
}


# Actions

data "aws_lambda_function" "action_notifier_function" {
  function_name = "${var.environment}_cloudwatch_action_notifier" # from shared infratructure
}

resource "aws_cloudwatch_event_rule" "instance_state_change" {
  name        = "glynt_api_ec2_instance_state_changed"
  description = "Triggers a slack notification via lambda function whenever an Glynt API ec2 instance is being turned off, terminated, or is booting up."

  event_pattern = <<PATTERN
{
  "source": [
    "aws.ec2"
  ],
  "detail-type": [
    "EC2 Instance State-change Notification"
  ],
  "detail": {
    "state": [
      "shutting-down",
      "stopping",
      "running"
    ],
    "instance-id": [
      "${var.job_server_id}",
      "${var.app_server_id}"
    ]
  }
}
PATTERN
}

resource "aws_cloudwatch_event_target" "ec2_state_change_lambda_notifier" {
  rule      = aws_cloudwatch_event_rule.instance_state_change.name
  target_id = "TriggerLambda"
  arn       = data.aws_lambda_function.action_notifier_function.arn
}

resource "aws_lambda_permission" "allow_state_change_action" {
  action        = "lambda:InvokeFunction"
  function_name = data.aws_lambda_function.action_notifier_function.function_name
  principal     = "events.amazonaws.com"
  source_arn    = aws_cloudwatch_event_rule.instance_state_change.arn
}

resource "aws_cloudwatch_event_rule" "periodic_api_heartbeat_trigger" {
  name                = "glynt_api_${var.environment}_heartbeat_trigger"
  description         = "Triggers a lambda function periodically which pings the api and publishes a heartbeat metric."
  schedule_expression = "rate(1 minute)"
}

resource "aws_cloudwatch_event_target" "api_heartbeat_lambda" {
  rule      = aws_cloudwatch_event_rule.periodic_api_heartbeat_trigger.name
  target_id = "TriggerLambda"
  arn       = data.aws_lambda_function.api_heartbeat.arn
}

resource "aws_lambda_permission" "allow_api_heartbeat_trigger" {
  statement_id  = "AllowExecutionFromCloudWatchEvent"
  action        = "lambda:InvokeFunction"
  function_name = data.aws_lambda_function.api_heartbeat.function_name
  principal     = "events.amazonaws.com"
  source_arn    = aws_cloudwatch_event_rule.periodic_api_heartbeat_trigger.arn
}
