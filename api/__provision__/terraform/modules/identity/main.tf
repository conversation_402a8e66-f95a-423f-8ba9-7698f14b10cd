locals {
  s3_bucket_prefix = "glynt.api.${var.environment}"
}

# resources created in glynt-infrastructure/glynt-services
data "aws_s3_bucket" "static_enrichment_bucket" {
  bucket = "glynt-${var.environment}-static-enrichment-tables"
}

data "aws_s3_bucket" "packager_bucket" {
  bucket = "glynt-${var.environment}-packager"
}
# This user is used for the s3 storage client in the API. Access keys are
# created for this user and used to create the presigned urls the API passes
# around. For more information, see the S3StorageClient docstring. This user is
# only granted the permissions necessary to enable that functionality in an
# effort to respect a minimum viable permissions model.
resource "aws_iam_user" "storage_user" {
  name = "glynt_api_${var.environment}_storage_user"
}

resource "aws_iam_role" "glynt_api_role" {
  name = "glynt_api_${var.environment}_role"

  assume_role_policy = <<EOF
{
    "Version": "2008-10-17",
    "Statement": [
        {
            "Sid": "",
            "Effect": "Allow",
            "Principal": {
                "Service": [
                    "ec2.amazonaws.com"
                ]
            },
            "Action": "sts:AssumeRole"
        }
    ]
}
EOF
}

# The S3 policy and it's attachment are separate from the main glynt_api_role_policy
# because these policies need to also be granted to the storage_user so that the
# user can manage presigned urls.
resource "aws_iam_policy" "glynt_api_s3_policy" {
  name = "glynt_api_${var.environment}_s3_policy"

  policy = <<EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "ManageAPIBuckets",
            "Effect": "Allow",
            "Action": [
                "s3:CreateBucket",
                "s3:ListBucket",
                "s3:DeleteBucket",
                "s3:GetBucketCORS",
                "s3:PutBucketCORS",
                "s3:GetBucketPolicy",
                "s3:PutBucketPolicy",
                "s3:GetEncryptionConfiguration",
                "s3:PutEncryptionConfiguration",
                "s3:PutBucketPublicAccessBlock",
                "s3:GetBucketPublicAccessBlock"
            ],
            "Resource": [
                "arn:aws:s3:::${local.s3_bucket_prefix}.*",
                "${data.aws_s3_bucket.packager_bucket.arn}/*"
                ]
        },
        {
            "Sid": "ManageAPIBucketObjects",
            "Effect": "Allow",
            "Action": [
                "s3:PutObject",
                "s3:GetObject",
                "s3:DeleteObject"
            ],
            "Resource": [
                "arn:aws:s3:::${local.s3_bucket_prefix}.*/*",
                "${data.aws_s3_bucket.packager_bucket.arn}/*"]
        },
        {
            "Sid": "ListBuckets",
            "Effect": "Allow",
            "Action": "s3:ListAllMyBuckets",
            "Resource": "*"
        },
        {
            "Sid": "ReadAccessToStaticEnrichBuckets",
            "Effect": "Allow",
            "Action": [
                "s3:GetObject"
            ],
            "Resource": "${data.aws_s3_bucket.static_enrichment_bucket.arn}/*"
        }
    ]
}
EOF
}

resource "aws_iam_role_policy_attachment" "s3_policy_to_glynt_api_role" {
  role       = aws_iam_role.glynt_api_role.name
  policy_arn = aws_iam_policy.glynt_api_s3_policy.arn
}

resource "aws_iam_user_policy_attachment" "s3_policy_to_storage_user" {
  user       = aws_iam_user.storage_user.name
  policy_arn = aws_iam_policy.glynt_api_s3_policy.arn
}

resource "aws_iam_access_key" "storage_user_access_key" {
  user = aws_iam_user.storage_user.name
}

resource "aws_secretsmanager_secret" "storage_creds" {
  name                    = "glynt_${var.environment}_storage_creds"
  recovery_window_in_days = 0
}

resource "aws_secretsmanager_secret_version" "storage_creds" {
  secret_id = aws_secretsmanager_secret.storage_creds.id
  secret_string = jsonencode({
    "access_key" = aws_iam_access_key.storage_user_access_key.id
    "secret_key" = aws_iam_access_key.storage_user_access_key.secret
  })
}

resource "aws_iam_policy" "glynt_api_role_policy" {
  name = "glynt_api_${var.environment}_role_policy"

  policy = <<EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "ManageCloudwatchAlarms",
            "Effect": "Allow",
            "Action": [
                "cloudwatch:PutMetricAlarm",
                "cloudwatch:PutDashboard",
                "cloudwatch:DeleteDashboards",
                "cloudwatch:PutMetricData",
                "cloudwatch:EnableAlarmActions",
                "cloudwatch:DeleteAlarms",
                "cloudwatch:DisableAlarmActions",
                "cloudwatch:SetAlarmState"
            ],
            "Resource": "*"
        },
        {
            "Sid": "PublishLogs",
            "Effect": "Allow",
            "Action": [
                "logs:CreateLogGroup",
                "logs:CreateLogStream",
                "logs:DescribeLogGroups",
                "logs:DescribeLogStreams",
                "logs:PutLogEvents",
                "logs:GetLogEvents",
                "logs:FilterLogEvents"
            ],
            "Resource": "arn:aws:logs:*:*:log-group:*"
        },
        {
            "Sid": "AccessSecrets",
            "Effect": "Allow",
            "Action": ["secretsmanager:GetSecretValue"],
            "Resource": "${aws_secretsmanager_secret.storage_creds.arn}"
        },
        {
            "Sid": "TransformersBetaECR",
            "Effect": "Allow",
            "Action": [
                "ecr:GetAuthorizationToken",
                "ecr:GetDownloadURLForLayer",
                "ecr:BatchGetImage"
            ],
            "Resource": "*"
        }
    ]
}
EOF
}

resource "aws_iam_policy" "glynt_api_parameter_store_policy" {

  name = "glynt_api_${var.environment}_parameter_store_policy"

  policy = <<EOF
{
   "Version":"2012-10-17",
   "Statement": [
        {
            "Effect": "Deny",
            "Action": [
                "ssm:DeleteParameter",
                "ssm:DeleteParameters"
            ],
            "Resource": "*"
        },
        {
            "Effect": "Allow",
            "Action": [
                "ssm:GetParametersByPath",
                "ssm:GetParameters",
                "ssm:GetParameter",
                "ssm:GetParameterHistory"
            ],
            "Resource": [
                "arn:aws:ssm:${var.aws_region}:*:parameter/glynt/shared/${var.environment}/*",
                "arn:aws:ssm:${var.aws_region}:*:parameter/glynt/api/${var.environment}/*",
                "arn:aws:ssm:${var.aws_region}:*:parameter/glynt/validator/${var.environment}/*",
                "arn:aws:ssm:${var.aws_region}:*:parameter/glynt/${var.environment}/glynt-workloads-base/*"
            ]
        },
        {
            "Effect": "Allow",
            "Action": [
                "ssm:PutParameter"
            ],
            "Resource": [
                "arn:aws:ssm:${var.aws_region}:*:parameter/glynt/${var.environment}/glynt-workloads-base/uipath_api_token_cache"
            ]
        }

    ]
}
EOF
}

resource "aws_iam_policy" "glynt_api_sns_policy" {

  name = "glynt_api_${var.environment}_sns_policy"

  policy = <<EOF
{
   "Version":"2012-10-17",
   "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "sns:Publish"
            ],
            "Resource": "*"
        }
    ]
}
EOF
}

resource "aws_iam_policy" "glynt_api_lambda_policy" {

  name = "glynt_api_${var.environment}_lambda_policy"

  policy = <<EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "LambdaPolicy",
            "Effect": "Allow",
            "Action": "lambda:InvokeFunction",
            "Resource": "arn:aws:lambda:${var.aws_region}:*:function:*"
        }
    ]
}
EOF
}

resource "aws_iam_role_policy_attachment" "glynt_api_sns_message_policy_attachment" {
  role       = aws_iam_role.glynt_api_role.name
  policy_arn = aws_iam_policy.glynt_api_sns_policy.arn
}

resource "aws_iam_role_policy_attachment" "glynt_api_parameter_store_policy_attachment" {
  role       = aws_iam_role.glynt_api_role.name
  policy_arn = aws_iam_policy.glynt_api_parameter_store_policy.arn
}

resource "aws_iam_role_policy_attachment" "glynt_api_role_policy_attachment" {
  role       = aws_iam_role.glynt_api_role.name
  policy_arn = aws_iam_policy.glynt_api_role_policy.arn
}

resource "aws_iam_role_policy_attachment" "glynt_api_lambda_policy_attachment" {
  role       = aws_iam_role.glynt_api_role.name
  policy_arn = aws_iam_policy.glynt_api_lambda_policy.arn
}

resource "aws_iam_instance_profile" "glynt_api_instance_profile" {
  name = "glynt_api_${var.environment}_instance_profile"
  role = aws_iam_role.glynt_api_role.name
}

resource "aws_iam_policy" "api_dev_ecr" {
  name = "glynt_api_${var.environment}_api_dev_ecr_policy"

  policy = <<EOF
{
   "Version":"2012-10-17",
   "Statement":[
        {
            "Effect": "Allow",
            "Action": [
                "ecr:GetAuthorizationToken",
                "ecr:BatchCheckLayerAvailability",
                "ecr:GetDownloadUrlForLayer",
                "ecr:GetRepositoryPolicy",
                "ecr:DescribeRepositories",
                "ecr:ListImages",
                "ecr:DescribeImages",
                "ecr:BatchGetImage",
                "ecr:GetLifecyclePolicy",
                "ecr:GetLifecyclePolicyPreview",
                "ecr:ListTagsForResource",
                "ecr:DescribeImageScanFindings"
            ],
            "Resource": "arn:aws:ecr:us-west-1:700095865679:repository/glynt/api/runtests"
        }
    ]
}
EOF
}

resource "aws_iam_group" "api_dev" {
  name = "api_dev_${var.environment}"
}

resource "aws_iam_group_policy_attachment" "api_dev_amazon_ec2_full_access" {
  group      = aws_iam_group.api_dev.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonEC2FullAccess"
}

resource "aws_iam_group_policy_attachment" "api_dev_amazon_s3_full_access" {
  group      = aws_iam_group.api_dev.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonS3FullAccess"
}

resource "aws_iam_group_policy_attachment" "api_dev_cloudwatch_full_access" {
  group      = aws_iam_group.api_dev.name
  policy_arn = "arn:aws:iam::aws:policy/CloudWatchFullAccess"
}

resource "aws_iam_group_policy_attachment" "api_dev_ecr_pull_image_access" {
  group      = aws_iam_group.api_dev.name
  policy_arn = aws_iam_policy.api_dev_ecr.arn
}
