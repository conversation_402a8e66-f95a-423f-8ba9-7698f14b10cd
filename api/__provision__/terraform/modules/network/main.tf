data "aws_region" "current" {}

data "aws_subnet" "public" {
  filter {
    name   = "tag:Name"
    values = ["glynt-${var.environment}-public-subnet"]
  }
}

data "aws_vpc" "public_vpc" {
  filter {
    name   = "tag:Name"
    values = ["glynt-${var.environment}-vpc"]
  }
}

resource "aws_default_security_group" "public_vpc_default" {
  vpc_id = data.aws_vpc.public_vpc.id
}

resource "aws_key_pair" "ssh_key" {
  key_name   = "glynt-api-${var.environment}"
  public_key = var.ssh_public_key
}

resource "aws_security_group" "lb_sg" {
  name   = "Glynt API Load Balancer SG (${var.environment})"
  vpc_id = data.aws_subnet.public.vpc_id

  ingress {
    from_port   = 80 # HTTP
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    from_port   = 443 # HTTPS
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    from_port       = 0 # Default egress rule
    to_port         = 0
    protocol        = "-1"
    security_groups = [aws_security_group.app_server_sg.id]
  }
}

resource "aws_security_group" "app_server_sg" {
  name   = "Glynt API App Servers (${var.environment})"
  vpc_id = data.aws_subnet.public.vpc_id
}

resource "aws_security_group_rule" "app_server_egress" {
  security_group_id = aws_security_group.app_server_sg.id
  from_port         = 0
  to_port           = 0
  protocol          = "-1"
  type              = "egress"
  cidr_blocks       = ["0.0.0.0/0"]
}

resource "aws_security_group_rule" "app_server_ssh" {
  security_group_id = aws_security_group.app_server_sg.id
  from_port         = 22
  to_port           = 22
  protocol          = "tcp"
  type              = "ingress"
  cidr_blocks       = ["0.0.0.0/0"]
}

resource "aws_security_group_rule" "app_server_ingress" {
  security_group_id        = aws_security_group.app_server_sg.id
  from_port                = 80
  to_port                  = 80
  protocol                 = "tcp"
  type                     = "ingress"
  source_security_group_id = aws_security_group.lb_sg.id
}

resource "aws_security_group_rule" "self_ingress_https" {
  security_group_id        = aws_security_group.app_server_sg.id
  from_port                = 443
  to_port                  = 443
  protocol                 = "tcp"
  type                     = "ingress"
  source_security_group_id = aws_security_group.app_server_sg.id
}


resource "aws_security_group" "job_server_sg" {
  name   = "Glynt API Job Servers (${var.environment})"
  vpc_id = data.aws_subnet.public.vpc_id

  ingress {
    from_port   = 22 # SSH
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    from_port   = 5555 # Flower (UI web access)
    to_port     = 5555
    description = "Flower"
    protocol    = "tcp"
    cidr_blocks = [data.aws_subnet.public.cidr_block]
  }

  egress {
    from_port   = 0 # Default egress rule
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}

resource "aws_vpc_endpoint" "internal_apigw_vpce" {
  vpc_id              = data.aws_subnet.public.vpc_id
  service_name        = "com.amazonaws.${data.aws_region.current.name}.execute-api"
  vpc_endpoint_type   = "Interface"
  security_group_ids  = [aws_security_group.app_server_sg.id]
  subnet_ids          = [data.aws_subnet.public.id]
  private_dns_enabled = true

  tags = {
    Name = "glynt-${var.environment}-internal-apigw-vpce"
  }
}

resource "aws_ssm_parameter" "internal_apigw_vpce_id_param" {
  name  = "/glynt/api/${var.environment}/internal_apigw_vpce_id"
  type  = "String"
  value = aws_vpc_endpoint.internal_apigw_vpce.id
}
