terraform {
  backend "s3" {
    bucket  = "tfstate.bacalar.wattzon"
    key     = "bacalar/glyntapi/production/main.tfstate"
    region  = "us-west-2"
    encrypt = true
  }
  required_version = ">=0.12.21"
}

variable "environment" {
  default = "production"
}

variable "domain" {
  default = "private.glynt.ai"
}

locals {
  private_dns_zone = "${var.environment}.${var.domain}"
}

variable "ebs_snapshot_schedule" {
  default = "0300;15;US/Pacific;wed,sat"
}

# vvvvv from_env vvvvv

variable "aws_region" {
  type = string
}

# ^^^^^ from_env ^^^^^

provider "aws" {
  region  = var.aws_region
  version = "~> 2.0"
}

module "network" {
  source         = "../../modules/network"
  environment    = var.environment
  ssh_public_key = "${file("files/key.pub")}"
}

module "storage" {
  source      = "../../modules/storage"
  environment = var.environment
}

module "compute" {
  source                        = "../../modules/compute"
  environment                   = var.environment
  ami_name                      = "ubuntu/images/hvm-ssd/ubuntu-focal-20.04-amd64-server-20250508.1"
  ebs_snapshot_schedule         = var.ebs_snapshot_schedule
  aws_key_name                  = module.network.ssh_key_name
  app_server_count              = 4
  app_server_instance_type      = "r5a.xlarge"
  app_server_sg_id              = module.network.app_server_sg_id
  app_server_root_volume_size   = 30
  job_server_count              = 3
  job_server_instance_type      = "c5a.xlarge"
  job_server_sg_id              = module.network.job_server_sg_id
  job_server_root_volume_size   = 16
  lb_sg_id                      = module.network.lb_sg_id
  vpc_id                        = module.network.public_vpc_id
  public_subnet_id              = module.network.public_subnet_id
  glynt_api_instance_profile    = module.identity.glynt_api_instance_profile
}

module "dns" {
  source           = "../../modules/dns"
  environment      = var.environment
  vpc_id           = module.network.public_vpc_id
  private_dns_zone = local.private_dns_zone
  dns_records = list(
    {
      name  = "api-app-server.${local.private_dns_zone}"
      value = module.compute.app_server_private_ip
      type  = "A"
    }
  )
}

locals {
  public_dns_suffix = ""
  public_domain     = "glynt.ai"
}

module "public_dns" {
  source      = "../../modules/public_dns"
  environment = var.environment
  dns_records = list(
    {
      name  = "api${local.public_dns_suffix}.${local.public_domain}"
      value = module.compute.elb_public_dns_name
      type  = "CNAME"
    },
    {
      name  = "flower${local.public_dns_suffix}.${local.public_domain}"
      value = module.compute.elb_public_dns_name
      type  = "CNAME"
    }
  )
}

module "identity" {
  source      = "../../modules/identity"
  environment = var.environment
  aws_region  = var.aws_region
}

module "alarms" {
  source                 = "../../modules/alarms"
  environment            = var.environment
  aws_region             = var.aws_region
  app_server_id          = module.compute.app_server_id
  app_server_private_dns = module.compute.app_server_private_dns
  job_server_id          = module.compute.job_server_id
  job_server_private_dns = module.compute.job_server_private_dns
}
