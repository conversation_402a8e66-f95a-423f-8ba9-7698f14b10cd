# Terraform

The API uses Terraform 0.12.21.

To manually run terraform, first ensure you have the aws CLI installed and
configured to use your credentials for interacting with the environment of your
choice. Numerous permissions are required to run successfully. The `api_dev_<env>`
IAM group should have all needed permissions, and will exist once this
provisioner has run at least once. Adding that group to your user is the
recommended way to add the permissions you need. Also ensure you have terraform
version 0.12.21 installed on your system.

Our glynt.ai ssl certificate must be manually installed in AWS Certificate
Manager for each environment you wish to provision before running terraform.
The SSL private key, cert and cert chain can be found in LastPass. Terraform
will source the cert automatically as long as it is installed in the same account
and region as the desired api environment. The cert is used to offload glynt.ai
ssl certification for the app servers to their elastic load balancer.

1) Copy the `terraform/template.tfvars` at
`terraform/environments/<environment_name>/terraform.tfvars`.

2) Fill out the newly created file with the needed values.

3) `cd` to `/terraform/environments/<env_to_deploy>`.

4) Run `terraform init -upgrade`.

5) Run `terraform plan`. Check that the plan will do what you expect.

6) Run `terraform apply`.

Note that, during CI, these secrets are instead gathered up by concourse via
files in S3 prior to running terraform. See the CI configuration for details.

This terraform configuration does not assign IAM users to any IAM groups it
creates. You must manually add users to the appropriate groups. API developers
should be added to the `api_dev_<env>` IAM groups.
