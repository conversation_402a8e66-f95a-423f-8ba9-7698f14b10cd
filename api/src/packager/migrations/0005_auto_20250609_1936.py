# Generated by Django 2.2.28 on 2025-06-09 19:36

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('packager', '0004_removed_unused_models'),
    ]

    operations = [
        migrations.AlterField(
            model_name='packageentry',
            name='status_in_package',
            field=models.CharField(choices=[('INCLUDED', 'INCLUDED'), ('TRANSMISSION_SUCCESSFUL', 'TRANSMISSION_SUCCESSFUL'), ('TRANSMISSION_FAILED', 'TRANSMISSION_FAILED'), ('EXCLUDED', 'EXCLUDED'), ('EXCLUDED_AS_DUPLICATE', 'EXCLUDED_AS_DUPLICATE'), ('EXCLUDED_BY_STATUS', 'EXCLUDED_BY_STATUS'), ('EXCLUDED_BY_CONFIG', 'EXCLUDED_BY_CONFIG'), ('PENDING', 'PENDING')], default='INCLUDED', max_length=64),
        ),
    ]
