import json
import logging

from django.core.exceptions import ValidationError as DjangoValidationError
from django.http import HttpResponse
from django.utils import timezone
from django_filters.filters import Char<PERSON>ilter
from glynt_schemas.packager.package_attributes import PackageStatus
from glynt_schemas.packager.packager_attributes import PackagerDataOutputFormats
from glynt_schemas.packager.packager_attributes import PackagerStatus
from pydantic import ValidationError as PydanticValidationError
from rest_framework import permissions
from rest_framework import status
from rest_framework.decorators import action
from rest_framework.renderers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework.request import Request
from rest_framework.response import Response

from glynt_api.filters import FilterSet
from glynt_api.mixins import CreatedAtUpdatedAtFilterMixin, LabelFilterMixin, StatusFilterMixin
from glynt_api.pagination import CountChangeLimitOffsetPagination
from glynt_api.renderers import BrowsableAP<PERSON>enderer
from glynt_api.viewsets import ModelViewSet
from organizations.mixins import DataPoolAwareViewSetMixin
from organizations.models import DataPool
from packager.models import (DocumentData, ExtractionBatchData, Package, Packager,
                             DocumentRelationship, DocumentRelationshipType, PackageEntry)
from packager.pydantic_classes import SimulatePackageInput
from packager.serializers import (DocumentDataSerializer, ExtractionBatchDataSerializer, PackageSerializer,
                                  PackagerSerializer, DocumentRelationshipSerializer,
                                  DocumentRelationshipTypeSerializer, PackageEntrySerializer)
from packager.services.coc_report_service import CoCReportService
from packager.services.package_operations_service import PackageOperationsService
from packager.tasks import deliver_package
from packager.utils.file_utils import encode_binary_content_for_api
from packager.utils.naming_utils import get_available_metadata_attributes

logger = logging.getLogger(__name__)


class PackagerFilter(LabelFilterMixin, StatusFilterMixin, CreatedAtUpdatedAtFilterMixin, FilterSet):
    packager_status = CharFilter(method="status_filter", label="packager_status")

    def status_filter(self, queryset, field_name, value):
        """Filter for packagers based on status choices based ontheir string rep"""
        filter_status_values = [status.name for status in PackagerStatus if status.value == value]
        return queryset.filter(packager_status__in=filter_status_values)


class PackagerViewSet(DataPoolAwareViewSetMixin, ModelViewSet, ):
    basic_fields = ["created_at", "updated_at", "url", "obfuscated_id", "uuid", "id", "label",
                    "schedule_cron_expression", "output_format", "packager_schedule_type", "delivery_config",
                    "last_run", "last_package_path", "packager_status", "document_status_filter", "eb_status_filter",
                    "document_id_filter", "eb_id_filter", "packages", "url", "delivery_data_grouping_strategy",
                    "delivery_data_grouping_metadata_key", "s3_bucket", "s3_prefix", "prefix", "region",
                    "delivery_schedule_type", "source_file_naming_config", "processed_data_naming_config",
                    "start_date_filter", "end_date_filter", "deduplication_content_fields",
                    "include_source_files_in_delivery", "include_relationship_details_in_data_export",
                    "exclude_duplicates_from_delivery", "append_coc_fields_to_data", "coc_field_config",
                    "coc_report_fields_config", "coc_report_start_date", "coc_report_end_date",
                    "packaging_begin_date", "packaging_end_date", ]
    basic_views = ["list", "retrieve", "create", "destroy", "partial_update", ]
    queryset = Packager.objects.all()

    model_class = Packager
    pagination_class = CountChangeLimitOffsetPagination
    serializer_class = PackagerSerializer
    permission_classes = [permissions.IsAuthenticated]

    filterset_class = PackagerFilter

    renderer_classes = [JSONRenderer, BrowsableAPIRenderer]

    @action(detail=True, methods=["post"], url_path="chain-of-custody-report")
    def chain_of_custody_report(self, request: Request, data_pools_obfuscated_id=None, obfuscated_id=None):
        """Generate Chain of Custody report using packager's configured date range.

        Body Parameters:
            format (str, optional): Output format - 'csv' or 'json'. Defaults to 'csv'.
            download (bool, optional): If true, returns file as download attachment.
                                     If false, returns data in response body. Defaults to true for CSV, false for JSON.
        """
        try:
            packager = self.get_object()

            data = request.data or {}
            format_param = data.get("format", "csv").lower()
            download_param = data.get("download")

            valid_formats = [PackagerDataOutputFormats.CSV.value, PackagerDataOutputFormats.JSON.value]
            if format_param not in valid_formats:
                return Response({"error": f"Format must be one of: {', '.join(valid_formats)}"},
                                status=status.HTTP_400_BAD_REQUEST)

            # Default download behavior: true for CSV, false for JSON
            if download_param is None:
                download_param = format_param == PackagerDataOutputFormats.CSV.value
            else:
                download_param = str(download_param).lower() in ["true", "1", "yes"]

            data_pool = DataPool.objects.get(id=self._data_pool_id)

            service = CoCReportService(data_pool=data_pool, report_start_date=None, report_end_date=None,
                                       packager=packager)

            if format_param == PackagerDataOutputFormats.JSON.value:
                json_data = service.generate_json_report()
                if download_param:
                    response = HttpResponse(content=json.dumps(json_data, indent=2), content_type="application/json", )
                    response["Content-Disposition"] = (
                        f'attachment; filename="coc_report_{packager.obfuscated_id}_{timezone.now():%Y%m%d}.json"')
                    return response
                else:
                    return Response(json_data, content_type="application/json")
            else:
                csv_data = service.generate_csv_report()
                logger.info(f"packager.coc_report.generated (csv) data_pool_id={data_pool.obfuscated_id} "
                            f"packager_id={packager.obfuscated_id} csv_length={len(csv_data) if csv_data else 0}")

                if download_param:
                    response = HttpResponse(content=csv_data, content_type="text/csv", )
                    response["Content-Disposition"] = (
                        f'attachment; filename="coc_report_{packager.obfuscated_id}_{timezone.now():%Y%m%d}.csv"')
                    return response
                else:
                    return Response({"csv_data": csv_data}, content_type="application/json")

        except Exception as e:
            logger.exception(f"packager.coc_report.error error={str(e)}")
            return Response({"error": "Failed to generate CoC report", "details": str(e)},
                            status=status.HTTP_500_INTERNAL_SERVER_ERROR, )

    @action(detail=True, methods=["post"], url_path="simulate-package")
    def simulate_package(self, request, *args, **kwargs):
        """Preview a package's data based on user-provided filters and configurations.
        This endpoint allows users to see what their data will look like with specific filters
        and configurations before creating an actual package. Does not save any Package or DocumentData.
        Body Parameters:
            document_id_filter (list, optional): List of document IDs to filter by.
            eb_id_filter (list, optional): List of extraction batch IDs to filter by.
            document_status_filter (str, optional): Document status to filter by.
            eb_status_filter (str, optional): Extraction batch status to filter by.
            delivery_data_grouping_strategy (str, optional): Override packager's grouping strategy
            (ALL_IN_ONE, SPLIT_BY_DOC, METADATA_MATCH_AND_MERGE)
            delivery_data_grouping_metadata_key (str, optional): Metadata key for grouping when using
            METADATA_MATCH_AND_MERGE
            output_format (str, optional): Override output format (JSON, CSV)
            source_file_naming_config (dict, optional): Override source file naming config
            processed_data_naming_config (dict, optional): Override processed data naming config
            include_relationship_details_in_data_export (bool, optional): Override
            include_relationship_details_in_data_export

        Returns:
            List of rendered data objects in format [(filename, formatted_data)] or
            [(filename, formatted_data, document_content_list)] if include_source_files_in_delivery is True.
        """
        packager = self.get_object()

        try:
            operations_service = PackageOperationsService(packager)
            input_data = SimulatePackageInput(**request.data)

            result, effective_config = operations_service.simulate_package(input_data)

            api_safe_rendered_items = encode_binary_content_for_api(result.rendered_items)

            return Response({
                "data": api_safe_rendered_items,
                "format": effective_config.output_format,
                "output_format": effective_config.output_format,
                "delivery_data_grouping_strategy": effective_config.delivery_data_grouping_strategy,
                "success": True,
                "errors": [error.dict() for error in result.errors],
            }, status=status.HTTP_200_OK, )

        except PydanticValidationError as e:
            return Response({"error": "Invalid input data", "details": e.errors()}, status=status.HTTP_400_BAD_REQUEST)
        except DjangoValidationError as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        except ValueError as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.exception(f"packager.simulate.error packager_id={packager.obfuscated_id} error={str(e)}")
            return Response({"error": "Internal Server Error"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=True, methods=["post"], url_path="create-package")
    def create_package(self, request, *args, **kwargs):
        """Create a new Package that will contain ExtractionBatchData and DocumentData
        as defined by the Packager configuration
        """
        packager = self.get_object()

        try:
            operations_service = PackageOperationsService(packager)
            document_data_queryset = DocumentData.objects.filter(data_pool=packager.data_pool)

            if packager.document_status_filter:
                document_data_queryset = document_data_queryset.filter(status__in=packager.document_status_filter)

            if packager.document_id_filter:
                document_data_queryset = document_data_queryset.filter(
                    document__obfuscated_id__in=packager.document_id_filter)

            document_data_list = list(document_data_queryset)

            if not document_data_list:
                return Response({"error": "No documents found matching the packager's filters"},
                                status=status.HTTP_400_BAD_REQUEST)

            package = operations_service.create_package(document_data_list=document_data_list)

            return Response(
                {"packager_id": packager.obfuscated_id, "id": package.obfuscated_id, "status": package.status},
                status=status.HTTP_201_CREATED, )

        except ValueError as e:
            logger.error(f"Package creation validation failed for packager {packager.obfuscated_id}: {str(e)}")
            return Response({"error": f"Package creation failed: {str(e)}"}, status=status.HTTP_400_BAD_REQUEST)
        except DjangoValidationError as e:
            logger.error(f"Package creation validation failed for packager {packager.obfuscated_id}: {str(e)}")
            return Response({"error": f"Package creation failed: {str(e)}"}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"Package creation error for packager {packager.obfuscated_id}: {str(e)}")
            return Response({"error": f"Package creation failed: {str(e)}"},
                            status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=True, methods=["get"], url_path="available-attributes")
    def available_attributes(self, request, *args, **kwargs):
        """Retrieve available metadata attributes for the specified Packager."""
        available_attributes = get_available_metadata_attributes()
        return Response(available_attributes)

    @action(detail=True, methods=["post"], url_path="trigger-delivery")
    def trigger_delivery(self, request, *args, **kwargs):
        """Manually trigger delivery for a specific package.
        Body:
        package_id: ID of the package to deliver
        """
        packager = self.get_object()

        try:
            operations_service = PackageOperationsService(packager)

            package, error_response = operations_service.get_package_with_validation(request.data)
            if error_response:
                return error_response

            if package.status != PackageStatus.VERIFIED.name:
                return operations_service.create_error_response(
                    f"Package {request.data.get('package_id')} is not in {PackageStatus.VERIFIED.name} status",
                    status.HTTP_400_BAD_REQUEST)

            deliver_package.delay(package_id=request.data.get("package_id"), data_pool_id=packager.data_pool.id)

            return Response({"message": "Delivery triggered successfully", "package_id": package.obfuscated_id,
                             "packager": packager.obfuscated_id, })

        except Exception as e:
            logger.error(f"trigger_delivery.error packager_id={packager.obfuscated_id} error={str(e)}")
            return operations_service.create_error_response(f"Manual package delivery failed: {str(e)}")

    @action(detail=True, methods=["post"], url_path="preview-package")
    def preview_package(self, request, *args, **kwargs):
        """Preview a package's data as it would appear when delivered.
        This endpoint allows users to see what their data will look like before actual delivery.
        It uses the Package render_package function which handles caching for better performance.
        Query Parameters:
            package_id: ID of the package to preview (required)
            include_relationship_details_in_data_export (bool, optional): Override
            include_relationship_details_in_data_export

        Returns:
            List of rendered data objects in format [(filename, formatted_data)],
            along with duplicate findings.
        """
        packager = self.get_object()

        try:
            operations_service = PackageOperationsService(packager)

            package_id = request.data.get("package_id")
            if not package_id:
                return Response({"error": "package_id is required"}, status=status.HTTP_400_BAD_REQUEST)

            try:
                package = operations_service._get_package_by_id(package_id)
            except Package.DoesNotExist:
                return Response({"error": f"Package {package_id} not found for Packager {packager.obfuscated_id}"},
                                status=status.HTTP_404_NOT_FOUND, )
            except Exception as e:
                return Response({"error": f"Invalid package_id format: {str(e)}"}, status=status.HTTP_400_BAD_REQUEST)

            render_result = operations_service._get_rendered_package_result(package, request.data)
            api_safe_package_items = encode_binary_content_for_api(render_result.rendered_items)

            response_data = {
                "data": api_safe_package_items,
                "format": package.packager.output_format,
                "output_format": package.packager.output_format,
                "delivery_data_grouping_strategy": package.packager.delivery_data_grouping_strategy,
                "success": True,
                "errors": [error.dict() for error in render_result.errors],
            }
            return Response(response_data)

        except Exception as e:
            logger.exception(f"preview_package.error packager_id={packager.obfuscated_id} error={str(e)}")
            return Response({"error": "Failed to generate preview", "details": str(e)},
                            status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=True, methods=["post"], url_path="download-package")
    def download_package(self, request, *args, **kwargs):
        """Download a simulated package or an existing package as a zip file.

        This endpoint has two modes of operation:
        1. If package_id is provided, it will download the exact data from that package (as would be delivered to S3)
        2. If package_id is not provided, it will generate a simulated package based on filter settings

        Body Parameters:
            package_id (str, optional): ID of an existing package to download.
            include_source_files (bool, optional): Whether to include source files in the package.

            For simulated packages (when package_id is not provided):
            document_id_filter (list, optional): List of document IDs to filter by.
            eb_id_filter (list, optional): List of extraction batch IDs to filter by.
            document_status_filter (str, optional): Document status to filter by.
            eb_status_filter (str, optional): Extraction batch status to filter by.
            delivery_data_grouping_strategy (str, optional): Override packager's grouping strategy
            output_format (str, optional): Override output format (JSON, CSV)
            include_relationship_details_in_data_export (bool, optional): Override
            include_relationship_details_in_data_export

        Returns:
            A zip file containing the package data.
        """
        packager = self.get_object()

        try:
            operations_service = PackageOperationsService(packager)

            return operations_service.download_package(request.data)

        except Exception as e:
            logger.error(f"download_package.error packager_id={packager.obfuscated_id} error={str(e)}")
            return Response({"error": f"Failed to download package: {str(e)}"},
                            status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class PackageViewSet(DataPoolAwareViewSetMixin, ModelViewSet):
    queryset = Package.objects.all()
    serializer_class = PackageSerializer
    model_class = Package

    basic_fields = ["created_at", "updated_at", "url", "obfuscated_id", "uuid", "id", "url", "status",
                    "extraction_batch_data", "document_data", ]
    basic_views = ["list", "retrieve", "create", "destroy", "partial_update"]


class DocumentDataViewSet(DataPoolAwareViewSetMixin, ModelViewSet):
    queryset = DocumentData.objects.all()
    serializer_class = DocumentDataSerializer
    model_class = DocumentData
    permission_classes = [permissions.IsAuthenticated]

    basic_fields = ["created_at", "updated_at", "url", "status", "obfuscated_id", "uuid", "id", "document", "package",
                    "transformed_data_urls", "url", "exclusion_reason", "parent_relationship",
                    "source_file_id", "source_file_name", "source_file_md5", "received_at",
                    "source_file_num_pages", "source_file_size", "source_file_detected_language",
                    "is_md5_check_completed", "is_label_check_completed", "is_content_check_completed",
                    "canonical_document_data", ]
    basic_views = ["list", "retrieve", "create", "destroy", "partial_update", ]


class ExtractionBatchDataViewSet(DataPoolAwareViewSetMixin, ModelViewSet):
    queryset = ExtractionBatchData.objects.all()
    serializer_class = ExtractionBatchDataSerializer
    model_class = ExtractionBatchData

    basic_fields = ["created_at", "updated_at", "url", "status", "obfuscated_id", "uuid", "id", "extraction_batch",
                    "package", ]

    basic_views = ["list", "retrieve", "create", "destroy", "partial_update", ]


class DocumentRelationshipViewSet(DataPoolAwareViewSetMixin, ModelViewSet):
    queryset = DocumentRelationship.objects.all()
    serializer_class = DocumentRelationshipSerializer
    model_class = DocumentRelationship

    basic_fields = ["created_at", "updated_at", "url", "obfuscated_id", "uuid", "id", "source_document_data",
                    "target_document_data", "relationship_type", "created_by", "metadata"]

    basic_views = ["list", "retrieve", "create", "destroy", "partial_update"]


class DocumentRelationshipTypeViewSet(DataPoolAwareViewSetMixin, ModelViewSet):
    queryset = DocumentRelationshipType.objects.all()
    serializer_class = DocumentRelationshipTypeSerializer
    model_class = DocumentRelationshipType

    basic_fields = ["created_at", "updated_at", "url", "obfuscated_id", "uuid", "id", "label", "description"]

    basic_views = ["list", "retrieve", "create", "destroy", "partial_update"]


class PackageEntryViewSet(DataPoolAwareViewSetMixin, ModelViewSet):
    queryset = PackageEntry.objects.all()
    serializer_class = PackageEntrySerializer
    model_class = PackageEntry
    permission_classes = [permissions.IsAuthenticated]

    basic_fields = ["created_at", "updated_at", "url", "obfuscated_id", "uuid", "id", "package", "document_data",
                    "status_in_package", "filename_in_package", "exclusion_reason", "delivered_at"]
    basic_views = ["list", "retrieve", "create", "destroy", "partial_update"]
