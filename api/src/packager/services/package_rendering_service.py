"""
Package Rendering Service

This service extracts the complex package rendering logic from the Package model
to improve maintainability and separation of concerns.
"""

import csv
import io
import json
import logging
from datetime import datetime, date
from typing import List, Dict, Any, Optional, Tuple

from django.apps import apps
from django.utils import timezone
from glynt_schemas.packager.package_attributes import PackageStatus
from glynt_schemas.packager.packager_attributes import PackagerGroupingOptions, PackagerDataOutputFormats

from packager.pydantic_classes import PackageRenderResult, DocumentError, RenderedPackageItem, SourceFile
from packager.utils.duplicate_utils import get_document_relationships
from packager.utils.grouping_utils import group_documents
from packager.utils.naming_utils import generate_filename

logger = logging.getLogger(__name__)


class PackageRenderingService:
    """
    Service responsible for rendering package data according to different grouping strategies.

    This service encapsulates the complex logic for:
    - Rendering packages with different grouping strategies
    - Handling document data formatting
    - Managing errors during rendering
    - Applying relationship details and duplicate flags
    """

    def __init__(self, package_instance):
        """
        Initialize the service with a package instance.

        Args:
            package_instance: The Package instance to render data for
        """
        self.package = package_instance
        try:
            self.config = package_instance.get_config()
        except Exception as e:
            logger.error(f"Error getting packager config during initialization: {str(e)}")
            from packager.pydantic_classes import PackagerConfig
            from glynt_schemas.packager.packager_attributes import PackagerDataOutputFormats, PackagerGroupingOptions
            self.config = PackagerConfig(
                output_format=PackagerDataOutputFormats.JSON.name,
                delivery_data_grouping_strategy=PackagerGroupingOptions.SPLIT_BY_DOC.name,
                append_coc_fields_to_data=False,
                coc_field_config=[],
                include_relationship_details_in_data_export=False,
                exclude_duplicates_from_delivery=False,
                include_source_files_in_delivery=False
            )
        self.data_pool = package_instance.data_pool

    def render_package_data(self, document_data_queryset=None, include_document_content=False):
        """
        Main entry point for rendering package data.

        Args:
            document_data_queryset: Optional queryset of DocumentData to render
            include_document_content: Whether to include source files in the package

        Returns:
            PackageRenderResult: Contains rendered items and any errors
        """
        logger.info(f"package.render.start package_id={self.package.obfuscated_id if self.package.pk else 'simulated'}")

        if self.package.pk:
            self.package.last_viewed = timezone.now()
            self.package.save(update_fields=["last_viewed", "updated_at"])

        try:
            queryset_to_use = self._get_document_queryset(document_data_queryset)
            queryset_to_use = self._filter_by_data_pool(queryset_to_use)

            include_document_content = include_document_content or self.config.include_source_files_in_delivery

            return self._render_by_strategy(queryset_to_use, include_document_content)

        except Exception as e:
            logger.exception(
                f"package.render.failed package_id={self.package.obfuscated_id if self.package.pk else 'simulated'} "
                f"error={str(e)}")
            return PackageRenderResult(rendered_items=[], errors=[
                DocumentError(document_id="PACKAGE_RENDER_ERROR", error=f"Error rendering package data: {str(e)}")], )

    def _get_document_queryset(self, document_data_queryset):
        """Get the appropriate document queryset to use for rendering."""
        if document_data_queryset is not None:
            return document_data_queryset

        DocumentData = apps.get_model("packager", "DocumentData")

        return DocumentData.objects.filter(package_entries__package=self.package, data_pool=self.data_pool).distinct()

    def _filter_by_data_pool(self, queryset_to_use):
        """Filter queryset by data pool if needed."""
        try:
            package_data_pool_pk = self.data_pool.id if self.data_pool else None
        except AttributeError:
            package_data_pool_pk = None

        if package_data_pool_pk is None:
            return queryset_to_use

        if isinstance(queryset_to_use, list):
            return [item for item in queryset_to_use if item.data_pool_id == package_data_pool_pk]
        else:
            return queryset_to_use.filter(data_pool_id=package_data_pool_pk)

    def _render_by_strategy(self, queryset_to_use, include_document_content):
        """Render documents according to the configured grouping strategy."""
        grouping_strategy = self.config.delivery_data_grouping_strategy
        metadata_key = self.config.delivery_data_grouping_metadata_key
        output_format = self.config.output_format
        logger.error(f"[_render_by_strategy] Grouping strategy: {grouping_strategy}")

        errors = []
        rendered_items = []

        if grouping_strategy == PackagerGroupingOptions.SPLIT_BY_DOC.name:
            rendered_items, doc_errors = self._render_split_by_doc(queryset_to_use, output_format,
                                                                   include_document_content)
            errors.extend(doc_errors)
        elif grouping_strategy == PackagerGroupingOptions.ALL_IN_ONE.name:
            rendered_items, doc_errors = self._render_all_in_one(queryset_to_use, output_format,
                                                                 include_document_content)
            errors.extend(doc_errors)
        elif grouping_strategy == PackagerGroupingOptions.METADATA_MATCH_AND_MERGE.name:
            rendered_items, doc_errors = self._render_metadata_match_and_merge(queryset_to_use, output_format,
                                                                               metadata_key, include_document_content)
            errors.extend(doc_errors)
        else:
            error_msg = f"Unsupported grouping strategy: {grouping_strategy}"
            logger.error(
                f"package.render.error package_id={self.package.obfuscated_id if self.package.pk else 'simulated'} "
                f"error={error_msg}")
            raise ValueError(error_msg)

        return PackageRenderResult(rendered_items=rendered_items,
                                   errors=errors if isinstance(errors, list) else [errors] if errors else [])

    def _render_split_by_doc(self, queryset_to_use, output_format, include_document_content):
        """Render documents individually using SPLIT_BY_DOC strategy."""
        errors = []
        rendered_items = []

        try:
            groups = group_documents(queryset_to_use, PackagerGroupingOptions.SPLIT_BY_DOC.name)
            if not groups:
                return [], []

            for doc_data in queryset_to_use:
                try:
                    rendered_item = self._render_single_document(doc_data, output_format)
                    if rendered_item:
                        rendered_items.append(rendered_item)
                except Exception as e:
                    logger.error(f"package.render.split_by_doc.doc_error package_id="
                                 f"{self.package.obfuscated_id if self.package.pk else 'simulated'} "
                                 f"doc_id={doc_data.obfuscated_id} error={str(e)}")
                    errors.append(DocumentError(document_id=doc_data.obfuscated_id,
                                                error=f"Error rendering document data: {str(e)}"))

            return rendered_items, errors

        except Exception as e:
            logger.error(f"package.render.split_by_doc.failed package_id="
                         f"{self.package.obfuscated_id if self.package.pk else 'simulated'} error={str(e)}")
            errors.append(DocumentError(document_id="SPLIT_BY_DOC_PROCESSING_ERROR",
                                        error=f"Error processing SPLIT_BY_DOC: {str(e)}"))
            return rendered_items, errors

    def _render_single_document(self, doc_data, output_format):
        """Render a single document for SPLIT_BY_DOC strategy."""
        rendered_data, updated_filename, result_msg = doc_data.render_document_data()

        if result_msg != PackageStatus.RENDER_SUCCESSFUL.name:
            raise ValueError(f"Document data rendering failed: {result_msg}")

        data = self._process_rendered_data(rendered_data)

        try:
            append_coc_fields = self.config.append_coc_fields_to_data or False
            coc_fields_to_append = self.config.coc_field_config or []
        except Exception as e:
            logger.error(f"Error getting packager config for SPLIT_BY_DOC rendering: {str(e)}")
            append_coc_fields = False
            coc_fields_to_append = []

        if append_coc_fields and coc_fields_to_append:
            coc_data_to_append = self._get_coc_fields_data_using_schema(doc_data, coc_fields_to_append)
            data.update(coc_data_to_append)

        if self.config.include_relationship_details_in_data_export:
            self._add_relationship_details(data, doc_data)

        content_bytes = self._format_data_content(data, output_format)

        return RenderedPackageItem(filename=updated_filename, content=content_bytes)

    def _process_rendered_data(self, rendered_data):
        """Process rendered data into a dictionary format."""
        data = {}
        rendered_data_list = rendered_data[0] if isinstance(rendered_data, tuple) else rendered_data

        if isinstance(rendered_data_list, list):
            for item in rendered_data_list:
                if isinstance(item, dict):
                    data.update(item)
        elif isinstance(rendered_data_list, dict):
            data = rendered_data_list

        if not isinstance(data, dict):
            raise ValueError(f"Rendered data is not a dictionary after merging (type: {type(data)})")

        return data

    def _add_relationship_details(self, data, doc_data):
        """Add relationship details to the data dictionary."""
        relationships = get_document_relationships([doc_data.document.obfuscated_id], self.data_pool.id)

        is_duplicate = any(rel["target_document_id"] == doc_data.document.obfuscated_id for rel in relationships)
        # Also check canonical_document_data for duplicate status
        if not is_duplicate:
            is_duplicate = doc_data.canonical_document_data is not None

        data["is_duplicate"] = str(is_duplicate)

        if is_duplicate:
            canonical_rel = next(
                (rel for rel in relationships if rel["target_document_id"] == doc_data.document.obfuscated_id), None)
            if canonical_rel:
                data["duplicate_of"] = canonical_rel["source_document_id"]
            elif doc_data.canonical_document_data and doc_data.canonical_document_data.document:
                data["duplicate_of"] = doc_data.canonical_document_data.document.obfuscated_id
            else:
                data["duplicate_of"] = None

        has_duplicates = any(rel["source_document_id"] == doc_data.document.obfuscated_id for rel in relationships)
        data["has_duplicates"] = str(has_duplicates)

        if has_duplicates:
            duplicate_ids = [rel["target_document_id"] for rel in relationships if
                             rel["source_document_id"] == doc_data.document.obfuscated_id]
            data["duplicate_doc_ids"] = ",".join(duplicate_ids)
        else:
            data["duplicate_doc_ids"] = None

    def _format_data_content(self, data, output_format):
        """Format data content according to the specified output format."""
        if output_format == PackagerDataOutputFormats.CSV.name:
            output = io.StringIO()
            writer = csv.DictWriter(output, fieldnames=list(data.keys()))
            writer.writeheader()
            writer.writerow(data)
            return output.getvalue().encode("utf-8")
        else:
            return json.dumps(data).encode("utf-8")

    def _render_all_in_one(self, queryset_to_use, output_format, include_document_content):
        """Render all documents together using ALL_IN_ONE strategy."""
        errors = []
        rendered_items = []

        try:
            groups = group_documents(queryset_to_use, PackagerGroupingOptions.ALL_IN_ONE.name)
            valid_doc_datas = groups.get("all", [])

            if not valid_doc_datas:
                return [], []

            if (
                    not self.config.exclude_duplicates_from_delivery
                    and self.config.include_relationship_details_in_data_export):
                try:
                    doc_ids = [dd.document.obfuscated_id for dd in valid_doc_datas if
                               dd.document and dd.document.obfuscated_id]
                    import packager.utils.duplicate_utils as duplicate_utils

                    duplicate_utils.get_document_relationships(doc_ids, self.data_pool.id)
                except Exception:
                    logger.debug("Failed to fetch document relationships for ALL_IN_ONE preview", exc_info=True)

            result = self._render_grouped_documents(groups, include_document_content)
            rendered_items.extend(result.rendered_items)
            errors.extend(result.errors)

        except ValueError as e:
            logger.error(f"package.render.all_in_one.group_error package_id="
                         f"{self.package.obfuscated_id if self.package.pk else 'simulated'} error={str(e)}")
            errors.append(DocumentError(document_id="ALL_IN_ONE_GROUPING_ERROR", error=f"Processing error: {str(e)}"))
        except Exception as e:
            logger.error(f"package.render.all_in_one.failed package_id="
                         f"{self.package.obfuscated_id if self.package.pk else 'simulated'} error={str(e)}")
            errors.append(
                DocumentError(document_id="ALL_IN_ONE_RENDERING_ERROR", error=f"Error processing ALL_IN_ONE: {str(e)}"))

        return rendered_items, errors

    def _render_metadata_match_and_merge(self, queryset_to_use, output_format, metadata_key, include_document_content):
        """Render documents grouped by metadata using METADATA_MATCH_AND_MERGE strategy."""
        errors = []
        rendered_items: List[RenderedPackageItem] = []

        try:
            groups = group_documents(queryset_to_use, PackagerGroupingOptions.METADATA_MATCH_AND_MERGE.name,
                                     metadata_key)
        except ValueError as e:
            logger.error(f"package.render.metadata_merge.group_error package_id="
                         f"{self.package.obfuscated_id if self.package.pk else 'simulated'} error={e}")
            errors.append(DocumentError(document_id="METADATA_GROUPING_ERROR", error=str(e)))
            return [], errors
        except Exception as e:
            logger.error(f"package.render.metadata_merge.unexpected_error package_id="
                         f"{self.package.obfuscated_id if self.package.pk else 'simulated'} error={e}")
            errors.append(DocumentError(document_id="METADATA_UNEXPECTED_GROUPING_ERROR",
                                        error=f"Unexpected grouping error: {str(e)}"))
            return [], errors

        if not groups:
            return [], []

        try:
            result = self._render_grouped_documents(groups, include_document_content)
            rendered_items.extend(result.rendered_items)
            errors.extend(result.errors)
            return rendered_items, errors
        except Exception as e:
            logger.error(f"package.render.metadata_merge.failed package_id="
                         f"{self.package.obfuscated_id if self.package.pk else 'simulated'} error={str(e)}")
            errors.append(DocumentError(document_id="METADATA_RENDERING_ERROR",
                                        error=f"Error in metadata match and merge rendering: {str(e)}", ))
            return rendered_items, errors

    def _create_document_entry(self, doc_data, document, append_coc_fields, coc_fields_to_append) -> dict:
        """Create base document entry with metadata and CoC fields."""

        doc_entry = {}

        if append_coc_fields and coc_fields_to_append:
            coc_data_to_append = self._get_coc_fields_data_using_schema(doc_data, coc_fields_to_append)
            doc_entry.update(coc_data_to_append)

        return doc_entry

    def _get_coc_fields_data_using_schema(self, doc_data, coc_fields_to_append):
        """
        Get CoC field data using the same schema approach as the CoC report service.
        This ensures consistency between package rendering and CoC reports.
        """

        report_start_date = self.config.coc_report_start_date
        report_end_date = self.config.coc_report_end_date

        if report_start_date and isinstance(report_start_date, datetime):
            report_start_date = report_start_date.date()
        if report_end_date and isinstance(report_end_date, datetime):
            report_end_date = report_end_date.date()

        actual_packager = self.package.packager if self.package else None

        try:
            from packager.services.coc_report_service import CoCReportService
            temp_coc_service = CoCReportService(
                data_pool=self.data_pool,
                report_start_date=report_start_date,
                report_end_date=report_end_date,
                packager=actual_packager
            )
            schema_obj = temp_coc_service.create_report_entry(doc_data, package_entry=None)
        except Exception as e:
            logger.error(f"Error creating CoC schema object: {str(e)}")
            return {field_name: None for field_name in coc_fields_to_append}

        schema_dict = schema_obj.dict(by_alias=True)

        coc_data_to_append = {}
        for field_name in coc_fields_to_append:
            if field_name in schema_dict:
                value = schema_dict[field_name]
                if value is not None:
                    if isinstance(value, (datetime, date)):
                        coc_data_to_append[field_name] = value.isoformat()
                    elif hasattr(value, 'value'):  # Handle enum values
                        coc_data_to_append[field_name] = value.value
                    else:
                        coc_data_to_append[field_name] = value
                else:
                    coc_data_to_append[field_name] = None
            else:
                coc_data_to_append[field_name] = None
                logger.warning(f"CoC field {field_name} not found in schema")

        return coc_data_to_append

    def _process_source_file(self, document) -> Optional[SourceFile]:
        """Process source file for a document, leveraging existing generate_filename utility."""
        try:
            if document.file_content is not None:
                source_file_naming_cfg = self.config.source_file_naming_config
                # Use direct attribute access with fallback
                doc_extension = document.file_extension if document.file_extension else ""

                source_filename = generate_filename(config=source_file_naming_cfg, extension=doc_extension,
                                                    source_obj=document,
                                                    fallback_name=document.label or f"source_{document.obfuscated_id}",
                                                    is_processed_data_config=False, append_unique_id=False, )

                source_path = f"source/{source_filename}"
                # Use direct attribute access with fallback
                content_type = document.content_type if document.content_type else "application/octet-stream"
                return SourceFile(filename=source_path, content=document.file_content, content_type=content_type, )
            else:
                logger.warning(f"Document {document.obfuscated_id} has no file_content for source file inclusion.")
                return None
        except AttributeError:
            logger.warning(f"Document {document.obfuscated_id} has no file_content attribute.")
            return None
        except Exception as e:
            logger.exception(f"Error preparing source file for doc {document.obfuscated_id}: {str(e)}")
            raise DocumentError(document_id=document.obfuscated_id, error=f"Error preparing source file: {str(e)}")

    def _merge_extraction_data(self, doc_entry, doc_data, include_document_content) -> dict:
        """Merge extraction data into document entry, leveraging existing extraction logic."""
        if include_document_content and doc_data.document:
            try:
                from extract.models import Extraction

                extractions = Extraction.objects.filter(document=doc_data.document)
                if extractions.exists():
                    extraction = extractions.first()
                    doc_data_from_extraction = extraction.get_transformed_results()
                    if doc_data_from_extraction and isinstance(doc_data_from_extraction, list):
                        for item in doc_data_from_extraction:
                            if isinstance(item, dict):
                                doc_entry.update(item)
                            else:
                                logger.warning(f"render_grouped_documents: Invalid item type in transformed_data for "
                                               f"document {doc_data.document.obfuscated_id}: {type(item)}")
                    elif doc_data_from_extraction and isinstance(doc_data_from_extraction, dict):
                        doc_entry.update(doc_data_from_extraction)
                    elif doc_data_from_extraction is not None:
                        logger.warning(f"render_grouped_documents: Unexpected transformed_data format for document "
                                       f"{doc_data.document.obfuscated_id}: {type(doc_data_from_extraction)}")
            except Exception as e:
                logger.error(f"Error getting extraction data for document {doc_data.document.obfuscated_id}: {str(e)}")

        return doc_entry

    def _render_grouped_documents(self, grouped_docs: Dict[str, List[Any]],
                                  include_document_content: bool = False) -> PackageRenderResult:
        """
        Render grouped documents according to packager config, optionally including document content.
        Includes configurable Chain of Custody fields in the output data.
        """
        result = []
        errors = []

        try:
            append_coc_fields = self.config.append_coc_fields_to_data or False
            coc_fields_to_append = self.config.coc_field_config or []
        except Exception as e:
            logger.error(f"Error getting packager config for rendering: {str(e)}")
            append_coc_fields = False
            coc_fields_to_append = []

        include_relationship_details = self.config.include_relationship_details_in_data_export or False
        exclude_duplicates_config = self.config.exclude_duplicates_from_delivery or False
        is_simulation_mode = self.package.pk is None

        # Apply duplicate filtering to each group
        filtered_grouped_docs = {}
        for group_key, doc_datas in grouped_docs.items():
            from packager.utils.duplicate_utils import filter_duplicate_documents
            filtered_docs = filter_duplicate_documents(doc_datas, exclude_duplicates_config, is_simulation_mode)
            filtered_grouped_docs[group_key] = filtered_docs
        grouped_docs = filtered_grouped_docs

        for group_name, docs in grouped_docs.items():
            try:
                rendered_item, group_errors = self._process_document_group(group_name, docs, include_document_content,
                                                                           exclude_duplicates_config,
                                                                           is_simulation_mode,
                                                                           include_relationship_details,
                                                                           append_coc_fields, coc_fields_to_append)
                if rendered_item:
                    result.append(rendered_item)
                errors.extend(group_errors)
            except Exception as e:
                group_name_str = str(group_name)
                logger.error(f"Error processing group {group_name_str}: {str(e)}")
                errors.append(DocumentError(document_id=group_name_str, error=f"Error processing group: {str(e)}"))

        return PackageRenderResult(rendered_items=result, errors=errors)

    def _process_document_group(self, group_name, docs, include_document_content, exclude_duplicates_config,
                                is_simulation_mode, include_relationship_details, append_coc_fields,
                                coc_fields_to_append) -> Tuple[Optional[RenderedPackageItem], List[DocumentError]]:
        """Process a single group of documents into a rendered item."""
        from django.db import models
        import re

        errors = []

        # Ensure group_name is always a string or scalar for downstream usage
        if isinstance(group_name, list):
            group_name_str = "_".join(str(x) for x in group_name)
        else:
            group_name_str = str(group_name)

        group_data = {"group_name": group_name_str, "documents": []}
        source_files_for_group: List[SourceFile] = []

        from packager.utils.duplicate_utils import filter_duplicate_documents
        current_docs_for_group = filter_duplicate_documents(docs, exclude_duplicates_config, is_simulation_mode)

        for doc_data in current_docs_for_group:
            # Refresh doc_data from DB to ensure latest values
            if isinstance(doc_data, models.Model) and doc_data.pk is not None:
                try:
                    doc_data = doc_data.__class__.objects.get(pk=doc_data.pk)
                except doc_data.__class__.DoesNotExist:
                    logger.error(
                        f"Failed to refresh DocumentData with pk: {doc_data.pk}, it no longer exists. Continuing with "
                        f"current instance.")
                except Exception as e:
                    logger.error(f"Error refreshing DocumentData with pk: {doc_data.pk}: {str(e)}")

            document = doc_data.document

            if not document:
                try:
                    doc_data_id_for_log = doc_data.obfuscated_id
                except AttributeError:
                    try:
                        doc_data_id_for_log = str(doc_data.id)
                    except AttributeError:
                        doc_data_id_for_log = "Unknown ID"
                logger.error(f"DocumentData object {doc_data_id_for_log} has no associated document.")
                errors.append(
                    DocumentError(document_id=doc_data_id_for_log, error="Document not found for DocumentData"))
                continue

            # Create document entry with CoC fields
            doc_entry = self._create_document_entry(doc_data, document, append_coc_fields, coc_fields_to_append)

            # Process source file if needed
            if include_document_content:
                try:
                    source_file = self._process_source_file(document)
                    if source_file:
                        source_files_for_group.append(source_file)
                except DocumentError as e:
                    errors.append(e)
                except Exception as e:
                    logger.exception(f"Error preparing source file for doc {document.obfuscated_id}: {str(e)}")
                    errors.append(DocumentError(document_id=document.obfuscated_id,
                                                error=f"Error preparing source file: {str(e)}"))

            # Apply duplicate flags
            try:
                if not isinstance(doc_data.id, (int, str)):
                    logger.error(f"Invalid doc_data.id type: {type(doc_data.id)} for document {document.obfuscated_id}")
                    temp_duplicate_info = {"is_duplicate": False, "has_duplicates": False, "duplicate_of": None}
                else:
                    from packager.utils.duplicate_utils import apply_duplicate_flags
                    temp_duplicate_info = apply_duplicate_flags({}, doc_data, for_json=True)
            except Exception as e:
                logger.error(f"Error applying duplicate flags for document {document.obfuscated_id}: {str(e)}")
                temp_duplicate_info = {"is_duplicate": False, "has_duplicates": False, "duplicate_of": None}

            if include_relationship_details:
                doc_entry.update(temp_duplicate_info)
            elif exclude_duplicates_config and temp_duplicate_info.get("has_duplicates"):
                doc_entry["has_duplicates"] = temp_duplicate_info["has_duplicates"]

            # Merge extraction data
            doc_entry = self._merge_extraction_data(doc_entry, doc_data, include_document_content)

            group_data["documents"].append(doc_entry)

        # Format content and create rendered item
        output_format = getattr(self.config, "output_format", PackagerDataOutputFormats.JSON.name)
        try:
            # Use 'json_data' key for CSV output, 'data' for others (like JSON)
            data_key = "json_data" if output_format == PackagerDataOutputFormats.CSV.name else "data"
            formatted_data = {data_key: group_data["documents"]}

            from packager.utils.formatting_utils import format_data
            content = format_data(formatted_data, output_format)

            first_doc_data_in_group = current_docs_for_group[0] if current_docs_for_group else None
            document_context_for_filename = None
            if first_doc_data_in_group and first_doc_data_in_group.document:
                document_context_for_filename = first_doc_data_in_group.document

            generated_filename = "default_group_filename.json"
            try:
                generated_filename = self.package.generate_processed_data_filename(
                    group_key=group_name_str,
                    document=document_context_for_filename)
            except AttributeError:
                logger.warning(f"render_grouped_documents: package_instance of type {type(self.package)} "
                               f"does not have generate_processed_data_filename method. Using fallback filename for "
                               f"group {group_name}.")
                safe_group_name = re.sub(r"[^\w\s.-]", "_", group_name)
                generated_filename = f"{safe_group_name}.{output_format.lower()}"

            if not isinstance(generated_filename, str) or not generated_filename:
                logger.error(f"Generated filename for group {group_name} is not a string or is empty. Using fallback.")
                generated_filename = f"group_{group_name_str}.{output_format.lower()}"

            rendered_item = RenderedPackageItem(filename=str(generated_filename), content=content,
                                                source_files=source_files_for_group, format=output_format, )
            return rendered_item, errors

        except Exception as e:
            logger.error(f"Error formatting data for group {group_name_str}: {str(e)}")
            errors.append(DocumentError(document_id=group_name_str, error=f"Error formatting data: {str(e)}"))
            return None, errors
