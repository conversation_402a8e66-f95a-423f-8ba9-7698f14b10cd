# Packager Technical Specification (Updated June 2025)

## 1. Overview

The Packager application automates the aggregation, packaging, and delivery of GLYNT data to customers, featuring
comprehensive audit tracking and robust configuration options. It is designed for reliability and scalability, catering
to diverse customer requirements for data formatting and delivery schedules.

**Key configurable features include:**

*   **Flexible Scheduling:** Supports CRON-based, manual, and real-time stream-based data packaging operations.
*   **Customizable File Naming:** Allows precise control over data file and source document file naming conventions using
    dynamic metadata and literal strings.
*   **Targeted Data Selection:** Enables filtering of documents and extraction batches based on status, IDs, and date
    ranges. Filtering logic is additive (OR) across different criteria within the configured date range.
*   **Varied Output Formats:** Supports data output in JSON and CSV formats.
*   **Sophisticated Data Grouping:** Offers multiple strategies for grouping data within packages, such as consolidating
    all data, splitting by document, or merging based on metadata.
*   **Automated and Manual Delivery:** Facilitates both fully automated delivery workflows and manual triggering of
    package delivery.
*   **Cross-Package Duplicate Detection:** Implements mechanisms to detect and flag duplicate documents across the entire
    data pool, with options to exclude them from delivery while maintaining audit trails.
*   **Chain of Custody (CoC):** Provides comprehensive audit logging for document and package lifecycles, including CoC
    event tracking and relationship management. CoC reports can be generated in JSON or CSV format.
*   **Source File Inclusion:** Option to include original source document files alongside processed data in the delivery.
*   **Relationship Details in Data Export:** Option to include details about duplicate relationships directly in the exported data files.
*   **Append CoC Fields to Data:** Option to append selected Chain of Custody fields to the output data files.

### 1.1. Architecture Overview

The packager module follows a service-oriented architecture with clear separation of concerns:

-   **Models**: Define data structures and delegate complex operations to services for improved maintainability. Key models include `Packager`, `Package`, `DocumentData`, `PackageEntry`, `DocumentRelationshipType`, and `DocumentRelationship`. `ExtractionBatchData` is largely legacy but still processed.
-   **Services**: Encapsulate complex business logic with consolidated services:
    -   `PackageRenderingService`: Handles package data rendering with different grouping strategies (ALL_IN_ONE, SPLIT_BY_DOC, METADATA_MATCH_AND_MERGE), including formatting, naming, and inclusion of relationship/CoC details.
    -   `PackageCreationService`: Manages package creation and reuse logic, primarily for determining the appropriate `Package` instance for processing based on schedule type and configuration changes.
    -   `StreamPackageService`: Processes stream-based package workflows for real-time document processing, triggered by new document creation.
    -   `PackageOperationsService`: Provides API-facing methods for simulation, download, and manual package creation (specifically, creating PackageEntry records for selected documents). This service also implements the additive filtering logic for document selection and handles configuration overrides for simulation/download.
    -   `DuplicateDetectionService`: Handles MD5, label, and content-based duplicate detection logic at the data pool level. Determines canonical documents and creates `DocumentRelationship` records.
    -   `CoCReportService`: Generates Chain of Custody reports in JSON and CSV formats with glynt-schemas compliance, filtering data by date range and including configurable fields.
-   **Views**: Handle HTTP requests and responses for CRUD operations, simulation, preview, and delivery. Utilize serializers for data validation and formatting.
-   **Tasks**: Asynchronous processing using Celery for the main package preparation and delivery workflows. `prepare_packages` uses database-level locking for concurrency control.
-   **Utils**: Provide reusable helper functions including naming, grouping, formatting, duplicate flagging, file operations, content hashing, and CoC utilities.
-   **Pydantic Classes**: Data validation and serialization for configurations (`PackagerConfig`, `NamingConfig`), input validation (`SimulatePackageInput`), and output formatting.
-   **Signals**: Thin orchestration layers that delegate to services or tasks (e.g., triggering `StreamPackageService` on document creation, triggering content checks on document verification).

### 1.2. High-Level Data Flow

The packager operates within a larger data processing pipeline. The following diagram illustrates its core interactions:

```mermaid
graph TD
    A[Document/Batch Creation] --> B{Data Pool};
    B -- Filters --> C[Packager Configuration];
    C -- Schedule/Trigger --> D[Package Preparation Task];
    D --> E{Data Collection & Filtering};
    E -- Documents/Batches --> F[DocumentData & PackageEntry Creation/Update];
    F --> G[Duplicate Detection Service];
    G --> H[Package Status Update: PROCESSING_SUCCESSFUL/FAILED];
    H -- Ready for Delivery --> I[Delivery Task];
    I -- Manual/Automatic Trigger --> J[Package Rendering];
    J -- Formatted Data --> K[S3 Upload];
    K --> L[Package & Packager Status Update: TRANSMISSION_SUCCESSFUL/FAILED];

    subgraph "Input & Triggers"
        A
        M[User via API] --> C;
        M --> I;
        N[System Signals] --> D;
    end

    subgraph "Core Packager Logic"
        C
        D
        E
        F
        G
        H
    end

    subgraph "Delivery"
        I
        J
        K
        L
    end

    subgraph "Auditing & State"
        O[CoC Event Logging]
        P[State Management]
        F --> O;
        G --> O;
        L --> O;
        D --> P;
        I --> P;
    end

    style A fill:#lightgrey,stroke:#333,stroke-width:2px
    style B fill:#lightblue,stroke:#333,stroke-width:2px
    style C fill:#lightgreen,stroke:#333,stroke-width:2px
    style D fill:#orange,stroke:#333,stroke-width:2px
    style I fill:#orange,stroke:#333,stroke-width:2px
    style K fill:#violet,stroke:#333,stroke-width:2px
    style O fill:#yellow,stroke:#333,stroke-width:2px
```

**Narrative:**

1.  **Data Ingestion & Initial Processing:** Documents and Extraction Batches are created in the system, typically
    associated with a `DataPool`. Signal handlers create corresponding `DocumentData` records and trigger initial
    duplicate checks (`MD5`, `LABEL`).
2.  **Package Preparation Trigger:** The package preparation Celery task (`packager.tasks.prepare_packages`) is initiated based on:
    *   **CRON Schedule:** Defined in the `Packager` configuration and managed by `django-celery-beat`.
    *   **STREAM Processing:** Triggered by a signal (`packager.signals.package_created_document_for_stream_schedule`) upon new document creation if a `Packager` is configured for `STREAM` and the document matches the packager's filters.
    *   **Other Internal Triggers:** The task can also be triggered by other internal system events.
3.  **Data Collection & Filtering:** The `prepare_packages` task retrieves the `Packager` configuration and filters `DocumentData`
    records using an additive (OR) logic based on defined criteria (status, IDs) within the configured date range. This filtering is handled by `PackageOperationsService.get_filtered_document_data_additive`.
4.  **Package and Entry Management:** A `Package` object is obtained (or created for `STREAM` or if configuration changed/package is in a terminal state) using `PackageCreationService.get_working_package`. `DocumentData` records are created/updated, and `PackageEntry` records are created to link these documents to the current package. Initial `PackageEntry` statuses are determined based on factors like duplicate status and document readiness.
5.  **Cross-Package Duplicate Detection:** The `DuplicateDetectionService` is invoked to perform MD5, label, and
    content-based duplicate checks on the `DocumentData` instances. **Importantly, duplicate detection operates across
    the entire data pool, not just within the current package.** This means documents from different packages can be
    detected as duplicates of each other. `PackageEntry` statuses are updated based on these findings and the
    `exclude_duplicates_from_delivery` setting. Documents must also meet specific readiness criteria (completion of MD5, Label, and Content checks if VERIFIED) to be included in automated packaging workflows.
6.  **Package Verification:** Although not explicitly a separate automated step, the system expects packages to reach a
    `VERIFIED` status (often through manual review or other processes) before automated delivery.
7.  **Delivery Trigger:** The delivery Celery task (`packager.tasks.deliver_package`) is initiated:
    *   **Automatic:** If the `Packager`'s `delivery_schedule_type` is `AUTOMATIC` and the `Package` is `VERIFIED`.
    *   **Manual API Trigger:** Via the `trigger-delivery` action on the `PackagerViewSet` or `PackageViewSet`.
8.  **Package Rendering & Formatting:** The delivery task calls package rendering methods (`PackageRenderingService.render_package_data`), which use grouping and naming
    utilities to structure and format the data according to the `PackagerConfig`. Formatting utilities handle JSON/CSV
    conversion. Relationship details and selected CoC fields can be included in the output data based on configuration.
9.  **S3 Upload:** The formatted data files (and optionally source files, handled by `PackageRenderingService` and uploaded via `_upload_to_s3` in `tasks.py`) are uploaded to the configured S3 bucket. When source files are included in downloads or simulations, their paths within the ZIP are prefixed with "source/".
10. **Status Updates & Auditing:** `Package`, `Packager`, and `PackageEntry` statuses are updated throughout the
    process. `CoCEvent`s are created at key stages via signal handlers and direct calls in services/tasks. Task failures are logged and result in status updates on associated package and packager instances.

## 2. Technology Stack

The Packager application leverages a robust and scalable technology stack:

*   **Framework:** **Django** with the Django ORM for data modeling, database interactions, and API development (via
    Django REST Framework).
    *   *Justification:* Django provides a mature, full-featured web framework with a powerful ORM, simplifying database
        operations and rapid development. DRF offers excellent tools for building RESTful APIs.
*   **Task Processing:** **Celery** for asynchronous background task handling, including package preparation and delivery. Uses database-level locking for concurrency control in package preparation.
    *   *Justification:* Celery is essential for offloading long-running processes like data aggregation, formatting, and
        S3 uploads, ensuring API responsiveness and enabling scheduled operations. Database locking prevents race conditions during package preparation.
*   **Database:** **MySQL** with `JSONField` support for flexible storage of configurations and metadata.
    *   *Justification:* MySQL is a reliable relational database. `JSONField` allows for storing complex, semi-structured
        data like naming configurations and filter arrays without rigid schema constraints.
*   **Storage:** **AWS S3** for durable storage and delivery of packaged data.
    *   *Justification:* S3 provides a scalable, reliable, and cost-effective solution for object storage, ideal for
        delivering data packages to customers.
*   **Scheduling:** **django-celery-beat** for CRON-based scheduling of Celery tasks.
    *   *Justification:* Integrates seamlessly with Celery and Django to manage periodic task execution directly from the
        database.
*   **Logging:** Standard Python **logging** module, configured for structured logging.
    *   *Justification:* Provides a flexible and standard way to record application events, errors, and debug information,
        crucial for monitoring and troubleshooting.
*   **Validation:** **Pydantic** for data validation and settings management, particularly for complex configurations like
    naming rules and API input (`SimulatePackageInput`). Django's built-in validation is also used at the model level.
    *   *Justification:* Pydantic offers robust data validation with clear error reporting and type hinting, improving
        code reliability and developer experience when handling complex configuration objects.
*   **Audit & Chain of Custody:** Custom models (`CoCEvent`, `DocumentRelationship`) and services (`CoCReportService`) for
    comprehensive audit logging and tracking document lineage.
    *   *Justification:* Essential for maintaining data integrity, providing transparency to customers, and meeting
        compliance requirements.

## 3. Core Configuration (`Packager` Model & `PackagerConfig`)

The `Packager` model is the central entity for defining how data packaging and delivery should occur for a specific
`DataPool`. Its configuration is mirrored and validated by the `PackagerConfig` Pydantic model.

### 3.1. Scheduling and Triggering

*   **`packager_schedule_type`**: Defines how package preparation is initiated.
    *   Values: `CRON_SCHEDULE`, `MANUAL`, `STREAM`.
    *   `CRON_SCHEDULE`: Uses `schedule_cron_expression` to define a recurring schedule via `django-celery-beat`.
    *   `MANUAL`: Package preparation is initiated via the `create-package` API action, which creates package entries for selected documents. The full package preparation workflow (including duplicate detection, rendering, and status updates) is handled by the asynchronous `prepare_packages` task, which is triggered by schedules or signals, not directly by this endpoint.
    *   `STREAM`: Package preparation is triggered by a signal when a new `Document` matching `document_status_filter` is
        created. Always creates new packages per document.
    *   *Justification:* Provides flexibility for various customer needs, from batch processing to real-time data feeds.
*   **`schedule_cron_expression`**: Standard CRON expression for `CRON_SCHEDULE` type. Validated by `croniter`. Default: `'0 * * * *'` (hourly).
*   **`delivery_schedule_type`**: Defines how package delivery is initiated.
    *   Values: `AUTOMATIC`, `MANUAL`.
    *   `AUTOMATIC`: Delivery task is triggered automatically once a package is `VERIFIED`.
    *   `MANUAL`: Delivery requires an API call (e.g., `trigger-delivery` action).
    *   *Justification:* Allows for review workflows before delivery if needed, or fully automated pipelines.

### 3.2. Data Selection Filters

Filtering for package preparation uses an additive (OR) logic across the specified criteria within the configured date range (`packaging_begin_date` and `packaging_end_date`). A `DocumentData` instance will be included if it matches *any* of the following conditions: its document ID is in `document_id_filter`, its associated extraction batch ID is in `eb_id_filter`, its status is in `document_status_filter`, or its associated extraction batch status is in `eb_status_filter`. This filtering is implemented in `PackageOperationsService.get_filtered_document_data_additive` and used by the `prepare_packages` Celery task.

*   **`document_status_filter`**: JSONField list of document statuses to include.
*   **`eb_status_filter`**: JSONField list of extraction batch statuses to include.
*   **`document_id_filter`**: JSONField list of specific document obfuscated IDs to include.
*   **`eb_id_filter`**: JSONField list of specific extraction batch obfuscated IDs to include.
    *   *Justification:* JSONFields provide flexibility for storing lists of filter criteria. Status-based filtering is
        common for workflow management, while ID-based filtering allows for specific ad-hoc packaging. The additive logic provides flexibility in defining inclusion criteria.

### 3.3. Output Formatting and Grouping

*   **`output_format`**: Output format for data files.
    *   Values: `JSON`, `CSV`.
    *   Handled by formatting utilities and `PackageRenderingService`.
*   **`delivery_data_grouping_strategy`**: Strategy for grouping documents into data files.
    *   Values: `ALL_IN_ONE`, `METADATA_MATCH_AND_MERGE`, `SPLIT_BY_DOC`.
    *   Implemented in grouping utilities and `PackageRenderingService`.
*   **`delivery_data_grouping_metadata_key`**: The metadata field path (e.g., `document.target_training_set`) used when
    `delivery_data_grouping_strategy` is `METADATA_MATCH_AND_MERGE`. Validated as a Pydantic `NamingConfigElement`.
    *   *Justification:* These options provide comprehensive control over the structure of the delivered data,
        accommodating various downstream consumption patterns.

### 3.4. File Naming Configuration

Managed by `source_file_naming_config` and `processed_data_naming_config`. Both are JSONFields storing configurations
validated by the `NamingConfig` Pydantic model.

*   **`NamingConfig` Structure:**
    *   `elements`: A list of `NamingConfigElement` objects.
    *   `separator`: A single character string (e.g., `-`, `_`, `.`) to join filename parts.
    *   `max_length`: An integer (10-255) defining the maximum filename length.
*   **`NamingConfigElement` Structure:**
    *   `type`: Element type:
        *   `STRING_LITERAL`: A fixed string.
        *   `METADATA`: A dynamic value from a related model's field (e.g., `document.created_at`, `data_pool.label`).
            Values are retrieved using naming utilities.
        *   `DATE_FORMAT`: A formatted date/time value.
        *   `DOCUMENT_DATA`: A dynamic value from the extracted data of a document (e.g., `AccountNumber`). Only valid for
            `processed_data_naming_config` when `delivery_data_grouping_strategy` is `SPLIT_BY_DOC`.
    *   `value`: The string literal, metadata field path, or document data field name.
    *   `format`: Optional date format string (e.g., `%Y-%m-%d`) for `METADATA` elements of datetime type. Validated by
        date format validation utilities.
*   **Validation:**
    *   `Packager.clean()` validates these configurations using Pydantic models.
    *   `source_file_naming_config` cannot use `DOCUMENT_DATA` elements.
    *   `processed_data_naming_config` can only use `DOCUMENT_DATA` elements if `delivery_data_grouping_strategy` is
        `SPLIT_BY_DOC`.
    *   *Justification:* Provides a highly flexible and validated system for constructing filenames according to complex
        customer requirements. Pydantic ensures configuration integrity.

### 3.5. Delivery Destination (S3)

*   **`s3_bucket`**: Name of the AWS S3 bucket.
*   **`s3_prefix`**: Optional prefix (folder path) within the S3 bucket.
*   **`region`**: AWS region for the S3 bucket.
*   **`packaging_begin_date`**: Start date for packaging document data filter. Documents/batches must have a `created_at` within this date range to be considered for packaging.
*   **`packaging_end_date`**: End date for packaging document data filter (defaults to current date). Documents/batches must have a `created_at` within this date range to be considered for packaging.
    *   *Justification:* Standard S3 configuration parameters for directing output and date range filtering for package
        data selection.

### 3.6. Duplicate Handling and Source File Inclusion

*   **`exclude_duplicates_from_delivery`**: Boolean (default: False), if true, documents identified as duplicates will not be included in
    the delivered package. **Note: Duplicate detection operates across the entire data pool, not just within individual
    packages.**
*   **`include_source_files_in_delivery`**: Boolean (default: False), if true, original source document files (e.g., PDFs) are included in
    the delivery alongside processed data.
*   **`include_relationship_details_in_data_export`**: Boolean (default: False), controls whether duplicate relationship details (
    `is_duplicate`, `has_duplicates`, `duplicate_of`, `duplicate_doc_ids`) are included as fields in the exported data files.
*   **`deduplication_content_fields`**: JSONField list of field names from extracted data to be used for content-based
    duplicate detection (via hashing). Default: `[]` (empty list - no content-based deduplication by default).
    *   *Justification:* These options provide control over data cleanliness and completeness in the final package.
        Cross-package duplicate detection ensures efficiency and prevents delivering the same document multiple times
        across different packages. Including relationship details provides transparency in the output data.

### 3.7. Chain of Custody (CoC) Configuration

*   **`append_coc_fields_to_data`**: Boolean (default: False), if true, selected fields from the `DocumentData` model are appended to the output data files.
*   **`coc_field_config`**: JSONField list of `DocumentData` field names (e.g., `source_file_name`, `received_at`) to append to the output data if
    `append_coc_fields_to_data` is true. Default: `[]`.
    *   *Note:* CoC reports are generated separately via the `chain-of-custody-report` API endpoint and are not controlled by these packager configuration fields.
    *   *Justification:* Allows for embedding audit information directly within data deliveries. CoC reports provide comprehensive audit trails for compliance and transparency.

## 4. Chain of Custody (CoC) Integration

### 4.1. glynt-schemas Compliance

The packager application is fully compliant with the glynt-schemas Chain of Custody specification, specifically the `GtdrChainOfCustodyCsv` schema. This ensures standardized audit reporting across all GLYNT deployments.

#### 4.1.1. Schema Mapping

The `DocumentData` model maps directly to the glynt-schemas CoC fields:

**Core CoC Fields:**
- `coc_report_start_date` / `coc_report_end_date`: Report date range (from service parameters)
- `source_file_received_at`: Maps to `DocumentData.received_at`
- `source_file_id`: Maps to `DocumentData.source_file_id`
- `source_file_original_name`: Maps to `DocumentData.source_file_name`
- `source_file_original_path`: Maps to `Document.inbound_path`
- `source_file_final_name`: Maps to `PackageEntry.filename_in_package`
- `num_pages`: Maps to `DocumentData.source_file_num_pages`
- `detected_language`: Maps to `DocumentData.source_file_detected_language`
- `size`: Maps to `DocumentData.source_file_size`

**Status and Relationship Fields:**
- `source_file_status`: Maps to `SourceFileStatus` enum based on document and package entry status
- `exclusion_reason`: Maps to `ExclusionReason` enum for duplicate-related exclusions only
- `parent_file_id`: Maps to `DocumentData.parent_file_id`
- `relationship_to_parent`: Maps to `ParentRelationship` enum for duplicate relationships only

**Package and Delivery Fields:**
- `package_id`: Maps to `Package.obfuscated_id`
- `delivered_at`: Maps to `PackageEntry.delivered_at`

#### 4.1.2. Enum Mappings

**SourceFileStatus Mapping:**
- `DELIVERED`: PackageEntry with TRANSMISSION_SUCCESSFUL status
- `EXCLUDED`: PackageEntry with any EXCLUDED_* status
- `PENDING`: PackageEntry with TRANSMISSION_FAILED status or documents with VERIFIED/CREATED status
- `RESEARCH`: Documents with PENDING_REVIEW status

**ExclusionReason Mapping (Duplicate-related only):**
- `DUPLICATE`: MD5_DUPLICATE, LABEL_DUPLICATE, DUPLICATE relationship types
- `CONTENT_DUPLICATE`: CONTENT_DUPLICATE relationship type
- Other exclusion reasons (file corruption, scope issues, etc.) are not mapped as they're outside CoC scope

**ParentRelationship Mapping (Duplicate-related only):**
- `DUPLICATE`: MD5_DUPLICATE, LABEL_DUPLICATE, CONTENT_DUPLICATE relationship types
- Other relationship types (SPLIT, TRANSLATED) are not mapped as they're outside current scope

#### 4.1.3. CoC Report Generation

The `CoCReportService` generates comprehensive CoC reports:

**Report Scope:**
- Includes all DocumentData within specified date range
- Covers both packaged and unpackaged documents
- Shows excluded duplicates for audit purposes
- Provides complete document lifecycle tracking

**Output Formats:**
- **JSON**: Structured data using glynt-schemas Pydantic models
- **CSV**: Formatted CSV with proper headers and field mapping

**API Integration:**
- `POST /api/v6/data-pools/{id}/packagers/{id}/chain-of-custody-report/`
- Supports request body parameters for date range and download options
- Uses packager configuration for default date ranges

### 4.2. DocumentData Lifecycle

The `DocumentData` model serves as the central Chain of Custody record for each document:

#### 4.2.1. Creation and Population

**Signal-based Creation:**
- Created automatically when `Document` is created via `handle_document_creation_for_coc` signal
- Populated with initial metadata from `Document` model fields
- `source_file_id` set to `document.obfuscated_id`
- `source_file_name` set to `document.label`
- `received_at` set to `document.created_at`

**Field Population:**
- Core fields populated from Document model during creation
- Additional CoC fields updated during processing lifecycle
- Duplicate relationship fields updated during duplicate detection
- Status tracking throughout document processing pipeline

#### 4.2.2. Duplicate Detection Integration

**Relationship Tracking:**
- `canonical_document_data`: Foreign key to canonical (parent) document
- `parent_file_id`: String ID of parent document for CoC reporting
- `relationship_to_parent`: Type of relationship (MD5_DUPLICATE, CONTENT_DUPLICATE, etc.)
- `exclusion_reason`: Reason for exclusion if applicable

**Detection Flags:**
- `is_md5_check_completed`: MD5 duplicate check completion status
- `is_label_check_completed`: Label duplicate check completion status
- `is_content_check_completed`: Content duplicate check completion status

**Document Readiness for Packaging:**
Documents are considered ready for automated packaging workflows (like CRON or STREAM) when they have completed the necessary duplicate checks. Specifically, this means:
- `is_md5_check_completed` is True
- `is_label_check_completed` is True
- If the document's status is `VERIFIED`, `is_content_check_completed` must also be True.

## 5. Duplicate Detection System

### 5.1. Cross-Package Detection Scope

The duplicate detection system operates at the **data pool level**, meaning it can identify duplicates across all
packages within the same organization's data pool. This design provides several benefits:

*   **Efficiency**: Prevents delivering the same document multiple times across different packages
*   **Compliance**: Maintains complete audit trails of all documents (including duplicates) in CoC reports
*   **Flexibility**: Allows organizations to manage duplicates at the data pool level rather than being constrained by
    package boundaries

### 5.2. Detection Types

The system supports three types of duplicate detection, all operating at the data pool level:

*   **MD5 Duplicate Detection**: Based on the source file's MD5 hash (`DocumentData.source_file_md5`).
    - Triggered during DocumentData creation
    - Creates MD5_DUPLICATE relationship type
    - Most reliable for exact file duplicates
*   **Label Duplicate Detection**: Based on the source file name (`DocumentData.source_file_name`).
    - Triggered during DocumentData creation
    - Creates LABEL_DUPLICATE relationship type
    - Useful for identifying renamed duplicates
*   **Content Duplicate Detection**: Based on a configurable hash of extracted data fields (`DocumentData.content_hash`).
    - Triggered by `trigger_document_data_checks_task` Celery task
    - Uses `deduplication_content_fields` configuration
    - Creates CONTENT_DUPLICATE relationship type
    - Most sophisticated detection method

### 5.3. Content-Based Detection Process

Content duplicate detection is the most advanced detection method:

**Configuration:**
- Uses `deduplication_content_fields` list from packager configuration
- Default: empty list (no content-based detection)
- Common fields: CustomerName, MeterKey, StatementDate, Commodity, etc.

**Process:**
1. Extract specified fields from document's processed extraction data
2. Generate MD5 hash of combined field values (`DocumentData.calculate_content_hash`)
3. Compare hash across all documents in the data pool
4. Create CONTENT_DUPLICATE relationships for matching hashes

**Triggering:**
- Triggered by `trigger_document_data_checks_task` Celery task
- Scheduled by signal handler when document status changes to VERIFIED
- Runs asynchronously to avoid blocking document processing

### 5.4. Relationship Management and Canonical Documents

**Canonical Document Selection:**
1. Oldest document by `received_at` timestamp
2. If timestamps equal, lowest `id` value
3. Canonical document becomes the "parent" in relationships

**Relationship Creation:**
1. Create `DocumentRelationship` records linking duplicates to canonical
2. Update duplicate's `canonical_document_data` field
3. Set `relationship_to_parent` field (MD5_DUPLICATE, CONTENT_DUPLICATE, etc.)
4. Set `parent_file_id` to canonical document's `source_file_id`
5. Record relationship metadata for audit purposes

**DocumentRelationshipType Management:**
- Relationship types created automatically as needed
- Scoped to data pool level
- Standard types: MD5_DUPLICATE, LABEL_DUPLICATE, CONTENT_DUPLICATE, DUPLICATE

### 5.5. Package Delivery Impact

**When `exclude_duplicates_from_delivery` is enabled:**
- Only canonical documents included in package deliveries
- Duplicate documents marked with `EXCLUDED_AS_DUPLICATE` status in `PackageEntry`
- Excluded duplicates remain visible in CoC reports for audit purposes
- Exclusion reason set to appropriate duplicate type

**When `include_relationship_details_in_data_export` is enabled:**
- Relationship details added as fields in exported data files
- Fields: `is_duplicate`, `has_duplicates`, `duplicate_of`, `duplicate_doc_ids`
- Provides transparency about duplicate relationships in delivered data

**CoC Integration:**
- All duplicates tracked in Chain of Custody reports
- Exclusion reasons mapped to glynt-schemas ExclusionReason enum
- Parent relationships mapped to ParentRelationship enum
- Complete audit trail maintained regardless of delivery inclusion

## 6. Service Architecture

### 6.1. Service Organization

The packager module implements a service-oriented architecture with the following services:

#### Core Services

-   **`PackageCreationService`**: Manages package creation and reuse logic, primarily for determining the appropriate `Package` instance for processing based on schedule type and configuration changes.
-   **`PackageRenderingService`**: Manages data rendering, grouping, and formatting for delivery. Handles inclusion of source files, relationship details, and CoC fields.
-   **`StreamPackageService`**: Specialized service for stream-based package processing, triggered by new document creation.
-   **`DuplicateDetectionService`**: Handles MD5, label, and content-based duplicate detection at the data pool level.
-   **`CoCReportService`**: Generates Chain of Custody reports in JSON and CSV formats based on date range and configurable fields.

#### Utility Services

-   **`PackageOperationsService`**: Provides API-facing methods for simulating packages, downloading packages (existing or simulated), and manual package creation (specifically, creating PackageEntry records for selected documents). Implements additive filtering logic for document selection and handles configuration overrides for simulation/download.
-   **Content Hashing Utilities**: MD5 hash calculation for duplicate detection.
-   **Naming Utilities**: Filename generation based on configurable naming rules.
-   **Grouping Utilities**: Document grouping strategies (ALL_IN_ONE, SPLIT_BY_DOC, METADATA_MATCH_AND_MERGE).
-   **Formatting Utilities**: JSON/CSV output formatting.
-   **Duplicate Utilities**: Helper functions for duplicate relationship management and filtering.

### 6.2. Service Responsibilities

#### PackageCreationService

-   Determines the appropriate `Package` instance for processing (get or create) based on schedule type and package reuse logic.
-   Handles package reuse logic for non-stream schedules, creating a new package if the existing one is in a terminal state or if the packager configuration has changed since its creation.
-   *Note:* The primary responsibility for creating `PackageEntry` records and associating documents with a package instance lies with the `prepare_packages` Celery task, which utilizes this service to obtain the working package.

#### DuplicateDetectionService

-   Performs MD5-based duplicate detection using `source_file_md5`.
-   Performs label-based duplicate detection using `source_file_name`.
-   Performs content-based duplicate detection using configurable field hashing.
-   Creates DocumentRelationship records for detected duplicates.
-   Updates DocumentData with canonical document references and relationship details.
-   Operates across entire data pool scope, not just individual packages.

#### CoCReportService

-   Generates comprehensive Chain of Custody reports in JSON and CSV formats.
-   Includes document processing history and relationship details.
-   Filters data based on a specified date range, defaulting to the packager's configured `coc_report_start_date` and `coc_report_end_date` if available.
-   Provides audit trail for compliance requirements.

#### PackageRenderingService

-   Renders package data according to the configured grouping strategy and output format.
-   Includes original source files if `include_source_files_in_delivery` is true.
-   Includes duplicate relationship details as fields in the output data if `include_relationship_details_in_data_export` is true.
-   Appends selected CoC fields to the output data if `append_coc_fields_to_data` is true.

#### PackageOperationsService

-   Provides API-facing methods for simulating packages, downloading packages (existing or simulated), and manually creating packages.
-   The `create_package` method in this service is called by the API endpoint and is responsible for selecting documents based on packager filters and creating the initial `PackageEntry` records within a package obtained from `PackageCreationService`.
-   Implements the additive filtering logic (`get_filtered_document_data_additive`) used by the package preparation task to select documents.
-   Handles configuration overrides provided in API requests for simulation and download.

#### StreamPackageService

-   Handles the specific workflow for STREAM scheduled packagers.
-   Triggered by a signal on new document creation.
-   Checks if the new document matches the packager's filters.
-   Triggers the `prepare_packages` Celery task for the specific document and packager.

## 7. Model Definitions & Relationships

The Packager application relies on several key Django models to manage its configuration, state, and data.

```mermaid
erDiagram
    DataPool ||--o{ Packager : "has"
    Packager ||--o{ Package : "creates"
    Packager ||--o{ PeriodicTask : "manages (Celery Beat)"

    Package ||--o{ PackageEntry : "contains"
    PackageEntry }o--|| DocumentData : "references"
    DocumentData }o--|| Document : "represents data for"
    DataPool ||--o{ DocumentData : "belongs to"
    DataPool ||--o{ Document : "belongs to"

    Package ||--o{ ExtractionBatchData : "contains (legacy)"
    ExtractionBatchData }o--|| ExtractionBatch : "references"
    ExtractionBatch ||--o{ Document : "groups"

    DocumentData ||--o{ DocumentRelationship : "can be source in"
    DocumentData ||--o{ DocumentRelationship : "can be target in"
    DocumentRelationshipType ||--o{ DocumentRelationship : "defines type of"
    DataPool ||--o{ DocumentRelationship : "belongs to"
    User ||--o{ DocumentRelationship : "created by (optional)"

    DocumentData }o--|| DocumentData : "canonical_document_data (FK to self for duplicates)"

    CoCEventType ||--o{ CoCEvent : "defines type of"
    DataPool ||--o{ CoCEvent : "belongs to"
    Document "o|..o{" CoCEvent : "related to (optional)"
    Package "o|..o{" CoCEvent : "related to (optional)"
    User ||--o{ CoCEvent : "triggered by (optional)"

    class DataPool {
        string obfuscated_id
        string label
    }
    class Packager {
        uuid uuid
        string label
        string packager_status
        string packager_schedule_type
        string schedule_cron_expression
        datetime last_run
        json document_status_filter
        json eb_status_filter
        json document_id_filter
        json eb_id_filter
        string output_format
        string delivery_schedule_type
        string delivery_data_grouping_strategy
        string delivery_data_grouping_metadata_key
        json source_file_naming_config
        json processed_data_naming_config
        bool include_source_files_in_delivery
        bool exclude_duplicates_from_delivery
        bool append_coc_fields_to_data
        json coc_field_config
        date packaging_begin_date
        date packaging_end_date
        # ... other S3 and CoC report fields
    }
    class Package {
        uuid uuid
        fk packager_id
        string status
        datetime last_viewed
    }
    class PackageEntry {
        uuid uuid
        fk package_id
        fk document_data_id
        string status_in_package
        string filename_in_package
        string exclusion_reason
        datetime delivered_at
    }
    class DocumentData {
        uuid uuid
        fk document_id
        fk data_pool_id
        fk canonical_document_data_id (self-referential)
        string status
        string source_file_id
        string source_file_name
        string source_file_md5
        datetime received_at
        string content_hash
        string relationship_to_parent
        string parent_file_id
        bool is_md5_check_completed
        bool is_label_check_completed
        bool is_content_check_completed
        # ... other CoC and source file fields
    }
    class Document {
        uuid uuid
        string obfuscated_id
        string label
        string status
        string content_md5
        # ... other document fields
    }
    class ExtractionBatchData {
        uuid uuid
        fk package_id
        fk extraction_batch_id
        string status
    }
    class ExtractionBatch {
        uuid uuid
        string label
        string verification_status
    }
    class DocumentRelationshipType {
        uuid uuid
        string label
        string description
    }
    class DocumentRelationship {
        uuid uuid
        fk source_document_data_id
        fk target_document_data_id
        fk relationship_type_id
        fk created_by_id
        json metadata
    }
    class CoCEventType {
        uuid uuid
        string label
        string description
    }
    class CoCEvent {
        uuid uuid
        fk event_type_id
        fk document_id
        fk package_id
        fk user_id
        datetime timestamp
        json details
    }
    class PeriodicTask {
        string name
        string task
        string crontab
        string kwargs
        bool enabled
    }
    class User {
        string username
    }
```

### 6.1. `Packager`

*   **Purpose:** Central configuration hub for defining data collection, filtering, formatting, naming, duplicate
    handling, and delivery rules for a specific `DataPool`.
*   **Key Attributes:** See "Core Configuration" section for a detailed breakdown.
*   **Relationships:**
    *   Belongs to one `DataPool`.
    *   Can have many `Package`s.
    *   Manages a Celery Beat `PeriodicTask` for CRON-based scheduling.
*   **Key Methods:**
    *   `get_working_package()`: Retrieves or creates the appropriate `Package` for processing based on schedule type and package reuse logic.
    *   `get_filtered_documents()` / `get_filtered_batches()`: Applies configured filters to fetch relevant data (used internally or by related services).
    *   `clean()`: Validates naming configurations and cron expressions using Pydantic.
    *   `save()`: Saves the instance and updates/creates the Celery Beat `PeriodicTask`.
    *   `schedule_task()`: Manages the Celery Beat `PeriodicTask`.
    *   `get_config()`: Returns a `PackagerConfig` Pydantic model instance representing the current configuration.
    *   `has_config_changed_since_package()`: Checks if the packager configuration has changed significantly since a given package was created, influencing package reuse decisions.

### 6.2. `Package`

*   **Purpose:** Represents a specific collection of data (documents) gathered according to a `Packager`'s configuration
    at a point in time. It tracks the overall status of this collection through preparation and delivery.
*   **Key Attributes:**
    *   `packager`: ForeignKey to the parent `Packager`.
    *   `status`: Current state of the package (e.g., `CREATED`, `PROCESSING`, `VERIFIED`, `TRANSMISSION_SUCCESSFUL`).
        See "State Management".
    *   `last_viewed`: Timestamp of the last preview/delivery.
    *   `_packager_config`: Internal field to hold a `PackagerConfig` for simulated packages, allowing configuration overrides without modifying the persistent `Packager` instance.
*   **Relationships:**
    *   Belongs to one `DataPool`.
    *   Has many `PackageEntry` records, linking it to the `DocumentData` included in this package instance.
    *   (Legacy) Has many `ExtractionBatchData` records.
*   **Key Methods:**
    *   `get_effective_packager()` / `get_config()`: Returns the effective `PackagerConfig` (either from the linked
        `Packager` or a temporary one for simulation).
    *   `generate_processed_data_filename()`: Generates filenames for processed data files based on the
        `processed_data_naming_config`.
    *   `render_package_data()`: Orchestrates the rendering of all data within the package by delegating to `PackageRenderingService`. It groups `DocumentData` (via
        `PackageEntry` records) using grouping utilities, then uses naming utilities to format data and generate filenames
        for each output file. It handles different grouping strategies and output formats, and includes options for source files, relationship details, and CoC fields.

### 6.3. `DocumentData`

*   **Purpose:** Represents a specific document's metadata and status within the context of the packaging and Chain of
    Custody system. It acts as the central point for tracking a document's lifecycle for delivery and audit purposes.
*   **Key Attributes:**
    *   `document`: ForeignKey to the core `Document` model.
    *   `data_pool`: ForeignKey to the `DataPool`.
    *   `status`: The overall status of this document data entry (e.g., `CREATED`, `PENDING_PROCESSING`,
        `CHECKS_COMPLETED`).
    *   `source_file_id`, `source_file_name`, `source_file_md5`, `received_at`, `source_file_num_pages`,
        `source_file_size`, `source_file_detected_language`: Mirrored/cached information from the original `Document` and
        its upload.
    *   `content_hash`: MD5 hash of selected extracted data fields, used for content-based duplicate detection. Calculated by `calculate_content_hash`.
    *   `canonical_document_data`: Self-referential ForeignKey. If set, this `DocumentData` is a duplicate of the
        referenced `DocumentData`.
    *   `relationship_to_parent`, `parent_file_id`: Fields to store details about the duplicate relationship if
        `canonical_document_data` is set.
    *   `is_md5_check_completed`, `is_label_check_completed`, `is_content_check_completed`: Booleans tracking the
        completion of different duplicate checks.
    *   `exclusion_reason`: Stores why a document might be excluded from processing or delivery.
*   **Relationships:**
    *   Belongs to one `DataPool`.
    *   References one `Document`.
    *   Can be linked to many `PackageEntry` records (representing its inclusion in different package instances).
    *   Can be a source or target in `DocumentRelationship` records.
    *   Can point to another `DocumentData` as its `canonical_document_data`.
*   **Key Methods:**
    *   `calculate_content_hash()`: Calculates the content hash based on configured `deduplication_content_fields`.
    *   `generate_source_filename()`: Generates the filename for the original source file based on
        `source_file_naming_config`.
    *   `render_document_data()`: Prepares the extracted data for a single document for inclusion in a package, including
        applying the correct filename. Used by `PackageRenderingService`.

### 6.4. `PackageEntry`

*   **Purpose:** Acts as a through-table linking a `Package` to the specific `DocumentData` instances included in that
    particular package instance. It tracks the status and filename of each document *within that specific package
    delivery*.
*   **Key Attributes:**
    *   `package`: ForeignKey to `Package`.
    *   `document_data`: ForeignKey to `DocumentData`.
    *   `status_in_package` (CharField): Status of this document within this package (e.g., `PENDING`, `INCLUDED`,
        `EXCLUDED_AS_DUPLICATE`, `TRANSMISSION_SUCCESSFUL`). See "State Management".
    *   `filename_in_package`: The actual filename used for this document's data file in this package.
    *   `exclusion_reason`: Reason for exclusion if `status_in_package` indicates exclusion.
    *   `delivered_at`: Timestamp of when this entry was successfully delivered.
*   **Relationships:**
    *   Belongs to one `Package`.
    *   Belongs to one `DocumentData`.
*   **Justification:** This model decouples the general state of a `DocumentData` from its state within a specific package
    instance. A single `DocumentData` can be part of multiple packages over time, potentially with different statuses or
    filenames in each. `PackageEntry` captures this instance-specific information.

### 6.5. `ExtractionBatchData`

*   **Purpose:** (Largely legacy) Links `ExtractionBatch`es to `Package`s. The primary mechanism for including documents
    in packages is now via `DocumentData` and `PackageEntry`.
*   **Key Attributes:** `package`, `extraction_batch`, `status`.
*   **Note:** While still present, its role is diminished. The package preparation task primarily focuses on
    `DocumentData` and creates `PackageEntry` records. If an `ExtractionBatchData` is created, its `save()` method automatically ensures corresponding
    `DocumentData` and `PackageEntry` records are made for the documents within that batch.

### 6.6. Chain of Custody & Relationship Models

*   **`CoCEventType`**: Defines types of CoC events (e.g., `DOCUMENT_CREATED`, `PACKAGE_CREATED`, `RELATIONSHIP_CREATED`).
*   **`CoCEvent`**: Records an instance of a CoC event, linking to `CoCEventType`, `DataPool`, and optionally `Document`,
    `Package`, `User`, with JSON `details`.
*   **`DocumentRelationshipType`**: Defines types of relationships between documents (e.g., `MD5_DUPLICATE`,
    `LABEL_DUPLICATE`, `CONTENT_DUPLICATE`).
*   **`DocumentRelationship`**: Records an explicit relationship between two `DocumentData` instances (source and target)
    of a specific `DocumentRelationshipType`. Used by `DuplicateDetectionService`.
    *   *Justification:* These models provide a robust framework for auditing and tracking document provenance and
        interconnections, crucial for data integrity and compliance.

## 8. State Management and Statuses

The Packager system uses distinct statuses for its core entities to track their lifecycle.

### 8.1. `PackagerStatus`

Governs the overall state of a `Packager` configuration.

| Status                    | Definition                                                                                                 |
| :------------------------ | :--------------------------------------------------------------------------------------------------------- |
| `CREATED`                 | Initial state after `Packager` configuration is saved. Awaiting first run or trigger.                      |
| `PACKAGE_CREATED`         | Indicates the `Packager` has successfully initiated the creation of a new `Package`. (More of an event)    |
| `IDLE`                    | The `Packager` is not actively processing any package. Awaiting next scheduled run or trigger.             |
| `PROCESSING`              | The `Packager`'s package preparation task is actively collecting data and preparing a `Package`.           |
| `PROCESSING_SUCCESSFUL`   | The package preparation task completed successfully for the latest run. Package data is ready.             |
| `PROCESSING_FAILED`       | The package preparation task encountered an error during the latest run.                                   |
| `UNVERIFIED`              | (Potentially legacy or for manual flows) A package associated with this packager is awaiting verification. |
| `VERIFIED`                | (Potentially legacy or for manual flows) A package associated with this packager has been verified.        |
| `TRANSMISSION_SUCCESSFUL` | The last delivery attempt for a package under this packager was successful.                                |
| `TRANSMISSION_FAILED`     | The last delivery attempt for a package under this packager failed.                                        |

### 8.2. `PackageStatus`

Tracks the state of an individual `Package` instance.

| Status                     | Definition                                                                                                      |
| :------------------------- | :-------------------------------------------------------------------------------------------------------------- |
| `CREATED`                  | Initial state of a `Package` after being created by a `Packager`. Data collection may not be complete.          |
| `PROCESSING`               | The package preparation task is actively working on this specific `Package` (populating `PackageEntry`s, etc.). |
| `IDLE`                     | The `Package` is not currently being actively processed.                                                        |
| `VERIFIED`                 | The `Package` has been verified (often a manual step or external process) and is ready for delivery.            |
| `FROZEN`                   | (Potentially for future use) The `Package` contents are locked and cannot be changed.                           |
| `RENDER_SUCCESSFUL`        | (Primarily internal/event) Data rendering for delivery was successful.                                          |
| `RENDER_FAILED`            | (Primarily internal/event) Data rendering for delivery failed.                                                  |
| `PROCESSING_SUCCESSFUL`    | All data collection and pre-processing (like duplicate checks) for this `Package` completed successfully.       |
| `PROCESSING_FAILED`        | An error occurred during data collection or pre-processing for this `Package`.                                  |
| `TRANSMISSION_SUCCESSFUL`  | This `Package` was successfully delivered to the destination (e.g., S3 upload confirmed).                       |
| `TRANSMISSION_FAILED`      | Delivery of this `Package` failed. Error details should be captured.                                            |
| `DOCUMENT_ADDED`           | (Event-like) A document was added to the package.                                                               |
| `DOCUMENT_REMOVED`         | (Event-like) A document was removed from the package.                                                           |
| `EXTRACTION_BATCH_ADDED`   | (Event-like, legacy) An extraction batch was added.                                                             |
| `EXTRACTION_BATCH_REMOVED` | (Event-like, legacy) An extraction batch was removed.                                                           |
| `TRANSFORMED_URL_ADDED`    | (Event-like, potentially for specific data types) A transformed URL was added.                                  |
| `TRANSFORMED_URL_REMOVED`  | (Event-like) A transformed URL was removed.                                                                     |

### 8.3. `DocumentDataStatus` (for `DocumentData.status`)

Tracks the general status of a document within the packaging context.

| Status                    | Definition (for `DocumentData.status`)                                                                      |
| :------------------------ | :---------------------------------------------------------------------------------------------------------- |
| `PENDING_CREATION`        | (More relevant to `Document.status`) Document record is being initialized.                                  |
| `CREATED`                 | `DocumentData` record created, typically upon `Document` creation. Initial duplicate checks may be pending. |
| `PENDING_PROCESSING`      | (Used internally by package preparation) `DocumentData` is identified for inclusion in a package.           |
| `CHECKS_COMPLETED`        | (Used by migration service) Initial duplicate checks (MD5, Label) have been run.                            |
| `PENDING_REVIEW`          | (More relevant to `Document.status`) Document data is awaiting human review.                                |
| `REVIEWED`                | (More relevant to `Document.status`) Document data has been reviewed.                                       |
| `VERIFIED`                | (More relevant to `Document.status`) Document data is verified and final. Content duplicate check can run.  |
| `TRANSMISSION_SUCCESSFUL` | (More relevant to `Document.status` or `PackageEntryStatus`) Document was part of a successful delivery.    |
| `TRANSMISSION_FAILED`     | (More relevant to `Document.status` or `PackageEntryStatus`) Document was part of a failed delivery.        |
| `MIGRATED`                | (Used by `DataMigrationService`) `DocumentData` was created from an existing `Document`.                    |

### 8.4. `PackageEntryStatus`

Tracks the status of a specific `DocumentData` *within a particular `Package` instance*.

| Status                    | Definition                                                                                                                       |
| :------------------------ | :------------------------------------------------------------------------------------------------------------------------------- |
| `PENDING`                 | DocumentData has been associated with the package, awaiting further processing (e.g., duplicate checks).                         |
| `INCLUDED`                | Document is marked for inclusion in this package delivery after checks.                                                          |
| `TRANSMISSION_SUCCESSFUL` | This document entry was successfully delivered as part of this package.                                                          |
| `EXCLUDED`                | Generic exclusion status. More specific statuses below are preferred.                                                            |
| `EXCLUDED_AS_DUPLICATE`   | Document was excluded from this package because it was identified as a duplicate and `exclude_duplicates_from_delivery` is true. |
| `EXCLUDED_BY_STATUS`      | Document was excluded due to its own status (e.g., `DocumentData.exclusion_reason` was set).                                     |
| `EXCLUDED_BY_CONFIG`      | Document was excluded based on some other packager configuration (less common, specific logic needed).                           |
| `TRANSMISSION_FAILED`     | This document entry failed to be delivered as part of this package.                                                              |

### 8.5. `ExtractionBatchData`

*   **Purpose:** (Largely legacy) Links `ExtractionBatch`es to `Package`s. The primary mechanism for including documents
    in packages is now via `DocumentData` and `PackageEntry`.
*   **Key Attributes:** `package`, `extraction_batch`, `status`.
*   **Note:** While still present, its role is diminished. The package preparation task primarily focuses on
    `DocumentData` and creates `PackageEntry` records. If an `ExtractionBatchData` is created, its `save()` method automatically ensures corresponding
    `DocumentData` and `PackageEntry` records are made for the documents within that batch.

### 8.6. Chain of Custody & Relationship Models

*   **`CoCEventType`**: Defines types of CoC events (e.g., `DOCUMENT_CREATED`, `PACKAGE_CREATED`, `RELATIONSHIP_CREATED`).
*   **`CoCEvent`**: Records an instance of a CoC event, linking to `CoCEventType`, `DataPool`, and optionally `Document`,
    `Package`, `User`, with JSON `details`.
*   **`DocumentRelationshipType`**: Defines types of relationships between documents (e.g., `MD5_DUPLICATE`,
    `LABEL_DUPLICATE`, `CONTENT_DUPLICATE`).
*   **`DocumentRelationship`**: Records an explicit relationship between two `DocumentData` instances (source and target)
    of a specific `DocumentRelationshipType`. Used by `DuplicateDetectionService`.
    *   *Justification:* These models provide a robust framework for auditing and tracking document provenance and
        interconnections, crucial for data integrity and compliance.

## 9. API Endpoints

The Packager application provides a comprehensive REST API for managing packagers, packages, and related operations.

### 9.1. Packager Management

#### List Packagers
- **Endpoint:** `GET /api/v6/data-pools/{data_pool_id}/packagers/`
- **Purpose:** Retrieve all packagers for a data pool
- **Query Parameters:**
  - `packager_status`: Filter by packager status
  - `label`: Filter by packager label
  - `advanced=true`: Required for access
- **Response:** Paginated list of packager configurations

#### Create Packager
- **Endpoint:** `POST /api/v6/data-pools/{data_pool_id}/packagers/`
- **Purpose:** Create new packager configuration
- **Required Fields:** `label`, `s3_bucket`
- **Optional Fields:** All other packager configuration fields
- **Validation:** Cron expressions, naming configs, filter arrays

#### Retrieve Packager
- **Endpoint:** `GET /api/v6/data-pools/{data_pool_id}/packagers/{packager_id}/`
- **Purpose:** Get detailed packager configuration
- **Response:** Complete packager configuration with all fields

#### Update Packager
- **Endpoint:** `PATCH /api/v6/data-pools/{data_pool_id}/packagers/{packager_id}/`
- **Purpose:** Partial update of packager configuration
- **Supports:** All packager fields for partial updates
- **Validation:** Same as create endpoint

#### Delete Packager
- **Endpoint:** `DELETE /api/v6/data-pools/{data_pool_id}/packagers/{packager_id}/`
- **Purpose:** Remove packager configuration
- **Effect:** Cascades to associated packages and entries

### 9.2. Package Operations

#### Simulate Package
- **Endpoint:** `POST /api/v6/data-pools/{data_pool_id}/packagers/{packager_id}/simulate-package/`
- **Purpose:** Preview package contents without creating actual package
- **Request Body:** `SimulatePackageInput` with optional configuration overrides
- **Response:** `PackageRenderResult` with simulated package contents
- **Features:**
  - Configuration overrides for testing
  - Document filtering preview
  - Duplicate detection simulation

#### Download Package
- **Endpoint:** `POST /api/v6/data-pools/{data_pool_id}/packagers/{packager_id}/download-package/`
- **Purpose:** Download existing or simulated package as ZIP
- **Request Body:** Package ID or simulation parameters
- **Response:** ZIP file with package contents
- **Contents:**
  - Data files (JSON/CSV)
  - Source files (if configured)
  - Package metadata

#### Create Package
- **Endpoint:** `POST /api/v6/data-pools/{data_pool_id}/packagers/{packager_id}/create-package/`
- **Purpose:** Manually initiate the creation of package entries for documents matching the packager's filters.
- **Effect:** Retrieves documents based on packager configuration and creates `PackageEntry` records within a package obtained from `PackageCreationService`. The full package preparation workflow (including duplicate detection, rendering, and status updates) is handled by the asynchronous `prepare_packages` task, which is triggered by schedules or signals, not directly by this endpoint.
- **Response:** Information about the package where entries were created.

#### Trigger Delivery
- **Endpoint:** `POST /api/v6/data-pools/{data_pool_id}/packagers/{packager_id}/trigger-delivery/`
- **Purpose:** Manually trigger package delivery
- **Request Body:** `package_id` (optional, uses latest if not provided)
- **Effect:** Initiates delivery to S3 with notifications by triggering the `deliver_package` Celery task.
- **Validation:** Package must be in VERIFIED status

### 9.3. Utility Endpoints

#### Available Attributes
- **Endpoint:** `GET /api/v6/data-pools/{data_pool_id}/packagers/{packager_id}/available-attributes/`
- **Purpose:** Get available metadata attributes for naming configuration
- **Response:** Hierarchical structure of available model fields
- **Usage:** Assists in building naming configurations

#### Chain of Custody Report
- **Endpoint:** `POST /api/v6/data-pools/{data_pool_id}/packagers/{packager_id}/chain-of-custody-report/`
- **Purpose:** Generate comprehensive CoC report
- **Request Body:**
  - `start_date`: Report start date (optional, defaults to packager config)
  - `end_date`: Report end date (optional, defaults to packager config)
  - `download`: Boolean for file download vs. JSON response
- **Response:** CoC report in JSON or CSV format
- **Compliance:** Full glynt-schemas GtdrChainOfCustodyCsv compliance

### 9.4. Authentication and Permissions

**Authentication Requirements:**
- All endpoints require user authentication
- Data pool access validation for all operations
- User must have access to specified data pool

**Permission Scoping:**
- Users can only access packagers within their assigned data pools
- Cross-data-pool access is forbidden
- Admin users have broader access within their organizations

**Error Handling:**
- `403 Forbidden`: Insufficient permissions or data pool access
- `400 Bad Request`: Validation errors with detailed messages
- `404 Not Found`: Packager or package not found
- `500 Internal Server Error`: System errors with logging

### 9.5. Request/Response Formats

**Content Types:**
- Request: `application/json`
- Response: `application/json` (default), `application/zip` (downloads), `text/csv` (CoC reports)

**Pagination:**
- List endpoints use standard DRF pagination
- Default page size: 20 items
- Includes `count`, `next`, `previous`, `results`

**Validation Responses:**
- Field-level validation errors
- Configuration validation (cron expressions, naming configs)
- Business rule validation (duplicate detection settings, etc.)

## 10. Task Processing and Celery Integration

### 10.1. Core Tasks

#### `prepare_packages`
- **Queue:** `glynt_api_packager`
- **Purpose:** Main package preparation task. Collects documents using additive filtering, creates or reuses a package, creates PackageEntry records, triggers duplicate detection, and updates package and packager statuses.
- **Parameters:**
  - `packager_id`: The packager to process
  - `document_id`: Optional specific document for stream processing
- **Workflow:**
  1. Acquires database lock for concurrency control
  2. Collects documents using additive filtering (`PackageOperationsService.get_filtered_document_data_additive`)
  3. Creates or reuses package based on schedule type (`PackageCreationService.get_working_package`)
  4. Creates PackageEntry records for selected documents
  5. Triggers duplicate detection (`DuplicateDetectionService.run_checks`)
  6. Updates PackageEntry statuses based on duplicate detection results
  7. Updates package and packager status

#### `deliver_package`
- **Queue:** `glynt_api_packager`
- **Purpose:** Package delivery to S3
- **Parameters:**
  - `package_id`: The package to deliver
  - `data_pool_id`: Data pool for validation
- **Workflow:**
  1. Validates package is in VERIFIED status
  2. Renders package data using PackageRenderingService
  3. Uploads data files and source files to S3
  4. Updates package status and PackageEntry delivery timestamps
  5. Sends delivery notifications

#### `trigger_document_data_checks_task`
- **Queue:** `glynt_api_packager`
- **Purpose:** Duplicate detection checks on DocumentData
- **Parameters:**
  - `document_data_pk`: The DocumentData instance to check
  - `check_types`: List of check types ('MD5', 'LABEL', 'CONTENT')
- **Workflow:**
  1. Initializes DuplicateDetectionService
  2. Runs specified duplicate detection checks
  3. Creates DocumentRelationship records
  4. Updates canonical pointers and check completion flags

### 10.2. Task Scheduling and Triggers

**CRON Scheduling:**
- Managed via `django-celery-beat`
- Database-stored periodic tasks
- Automatic task creation/update when packager configuration changes

**Stream Processing:**
- Triggered by Django signals on document creation
- Real-time processing for immediate delivery needs
- Always creates new packages per document

**Manual Triggers:**
- API endpoints for on-demand processing
- User-initiated package creation (creates entries) and delivery (triggers task)
- Testing and debugging workflows

**Error Handling:**
- Comprehensive logging with structured data
- Status tracking with rollback capabilities
- Retry mechanisms for transient failures
- Dead letter queues for failed tasks. Task failures are logged and result in status updates on associated package and packager instances.

**Queue Management:**
- Dedicated `glynt_api_packager` queue
- Resource allocation and monitoring
- Priority handling for different task types

This specification provides a comprehensive overview of the Packager system's architecture, functionality, and implementation details. The system is designed for reliability, scalability, and compliance with data governance requirements.
