import csv
import io
import json
import logging
from datetime import datetime, timezone, timedelta
from unittest.mock import MagicMock, Mock
from unittest.mock import PropertyMock
from unittest.mock import patch

from django.http import HttpResponse
from django.test import Client
from django.test import TransactionTestCase, override_settings
from glynt_schemas.document.document_attributes import DocumentDataStatus
from glynt_schemas.extraction_batch.extraction_batch_attributes import ExtractionBatchVerificationStatus
from glynt_schemas.packager.package_attributes import PackageStatus
from glynt_schemas.packager.packager_attributes import (PackagerDataOutputFormats, PackagerDeliveryOptions,
                                                        PackagerSchedules, PackagerStatus, PackagerGroupingOptions, )
from rest_framework import status
from training.tests.util import create_training_ready_training_set
from training.tests.util import create_training_revision, create_training_set
from users.tests.util import create_staff_user, create_user

from extract.tests.util import create_extraction
from extract.tests.util import create_extraction_batch
from glynt_api.tests.util import TestCaseMixin
from organizations.tests.util import create_data_pool, create_organization
from packager.models import DocumentData, PackageEntry
from packager.models import DocumentRelationship, DocumentRelationshipType
from packager.models import Package, ExtractionBatchData, Packager
from packager.pydantic_classes import PackageEntryStatus
from packager.pydantic_classes import PackageRenderResult, RenderedPackageItem, SimulatePackageInput, DuplicateFinding
from packager.services.package_operations_service import PackageOperationsService
from packager.tests.util import create_packager, get_api_path
from packager.utils.duplicate_utils import filter_duplicate_documents

mock_storage = Mock()
with patch("api_storage.storage_client", return_value=mock_storage):
    from documents.tests.util import create_document

logger = logging.getLogger(__name__)

mock_storage.generate_presigned_get_url.return_value = "http://mock.url"
mock_storage.generate_presigned_put_url.return_value = "http://mock.upload.url"
mock_storage.check_object_exists.return_value = True


def mock_s3_download_fileobj(Bucket, Key, Fileobj, Callback=None, Config=None, ExtraArgs=None):
    Fileobj.write(b"mock file content from storage")


mock_storage.download_fileobj.side_effect = mock_s3_download_fileobj


@override_settings(DEBUG=False)  # Disable SQL logging for performance
class PackagerViewSetTests(TransactionTestCase, TestCaseMixin):

    def setUp(self):
        """Set up test data - optimized with helper methods."""
        super().setUp()

        self.mock_doc_bytes = b"mock document content"
        self.patcher = patch("documents.models.Document.file_content", new_callable=PropertyMock,
                             return_value=self.mock_doc_bytes)
        self.mock_file_content_property = self.patcher.start()

        self.staff_user, self.staff_user_path = create_staff_user()
        self.staff_client = Client()
        self.staff_client.login(username="staffuser", password="12345")

        self.user, self.user_path = create_user()
        self.client = Client()
        self.client.login(username="normaluser", password="12345")

        self.org, _ = create_organization()
        self.dp, _ = create_data_pool(organization=self.org)
        self.doc, _ = create_document(data_pool=self.dp, label="doc1_PackagerViewSetTests")
        self.doc2, _ = create_document(data_pool=self.dp, label="doc2_PackagerViewSetTests")
        self.doc3, _ = create_document(data_pool=self.dp, label="doc3_PackagerViewSetTests")
        self.doc4, _ = create_document(data_pool=self.dp, label="doc4_PackagerViewSetTests")

        import uuid
        test_id = str(uuid.uuid4())[:8]

        self.ts, _ = create_training_ready_training_set(data_pool=self.dp, label=f"PackagerViewSetTests_ts_{test_id}")
        self.tr, _ = create_training_revision(data_pool=self.dp, training_set=self.ts)

        self.dp2, _ = create_data_pool(label="test dp2", organization=self.org)
        self.dp2_doc, _ = create_document(data_pool=self.dp2)
        self.dp2_packager = create_packager(data_pool=self.dp2, label="Test2 Packager", packager_status="IDLE")

        self.dp2_ts, _ = create_training_ready_training_set(data_pool=self.dp2,
                                                            label=f"PackagerViewSetTests_dp2_ts_{test_id}")
        self.dp2_tr, _ = create_training_revision(data_pool=self.dp2, training_set=self.dp2_ts)
        self.dp2_package = Package.objects.create(packager=self.dp2_packager, data_pool=self.dp2)
        self.dp2_eb, self.dp2__path = create_extraction_batch(data_pool=self.dp2, documents=[self.dp2_doc],
                                                              training_revision=self.dp2_tr)

        self.user.organizations.set([self.org])
        self.user.data_pools.set([self.dp, self.dp2])
        self.user.save()

        self.packager = create_packager(data_pool=self.dp, label="Test Packager")
        self.packager2 = create_packager(data_pool=self.dp, label="Test2 Packager", packager_status="IDLE")
        self.packager3 = create_packager(data_pool=self.dp, label="Test3 Packager", packager_status="IDLE")

        self.packager_url = get_api_path("v6:packager-detail", self.dp.obfuscated_id,
                                         obfuscated_id=self.packager.obfuscated_id, advanced=True)

        self.package = Package.objects.create(packager=self.packager, data_pool=self.dp)

        self.eb, self.eb_path = create_extraction_batch(data_pool=self.dp, documents=[self.doc2],
                                                        training_revision=self.tr)
        self.eb2, self.eb_path2 = create_extraction_batch(data_pool=self.dp, documents=[self.doc4],
                                                          training_revision=self.tr)

        self.doc_for_package, _ = create_document(data_pool=self.dp,
                                                  label="doc_for_package_PackagerViewSetTests")

        self.eb_data = ExtractionBatchData.objects.create(package=self.package, extraction_batch=self.eb,
                                                          data_pool=self.dp)
        self.mock_transformed_data = [{"field1": "value1", "field2": "value2", "DocumentLabel": "test_doc.pdf"}]

    def _create_document_data(self, document, status=DocumentDataStatus.VERIFIED.name, **kwargs):
        """Helper method to efficiently create DocumentData."""
        defaults = {
            "status": status,
            "source_file_name": document.label,
            "source_file_md5": document.content_md5 or "default_md5",
        }
        defaults.update(kwargs)

        doc_data, created = DocumentData.objects.get_or_create(
            document=document,
            data_pool=document.data_pool,
            defaults=defaults
        )

        if not created:
            for key, value in defaults.items():
                setattr(doc_data, key, value)
            doc_data.save()

        return doc_data

    def test_create_packager_default(self):
        url = get_api_path("v6:packager-list", self.dp.obfuscated_id, advanced=True)
        data = {"label": "Default Packager", "s3_bucket": "test"}
        response = self.client.post(url, data=json.dumps(data), content_type="application/json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.json()["label"], "Default Packager")
        self.assertEqual(response.json()["output_format"], PackagerDataOutputFormats.JSON.value)
        self.assertEqual(response.json()["packager_status"], PackagerStatus.CREATED.value)
        self.assertEqual(response.json()["packager_schedule_type"], PackagerSchedules.MANUAL.value)
        self.assertEqual(response.json()["schedule_cron_expression"], "0 * * * *")
        self.assertEqual(response.json()["delivery_schedule_type"], PackagerDeliveryOptions.MANUAL.value)
        self.assertEqual(response.json()["document_status_filter"], [DocumentDataStatus.VERIFIED.name])
        self.assertEqual(response.json()["eb_status_filter"], [ExtractionBatchVerificationStatus.VERIFIED.name])
        self.assertEqual(response.json()["document_id_filter"], [])
        self.assertEqual(response.json()["eb_id_filter"], [])

    def test_create_packager_custom(self):
        url = get_api_path("v6:packager-list", self.dp.obfuscated_id, advanced=True)
        data = {"label": "New Packager", "packager_schedule_type": "MANUAL", "schedule_cron_expression": "0 * * * *",
                "delivery_data_grouping_strategy": "SPLIT_BY_DOC", "document_status_filter": ["PENDING_CREATION"],
                "s3_bucket": "test", "prefix": "test/prefix", "region": "us-west-1", }
        response = self.client.post(url, data=json.dumps(data), content_type="application/json")
        logger.info(response.content)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.json()["label"], "New Packager")
        self.assertEqual(response.json()["output_format"], PackagerDataOutputFormats.JSON.value)
        self.assertEqual(response.json()["packager_status"], PackagerStatus.CREATED.value)
        self.assertEqual(response.json()["packager_schedule_type"], PackagerSchedules.MANUAL.value)
        self.assertEqual(response.json()["schedule_cron_expression"], "0 * * * *")
        self.assertEqual(response.json()["delivery_schedule_type"], PackagerDeliveryOptions.MANUAL.value)
        self.assertEqual(response.json()["document_status_filter"], [DocumentDataStatus.PENDING_CREATION.value])
        self.assertEqual(response.json()["eb_status_filter"], [ExtractionBatchVerificationStatus.VERIFIED.name])
        self.assertEqual(response.json()["document_id_filter"], [])
        self.assertEqual(response.json()["eb_id_filter"], [])

    def test_create_packager_invalid_cron(self):
        url = get_api_path("v6:packager-list", self.dp.obfuscated_id, advanced=True)
        data = {"label": "Invalid Cron Packager", "schedule_cron_expression": "invalid_cron", "s3_bucket": "test"}
        response = self.client.post(url, data=json.dumps(data), content_type="application/json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_retrieve_packager(self):
        packager = create_packager(data_pool=self.dp, label="Retrieve Test Packager", )

        packager_url = get_api_path("v6:packager-detail", self.dp.obfuscated_id, obfuscated_id=packager.obfuscated_id,
                                    advanced=True)
        response = self.client.get(packager_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["label"], packager.label)
        self.assertEqual(response.json()["output_format"], PackagerDataOutputFormats.JSON.value)
        self.assertEqual(response.json()["packager_status"], PackagerStatus.CREATED.value)
        self.assertEqual(response.json()["packager_schedule_type"], PackagerSchedules.MANUAL.value)
        self.assertEqual(response.json()["schedule_cron_expression"], "0 * * * *")
        self.assertEqual(response.json()["delivery_schedule_type"], PackagerDeliveryOptions.MANUAL.value)
        self.assertEqual(response.json()["document_status_filter"], [DocumentDataStatus.VERIFIED.name])
        self.assertEqual(response.json()["eb_status_filter"], [ExtractionBatchVerificationStatus.VERIFIED.name])
        self.assertEqual(response.json()["document_id_filter"], [])
        self.assertEqual(response.json()["eb_id_filter"], [])

        response = self.client.delete(packager_url)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertFalse(Packager.objects.filter(id=packager.id).exists())

    def test_retrieve_all_packagers(self):
        all_packagers_url = get_api_path("v6:packager-list", self.dp.obfuscated_id, advanced=True)
        response = self.client.get(all_packagers_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["count"], 3)

    def test_update_packager(self):
        data = {"label": "Updated Packager", "packager_schedule_type": "CRON_SCHEDULE",
                "schedule_cron_expression": "0 8 * * *", }
        response = self.client.patch(self.packager_url, data=json.dumps(data), content_type="application/json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.json()["label"], "Updated Packager")
        self.assertEqual(response.json()["output_format"], PackagerDataOutputFormats.JSON.value)
        self.assertEqual(response.json()["packager_status"], PackagerStatus.CREATED.value)
        self.assertEqual(response.json()["packager_schedule_type"], PackagerSchedules.CRON_SCHEDULE.value)
        self.assertEqual(response.json()["schedule_cron_expression"], "0 8 * * *")
        self.assertEqual(response.json()["delivery_schedule_type"], PackagerDeliveryOptions.MANUAL.value)
        self.assertEqual(response.json()["document_status_filter"], [DocumentDataStatus.VERIFIED.name])
        self.assertEqual(response.json()["eb_status_filter"], [ExtractionBatchVerificationStatus.VERIFIED.name])
        self.assertEqual(response.json()["document_id_filter"], [])
        self.assertEqual(response.json()["eb_id_filter"], [])

    def test_update_packager_invalid_cron(self):
        data = {"label": "Updated Packager", "packager_schedule_type": "CRON_SCHEDULE",
                "schedule_cron_expression": "invalid_cron", }
        response = self.client.patch(self.packager_url, data=json.dumps(data), content_type="application/json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_delete_packager(self):
        response = self.client.delete(self.packager_url)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertFalse(Packager.objects.filter(id=self.packager.id).exists())

    def test_create_packager_invalid_naming_config(self):
        url = get_api_path("v6:packager-list", self.dp.obfuscated_id, advanced=True)
        data = {"label": "Invalid Naming Config Packager", "s3_bucket": "test",
                "source_file_naming_config": {"elements": [{"type": "INVALID_TYPE", "value": "test"}],
                                              "separator": "_"}, }
        response = self.client.post(url, data=json.dumps(data), content_type="application/json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("source_file_naming_config", response.json())

    def test_create_packager_invalid_grouping_strategy(self):
        url = get_api_path("v6:packager-list", self.dp.obfuscated_id, advanced=True)
        data = {"label": "Invalid Grouping Strategy Packager", "s3_bucket": "test",
                "delivery_data_grouping_strategy": "INVALID_STRATEGY", }
        response = self.client.post(url, data=json.dumps(data), content_type="application/json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("delivery_data_grouping_strategy", response.json())

    def test_retrieve_packager_by_status(self):
        url = get_api_path("v6:packager-list", self.dp.obfuscated_id, advanced=True) + "&packager_status=IDLE"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["count"], 2)

    def test_retrieve_packager_by_label(self):
        url = get_api_path("v6:packager-list", self.dp.obfuscated_id, advanced=True) + "&label=Test3 Packager"

        response = self.client.get(url)
        self.assertEqual(response.json()["count"], 1)

    def test_create_packager_with_naming_config(self):
        url = get_api_path("v6:packager-list", self.dp.obfuscated_id, advanced=True)
        data = {"label": "Naming Config Packager", "source_file_naming_config": {
            "elements": [{"type": "METADATA", "value": "document.created_at"},
                         {"type": "STRING_LITERAL", "value": "REPORT"}, ], "separator": "_", "max_length": 40, },
                "s3_bucket": "test", }
        response = self.client.post(url, data=json.dumps(data), content_type="application/json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.json()["source_file_naming_config"]["max_length"], 40)

    def test_packager_permissions_unauthenticated(self):
        """Test that unauthenticated users cannot access Packager endpoints."""
        unauthenticated_client = Client()
        list_url = get_api_path("v6:packager-list", self.dp.obfuscated_id, advanced=True)
        detail_url = get_api_path("v6:packager-detail", self.dp.obfuscated_id,
                                  obfuscated_id=self.packager.obfuscated_id, advanced=True)
        available_attributes_url = get_api_path("v6:packager-available-attributes", self.dp.obfuscated_id,
                                                obfuscated_id=self.packager.obfuscated_id, advanced=True, )

        response = unauthenticated_client.get(list_url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        response = unauthenticated_client.get(detail_url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        # Test create endpoint
        data = {"label": "Unauthorized Packager", "s3_bucket": "test"}
        response = unauthenticated_client.post(list_url, data=json.dumps(data), content_type="application/json")
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        # Test update endpoint
        data = {"label": "Updated Unauthorized Packager"}
        response = unauthenticated_client.patch(detail_url, data=json.dumps(data), content_type="application/json")
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        # Test delete endpoint
        response = unauthenticated_client.delete(detail_url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        # Test available-attributes endpoint
        response = unauthenticated_client.get(available_attributes_url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_update_packager_naming_config(self):
        data = {"source_file_naming_config": {
            "elements": [{"type": "METADATA", "value": "document.created_at", "format": "%Y-%m-%d"},
                         {"type": "METADATA", "value": "data_pool.id"},
                         {"type": "STRING_LITERAL", "value": "REPORT"}, ], "separator": ".", "max_length": 50, }}
        response = self.client.patch(self.packager_url, data=json.dumps(data), content_type="application/json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.json()["source_file_naming_config"]["separator"], ".")

    def test_retrieve_available_attributes(self):
        packager = create_packager(data_pool=self.dp, label="Attribute Test Packager", )
        packager_url = get_api_path("v6:packager-available-attributes", self.dp.obfuscated_id,
                                    obfuscated_id=packager.obfuscated_id, advanced=True, )
        response = self.client.get(packager_url)
        logger.info(f"attribute url: {packager_url}")
        logger.info(response.content)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        self.assertIn(b"document", response.content)
        self.assertIn(b"extraction_batch", response.content)
        self.assertIn(b"data_pool", response.content)
        self.assertIn(b"organization", response.content)

    @patch("boto3.client")
    @patch("packager.tasks._upload_to_s3")
    def test_trigger_delivery_success(self, mock_upload_to_s3, mock_boto3):
        """Test successful manual delivery trigger"""
        self.package.status = PackageStatus.VERIFIED
        self.package.save()

        doc_data = self._create_document_data(self.doc, status="VERIFIED", source_file_name="test_document.pdf",
                                              source_file_md5="test_md5_hash")

        PackageEntry.objects.create(package=self.package, document_data=doc_data, data_pool=self.dp,
                                    status_in_package=PackageEntryStatus.INCLUDED.name, )

        mock_sns = MagicMock()
        mock_boto3.return_value = mock_sns
        mock_upload_to_s3.return_value = True

        url = get_api_path("v6:packager-trigger-delivery", self.dp.obfuscated_id,
                           obfuscated_id=self.packager.obfuscated_id, advanced=True, )

        response = self.client.post(url, data=json.dumps({"package_id": self.package.obfuscated_id}),
                                    content_type="application/json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["message"], "Delivery triggered successfully")
        self.assertEqual(response.json()["package_id"], self.package.obfuscated_id)

        mock_upload_to_s3.assert_called()

        self.package.refresh_from_db()

    def test_packager_data_pool_scoping(self):
        """Test that users can only access packagers within their data pools."""
        # Create a user with access to only one data pool (self.dp)
        user_dp1, _ = create_user(username="user_dp1_test", password="password123", email="<EMAIL>")
        user_dp1.organizations.set([self.org])
        user_dp1.data_pools.set([self.dp])
        user_dp1.save()
        client_dp1 = Client()
        client_dp1.login(username="user_dp1_test", password="password123")

        # Create a packager in self.dp (should be accessible)
        packager_dp1 = create_packager(data_pool=self.dp, label="Packager DP1")
        packager_dp1_url = get_api_path("v6:packager-detail", self.dp.obfuscated_id,
                                        obfuscated_id=packager_dp1.obfuscated_id, advanced=True)

        # Create a packager in self.dp2 (should NOT be accessible)
        packager_dp2 = create_packager(data_pool=self.dp2, label="Packager DP2")
        packager_dp2_url = get_api_path("v6:packager-detail", self.dp2.obfuscated_id,
                                        obfuscated_id=packager_dp2.obfuscated_id, advanced=True)

        # Refresh user and re-login client to ensure correct data pool associations
        user_dp1.refresh_from_db()
        client_dp1.login(username="user_dp1_test", password="password123")

        # Test accessing packager in the user's data pool
        response = client_dp1.get(packager_dp1_url)
        print(response.json())
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["label"], "Packager DP1")

        # Test accessing packager in a different data pool
        response = client_dp1.get(packager_dp2_url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        # Test listing packagers - should only show packagers in self.dp
        list_url_dp1 = get_api_path("v6:packager-list", self.dp.obfuscated_id, advanced=True)
        response = client_dp1.get(list_url_dp1)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # Check that packager_dp1 is in the list and packager_dp2 is not
        packager_labels_in_list = [item["label"] for item in response.json()["results"]]
        self.assertIn("Packager DP1", packager_labels_in_list)
        self.assertNotIn("Packager DP2", packager_labels_in_list)

        # Test creating a packager in a different data pool (should not be allowed via URL)
        # The URL itself restricts to the data pool in the path, so this tests the URL structure more than scoping
        list_url_dp2 = get_api_path("v6:packager-list", self.dp2.obfuscated_id, advanced=True)
        data = {"label": "Unauthorized Packager DP2", "s3_bucket": "test"}
        response = client_dp1.post(list_url_dp2, data=json.dumps(data), content_type="application/json")
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        # Test updating a packager in a different data pool
        data = {"label": "Updated Unauthorized Packager DP2"}
        response = client_dp1.patch(packager_dp2_url, data=json.dumps(data), content_type="application/json")
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        # Test deleting a packager in a different data pool
        response = client_dp1.delete(packager_dp2_url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_partial_update_packager(self):
        """Test partial updates for various fields of the Packager model."""
        packager_to_update = create_packager(data_pool=self.dp, label="Packager to Update")
        packager_to_update_url = get_api_path("v6:packager-detail", self.dp.obfuscated_id,
                                              obfuscated_id=packager_to_update.obfuscated_id, advanced=True)

        # Test updating label
        data = {"label": "Partially Updated Packager"}
        response = self.client.patch(packager_to_update_url, data=json.dumps(data), content_type="application/json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        packager_to_update.refresh_from_db()
        self.assertEqual(packager_to_update.label, "Partially Updated Packager")

        # Test updating packager_schedule_type and schedule_cron_expression
        data = {"packager_schedule_type": "CRON_SCHEDULE", "schedule_cron_expression": "0 10 * * *"}
        response = self.client.patch(packager_to_update_url, data=json.dumps(data), content_type="application/json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        packager_to_update.refresh_from_db()
        self.assertEqual(packager_to_update.packager_schedule_type, PackagerSchedules.CRON_SCHEDULE.value)
        self.assertEqual(packager_to_update.schedule_cron_expression, "0 10 * * *")

        # Test updating delivery_data_grouping_strategy
        data = {"delivery_data_grouping_strategy": "SPLIT_BY_DOC"}
        response = self.client.patch(packager_to_update_url, data=json.dumps(data), content_type="application/json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        packager_to_update.refresh_from_db()
        self.assertEqual(packager_to_update.delivery_data_grouping_strategy, PackagerGroupingOptions.SPLIT_BY_DOC.value)

        # Test updating document_status_filter
        data = {"document_status_filter": ["PENDING_VERIFICATION", "ERROR"]}
        response = self.client.patch(packager_to_update_url, data=json.dumps(data), content_type="application/json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        packager_to_update.refresh_from_db()
        self.assertEqual(packager_to_update.document_status_filter, ["PENDING_VERIFICATION", "ERROR"])

        # Test updating eb_status_filter
        data = {"eb_status_filter": ["PENDING_VERIFICATION"]}
        response = self.client.patch(packager_to_update_url, data=json.dumps(data), content_type="application/json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        packager_to_update.refresh_from_db()
        self.assertEqual(packager_to_update.eb_status_filter, ["PENDING_VERIFICATION"])

        # Test updating document_id_filter
        data = {"document_id_filter": [self.doc.obfuscated_id, self.doc2.obfuscated_id]}
        response = self.client.patch(packager_to_update_url, data=json.dumps(data), content_type="application/json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        packager_to_update.refresh_from_db()
        self.assertEqual(packager_to_update.document_id_filter, [self.doc.obfuscated_id, self.doc2.obfuscated_id])

        # Test updating eb_id_filter
        data = {"eb_id_filter": [self.eb.obfuscated_id]}
        response = self.client.patch(packager_to_update_url, data=json.dumps(data), content_type="application/json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        packager_to_update.refresh_from_db()
        self.assertEqual(packager_to_update.eb_id_filter, [self.eb.obfuscated_id])

        # Test updating naming configs
        source_naming_config = {"elements": [{"type": "METADATA", "value": "document.label"}], "separator": "-",
                                "max_length": 30, }
        processed_naming_config = {"elements": [{"type": "STRING_LITERAL", "value": "processed"}], "separator": "_",
                                   "max_length": 20, }
        data = {"source_file_naming_config": source_naming_config,
                "processed_data_naming_config": processed_naming_config, }
        response = self.client.patch(packager_to_update_url, data=json.dumps(data), content_type="application/json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        packager_to_update.refresh_from_db()
        self.assertEqual(packager_to_update.source_file_naming_config, source_naming_config)
        self.assertEqual(packager_to_update.processed_data_naming_config, processed_naming_config)

        # Test updating boolean flags
        data = {"include_source_files_in_delivery": True, "include_relationship_details_in_data_export": True}
        response = self.client.patch(packager_to_update_url, data=json.dumps(data), content_type="application/json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        packager_to_update.refresh_from_db()
        self.assertEqual(packager_to_update.include_source_files_in_delivery, True)
        self.assertEqual(packager_to_update.include_relationship_details_in_data_export, True)

    def test_chain_of_custody_report_with_dates(self):
        """Test successful chain-of-custody report generation with configured date range."""
        # Set up the packager with CoC report date range
        from datetime import date

        self.packager.coc_report_start_date = date(2023, 1, 1)
        self.packager.coc_report_end_date = date(2023, 1, 31)
        self.packager.save()

        report_url = get_api_path("v6:packager-chain-of-custody-report", data_pools_obfuscated_id=self.dp.obfuscated_id,
                                  obfuscated_id=self.packager.obfuscated_id, advanced=True, )

        package = Package.objects.create(packager=self.packager, data_pool=self.dp)

        doc_data1 = self._create_document_data(self.doc)
        doc_data2 = self._create_document_data(self.doc2)
        doc_data3 = self._create_document_data(self.doc3)

        entry1_date = datetime(2023, 1, 15, 10, 0, 0, tzinfo=timezone.utc)
        entry2_date = datetime(2023, 1, 20, 12, 0, 0, tzinfo=timezone.utc)
        entry3_date = datetime(2023, 2, 10, 8, 0, 0, tzinfo=timezone.utc)

        PackageEntry.objects.create(package=package, document_data=doc_data1, data_pool=self.dp,
                                    status_in_package=PackageEntryStatus.INCLUDED.name, created_at=entry1_date, )
        PackageEntry.objects.create(package=package, document_data=doc_data2, data_pool=self.dp,
                                    status_in_package=PackageEntryStatus.INCLUDED.name, created_at=entry2_date, )
        # Entry outside the date range
        PackageEntry.objects.create(package=package, document_data=doc_data3,  # Using doc_data3 for a different entry
                                    data_pool=self.dp, status_in_package=PackageEntryStatus.EXCLUDED_AS_DUPLICATE.name,
                                    created_at=entry3_date, )

        response = self.client.post(report_url, data={}, content_type="application/json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response["Content-Type"], "text/csv")
        self.assertIn(f'attachment; filename="coc_report_{self.packager.obfuscated_id}_',
                      response["Content-Disposition"])

    def test_chain_of_custody_report_only_start_date(self):
        """Test successful chain-of-custody report generation with only start_date configured."""
        # Set up the packager with only CoC report start date
        from datetime import date

        self.packager.coc_report_start_date = date(2023, 3, 1)
        self.packager.coc_report_end_date = None  # No end date
        self.packager.save()

        report_url = get_api_path("v6:packager-chain-of-custody-report", data_pools_obfuscated_id=self.dp.obfuscated_id,
                                  obfuscated_id=self.packager.obfuscated_id, advanced=True, )

        # Create some PackageEntry objects with specific dates for testing
        from packager.models import PackageEntry, PackageEntryStatus
        from datetime import datetime, timezone

        package = Package.objects.create(packager=self.packager, data_pool=self.dp)

        doc_data1 = self._create_document_data(self.doc)
        doc_data2 = self._create_document_data(self.doc2)
        doc_data3 = self._create_document_data(self.doc3)

        entry1_date = datetime(2023, 3, 10, 10, 0, 0, tzinfo=timezone.utc)
        entry2_date = datetime(2023, 3, 25, 12, 0, 0, tzinfo=timezone.utc)
        entry3_date = datetime(2023, 2, 15, 8, 0, 0, tzinfo=timezone.utc)  # Before start_date

        PackageEntry.objects.create(package=package, document_data=doc_data1, data_pool=self.dp,
                                    status_in_package=PackageEntryStatus.INCLUDED.name, created_at=entry1_date, )
        PackageEntry.objects.create(package=package, document_data=doc_data2, data_pool=self.dp,
                                    status_in_package=PackageEntryStatus.INCLUDED.name, created_at=entry2_date, )
        PackageEntry.objects.create(package=package, document_data=doc_data3, data_pool=self.dp,
                                    status_in_package=PackageEntryStatus.EXCLUDED_AS_DUPLICATE.name,
                                    created_at=entry3_date, )

        response = self.client.post(report_url, data={}, content_type="application/json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response["Content-Type"], "text/csv")
        self.assertIn(f'attachment; filename="coc_report_{self.packager.obfuscated_id}_',
                      response["Content-Disposition"])

    def test_chain_of_custody_report_only_end_date(self):
        """Test successful chain-of-custody report generation with only end_date configured."""
        # Set up the packager with only CoC report end date
        from datetime import date

        self.packager.coc_report_start_date = None  # No start date
        self.packager.coc_report_end_date = date(2023, 4, 30)
        self.packager.save()

        report_url = get_api_path("v6:packager-chain-of-custody-report", data_pools_obfuscated_id=self.dp.obfuscated_id,
                                  obfuscated_id=self.packager.obfuscated_id, advanced=True, )

        # Create some PackageEntry objects with specific dates for testing
        from packager.models import PackageEntry, PackageEntryStatus
        from datetime import datetime, timezone

        package = Package.objects.create(packager=self.packager, data_pool=self.dp)

        doc_data1 = self._create_document_data(self.doc)
        doc_data2 = self._create_document_data(self.doc2)
        doc_data3 = self._create_document_data(self.doc3)

        entry1_date = datetime(2023, 4, 5, 10, 0, 0, tzinfo=timezone.utc)  # Before end_date
        entry2_date = datetime(2023, 4, 18, 12, 0, 0, tzinfo=timezone.utc)  # Before end_date
        entry3_date = datetime(2023, 5, 1, 8, 0, 0, tzinfo=timezone.utc)  # After end_date

        PackageEntry.objects.create(package=package, document_data=doc_data1, data_pool=self.dp,
                                    status_in_package=PackageEntryStatus.INCLUDED.name, created_at=entry1_date, )
        PackageEntry.objects.create(package=package, document_data=doc_data2, data_pool=self.dp,
                                    status_in_package=PackageEntryStatus.INCLUDED.name, created_at=entry2_date, )
        PackageEntry.objects.create(package=package, document_data=doc_data3, data_pool=self.dp,
                                    status_in_package=PackageEntryStatus.EXCLUDED_AS_DUPLICATE.name,
                                    created_at=entry3_date, )

        response = self.client.post(report_url, data={}, content_type="application/json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response["Content-Type"], "text/csv")
        self.assertIn(f'attachment; filename="coc_report_{self.packager.obfuscated_id}_',
                      response["Content-Disposition"])

    def test_chain_of_custody_report_no_dates(self):
        """Test chain-of-custody report generation with no date configuration should fail."""
        # Set up the packager with no CoC report date configuration
        self.packager.coc_report_start_date = None
        self.packager.coc_report_end_date = None
        self.packager.save()

        report_url = get_api_path("v6:packager-chain-of-custody-report", data_pools_obfuscated_id=self.dp.obfuscated_id,
                                  obfuscated_id=self.packager.obfuscated_id, advanced=True, )

        package = Package.objects.create(packager=self.packager, data_pool=self.dp)

        doc_data1 = self._create_document_data(self.doc)
        doc_data2 = self._create_document_data(self.doc2)
        doc_data3 = self._create_document_data(self.doc3)

        entry1_date = datetime(2023, 1, 1, 0, 0, 0, tzinfo=timezone.utc)  # Should be included in default range
        entry2_date = datetime.now(timezone.utc) - timedelta(days=1)  # Should be included in default range
        entry3_date = datetime.now(timezone.utc) + timedelta(days=1)  # Should NOT be included in default range (future)

        PackageEntry.objects.create(package=package, document_data=doc_data1, data_pool=self.dp,
                                    status_in_package=PackageEntryStatus.INCLUDED.name, created_at=entry1_date, )
        PackageEntry.objects.create(package=package, document_data=doc_data2, data_pool=self.dp,
                                    status_in_package=PackageEntryStatus.INCLUDED.name, created_at=entry2_date, )
        PackageEntry.objects.create(package=package, document_data=doc_data3, data_pool=self.dp,
                                    status_in_package=PackageEntryStatus.EXCLUDED_AS_DUPLICATE.name,
                                    created_at=entry3_date, )

        response = self.client.post(report_url, data={}, content_type="application/json")

        self.assertEqual(response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR)
        self.assertIn("error", response.json())

    def test_chain_of_custody_report_success(self):
        """Test successful chain-of-custody report generation using packager configuration."""
        # Set up the packager with CoC report date range
        from datetime import date

        self.packager.coc_report_start_date = date(2023, 1, 1)
        self.packager.coc_report_end_date = date(2023, 12, 31)
        self.packager.save()

        report_url = get_api_path("v6:packager-chain-of-custody-report", data_pools_obfuscated_id=self.dp.obfuscated_id,
                                  obfuscated_id=self.packager.obfuscated_id, advanced=True, )

        # Test CSV format (default)
        response = self.client.post(report_url, data={}, content_type="application/json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response["Content-Type"], "text/csv")
        self.assertIn(f'attachment; filename="coc_report_{self.packager.obfuscated_id}_',
                      response["Content-Disposition"])

        # Test JSON format
        data = {"format": "json"}
        response = self.client.post(report_url, data=json.dumps(data), content_type="application/json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response["Content-Type"], "application/json")
        response_data = response.json()
        self.assertIsInstance(response_data, list)  # JSON report returns a list

    @patch("extract.models.Extraction.get_transformed_results")
    def test_simulate_package_success(self, mock_get_transformed_results):
        """Test successful package simulation"""

        self._create_document_data(self.doc, status="VERIFIED", source_file_name="simulation_test.pdf",
                                   source_file_md5="simulation_md5")

        training_set, _ = create_training_set(data_pool=self.dp)
        training_revision, _ = create_training_revision(data_pool=self.dp, training_set=training_set)

        extraction, _ = create_extraction(data_pool=self.dp, document=self.doc, training_revision=training_revision)

        mock_get_transformed_results.return_value = [{"CustomerName": "Test Corp", "AccountNumber": "12345"}]

        simulate_url = get_api_path("v6:packager-simulate-package", self.dp.obfuscated_id,
                                    obfuscated_id=self.packager.obfuscated_id, advanced=True, )
        data = {"document_status_filter": ["VERIFIED"], "output_format": "JSON"}
        response = self.client.post(simulate_url, data=json.dumps(data), content_type="application/json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_data = response.json()
        self.assertTrue(response_data["success"])

        self.assertEqual(response_data["format"], "JSON")
        self.assertEqual(response_data["output_format"], "JSON")
        self.assertEqual(response_data["delivery_data_grouping_strategy"],
                         self.packager.delivery_data_grouping_strategy)

        self.assertIsInstance(response_data["data"], list)
        self.assertIsInstance(response_data["errors"], list)

        self.assertNotIn("package_entries", response_data)
        self.assertNotIn("summary", response_data)
        self.assertNotIn("exclude_duplicates_from_delivery", response_data)

    def test_filter_duplicate_documents_respects_exclude_config_in_simulation(self):
        """Test that filter_duplicate_documents respects exclude_duplicates_config even in simulation mode."""
        duplicate_doc, _ = create_document(
            data_pool=self.dp,
            label="duplicate_doc.pdf",
            content_md5="duplicate_md5",
            status="VERIFIED"
        )

        original_doc_data = self._create_document_data(
            self.doc, status="VERIFIED",
            source_file_name="original.pdf", source_file_md5="original_md5"
        )
        duplicate_doc_data = self._create_document_data(
            duplicate_doc, status="VERIFIED",
            source_file_name="duplicate.pdf", source_file_md5="duplicate_md5"
        )

        content_duplicate_type = DocumentRelationshipType.objects.create(
            label="CONTENT_DUPLICATE",
            data_pool=self.dp
        )

        DocumentRelationship.objects.create(
            source_document_data=original_doc_data,
            target_document_data=duplicate_doc_data,
            relationship_type=content_duplicate_type,
            data_pool=self.dp,
            metadata={"detection_type": "content_duplicate"}
        )

        doc_list = [original_doc_data, duplicate_doc_data]

        filtered_docs = filter_duplicate_documents(
            doc_list,
            exclude_duplicates_config=True,
            is_simulation_mode=True
        )

        self.assertEqual(len(filtered_docs), 1)
        self.assertEqual(filtered_docs[0].id, original_doc_data.id)

        filtered_docs = filter_duplicate_documents(
            doc_list,
            exclude_duplicates_config=False,
            is_simulation_mode=True
        )

        self.assertEqual(len(filtered_docs), 2)

    @patch("extract.models.Extraction.get_transformed_results")
    def test_simulate_package_returns_pure_delivery_format(self, mock_get_transformed_results):
        """Test that simulate_package service returns pure delivery content only."""
        mock_get_transformed_results.return_value = [{"field1": "value1", "field2": "value2"}]

        self._create_document_data(self.doc, status="VERIFIED", source_file_name="test.pdf", source_file_md5="test_md5")

        service = PackageOperationsService(self.packager)
        input_data = SimulatePackageInput(document_status_filter=["VERIFIED"])

        result, config = service.simulate_package(input_data)

        self.assertIsNotNone(result)
        self.assertIsNotNone(config)

        self.assertGreater(len(result.rendered_items), 0)

    @patch("extract.models.Extraction.get_transformed_results")
    def test_simulate_package_view_pure_delivery_response(self, mock_get_transformed_results):
        """Test that the simulate_package view returns pure delivery content only."""
        mock_get_transformed_results.return_value = [{"field1": "value1", "field2": "value2"}]

        self._create_document_data(self.doc, status="VERIFIED", source_file_name="test.pdf", source_file_md5="test_md5")

        url = f"/v6/data-pools/{self.dp.obfuscated_id}/packagers/{self.packager.obfuscated_id}/simulate-package/"
        data = {"document_status_filter": ["VERIFIED"]}

        response = self.client.post(url, data=json.dumps(data), content_type="application/json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_data = response.json()

        self.assertTrue(response_data["success"])
        self.assertIn("data", response_data)
        self.assertIn("format", response_data)
        self.assertIn("errors", response_data)

        self.assertNotIn("package_entries", response_data)
        self.assertNotIn("summary", response_data)
        self.assertNotIn("exclude_duplicates_from_delivery", response_data)
        self.assertNotIn("last_viewed", response_data)

        data_items = response_data["data"]
        self.assertIsInstance(data_items, list)

    @patch("extract.models.Extraction.get_transformed_results")
    def test_simulate_package_complete_integration_with_duplicate_filtering(self, mock_get_transformed_results):
        """Complete integration test validating every field in simulation response with duplicate filtering."""
        # Mock returns the same data for all extractions - the duplicate filtering will choose one
        mock_get_transformed_results.return_value = [
            {"CustomerName": "Beta LLC", "InvoiceNumber": "INV-002", "Amount": "1000.00"}
        ]

        self.packager.exclude_duplicates_from_delivery = True
        self.packager.output_format = "JSON"
        self.packager.delivery_data_grouping_strategy = "ALL_IN_ONE"
        self.packager.append_coc_fields_to_data = True
        self.packager.save()

        original_doc, _ = create_document(
            data_pool=self.dp,
            label="original_invoice.pdf",
            content_md5="original_md5_hash",
            status="VERIFIED"
        )

        duplicate_doc, _ = create_document(
            data_pool=self.dp,
            label="duplicate_invoice.pdf",
            content_md5="duplicate_md5_hash",
            status="VERIFIED"
        )

        original_doc_data = self._create_document_data(
            original_doc,
            status="VERIFIED",
            source_file_name="original_invoice.pdf",
            source_file_md5="original_md5_hash"
        )
        duplicate_doc_data = self._create_document_data(
            duplicate_doc,
            status="VERIFIED",
            source_file_name="duplicate_invoice.pdf",
            source_file_md5="duplicate_md5_hash"
        )

        rel_type, _ = DocumentRelationshipType.objects.get_or_create(
            label="CONTENT_DUPLICATE",
            data_pool=self.dp
        )
        DocumentRelationship.objects.create(
            source_document_data=original_doc_data,
            target_document_data=duplicate_doc_data,
            relationship_type=rel_type,
            data_pool=self.dp
        )

        training_set, _ = create_training_set(data_pool=self.dp)
        training_revision, _ = create_training_revision(data_pool=self.dp, training_set=training_set)

        create_extraction(data_pool=self.dp, document=original_doc, training_revision=training_revision)
        create_extraction(data_pool=self.dp, document=duplicate_doc, training_revision=training_revision)

        url = f"/v6/data-pools/{self.dp.obfuscated_id}/packagers/{self.packager.obfuscated_id}/simulate-package/"
        data = {"document_status_filter": ["VERIFIED"]}

        response = self.client.post(url, data=json.dumps(data), content_type="application/json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_data = response.json()

        self.assertNotIn("package_entries", response_data)
        self.assertNotIn("summary", response_data)
        self.assertNotIn("exclude_duplicates_from_delivery", response_data)
        self.assertNotIn("last_viewed", response_data)

        required_fields = ["data", "format", "output_format", "delivery_data_grouping_strategy", "success", "errors"]
        for field in required_fields:
            self.assertIn(field, response_data, f"Required field '{field}' missing from response")

        self.assertTrue(response_data["success"])
        self.assertEqual(response_data["format"], "JSON")
        self.assertEqual(response_data["output_format"], "JSON")
        self.assertEqual(response_data["delivery_data_grouping_strategy"], "ALL_IN_ONE")
        self.assertIsInstance(response_data["errors"], list)
        self.assertEqual(len(response_data["errors"]), 0)

        data_items = response_data["data"]
        self.assertIsInstance(data_items, list)
        self.assertEqual(len(data_items), 1, "Should only contain 1 item (duplicate excluded)")

        data_item = data_items[0]
        self.assertIn("filename", data_item)
        self.assertIn("content", data_item)

        self.assertTrue(data_item["filename"].endswith(".json"))

        content = json.loads(data_item["content"])
        self.assertIn("data", content)
        self.assertIsInstance(content["data"], list)

        document_data_list = content["data"]
        self.assertEqual(len(document_data_list), 1)

        doc_data = document_data_list[0]
        expected_fields = ["CustomerName", "InvoiceNumber", "Amount"]
        for field in expected_fields:
            self.assertIn(field, doc_data, f"Expected field '{field}' missing from document data")

        # Validate extracted field values (duplicate filtering will return one of the documents)
        # The system is correctly filtering duplicates and returning the remaining document
        self.assertEqual(doc_data["CustomerName"], "Beta LLC")
        self.assertEqual(doc_data["InvoiceNumber"], "INV-002")
        self.assertEqual(doc_data["Amount"], "1000.00")

        self.assertIn("is_duplicate", doc_data)
        self.assertIn("has_duplicates", doc_data)
        self.assertIn("duplicate_doc_ids", doc_data)

    @patch("extract.models.Extraction.get_transformed_results")
    def test_preview_package_complete_integration(self, mock_get_transformed_results):
        """Complete integration test validating every field in preview response."""
        mock_get_transformed_results.return_value = [
            {"CompanyName": "Preview Corp", "ContractNumber": "CTR-123", "Value": "5000.00"}
        ]

        self.packager.output_format = "JSON"
        self.packager.delivery_data_grouping_strategy = "SPLIT_BY_DOC"
        self.packager.append_coc_fields_to_data = True
        self.packager.save()

        doc_data = self._create_document_data(
            self.doc,
            status="VERIFIED",
            source_file_name="contract.pdf",
            source_file_md5="contract_md5"
        )

        PackageEntry.objects.create(
            package=self.package,
            document_data=doc_data,
            data_pool=self.dp,
            status_in_package=PackageEntryStatus.INCLUDED.name,
            filename_in_package="contract.pdf"
        )

        training_set, _ = create_training_set(data_pool=self.dp)
        training_revision, _ = create_training_revision(data_pool=self.dp, training_set=training_set)
        create_extraction(data_pool=self.dp, document=self.doc, training_revision=training_revision)

        url = f"/v6/data-pools/{self.dp.obfuscated_id}/packagers/{self.packager.obfuscated_id}/preview-package/"
        data = {"package_id": self.package.obfuscated_id}

        response = self.client.post(url, data=json.dumps(data), content_type="application/json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_data = response.json()

        self.assertNotIn("json_data", response_data)
        self.assertNotIn("last_viewed", response_data)
        self.assertNotIn("package_entries", response_data)
        self.assertNotIn("summary", response_data)

        required_fields = ["data", "format", "output_format", "delivery_data_grouping_strategy", "success", "errors"]
        for field in required_fields:
            self.assertIn(field, response_data, f"Required field '{field}' missing from preview response")

        self.assertTrue(response_data["success"])
        self.assertEqual(response_data["format"], "JSON")
        self.assertEqual(response_data["output_format"], "JSON")
        self.assertEqual(response_data["delivery_data_grouping_strategy"], "SPLIT_BY_DOC")
        self.assertIsInstance(response_data["errors"], list)
        self.assertEqual(len(response_data["errors"]), 0)

        data_items = response_data["data"]
        self.assertIsInstance(data_items, list)
        self.assertGreater(len(data_items), 0, "Preview should contain data items")

        data_item = data_items[0]
        self.assertIn("filename", data_item)
        self.assertIn("content", data_item)

        filename = data_item["filename"]
        self.assertIsInstance(filename, str)
        self.assertGreater(len(filename), 0, "Filename should not be empty")

        import json as json_module
        content = json_module.loads(data_item["content"])

        # For SPLIT_BY_DOC, content is the raw extracted data, not wrapped in {"data": [...]}
        # The content should be the direct extracted data dictionary
        doc_data = content
        self.assertIsInstance(doc_data, dict)
        # Validate extracted fields (DocumentID is not a standard CoC field, so not expected)
        expected_fields = ["CompanyName", "ContractNumber", "Value"]
        for field in expected_fields:
            self.assertIn(field, doc_data, f"Expected field '{field}' missing from preview data")

        # Validate extracted values
        self.assertEqual(doc_data["CompanyName"], "Preview Corp")
        self.assertEqual(doc_data["ContractNumber"], "CTR-123")
        self.assertEqual(doc_data["Value"], "5000.00")

        # Validate relationship details are included (since include_relationship_details_in_data_export=True)
        self.assertIn("is_duplicate", doc_data)
        self.assertIn("has_duplicates", doc_data)
        self.assertIn("duplicate_doc_ids", doc_data)

    @patch("extract.models.Extraction.get_transformed_results")
    def test_simulate_package_csv_format_integration(self, mock_get_transformed_results):
        """Complete integration test for CSV format simulation with field validation."""
        mock_get_transformed_results.return_value = [
            {"VendorName": "CSV Corp", "PurchaseOrder": "PO-456", "Total": "3000.00"}
        ]

        self.packager.exclude_duplicates_from_delivery = False
        self.packager.output_format = "CSV"
        self.packager.delivery_data_grouping_strategy = "ALL_IN_ONE"
        self.packager.append_coc_fields_to_data = True
        self.packager.save()

        self._create_document_data(
            self.doc,
            status="VERIFIED",
            source_file_name="purchase_order.pdf",
            source_file_md5="po_md5"
        )

        training_set, _ = create_training_set(data_pool=self.dp)
        training_revision, _ = create_training_revision(data_pool=self.dp, training_set=training_set)
        create_extraction(data_pool=self.dp, document=self.doc, training_revision=training_revision)

        url = f"/v6/data-pools/{self.dp.obfuscated_id}/packagers/{self.packager.obfuscated_id}/simulate-package/"
        data = {"document_status_filter": ["VERIFIED"]}

        response = self.client.post(url, data=json.dumps(data), content_type="application/json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_data = response.json()

        self.assertNotIn("package_entries", response_data)
        self.assertNotIn("summary", response_data)

        self.assertTrue(response_data["success"])
        self.assertEqual(response_data["format"], "CSV")
        self.assertEqual(response_data["output_format"], "CSV")
        self.assertEqual(response_data["delivery_data_grouping_strategy"], "ALL_IN_ONE")

        data_items = response_data["data"]
        self.assertIsInstance(data_items, list)
        self.assertEqual(len(data_items), 1, "Should contain 1 CSV file")

        csv_item = data_items[0]
        self.assertIn("filename", csv_item)
        self.assertIn("content", csv_item)
        self.assertTrue(csv_item["filename"].endswith(".csv"))

        csv_content = csv_item["content"]
        self.assertIsInstance(csv_content, str)

        csv_reader = csv.DictReader(io.StringIO(csv_content))
        rows = list(csv_reader)

        self.assertEqual(len(rows), 1)

        headers = csv_reader.fieldnames
        expected_headers = ["VendorName", "PurchaseOrder", "Total"]
        for header in expected_headers:
            self.assertIn(header, headers, f"Expected header '{header}' missing from CSV")

        row = rows[0]
        self.assertEqual(row["VendorName"], "CSV Corp")
        self.assertEqual(row["PurchaseOrder"], "PO-456")
        self.assertEqual(row["Total"], "3000.00")

    @patch("extract.models.Extraction.get_transformed_results")
    def test_simulate_package_with_duplicate_exclusion(self, mock_get_transformed_results):
        """Test that simulation respects exclude_duplicates_from_delivery configuration."""
        mock_get_transformed_results.return_value = [{"field1": "value1", "field2": "value2"}]

        self.packager.exclude_duplicates_from_delivery = True
        self.packager.save()

        duplicate_doc, _ = create_document(
            data_pool=self.dp,
            label="duplicate_doc.pdf",
            content_md5="duplicate_md5",
            status="VERIFIED"
        )

        original_doc_data = self._create_document_data(
            self.doc, status="VERIFIED",
            source_file_name="original.pdf", source_file_md5="original_md5"
        )
        duplicate_doc_data = self._create_document_data(
            duplicate_doc, status="VERIFIED",
            source_file_name="duplicate.pdf", source_file_md5="duplicate_md5"
        )

        rel_type, _ = DocumentRelationshipType.objects.get_or_create(
            label="CONTENT_DUPLICATE", data_pool=self.dp
        )
        DocumentRelationship.objects.create(
            source_document_data=original_doc_data,
            target_document_data=duplicate_doc_data,
            relationship_type=rel_type,
            data_pool=self.dp
        )

        url = f"/v6/data-pools/{self.dp.obfuscated_id}/packagers/{self.packager.obfuscated_id}/simulate-package/"
        data = {"document_status_filter": ["VERIFIED"]}

        response = self.client.post(url, data=json.dumps(data), content_type="application/json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_data = response.json()

        self.assertNotIn("package_entries", response_data)
        self.assertNotIn("summary", response_data)
        self.assertNotIn("exclude_duplicates_from_delivery", response_data)

        data_items = response_data["data"]
        self.assertEqual(len(data_items), 1)

        self.assertTrue(response_data["success"])
        self.assertIn("format", response_data)
        self.assertIn("errors", response_data)

    @patch("extract.models.Extraction.get_transformed_results")
    def test_simulate_package_with_errors(self, mock_get_transformed_results):
        """Test package simulation error handling with"""

        self._create_document_data(self.doc, status="VERIFIED",
                                   source_file_name="error_test.pdf",
                                   source_file_md5="error_md5")

        training_set, _ = create_training_set(data_pool=self.dp)
        training_revision, _ = create_training_revision(data_pool=self.dp, training_set=training_set)

        create_extraction(data_pool=self.dp, document=self.doc, training_revision=training_revision)

        mock_get_transformed_results.side_effect = Exception("External pipeline failure")

        simulate_url = get_api_path("v6:packager-simulate-package", self.dp.obfuscated_id,
                                    obfuscated_id=self.packager.obfuscated_id, advanced=True, )
        data = {"document_status_filter": ["VERIFIED"], "output_format": "JSON"}
        response = self.client.post(simulate_url, data=json.dumps(data), content_type="application/json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_data = response.json()
        self.assertTrue(response_data["success"])

        self.assertIsInstance(response_data["errors"], list)

    def test_create_package_success(self):
        """Test successful creation of a Package via API."""
        create_package_url = get_api_path("v6:packager-create-package", self.dp.obfuscated_id,
                                          obfuscated_id=self.packager.obfuscated_id, advanced=True, )

        self.package.status = PackageStatus.TRANSMISSION_SUCCESSFUL.name
        self.package.save()

        self.assertEqual(self.packager.packages.count(), 1)

        self._create_document_data(self.doc, status=DocumentDataStatus.VERIFIED.name)

        self.doc.status = DocumentDataStatus.VERIFIED.name
        self.doc.save()

        response = self.client.post(create_package_url)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        response_data = response.json()
        self.assertIn("packager_id", response_data)
        self.assertIn("id", response_data)
        self.assertIn("status", response_data)
        self.assertEqual(response_data["packager_id"], self.packager.obfuscated_id)
        self.assertEqual(response_data["status"], PackageStatus.CREATED.value)

        self.assertEqual(self.packager.packages.count(), 2)
        new_package = self.packager.packages.order_by("-created_at").first()
        self.assertEqual(new_package.obfuscated_id, response_data["id"])
        self.assertEqual(new_package.status, PackageStatus.CREATED.value)

    def test_create_package_reuses_existing_package(self):
        """Test that API reuses existing package when it's in a reusable state."""
        create_package_url = get_api_path("v6:packager-create-package", self.dp.obfuscated_id,
                                          obfuscated_id=self.packager.obfuscated_id, advanced=True, )

        self.package.status = PackageStatus.CREATED.name
        self.package.save()

        self.assertEqual(self.packager.packages.count(), 1)
        original_package_id = self.package.obfuscated_id

        self._create_document_data(self.doc, status=DocumentDataStatus.VERIFIED.name)

        self.doc.status = DocumentDataStatus.VERIFIED.name
        self.doc.save()

        response = self.client.post(create_package_url)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        response_data = response.json()
        self.assertIn("packager_id", response_data)
        self.assertIn("id", response_data)
        self.assertIn("status", response_data)
        self.assertEqual(response_data["packager_id"], self.packager.obfuscated_id)
        self.assertEqual(response_data["status"], PackageStatus.CREATED.value)

        self.assertEqual(self.packager.packages.count(), 1)
        self.assertEqual(response_data["id"], original_package_id)

    def test_create_package_invalid_packager(self):
        """Test creating a package for a non-existent packager."""
        create_package_url = get_api_path("v6:packager-create-package", self.dp.obfuscated_id,
                                          obfuscated_id="nonexistent-packager-id", advanced=True)

        response = self.client.post(create_package_url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    @patch("packager.services.package_operations_service.PackageOperationsService._create_zip_response")
    @patch("extract.models.Extraction.get_transformed_results")
    def test_download_existing_package_success(self, mock_get_transformed_results, mock_create_zip_response):
        """Test successful download of an existing package."""
        doc_data, _ = DocumentData.objects.get_or_create(document=self.doc, data_pool=self.dp,
                                                         defaults={"status": "VERIFIED",
                                                                   "source_file_name": "download_test.pdf",
                                                                   "source_file_md5": "download_md5"}, )

        PackageEntry.objects.create(package=self.package, document_data=doc_data, data_pool=self.dp,
                                    status_in_package=PackageEntryStatus.INCLUDED.name,
                                    filename_in_package="download_test.pdf", )

        training_set, _ = create_training_set(data_pool=self.dp)
        training_revision, _ = create_training_revision(data_pool=self.dp, training_set=training_set)

        create_extraction(data_pool=self.dp, document=self.doc, training_revision=training_revision)

        mock_get_transformed_results.return_value = [{"CustomerName": "Download Corp", "AccountNumber": "67890"}]

        mock_zip_response = HttpResponse(content=b"real zip content", content_type="application/zip")
        mock_zip_response["Content-Disposition"] = f'attachment; filename="package_{self.package.obfuscated_id}.zip"'
        mock_create_zip_response.return_value = mock_zip_response

        download_url = get_api_path("v6:packager-download-package", self.dp.obfuscated_id,
                                    obfuscated_id=self.packager.obfuscated_id, advanced=True, )
        data = {"package_id": self.package.obfuscated_id, "include_source_files": True}
        response = self.client.post(download_url, data=json.dumps(data), content_type="application/json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response["Content-Type"], "application/zip")
        self.assertIn(f'attachment; filename="package_{self.package.obfuscated_id}.zip"',
                      response["Content-Disposition"])
        self.assertEqual(response.content, b"real zip content")

        mock_create_zip_response.assert_called_once()

        call_args = mock_create_zip_response.call_args[0]
        rendered_items = call_args[0]
        filename = call_args[1]

        self.assertEqual(filename, f"package_{self.package.obfuscated_id}.zip")
        self.assertIsInstance(rendered_items, list)

    @patch("packager.services.package_operations_service.PackageOperationsService.simulate_package")
    @patch("packager.services.package_operations_service.PackageOperationsService._create_zip_response")
    def test_download_simulated_package_success(self, mock_create_zip_response, mock_simulate_package):
        mock_rendered_item = RenderedPackageItem(filename="simulated.csv", content="header\nvalue")
        mock_render_result = PackageRenderResult(rendered_items=[mock_rendered_item], errors=[])
        mock_packager_config = self.packager.get_config()
        mock_simulate_package.return_value = (mock_render_result, mock_packager_config)

        mock_zip_response = HttpResponse(content=b"simulated zip content", content_type="application/zip")
        mock_zip_response["Content-Disposition"] = 'attachment; filename="simulated_package.zip"'
        mock_create_zip_response.return_value = mock_zip_response

        download_url = get_api_path("v6:packager-download-package", self.dp.obfuscated_id,
                                    obfuscated_id=self.packager.obfuscated_id, advanced=True, )
        data = {"document_status_filter": ["PENDING_CREATION"], "output_format": "CSV",
                "include_relationship_details_in_data_export": True,  # Test passing this flag
                }
        response = self.client.post(download_url, data=json.dumps(data), content_type="application/json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response["Content-Type"], "application/zip")
        self.assertIn('attachment; filename="simulated_package.zip"', response["Content-Disposition"])
        self.assertEqual(response.content, b"simulated zip content")

        mock_simulate_package.assert_called_once()
        call_args, call_kwargs = mock_simulate_package.call_args
        self.assertEqual(len(call_args), 1)
        sim_input_data_passed = call_args[0]
        self.assertIsInstance(sim_input_data_passed, SimulatePackageInput)
        self.assertEqual(sim_input_data_passed.document_status_filter, ["PENDING_CREATION"])
        self.assertEqual(sim_input_data_passed.output_format, "CSV")
        self.assertEqual(sim_input_data_passed.include_relationship_details_in_data_export, True)

        mock_create_zip_response.assert_called_once()
        call_args, call_kwargs = mock_create_zip_response.call_args
        self.assertEqual(len(call_args), 2)
        self.assertEqual(call_args[1], "simulated_package.zip")
        decoded_items_passed = call_args[0]
        self.assertEqual(len(decoded_items_passed), 1)
        self.assertEqual(decoded_items_passed[0].filename, "simulated.csv")
        self.assertEqual(decoded_items_passed[0].content, "header\nvalue")

    def test_download_package_invalid_package_id(self):
        """Test downloading an existing package with an invalid package ID."""
        download_url = get_api_path("v6:packager-download-package", self.dp.obfuscated_id,
                                    obfuscated_id=self.packager.obfuscated_id, advanced=True, )
        data = {"package_id": "invalid-package-id"}
        response = self.client.post(download_url, data=json.dumps(data), content_type="application/json")
        self.assertEqual(response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR)
        self.assertIn("Failed to download package: Package matching query does not exist.", response.json()["error"])

    def test_download_package_missing_package_id_for_existing(self):
        """Test downloading an existing package without providing package_id."""
        download_url = get_api_path("v6:packager-download-package", self.dp.obfuscated_id,
                                    obfuscated_id=self.packager.obfuscated_id, advanced=True, )
        data = {"document_status_filter": ["VERIFIED"]}
        with patch("packager.services.package_operations_service.PackageOperationsService.download_package") as mock_dn:
            mock_dn.return_value = HttpResponse(content=b"simulated zip", content_type="application/zip")
            response = self.client.post(download_url, data=json.dumps(data), content_type="application/json")
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            mock_dn.assert_called_once()

    @patch("extract.models.Extraction.get_transformed_results")
    def test_preview_package_success(self, mock_get_transformed_results):
        """Test successful preview of an existing package"""

        doc_data, _ = DocumentData.objects.get_or_create(document=self.doc, data_pool=self.dp,
                                                         defaults={"status": "VERIFIED",
                                                                   "source_file_name": "preview_test.pdf",
                                                                   "source_file_md5": "preview_md5"})

        PackageEntry.objects.create(package=self.package, document_data=doc_data, data_pool=self.dp,
                                    status_in_package=PackageEntryStatus.INCLUDED.name,
                                    filename_in_package="preview_test.pdf", )

        training_set, _ = create_training_set(data_pool=self.dp)
        training_revision, _ = create_training_revision(data_pool=self.dp, training_set=training_set)

        create_extraction(data_pool=self.dp, document=self.doc, training_revision=training_revision)

        mock_get_transformed_results.return_value = [{"CustomerName": "Preview Corp", "AccountNumber": "54321"}]

        preview_url = get_api_path("v6:packager-preview-package", self.dp.obfuscated_id,
                                   obfuscated_id=self.packager.obfuscated_id, advanced=True, )
        data = {"package_id": self.package.obfuscated_id, "include_relationship_details_in_data_export": True}
        response = self.client.post(preview_url, data=json.dumps(data), content_type="application/json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_data = response.json()
        self.assertTrue(response_data["success"])

        self.assertEqual(response_data["format"], self.packager.output_format)
        self.assertIsInstance(response_data["data"], list)
        self.assertIsInstance(response_data["errors"], list)
        self.assertNotIn("last_viewed", response_data)

    @patch("packager.views.Package.render_package_data")
    def test_preview_package_with_errors(self, mock_render_package_data):
        """Test preview of an existing package with rendering errors."""
        # Mock the return value to include errors
        mock_error = {"document_id": "doc456", "error": "Preview failed"}
        mock_render_result = PackageRenderResult(rendered_items=[], errors=[mock_error])
        mock_render_package_data.return_value = mock_render_result

        preview_url = get_api_path("v6:packager-preview-package", self.dp.obfuscated_id,
                                   obfuscated_id=self.packager.obfuscated_id, advanced=True, )
        data = {"package_id": self.package.obfuscated_id}
        response = self.client.post(preview_url, data=json.dumps(data), content_type="application/json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_data = response.json()
        self.assertTrue(response_data["success"])
        self.assertEqual(len(response_data["data"]), 0)
        self.assertEqual(len(response_data["errors"]), 1)
        self.assertEqual(response_data["errors"][0], mock_error)

    def test_preview_package_invalid_package_id(self):
        """Test previewing an existing package with an invalid package ID."""
        preview_url = get_api_path("v6:packager-preview-package", self.dp.obfuscated_id,
                                   obfuscated_id=self.packager.obfuscated_id, advanced=True, )
        data = {"package_id": "invalid-package-id"}
        response = self.client.post(preview_url, data=json.dumps(data), content_type="application/json")
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertIn("Package invalid-package-id not found", response.json()["error"])

    def test_preview_package_missing_package_id(self):
        """Test previewing an existing package without providing package_id."""
        preview_url = get_api_path("v6:packager-preview-package", self.dp.obfuscated_id,
                                   obfuscated_id=self.packager.obfuscated_id, advanced=True, )
        data = {  # package_id is missing
        }
        response = self.client.post(preview_url, data=json.dumps(data), content_type="application/json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("package_id is required", response.json()["error"])

    def test_preview_package_include_relationship_details_override(self):
        """Test that include_relationship_details_in_data_export override works for preview."""

        self.packager.include_relationship_details_in_data_export = False
        self.packager.include_source_files_in_delivery = False
        self.packager.save()

        preview_url = get_api_path("v6:packager-preview-package", self.dp.obfuscated_id,
                                   obfuscated_id=self.packager.obfuscated_id, advanced=True, )
        data = {"package_id": self.package.obfuscated_id, "include_relationship_details_in_data_export": True,

                "include_source_files": False, }

        with patch("packager.views.Package.render_package_data") as mock_render:
            mock_render.return_value = PackageRenderResult(rendered_items=[], errors=[])
            self.client.post(preview_url, data=json.dumps(data), content_type="application/json")
            mock_render.assert_called_once_with(include_document_content=False)
            self.assertTrue(mock_render.called)

        self.packager.include_relationship_details_in_data_export = True
        self.packager.save()

        data = {"package_id": self.package.obfuscated_id, "include_relationship_details_in_data_export": False,

                "include_source_files": False, }

        with patch("packager.views.Package.render_package_data") as mock_render:
            mock_render.return_value = PackageRenderResult(rendered_items=[], errors=[])
            self.client.post(preview_url, data=json.dumps(data), content_type="application/json")

            mock_render.assert_called_once_with(include_document_content=False)

            self.assertTrue(mock_render.called)

    def test_download_package_include_relationship_details_override(self):
        self.packager.include_relationship_details_in_data_export = False
        self.packager.include_source_files_in_delivery = False
        self.packager.save()

        download_url = get_api_path("v6:packager-download-package", self.dp.obfuscated_id,
                                    obfuscated_id=self.packager.obfuscated_id, advanced=True, )
        data = {"package_id": self.package.obfuscated_id, "include_relationship_details_in_data_export": True,

                "include_source_files": False, }

        mock_render_patch = patch("packager.views.Package.render_package_data")
        mock_zip = patch("packager.services.package_operations_service.PackageOperationsService._create_zip_response")
        with mock_render_patch as mock_render, mock_zip as mock_zip_combined:
            mock_render.return_value = PackageRenderResult(rendered_items=[], errors=[])
            mock_zip_combined.return_value = HttpResponse(content=b"zip content", content_type="application/zip")
            self.client.post(download_url, data=json.dumps(data), content_type="application/json")

            mock_render.assert_called_once_with(include_document_content=False)
            self.assertTrue(mock_render.called)

        self.packager.include_relationship_details_in_data_export = True
        self.packager.save()

        data = {"package_id": self.package.obfuscated_id, "include_relationship_details_in_data_export": False,

                "include_source_files": False, }
        mock_render_patch = patch("packager.views.Package.render_package_data")
        mock_zip = patch("packager.services.package_operations_service.PackageOperationsService._create_zip_response")
        with mock_render_patch as mock_render, mock_zip as mock_zip_combined:
            mock_render.return_value = PackageRenderResult(rendered_items=[], errors=[])
            mock_zip_combined.return_value = HttpResponse(content=b"zip content", content_type="application/zip")
            self.client.post(download_url, data=json.dumps(data), content_type="application/json")

            mock_render.assert_called_once_with(include_document_content=False)

            self.assertTrue(mock_render.called)

    def test_download_simulated_package_include_relationship_details_override(self):
        self.packager.include_relationship_details_in_data_export = False
        self.packager.save()

        download_url = get_api_path("v6:packager-download-package", self.dp.obfuscated_id,
                                    obfuscated_id=self.packager.obfuscated_id, advanced=True, )
        data = {"document_status_filter": ["VERIFIED"], "include_relationship_details_in_data_export": True,

                }

        mock_sim_patch = patch("packager.services.package_operations_service.PackageOperationsService.simulate_package")
        mock_zip = patch("packager.services.package_operations_service.PackageOperationsService._create_zip_response")
        with mock_sim_patch as mock_sim, mock_zip as mock_create_zip_response:
            mock_sim.return_value = (PackageRenderResult(rendered_items=[], errors=[]), self.packager.get_config())
            mock_create_zip_response.return_value = HttpResponse(content=b"zip content", content_type="application/zip")
            self.client.post(download_url, data=json.dumps(data), content_type="application/json")

            mock_sim.assert_called_once()
            sim_input_data_passed = mock_sim.call_args[0][0]
            self.assertTrue(sim_input_data_passed.include_relationship_details_in_data_export)

        self.packager.include_relationship_details_in_data_export = True
        self.packager.save()

        data = {"document_status_filter": ["VERIFIED"], "include_relationship_details_in_data_export": False,

                }

        mock_sim_patch = patch("packager.services.package_operations_service.PackageOperationsService.simulate_package")
        mock_zip = patch("packager.services.package_operations_service.PackageOperationsService._create_zip_response")
        with mock_sim_patch as mock_sim, mock_zip as mock_create_zip_response:
            mock_sim.return_value = (PackageRenderResult(rendered_items=[], errors=[]), self.packager.get_config())
            mock_create_zip_response.return_value = HttpResponse(content=b"zip content", content_type="application/zip")
            self.client.post(download_url, data=json.dumps(data), content_type="application/json")

            mock_sim.assert_called_once()
            sim_input_data_passed = mock_sim.call_args[0][0]
            self.assertFalse(sim_input_data_passed.include_relationship_details_in_data_export)

    def test_download_package_include_duplicate_fields(self):
        """Test that include_duplicate_fields parameter is handled for download."""
        download_url = get_api_path("v6:packager-download-package", self.dp.obfuscated_id,
                                    obfuscated_id=self.packager.obfuscated_id, advanced=True, )
        data = {"package_id": self.package.obfuscated_id, "include_duplicate_fields": True,  # Test passing this flag
                }

        # Mock render_package_data to return some duplicate findings
        mock_rendered_item = RenderedPackageItem(filename="test.json", content='{"key": "value"}')
        mock_duplicate_finding = DuplicateFinding(doc_id="doc123", is_duplicate=True)
        mock_render_result = PackageRenderResult(rendered_items=[mock_rendered_item], errors=[],
                                                 duplicate_findings=[mock_duplicate_finding])
        mr = patch("packager.services.package_operations_service.PackageOperationsService._get_rendered_package_result")
        mock_zip = patch("packager.services.package_operations_service.PackageOperationsService._create_zip_response")
        with mr as mock_render, mock_zip as mock_create_zip_response:
            mock_render.return_value = mock_render_result
            mock_create_zip_response.return_value = HttpResponse(content=b"zip content", content_type="application/zip")
            response = self.client.post(download_url, data=json.dumps(data), content_type="application/json")

            self.assertEqual(response.status_code, status.HTTP_200_OK)

            mock_create_zip_response.assert_called_once()
            call_args, call_kwargs = mock_create_zip_response.call_args
            self.assertIn("duplicate_findings_list", call_kwargs)
            self.assertEqual(call_kwargs["duplicate_findings_list"], [mock_duplicate_finding.dict()])

        # Test with include_duplicate_fields = False
        data = {"package_id": self.package.obfuscated_id, "include_duplicate_fields": False}
        mr = patch("packager.services.package_operations_service.PackageOperationsService._get_rendered_package_result")
        mock_zip = patch("packager.services.package_operations_service.PackageOperationsService._create_zip_response")
        with mr as mock_render, mock_zip as mock_create_zip_response:
            mock_render.return_value = mock_render_result
            mock_create_zip_response.return_value = HttpResponse(content=b"zip content", content_type="application/zip")
            response = self.client.post(download_url, data=json.dumps(data), content_type="application/json")

            self.assertEqual(response.status_code, status.HTTP_200_OK)

            mock_create_zip_response.assert_called_once()
            call_args, call_kwargs = mock_create_zip_response.call_args
            self.assertNotIn("duplicate_findings_list", call_kwargs)

    def test_download_simulated_package_include_duplicate_fields(self):
        """Test that include_duplicate_fields parameter is handled for simulated download."""
        download_url = get_api_path("v6:packager-download-package", self.dp.obfuscated_id,
                                    obfuscated_id=self.packager.obfuscated_id, advanced=True, )
        data = {"document_status_filter": ["VERIFIED"], "include_duplicate_fields": True,  # Test passing this flag
                }

        # Mock _prepare_and_run_simulation to return some duplicate findings
        mock_rendered_item = RenderedPackageItem(filename="simulated.json", content='{"key": "value"}')
        mock_duplicate_finding = DuplicateFinding(doc_id="doc456", is_duplicate=True)
        mock_render_result = PackageRenderResult(rendered_items=[mock_rendered_item], errors=[],
                                                 duplicate_findings=[mock_duplicate_finding])
        mock_packager_config = self.packager.get_config()
        mock_sim_patch = patch("packager.services.package_operations_service.PackageOperationsService.simulate_package")
        mock_zip = patch("packager.services.package_operations_service.PackageOperationsService._create_zip_response")
        with mock_sim_patch as mock_sim, mock_zip as mock_create_zip_response:
            mock_sim.return_value = (mock_render_result, mock_packager_config)
            mock_create_zip_response.return_value = HttpResponse(content=b"zip content", content_type="application/zip")
            response = self.client.post(download_url, data=json.dumps(data), content_type="application/json")

            self.assertEqual(response.status_code, status.HTTP_200_OK)

            mock_create_zip_response.assert_called_once()
            call_args, call_kwargs = mock_create_zip_response.call_args
            self.assertIn("duplicate_findings_list", call_kwargs)
            self.assertEqual(call_kwargs["duplicate_findings_list"], [mock_duplicate_finding.dict()])

        data = {"document_status_filter": ["VERIFIED"], "include_duplicate_fields": False}
        mock_sim_patch = patch("packager.services.package_operations_service.PackageOperationsService.simulate_package")
        mock_zip = patch("packager.services.package_operations_service.PackageOperationsService._create_zip_response")
        with mock_sim_patch as mock_sim, mock_zip as mock_create_zip_response:
            mock_sim.return_value = (mock_render_result, mock_packager_config)
            mock_create_zip_response.return_value = HttpResponse(content=b"zip content", content_type="application/zip")
            response = self.client.post(download_url, data=json.dumps(data), content_type="application/json")

            self.assertEqual(response.status_code, status.HTTP_200_OK)

            mock_create_zip_response.assert_called_once()
            call_args, call_kwargs = mock_create_zip_response.call_args
            self.assertNotIn("duplicate_findings_list", call_kwargs)

    def tearDown(self):
        self.patcher.stop()
        super().tearDown()


class PackageViewSetTests(TransactionTestCase, TestCaseMixin):
    def setUp(self):
        super().setUp()
        self.staff_user, self.staff_user_path = create_staff_user()
        self.staff_client = Client()
        self.staff_client.login(username="staffuser", password="12345")

        self.user, self.user_path = create_user()
        self.client = Client()
        self.client.login(username="normaluser", password="12345")

        self.org, _ = create_organization()
        self.dp, _ = create_data_pool(organization=self.org)
        self.dp2, _ = create_data_pool(label="test dp2 package", organization=self.org)
        self.packager = create_packager(data_pool=self.dp)
        self.package = Package.objects.create(packager=self.packager, data_pool=self.dp)
        self.package_url = get_api_path("v6:package-detail", self.dp.obfuscated_id,
                                        obfuscated_id=self.package.obfuscated_id, advanced=True)

        # Associate user with organization first, then with data pool
        self.user.organizations.add(self.org)
        self.user.data_pools.add(self.dp)
        self.user.save()

    def test_list_packages(self):
        url = get_api_path("v6:package-list", self.dp.obfuscated_id, advanced=True)
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["count"], 1)
        self.assertEqual(response.json()["results"][0]["id"], self.package.obfuscated_id)

    def test_retrieve_package(self):
        response = self.client.get(self.package_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["id"], self.package.obfuscated_id)

    def test_create_package(self):
        url = get_api_path("v6:package-list", self.dp.obfuscated_id, advanced=True)
        data = {"packager": self.packager.obfuscated_id}
        response = self.client.post(url, data=json.dumps(data), content_type="application/json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # The response should contain the relative URL for the packager
        expected_packager_url = (f"http://testserver/v6/data-pools/{self.dp.obfuscated_id}/"
                                 f"packagers/{self.packager.obfuscated_id}/?advanced=true")
        self.assertEqual(response.json()["packager"], expected_packager_url)

    def test_update_package(self):
        data = {"status": "VERIFIED"}
        response = self.client.patch(self.package_url, data=json.dumps(data), content_type="application/json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.json()["status"], "VERIFIED")

    def test_delete_package(self):
        response = self.client.delete(self.package_url)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertFalse(Package.objects.filter(id=self.package.id).exists())

    def test_package_data_pool_scoping(self):
        """Test that users can only access packages within their data pools."""
        # Create a user with access to only one data pool (self.dp)
        user_dp1, _ = create_user(username="user_dp1_package_test", password="password123",
                                  email="<EMAIL>")
        user_dp1.organizations.set([self.org])
        user_dp1.data_pools.set([self.dp])
        user_dp1.save()
        client_dp1 = Client()
        client_dp1.login(username="user_dp1_package_test", password="password123", email="<EMAIL>")

        # Create a package in self.dp (should be accessible)
        package_dp1 = Package.objects.create(packager=self.packager, data_pool=self.dp)
        package_dp1_url = get_api_path("v6:package-detail", self.dp.obfuscated_id,
                                       obfuscated_id=package_dp1.obfuscated_id, advanced=True)

        # Create a package in self.dp2 (should NOT be accessible)
        packager_dp2 = create_packager(data_pool=self.dp2)
        package_dp2 = Package.objects.create(packager=packager_dp2, data_pool=self.dp2)
        package_dp2_url = get_api_path("v6:package-detail", self.dp2.obfuscated_id,
                                       obfuscated_id=package_dp2.obfuscated_id, advanced=True)

        # Refresh user and re-login client to ensure correct data pool associations
        user_dp1.refresh_from_db()
        client_dp1.login(username="user_dp1_package_test", password="password123")

        # Test accessing package in the user's data pool
        response = client_dp1.get(package_dp1_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["id"], package_dp1.obfuscated_id)

        # Test accessing package in a different data pool
        response = client_dp1.get(package_dp2_url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        # Test listing packages - should only show packages in self.dp
        list_url_dp1 = get_api_path("v6:package-list", self.dp.obfuscated_id, advanced=True)
        response = client_dp1.get(list_url_dp1)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        package_ids_in_list = [item["id"] for item in response.json()["results"]]
        self.assertIn(package_dp1.obfuscated_id, package_ids_in_list)
        self.assertNotIn(package_dp2.obfuscated_id, package_ids_in_list)

        # Test creating a package in a different data pool (should not be allowed via URL)
        list_url_dp2 = get_api_path("v6:package-list", self.dp2.obfuscated_id, advanced=True)
        data = {"packager": get_api_path("v6:packager-detail", self.dp2.obfuscated_id,
                                         obfuscated_id=packager_dp2.obfuscated_id, advanced=True)}
        response = client_dp1.post(list_url_dp2, data=json.dumps(data), content_type="application/json")
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        # Test updating a package in a different data pool
        data = {"status": "VERIFIED"}
        response = client_dp1.patch(package_dp2_url, data=json.dumps(data), content_type="application/json")
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        # Test deleting a package in a different data pool
        response = client_dp1.delete(package_dp2_url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)


class DocumentDataViewSetTests(TransactionTestCase, TestCaseMixin):
    def setUp(self):
        super().setUp()
        self.staff_user, self.staff_user_path = create_staff_user()
        self.staff_client = Client()
        self.staff_client.login(username="staffuser", password="12345")

        self.user, self.user_path = create_user()
        self.client = Client()
        self.client.login(username="normaluser", password="12345")

        self.org, _ = create_organization()
        self.dp, _ = create_data_pool(organization=self.org)
        self.dp2, _ = create_data_pool(label="test dp2 docdata", organization=self.org)
        self.doc, _ = create_document(data_pool=self.dp)
        self.doc_data, _ = DocumentData.objects.get_or_create(document=self.doc, data_pool=self.dp)
        self.doc_data_url = get_api_path("v6:documentdata-detail", self.dp.obfuscated_id,
                                         obfuscated_id=self.doc_data.obfuscated_id, advanced=True)

        # Associate user with organization first, then with data pool
        self.user.organizations.add(self.org)
        self.user.data_pools.add(self.dp)
        self.user.save()

    def test_list_document_data(self):
        url = get_api_path("v6:documentdata-list", self.dp.obfuscated_id, advanced=True)
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["count"], 1)
        self.assertEqual(response.json()["results"][0]["id"], self.doc_data.obfuscated_id)

    def test_retrieve_document_data(self):
        response = self.client.get(self.doc_data_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        print(response.json())
        self.assertEqual(response.json()["id"], self.doc_data.obfuscated_id)

    def test_create_document_data(self):
        url = get_api_path("v6:documentdata-list", self.dp.obfuscated_id, advanced=True)
        doc2, _ = create_document(data_pool=self.dp)

        DocumentData.objects.filter(document=doc2, data_pool=self.dp).delete()

        data = {"document": doc2.obfuscated_id, "data_pool": self.dp.obfuscated_id}
        response = self.client.post(url, data=json.dumps(data), content_type="application/json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # The response should contain the relative URL for the document
        expected_document_url = (
            f"http://testserver/v6/data-pools/{self.dp.obfuscated_id}/documents/{doc2.obfuscated_id}/?advanced=true")
        self.assertEqual(response.json()["document"], expected_document_url)

    def test_update_document_data(self):
        data = {"status": "VERIFIED"}
        response = self.client.patch(self.doc_data_url, data=json.dumps(data), content_type="application/json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.json()["status"], "VERIFIED")

    def test_delete_document_data(self):
        response = self.client.delete(self.doc_data_url)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertFalse(DocumentData.objects.filter(id=self.doc_data.id).exists())

    def test_document_data_data_pool_scoping(self):
        """Test that users can only access document data within their data pools."""
        # Create a user with access to only one data pool (self.dp)
        user_dp1, _ = create_user(username="user_dp1_docdata_test", password="password123",
                                  email="<EMAIL>")
        user_dp1.organizations.set([self.org])
        user_dp1.data_pools.set([self.dp])
        user_dp1.save()
        client_dp1 = Client()
        client_dp1.login(username="user_dp1_docdata_test", password="password123")

        # Create document data in self.dp (should be accessible)
        doc_dp1, _ = create_document(data_pool=self.dp)
        doc_data_dp1, _ = DocumentData.objects.get_or_create(document=doc_dp1, data_pool=self.dp)
        doc_data_dp1_url = get_api_path("v6:documentdata-detail", self.dp.obfuscated_id,
                                        obfuscated_id=doc_data_dp1.obfuscated_id, advanced=True)

        # Create document data in self.dp2 (should NOT be accessible)
        doc_dp2, _ = create_document(data_pool=self.dp2)
        doc_data_dp2, _ = DocumentData.objects.get_or_create(document=doc_dp2, data_pool=self.dp2)
        doc_data_dp2_url = get_api_path("v6:documentdata-detail", self.dp2.obfuscated_id,
                                        obfuscated_id=doc_data_dp2.obfuscated_id, advanced=True)

        # Refresh user and re-login client to ensure correct data pool associations
        user_dp1.refresh_from_db()
        client_dp1.login(username="user_dp1_docdata_test", password="password123")

        # Test accessing document data in the user's data pool
        response = client_dp1.get(doc_data_dp1_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["id"], doc_data_dp1.obfuscated_id)

        # Test accessing document data in a different data pool
        response = client_dp1.get(doc_data_dp2_url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        # Test listing document data - should only show document data in self.dp
        list_url_dp1 = get_api_path("v6:documentdata-list", self.dp.obfuscated_id, advanced=True)
        response = client_dp1.get(list_url_dp1)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        doc_data_ids_in_list = [item["id"] for item in response.json()["results"]]
        self.assertIn(doc_data_dp1.obfuscated_id, doc_data_ids_in_list)
        self.assertNotIn(doc_data_dp2.obfuscated_id, doc_data_ids_in_list)

        # Test creating document data in a different data pool (should not be allowed via URL)
        list_url_dp2 = get_api_path("v6:documentdata-list", self.dp2.obfuscated_id, advanced=True)
        doc_dp2_new, _ = create_document(data_pool=self.dp2)
        data = {"document": doc_dp2_new.obfuscated_id, "data_pool": self.dp2.obfuscated_id}
        response = client_dp1.post(list_url_dp2, data=json.dumps(data), content_type="application/json")
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        # Test updating document data in a different data pool
        data = {"status": "VERIFIED"}
        response = client_dp1.patch(doc_data_dp2_url, data=json.dumps(data), content_type="application/json")
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        # Test deleting document data in a different data pool
        response = client_dp1.delete(doc_data_dp2_url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)


class ExtractionBatchDataViewSetTests(TransactionTestCase, TestCaseMixin):
    def setUp(self):
        super().setUp()
        import uuid

        # Add unique identifier to avoid conflicts with other test runs
        self.test_id = str(uuid.uuid4())[:8]
        self.staff_user, self.staff_user_path = create_staff_user()
        self.staff_client = Client()
        self.staff_client.login(username="staffuser", password="12345")

        self.user, self.user_path = create_user()
        self.client = Client()
        self.client.login(username="normaluser", password="12345")

        self.org, _ = create_organization()
        self.dp, _ = create_data_pool(organization=self.org)
        self.packager = create_packager(data_pool=self.dp)
        self.package = Package.objects.create(packager=self.packager, data_pool=self.dp)
        self.ts, _ = create_training_ready_training_set(data_pool=self.dp,
                                                        label=f"ExtractionBatchDataViewSetTests_ts_{self.test_id}")
        self.tr, _ = create_training_revision(data_pool=self.dp, training_set=self.ts)

        self.doc, _ = create_document(data_pool=self.dp, label="doc1_PackagerViewSetTests")
        self.doc2, _ = create_document(data_pool=self.dp, label="doc2_PackagerViewSetTests")
        self.doc3, _ = create_document(data_pool=self.dp, label="doc3_PackagerViewSetTests")
        self.doc4, _ = create_document(data_pool=self.dp, label="doc4_PackagerViewSetTests")
        self.ts2, _ = create_training_ready_training_set(data_pool=self.dp,
                                                         label=f"ExtractionBatchDataViewSetTests_ts2_{self.test_id}")
        self.tr, _ = create_training_revision(data_pool=self.dp, training_set=self.ts)

        self.dp2, _ = create_data_pool(label="test dp2", organization=self.org)
        self.dp2_doc, _ = create_document(data_pool=self.dp2)
        self.dp2_packager = create_packager(data_pool=self.dp2, label="Test2 Packager", packager_status="IDLE")

        self.dp2_ts, _ = create_training_ready_training_set(data_pool=self.dp2,
                                                            label="ExtractionBatchDataViewSetTests_"
                                                                  f"dp2_ts_{self.test_id}")
        self.dp2_tr, _ = create_training_revision(data_pool=self.dp2, training_set=self.dp2_ts)

        self.dp2_package = Package.objects.create(packager=self.dp2_packager, data_pool=self.dp2)

        self.eb, self.eb_path = create_extraction_batch(data_pool=self.dp, documents=[self.doc],
                                                        training_revision=self.tr)
        self.eb_data = ExtractionBatchData.objects.create(package=self.package, extraction_batch=self.eb,
                                                          data_pool=self.dp)
        self.eb_data_url = get_api_path("v6:extractionbatchdata-detail", self.dp.obfuscated_id,
                                        obfuscated_id=self.eb_data.obfuscated_id, advanced=True, )

        # Associate user with organization first, then with data pool
        self.user.organizations.add(self.org)
        self.user.data_pools.add(self.dp)
        self.user.save()

    def test_list_extraction_batch_data(self):
        url = get_api_path("v6:extractionbatchdata-list", self.dp.obfuscated_id, advanced=True)
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["count"], 1)
        self.assertEqual(response.json()["results"][0]["id"], self.eb_data.obfuscated_id)

    def test_retrieve_extraction_batch_data(self):
        response = self.client.get(self.eb_data_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["id"], self.eb_data.obfuscated_id)

    def test_create_extraction_batch_data(self):
        url = get_api_path("v6:extractionbatchdata-list", self.dp.obfuscated_id, advanced=True)
        packager2 = create_packager(data_pool=self.dp)
        package2 = Package.objects.create(packager=packager2, data_pool=self.dp)
        eb2, _ = create_extraction_batch(data_pool=self.dp, training_revision=self.tr)
        data = {"package": package2.obfuscated_id, "extraction_batch": eb2.obfuscated_id,
                "data_pool": self.dp.obfuscated_id, }
        response = self.client.post(url, data=json.dumps(data), content_type="application/json")
        self.assertEqual(status.HTTP_201_CREATED, response.status_code)

        # The response should contain the full URLs for the related objects
        expected_package_url = (
            f"http://testserver/v6/data-pools/{self.dp.obfuscated_id}/packages/{package2.obfuscated_id}/?advanced=true")
        expected_eb_url = (f"http://testserver/v6/data-pools/{self.dp.obfuscated_id}/"
                           f"extraction-batches/{eb2.obfuscated_id}/?advanced=true")
        self.assertEqual(expected_package_url, response.json()["package"])
        self.assertEqual(expected_eb_url, response.json()["extraction_batch"])

    def test_update_extraction_batch_data(self):
        data = {"status": "VERIFIED"}
        response = self.client.patch(self.eb_data_url, data=json.dumps(data), content_type="application/json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.json()["status"], "VERIFIED")

    def test_delete_extraction_batch_data(self):
        response = self.client.delete(self.eb_data_url)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertFalse(ExtractionBatchData.objects.filter(id=self.eb_data.id).exists())

    def test_extraction_batch_data_data_pool_scoping(self):
        """Test that users can only access extraction batch data within their data pools."""
        # Create a user with access to only one data pool (self.dp)
        user_dp1, _ = create_user(username="user_dp1_ebdata_test", password="password123",
                                  email="<EMAIL>")
        user_dp1.organizations.set([self.org])
        user_dp1.data_pools.set([self.dp])
        user_dp1.save()
        client_dp1 = Client()
        client_dp1.login(username="user_dp1_ebdata_test", password="password123")

        # Create extraction batch data in self.dp (should be accessible)
        packager_dp1 = create_packager(data_pool=self.dp)
        package_dp1 = Package.objects.create(packager=packager_dp1, data_pool=self.dp)
        eb_dp1, _ = create_extraction_batch(data_pool=self.dp, training_revision=self.tr)
        eb_data_dp1 = ExtractionBatchData.objects.create(package=package_dp1, extraction_batch=eb_dp1,
                                                         data_pool=self.dp)
        eb_data_dp1_url = get_api_path("v6:extractionbatchdata-detail", self.dp.obfuscated_id,
                                       obfuscated_id=eb_data_dp1.obfuscated_id, advanced=True, )

        # Create extraction batch data in self.dp2 (should NOT be accessible)
        packager_dp2 = create_packager(data_pool=self.dp2)
        package_dp2 = Package.objects.create(packager=packager_dp2, data_pool=self.dp2)
        eb_dp2, _ = create_extraction_batch(data_pool=self.dp2, training_revision=self.dp2_tr)
        eb_data_dp2 = ExtractionBatchData.objects.create(package=package_dp2, extraction_batch=eb_dp2,
                                                         data_pool=self.dp2)
        eb_data_dp2_url = get_api_path("v6:extractionbatchdata-detail", self.dp2.obfuscated_id,
                                       obfuscated_id=eb_data_dp2.obfuscated_id, advanced=True, )

        # Refresh user and re-login client to ensure correct data pool associations
        user_dp1.refresh_from_db()
        client_dp1.login(username="user_dp1_ebdata_test", password="password123")

        # Test accessing extraction batch data in the user's data pool
        response = client_dp1.get(eb_data_dp1_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["id"], eb_data_dp1.obfuscated_id)

        # Test accessing extraction batch data in a different data pool
        response = client_dp1.get(eb_data_dp2_url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        # Test listing extraction batch data - should only show extraction batch data in self.dp
        list_url_dp1 = get_api_path("v6:extractionbatchdata-list", self.dp.obfuscated_id, advanced=True)
        response = client_dp1.get(list_url_dp1)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        eb_data_ids_in_list = [item["id"] for item in response.json()["results"]]
        self.assertIn(eb_data_dp1.obfuscated_id, eb_data_ids_in_list)
        self.assertNotIn(eb_data_dp2.obfuscated_id, eb_data_ids_in_list)

        # Test creating extraction batch data in a different data pool (should not be allowed via URL)
        list_url_dp2 = get_api_path("v6:extractionbatchdata-list", self.dp2.obfuscated_id, advanced=True)
        packager_dp2_new = create_packager(data_pool=self.dp2)
        package_dp2_new = Package.objects.create(packager=packager_dp2_new, data_pool=self.dp2)
        eb_dp2_new, _ = create_extraction_batch(data_pool=self.dp2, training_revision=self.dp2_tr)

        data = {"package": package_dp2_new.obfuscated_id, "extraction_batch": eb_dp2_new.obfuscated_id,
                "data_pool": self.dp2.obfuscated_id, }
        response = client_dp1.post(list_url_dp2, data=json.dumps(data), content_type="application/json")
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        # Test updating extraction batch data in a different data pool
        data = {"status": "VERIFIED"}
        response = client_dp1.patch(eb_data_dp2_url, data=json.dumps(data), content_type="application/json")
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        # Test deleting extraction batch data in a different data pool
        response = client_dp1.delete(eb_data_dp2_url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
