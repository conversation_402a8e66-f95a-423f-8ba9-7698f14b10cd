import csv
import io
import json
from datetime import date, datetime

from django.test import TransactionTestCase, override_settings
from django.utils import timezone
from glynt_schemas.data.output.chain_of_custody import (GtdrChainOfCustodyCsv, SourceFileStatus, ExclusionReason,
                                                        ParentRelationship, )
from glynt_schemas.document.document_attributes import DocumentDataStatus

from documents.tests.util import create_document
from glynt_api.tests.util import TestCaseMixin
from organizations.tests.util import create_data_pool, create_organization
from packager.models import DocumentData, Package, PackageEntry
from packager.pydantic_classes import PackageEntryStatus, DocumentRelationshipTypeLabel
from packager.services.coc_report_service import CoCReportService
from packager.services.package_rendering_service import PackageRenderingService
from packager.tests.util import create_packager
from packager.utils.coc_utils import get_canonical_document_data, get_duplicate_family


@override_settings(DEBUG=False)
class CoCReportServiceTests(TransactionTestCase, TestCaseMixin):
    """Tests for CoC report service functionality."""

    def setUp(self):
        """Set up test data for CoC report service tests."""
        super().setUp()
        self.org, _ = create_organization()
        self.data_pool, _ = create_data_pool(organization=self.org)
        self.packager = create_packager(data_pool=self.data_pool)
        self.start_date = date(2024, 1, 1)
        self.end_date = date(2024, 12, 31)

        self._service = None

    @property
    def service(self):
        """Cached service instance to avoid recreating."""
        if self._service is None:
            self._service = CoCReportService(self.data_pool, self.start_date, self.end_date)
        return self._service

    def _create_document_with_data(self, label, content_md5, status=DocumentDataStatus.VERIFIED.name,
                                   **doc_data_kwargs):
        """Helper method to efficiently create document with DocumentData."""
        doc, _ = create_document(data_pool=self.data_pool, label=label, content_md5=content_md5, status=status, )

        defaults = {"status": status, "source_file_id": doc.obfuscated_id, "source_file_name": doc.label,
                    "source_file_md5": doc.content_md5, "received_at": datetime(2024, 6, 1, tzinfo=timezone.utc),
                    "source_file_num_pages": 10, "source_file_size": 1024, }
        defaults.update(doc_data_kwargs)

        doc_data, created = DocumentData.objects.get_or_create(document=doc, data_pool=self.data_pool,
                                                               defaults=defaults)

        if not created:
            for key, value in defaults.items():
                setattr(doc_data, key, value)
            doc_data.save()

        return doc, doc_data

    def test_generate_report_data_with_package_entries(self):
        """Test that generate_report_data includes documents from package entries within the date range."""
        doc1, doc_data1 = self._create_document_with_data("Doc1.pdf", "md5_1",
                                                          received_at=datetime(2024, 6, 1, tzinfo=timezone.utc),
                                                          source_file_num_pages=10, source_file_size=1024)

        doc2, doc_data2 = self._create_document_with_data("Doc2.pdf", "md5_2",
                                                          received_at=datetime(2023, 12, 1, tzinfo=timezone.utc),
                                                          source_file_num_pages=5, source_file_size=512)

        package1 = Package.objects.create(packager=self.packager, data_pool=self.data_pool)
        PackageEntry.objects.create(package=package1, document_data=doc_data1, data_pool=self.data_pool,
                                    status_in_package=PackageEntryStatus.INCLUDED.name,
                                    delivered_at=datetime(2024, 6, 15, tzinfo=timezone.utc))

        package2 = Package.objects.create(packager=self.packager, data_pool=self.data_pool)
        PackageEntry.objects.create(package=package2, document_data=doc_data2, data_pool=self.data_pool,
                                    status_in_package=PackageEntryStatus.INCLUDED.name,
                                    delivered_at=datetime(2024, 1, 5, tzinfo=timezone.utc))

        report_data = self.service.generate_report_data()

        self.assertEqual(len(report_data), 2)

        for entry in report_data:
            self.assertIsInstance(entry, GtdrChainOfCustodyCsv)
            self.assertEqual(entry.coc_report_start_date, self.start_date)
            self.assertEqual(entry.coc_report_end_date, self.end_date)

    def test_generate_report_data_with_docs_not_in_packages(self):
        doc1, doc_data1 = self._create_document_with_data("Standalone1.pdf", "md5_standalone1",
                                                          received_at=datetime(2024, 6, 1, tzinfo=timezone.utc),
                                                          source_file_num_pages=10, source_file_size=1024)

        doc2, doc_data2 = self._create_document_with_data("Standalone2.pdf", "md5_standalone2",
                                                          received_at=datetime(2024, 7, 1, tzinfo=timezone.utc),
                                                          source_file_num_pages=5, source_file_size=512)

        self.assertEqual(PackageEntry.objects.filter(document_data__in=[doc_data1, doc_data2]).count(), 0)

        report_data = self.service.generate_report_data()

        self.assertEqual(len(report_data), 2)

        for entry in report_data:
            self.assertIsInstance(entry, GtdrChainOfCustodyCsv)
            self.assertEqual(entry.coc_report_start_date, self.start_date)
            self.assertEqual(entry.coc_report_end_date, self.end_date)

    def test_create_report_entry(self):
        """Test that create_report_entry maps fields correctly."""
        doc, _ = create_document(data_pool=self.data_pool, label="original.pdf", content_md5="md5_abc",
                                 status=DocumentDataStatus.VERIFIED.name, inbound_path="/inbound/original.pdf", )
        doc_data, created = DocumentData.objects.get_or_create(document=doc, data_pool=self.data_pool,
                                                               defaults={"status": DocumentDataStatus.VERIFIED.name,
                                                                         "source_file_id": "sf_abc",
                                                                         "source_file_name": "original.pdf",
                                                                         "source_file_md5": "md5_abc",
                                                                         "source_file_num_pages": 10,
                                                                         "source_file_size": 1024,
                                                                         "source_file_detected_language": "en",
                                                                         "received_at": datetime(2024, 5, 10, 10, 0, 0,
                                                                                                 tzinfo=timezone.utc),
                                                                         "exclusion_reason": "Duplicate content",
                                                                         "parent_file_id": "sf_xyz",
                                                                         "relationship_to_parent": "CONTENT_DUPLICATE"})
        if not created:
            doc_data.exclusion_reason = "Duplicate content"
            doc_data.parent_file_id = "sf_xyz"
            doc_data.relationship_to_parent = "CONTENT_DUPLICATE"
            doc_data.save()

        package = Package.objects.create(packager=self.packager, data_pool=self.data_pool)
        package_entry = PackageEntry.objects.create(package=package, document_data=doc_data, data_pool=self.data_pool,
                                                    status_in_package=PackageEntryStatus.EXCLUDED_AS_DUPLICATE.name,
                                                    filename_in_package="final.json",
                                                    delivered_at=datetime(2024, 5, 20, 11, 0, 0, tzinfo=timezone.utc),
                                                    exclusion_reason=DocumentRelationshipTypeLabel.DUPLICATE.value)

        service = CoCReportService(self.data_pool, self.start_date, self.end_date)
        report_entry = service.create_report_entry(doc_data, package_entry)

        self.assertIsInstance(report_entry, GtdrChainOfCustodyCsv)
        self.assertEqual(report_entry.coc_report_start_date, self.start_date)
        self.assertEqual(report_entry.coc_report_end_date, self.end_date)
        self.assertEqual(report_entry.source_file_received_at, doc_data.received_at)
        self.assertEqual(report_entry.source_file_id, doc_data.source_file_id)
        self.assertEqual(report_entry.source_file_original_name, doc_data.source_file_name)
        self.assertEqual(report_entry.source_file_original_path, "/inbound/original.pdf")
        self.assertEqual(report_entry.source_file_final_name, package_entry.filename_in_package)
        self.assertIsInstance(report_entry.source_file_status, SourceFileStatus)
        self.assertEqual(report_entry.exclusion_reason, ExclusionReason.DUPLICATE)
        self.assertEqual(report_entry.parent_file_id, doc_data.parent_file_id)
        self.assertEqual(report_entry.relationship_to_parent, ParentRelationship.DUPLICATE)
        self.assertEqual(report_entry.package_id, package_entry.package.obfuscated_id)
        self.assertEqual(report_entry.delivered_at, package_entry.delivered_at)
        expected_num_pages = doc_data.source_file_num_pages if doc_data.source_file_num_pages else -1
        self.assertEqual(report_entry.num_pages, expected_num_pages)
        self.assertEqual(report_entry.detected_language, doc_data.source_file_detected_language)
        self.assertEqual(report_entry.size, doc_data.source_file_size)

    def test_create_report_entry_no_package_entry(self):
        """Test create_report_entry when no package entry is provided."""
        doc, _ = create_document(data_pool=self.data_pool, label="standalone.pdf", content_md5="md5_standalone",
                                 status=DocumentDataStatus.PENDING_REVIEW.name, )
        doc_data, _ = DocumentData.objects.get_or_create(document=doc, data_pool=self.data_pool,
                                                         defaults={"status": DocumentDataStatus.PENDING_REVIEW.name,
                                                                   "source_file_id": "sf_abc",
                                                                   "source_file_name": doc.label,
                                                                   "source_file_md5": doc.content_md5,
                                                                   "received_at": datetime(2024, 5, 10, 10, 0, 0,
                                                                                           tzinfo=timezone.utc),
                                                                   "exclusion_reason": "Not in scope",
                                                                   "source_file_num_pages": 1, "source_file_size": 100})

        service = CoCReportService(self.data_pool, self.start_date, self.end_date)
        report_entry = service.create_report_entry(doc_data, None)

        self.assertIsInstance(report_entry, GtdrChainOfCustodyCsv)
        self.assertEqual(report_entry.coc_report_start_date, self.start_date)
        self.assertEqual(report_entry.coc_report_end_date, self.end_date)
        self.assertEqual(report_entry.source_file_received_at, doc_data.received_at)
        self.assertEqual(report_entry.source_file_id, doc_data.source_file_id)
        self.assertEqual(report_entry.source_file_final_name, "")
        self.assertIsInstance(report_entry.source_file_status, SourceFileStatus)
        self.assertIsNone(report_entry.exclusion_reason)
        self.assertIsNone(report_entry.parent_file_id)
        self.assertEqual(report_entry.package_id, "")
        self.assertIsInstance(report_entry.delivered_at, datetime)

    def test_map_status_for_report_delivered(self):
        """Test status mapping for TRANSMISSION_SUCCESSFUL package entry."""
        doc, _ = create_document(data_pool=self.data_pool, status=DocumentDataStatus.VERIFIED.name)
        doc_data, _ = DocumentData.objects.get_or_create(document=doc, data_pool=self.data_pool,
                                                         defaults={"status": DocumentDataStatus.VERIFIED.name,
                                                                   "source_file_id": doc.obfuscated_id,
                                                                   "source_file_name": doc.label,
                                                                   "source_file_md5": doc.content_md5,
                                                                   "received_at": timezone.now(),
                                                                   "source_file_num_pages": 1,
                                                                   "source_file_size": 100, }, )

        package = Package.objects.create(packager=self.packager, data_pool=self.data_pool)
        package_entry = PackageEntry.objects.create(package=package, document_data=doc_data, data_pool=self.data_pool,
                                                    status_in_package=PackageEntryStatus.TRANSMISSION_SUCCESSFUL.name)

        service = CoCReportService(self.data_pool, None, None)
        status = service._map_status_for_report(doc_data, package_entry)
        self.assertEqual(status, SourceFileStatus.DELIVERED)

    def test_map_status_for_report_excluded_package_entry(self):
        """Test status mapping for EXCLUDED package entry statuses."""
        doc, _ = create_document(data_pool=self.data_pool, status=DocumentDataStatus.VERIFIED.name)
        doc_data, _ = DocumentData.objects.get_or_create(document=doc, data_pool=self.data_pool,
                                                         defaults={"status": DocumentDataStatus.VERIFIED.name,
                                                                   "source_file_id": doc.obfuscated_id,
                                                                   "source_file_name": doc.label,
                                                                   "source_file_md5": doc.content_md5,
                                                                   "received_at": timezone.now(),
                                                                   "source_file_num_pages": 1,
                                                                   "source_file_size": 100, }, )

        service = CoCReportService(self.data_pool, None, None)
        package = Package.objects.create(packager=self.packager, data_pool=self.data_pool)

        excluded_statuses = [PackageEntryStatus.EXCLUDED, PackageEntryStatus.EXCLUDED_AS_DUPLICATE,
                             PackageEntryStatus.EXCLUDED_BY_STATUS, PackageEntryStatus.EXCLUDED_BY_CONFIG, ]
        for pe_status in excluded_statuses:
            package_entry = PackageEntry.objects.create(package=package, document_data=doc_data,
                                                        data_pool=self.data_pool, status_in_package=pe_status.name, )
            status = service._map_status_for_report(doc_data, package_entry)
            self.assertEqual(status, SourceFileStatus.EXCLUDED)
            package_entry.delete()

    def test_map_status_for_report_transmission_failed(self):
        """Test status mapping for TRANSMISSION_FAILED package entry."""
        doc, _ = create_document(data_pool=self.data_pool, status=DocumentDataStatus.VERIFIED.name)
        doc_data, _ = DocumentData.objects.get_or_create(document=doc, data_pool=self.data_pool,
                                                         defaults={"status": DocumentDataStatus.VERIFIED.name,
                                                                   "source_file_id": doc.obfuscated_id,
                                                                   "source_file_name": doc.label,
                                                                   "source_file_md5": doc.content_md5,
                                                                   "received_at": timezone.now(),
                                                                   "source_file_num_pages": 1,
                                                                   "source_file_size": 100, }, )

        package = Package.objects.create(packager=self.packager, data_pool=self.data_pool)
        package_entry = PackageEntry.objects.create(package=package, document_data=doc_data, data_pool=self.data_pool,
                                                    status_in_package=PackageEntryStatus.TRANSMISSION_FAILED.name, )

        service = CoCReportService(self.data_pool, None, None)
        status = service._map_status_for_report(doc_data, package_entry)
        self.assertEqual(status, SourceFileStatus.PENDING)

    def test_map_status_for_report_document_status(self):
        """Test status mapping based on DocumentData status when no package entry exclusion/delivery."""
        service = CoCReportService(self.data_pool, None, None)
        doc_status = DocumentDataStatus.PENDING_REVIEW.name
        doc1, _ = create_document(data_pool=self.data_pool, status=DocumentDataStatus.PENDING_REVIEW.name)
        doc_data_research, created = DocumentData.objects.get_or_create(document=doc1, data_pool=self.data_pool,
                                                                        defaults={"status": doc_status,
                                                                                  "source_file_id": doc1.obfuscated_id,
                                                                                  "source_file_name": doc1.label,
                                                                                  "source_file_md5": doc1.content_md5,
                                                                                  "received_at": timezone.now(),
                                                                                  "source_file_num_pages": 1,
                                                                                  "source_file_size": 100})
        if not created:
            doc_data_research.status = DocumentDataStatus.PENDING_REVIEW.name
            doc_data_research.save()
        status_research = service._map_status_for_report(doc_data_research, None)
        self.assertEqual(status_research, SourceFileStatus.RESEARCH)

        doc2, _ = create_document(data_pool=self.data_pool, status=DocumentDataStatus.CREATED.name)
        doc_data_pending, created = DocumentData.objects.get_or_create(document=doc2, data_pool=self.data_pool,
                                                                       defaults={
                                                                           "status": DocumentDataStatus.CREATED.name,
                                                                           "source_file_id": doc2.obfuscated_id,
                                                                           "source_file_name": doc2.label,
                                                                           "source_file_md5": doc2.content_md5,
                                                                           "received_at": timezone.now(),
                                                                           "source_file_num_pages": 1,
                                                                           "source_file_size": 100, }, )
        if not created:
            doc_data_pending.status = DocumentDataStatus.CREATED.name
            doc_data_pending.save()
        status_pending = service._map_status_for_report(doc_data_pending, None)
        self.assertEqual(status_pending, SourceFileStatus.PENDING)

        doc3, _ = create_document(data_pool=self.data_pool, status=DocumentDataStatus.VERIFIED.name)
        doc_data_verified, created = DocumentData.objects.get_or_create(document=doc3, data_pool=self.data_pool,
                                                                        defaults={
                                                                            "status": DocumentDataStatus.VERIFIED.name,
                                                                            "source_file_id": doc3.obfuscated_id,
                                                                            "source_file_name": doc3.label,
                                                                            "source_file_md5": doc3.content_md5,
                                                                            "received_at": timezone.now(),
                                                                            "source_file_num_pages": 1,
                                                                            "source_file_size": 100, }, )
        if not created:
            doc_data_verified.status = DocumentDataStatus.VERIFIED.name
            doc_data_verified.save()
        status_verified = service._map_status_for_report(doc_data_verified, None)
        self.assertEqual(status_verified, SourceFileStatus.PENDING)

    def test_generate_csv_report(self):
        """Test that generate_csv_report generates a CSV string with correct headers and data."""
        doc1, _ = create_document(data_pool=self.data_pool, label="original.pdf", content_md5="md5_csv1",
                                  status=DocumentDataStatus.VERIFIED.name, )
        # Set the inbound_path on the document for CoC reporting
        doc1.inbound_path = "/inbound/original.pdf"
        doc1.save()

        doc_data1, created = DocumentData.objects.get_or_create(document=doc1, data_pool=self.data_pool,
                                                                defaults={"status": DocumentDataStatus.VERIFIED.name,
                                                                          "source_file_id": "sf_abc",
                                                                          "source_file_name": "original.pdf",
                                                                          "source_file_md5": "md5_csv1",
                                                                          "received_at": datetime(2024, 6, 1, 10, 0, 0,
                                                                                                  tzinfo=timezone.utc),
                                                                          "source_file_num_pages": 10,
                                                                          "source_file_detected_language": "en",
                                                                          "source_file_size": 1024, }, )
        # If the object already existed (created by signal), update it with our test values
        if not created:
            doc_data1.source_file_id = "sf_abc"
            doc_data1.source_file_name = "original.pdf"
            doc_data1.source_file_md5 = "md5_csv1"
            doc_data1.received_at = datetime(2024, 6, 1, 10, 0, 0, tzinfo=timezone.utc)
            doc_data1.source_file_num_pages = 10
            doc_data1.source_file_detected_language = "en"
            doc_data1.source_file_size = 1024
            doc_data1.save()

        package1 = Package.objects.create(packager=self.packager, data_pool=self.data_pool)
        PackageEntry.objects.create(package=package1, document_data=doc_data1, data_pool=self.data_pool,
                                    status_in_package=PackageEntryStatus.TRANSMISSION_SUCCESSFUL.name,
                                    filename_in_package="final.json",
                                    delivered_at=datetime(2024, 6, 15, 11, 0, 0, tzinfo=timezone.utc), )

        doc2, _ = create_document(data_pool=self.data_pool, label="another.pdf", content_md5="md5_csv2",
                                  status=DocumentDataStatus.VERIFIED.name, )
        # Set the inbound_path on the document for CoC reporting
        doc2.inbound_path = "/inbound/another.pdf"
        doc2.save()

        doc_data2, created = DocumentData.objects.get_or_create(document=doc2, data_pool=self.data_pool,
                                                                defaults={"status": DocumentDataStatus.VERIFIED.name,
                                                                          "source_file_id": "sf_xyz",
                                                                          "source_file_name": "another.pdf",
                                                                          "source_file_md5": "md5_csv2",
                                                                          "received_at": datetime(2024, 7, 1, 10, 0, 0,
                                                                                                  tzinfo=timezone.utc),
                                                                          "source_file_num_pages": 5,
                                                                          "source_file_detected_language": "es",
                                                                          "source_file_size": 512, }, )
        # If the object already existed (created by signal), update it with our test values
        if not created:
            doc_data2.received_at = datetime(2024, 7, 1, 10, 0, 0, tzinfo=timezone.utc)
            doc_data2.source_file_id = "sf_xyz"
            doc_data2.source_file_name = "another.pdf"
            doc_data2.source_file_md5 = "md5_csv2"
            doc_data2.source_file_num_pages = 5
            doc_data2.source_file_detected_language = "es"
            doc_data2.source_file_size = 512
            doc_data2.save()

        # Test the real service with real data
        service = CoCReportService(self.data_pool, self.start_date, self.end_date)
        csv_string = service.generate_csv_report()

        self.assertIsInstance(csv_string, str)
        self.assertTrue(len(csv_string) > 0)

        reader = csv.DictReader(io.StringIO(csv_string))
        expected_headers = service.get_field_names()
        self.assertEqual(reader.fieldnames, expected_headers)

        rows = list(reader)
        self.assertEqual(len(rows), 2)

        csv_content = csv_string.lower()
        self.assertIn("sf_abc", csv_content)
        self.assertIn("sf_xyz", csv_content)
        self.assertIn("original.pdf", csv_content)
        self.assertIn("another.pdf", csv_content)

    def test_generate_csv_report_empty(self):
        """Test generate_csv_report with no data."""
        # Create a data pool with no documents in the date range
        empty_org, _ = create_organization(label="EmptyOrg")
        empty_data_pool, _ = create_data_pool(organization=empty_org)

        service = CoCReportService(empty_data_pool, self.start_date, self.end_date)
        csv_string = service.generate_csv_report()
        self.assertEqual(csv_string, "")

    def test_map_exclusion_reason_duplicates_only(self):
        """Test exclusion reason mapping for duplicate-related exclusions only."""
        service = CoCReportService(self.data_pool, None, None)

        doc1, _ = create_document(data_pool=self.data_pool)
        doc_data_md5, created = DocumentData.objects.get_or_create(document=doc1, data_pool=self.data_pool,
                                                                   defaults={"status": DocumentDataStatus.VERIFIED.name,
                                                                             "source_file_id": doc1.obfuscated_id,
                                                                             "source_file_name": doc1.label,
                                                                             "source_file_md5": doc1.content_md5,
                                                                             "received_at": timezone.now(),
                                                                             "exclusion_reason": "MD5_DUPLICATE",
                                                                             "source_file_num_pages": 1,
                                                                             "source_file_size": 100, }, )
        # If the object already existed (created by signal), update it with our test values
        if not created:
            doc_data_md5.exclusion_reason = "MD5_DUPLICATE"
            doc_data_md5.save()
        self.assertEqual(service._map_exclusion_reason(doc_data_md5, None), ExclusionReason.DUPLICATE)

        doc2, _ = create_document(data_pool=self.data_pool)
        doc_data_label, created = DocumentData.objects.get_or_create(document=doc2, data_pool=self.data_pool, defaults={
            "status": DocumentDataStatus.VERIFIED.name, "source_file_id": doc2.obfuscated_id,
            "source_file_name": doc2.label, "source_file_md5": doc2.content_md5, "received_at": timezone.now(),
            "exclusion_reason": "LABEL_DUPLICATE", "source_file_num_pages": 1, "source_file_size": 100, }, )
        # If the object already existed (created by signal), update it with our test values
        if not created:
            doc_data_label.exclusion_reason = "LABEL_DUPLICATE"
            doc_data_label.save()
        self.assertEqual(service._map_exclusion_reason(doc_data_label, None), ExclusionReason.DUPLICATE)

        doc3, _ = create_document(data_pool=self.data_pool)
        doc_data_content, created = DocumentData.objects.get_or_create(document=doc3, data_pool=self.data_pool,
                                                                       defaults={
                                                                           "status": DocumentDataStatus.VERIFIED.name,
                                                                           "source_file_id": doc3.obfuscated_id,
                                                                           "source_file_name": doc3.label,
                                                                           "source_file_md5": doc3.content_md5,
                                                                           "received_at": timezone.now(),
                                                                           "exclusion_reason": "CONTENT_DUPLICATE",
                                                                           "source_file_num_pages": 1,
                                                                           "source_file_size": 100, }, )
        # If the object already existed (created by signal), update it with our test values
        if not created:
            doc_data_content.exclusion_reason = "CONTENT_DUPLICATE"
            doc_data_content.save()
        self.assertEqual(service._map_exclusion_reason(doc_data_content, None), ExclusionReason.CONTENT_DUPLICATE)

        # Test non-duplicate exclusion reasons (should not map - out of scope)
        doc4, _ = create_document(data_pool=self.data_pool)
        doc_data_other, _ = DocumentData.objects.get_or_create(document=doc4, data_pool=self.data_pool,
                                                               defaults={"status": DocumentDataStatus.VERIFIED.name,
                                                                         "source_file_id": doc4.obfuscated_id,
                                                                         "source_file_name": doc4.label,
                                                                         "source_file_md5": doc4.content_md5,
                                                                         "received_at": timezone.now(),
                                                                         "exclusion_reason": "FILE_CORRUPT",
                                                                         "source_file_num_pages": 1,
                                                                         "source_file_size": 100, }, )
        self.assertIsNone(service._map_exclusion_reason(doc_data_other, None))

        # Test None exclusion reason
        doc5, _ = create_document(data_pool=self.data_pool)
        doc_data_none, _ = DocumentData.objects.get_or_create(document=doc5, data_pool=self.data_pool,
                                                              defaults={"status": DocumentDataStatus.VERIFIED.name,
                                                                        "source_file_id": doc5.obfuscated_id,
                                                                        "source_file_name": doc5.label,
                                                                        "source_file_md5": doc5.content_md5,
                                                                        "received_at": timezone.now(),
                                                                        "exclusion_reason": None,
                                                                        "source_file_num_pages": 1,
                                                                        "source_file_size": 100, }, )
        self.assertIsNone(service._map_exclusion_reason(doc_data_none, None))

    def test_map_relationship_to_parent_duplicates_only(self):
        """Test relationship mapping for duplicate relationships only."""
        service = CoCReportService(self.data_pool, None, None)

        doc1, _ = create_document(data_pool=self.data_pool)
        doc_data_md5, created = DocumentData.objects.get_or_create(document=doc1, data_pool=self.data_pool,
                                                                   defaults={"status": DocumentDataStatus.VERIFIED.name,
                                                                             "source_file_id": doc1.obfuscated_id,
                                                                             "source_file_name": doc1.label,
                                                                             "source_file_md5": doc1.content_md5,
                                                                             "received_at": timezone.now(),
                                                                             "relationship_to_parent": "MD5_DUPLICATE",
                                                                             "source_file_num_pages": 1,
                                                                             "source_file_size": 100, }, )
        # If the object already existed (created by signal), update it with our test values
        if not created:
            doc_data_md5.relationship_to_parent = "MD5_DUPLICATE"
            doc_data_md5.save()
        self.assertEqual(service._map_relationship_to_parent(doc_data_md5), ParentRelationship.DUPLICATE)

        doc2, _ = create_document(data_pool=self.data_pool)
        doc_data_label, created = DocumentData.objects.get_or_create(document=doc2, data_pool=self.data_pool, defaults={
            "status": DocumentDataStatus.VERIFIED.name, "source_file_id": doc2.obfuscated_id,
            "source_file_name": doc2.label, "source_file_md5": doc2.content_md5, "received_at": timezone.now(),
            "relationship_to_parent": "LABEL_DUPLICATE", "source_file_num_pages": 1, "source_file_size": 100, }, )
        # If the object already existed (created by signal), update it with our test values
        if not created:
            doc_data_label.relationship_to_parent = "LABEL_DUPLICATE"
            doc_data_label.save()
        self.assertEqual(service._map_relationship_to_parent(doc_data_label), ParentRelationship.DUPLICATE)

        doc3, _ = create_document(data_pool=self.data_pool)
        doc_data_content, created = DocumentData.objects.get_or_create(
            document=doc3,
            data_pool=self.data_pool,
            defaults={
                "status": DocumentDataStatus.VERIFIED.name,
                "source_file_id": doc3.obfuscated_id,
                "source_file_name": doc3.label,
                "source_file_md5": doc3.content_md5,
                "received_at": timezone.now(),
                "relationship_to_parent": "CONTENT_DUPLICATE",
                "source_file_num_pages": 1,
                "source_file_size": 100
            }
        )
        # If the object already existed (created by signal), update it with our test values
        if not created:
            doc_data_content.relationship_to_parent = "CONTENT_DUPLICATE"
            doc_data_content.save()
        self.assertEqual(service._map_relationship_to_parent(doc_data_content), ParentRelationship.DUPLICATE)

        # Test non-duplicate relationships (should not map - out of scope)
        doc4, _ = create_document(data_pool=self.data_pool)
        doc_data_split, _ = DocumentData.objects.get_or_create(document=doc4, data_pool=self.data_pool,
                                                               defaults={"status": DocumentDataStatus.VERIFIED.name,
                                                                         "source_file_id": doc4.obfuscated_id,
                                                                         "source_file_name": doc4.label,
                                                                         "source_file_md5": doc4.content_md5,
                                                                         "received_at": timezone.now(),
                                                                         "relationship_to_parent": "SPLIT",
                                                                         "source_file_num_pages": 1,
                                                                         "source_file_size": 100, }, )
        self.assertIsNone(service._map_relationship_to_parent(doc_data_split))

        # Test None relationship
        doc5, _ = create_document(data_pool=self.data_pool)
        doc_data_none, _ = DocumentData.objects.get_or_create(document=doc5, data_pool=self.data_pool,
                                                              defaults={"status": DocumentDataStatus.VERIFIED.name,
                                                                        "source_file_id": doc5.obfuscated_id,
                                                                        "source_file_name": doc5.label,
                                                                        "source_file_md5": doc5.content_md5,
                                                                        "received_at": timezone.now(),
                                                                        "relationship_to_parent": None,
                                                                        "source_file_num_pages": 1,
                                                                        "source_file_size": 100, }, )
        self.assertIsNone(service._map_relationship_to_parent(doc_data_none))

    def test_comprehensive_report_data_validation(self):
        """Test that every report entry has valid, non-empty data for required fields."""
        test_scenarios = [
            {"label": "delivered_document.pdf", "md5": "md5_delivered", "status": DocumentDataStatus.VERIFIED.name,
             "package_status": PackageEntryStatus.TRANSMISSION_SUCCESSFUL.name, "exclusion_reason": None,
             "parent_file_id": None, "relationship_to_parent": None, },
            {"label": "excluded_duplicate.pdf", "md5": "md5_excluded", "status": DocumentDataStatus.VERIFIED.name,
             "package_status": PackageEntryStatus.EXCLUDED_AS_DUPLICATE.name, "exclusion_reason": "MD5_DUPLICATE",
             "parent_file_id": "parent_123", "relationship_to_parent": "MD5_DUPLICATE", },
            {"label": "content_duplicate.pdf", "md5": "md5_content_dup", "status": DocumentDataStatus.VERIFIED.name,
             "package_status": PackageEntryStatus.EXCLUDED_AS_DUPLICATE.name, "exclusion_reason": "CONTENT_DUPLICATE",
             "parent_file_id": "parent_456", "relationship_to_parent": "CONTENT_DUPLICATE", },
            {"label": "failed_transmission.pdf", "md5": "md5_failed", "status": DocumentDataStatus.VERIFIED.name,
             "package_status": PackageEntryStatus.TRANSMISSION_FAILED.name, "exclusion_reason": None,
             "parent_file_id": None, "relationship_to_parent": None, },
            {"label": "research_document.pdf", "md5": "md5_research", "status": DocumentDataStatus.PENDING_REVIEW.name,
             "package_status": None,  # No package entry
             "exclusion_reason": None, "parent_file_id": None, "relationship_to_parent": None, }, ]

        created_documents = []
        for scenario in test_scenarios:
            doc, doc_data = self._create_document_with_data(scenario["label"], scenario["md5"],
                                                            status=scenario["status"],
                                                            received_at=datetime(2024, 6, 1, tzinfo=timezone.utc),
                                                            source_file_num_pages=10, source_file_size=1024,
                                                            source_file_detected_language="en",
                                                            exclusion_reason=scenario["exclusion_reason"],
                                                            parent_file_id=scenario["parent_file_id"],
                                                            relationship_to_parent=scenario["relationship_to_parent"], )

            doc.inbound_path = f"/inbound/{scenario['label']}"
            doc.save()

            package_entry = None
            if scenario["package_status"]:
                package = Package.objects.create(packager=self.packager, data_pool=self.data_pool)
                package_entry = PackageEntry.objects.create(package=package, document_data=doc_data,
                                                            data_pool=self.data_pool,
                                                            status_in_package=scenario["package_status"],
                                                            filename_in_package=f"final_{scenario['label']}.json",
                                                            delivered_at=datetime(2024, 6, 15, tzinfo=timezone.utc),
                                                            exclusion_reason=scenario["exclusion_reason"], )

            created_documents.append(
                {"doc": doc, "doc_data": doc_data, "package_entry": package_entry, "scenario": scenario, })

        service = CoCReportService(self.data_pool, self.start_date, self.end_date)
        report_data = service.generate_report_data()

        self.assertEqual(len(report_data), len(test_scenarios))

        for i, entry in enumerate(report_data):
            with self.subTest(scenario=test_scenarios[i]["label"]):
                self.assertIsInstance(entry, GtdrChainOfCustodyCsv)

                self.assertIsNotNone(entry.coc_report_start_date)
                self.assertIsNotNone(entry.coc_report_end_date)
                self.assertIsNotNone(entry.source_file_received_at)
                self.assertIsNotNone(entry.source_file_id)
                self.assertIsNotNone(entry.source_file_original_name)
                self.assertIsNotNone(entry.source_file_status)
                self.assertIsNotNone(entry.delivered_at)

                self.assertIsInstance(entry.coc_report_start_date, date)
                self.assertIsInstance(entry.coc_report_end_date, date)
                self.assertIsInstance(entry.source_file_received_at, datetime)
                self.assertIsInstance(entry.source_file_id, str)
                self.assertIsInstance(entry.source_file_original_name, str)
                self.assertIsInstance(entry.source_file_status, SourceFileStatus)
                self.assertIsInstance(entry.delivered_at, datetime)

                self.assertTrue(len(entry.source_file_id) > 0)
                self.assertTrue(len(entry.source_file_original_name) > 0)

                if entry.num_pages is not None:
                    self.assertGreaterEqual(entry.num_pages, 0)
                if entry.size is not None:
                    self.assertGreaterEqual(entry.size, 0)

                self.assertIn(entry.source_file_status, list(SourceFileStatus))
                if entry.exclusion_reason is not None:
                    self.assertIn(entry.exclusion_reason, list(ExclusionReason))
                if entry.relationship_to_parent is not None:
                    self.assertIn(entry.relationship_to_parent, list(ParentRelationship))

    def test_report_data_completeness_for_each_scenario(self):
        """Test that each type of document scenario produces complete report data."""
        scenarios = {"delivered": {"expected_status": SourceFileStatus.DELIVERED, "expected_exclusion": None,
                                   "expected_relationship": None,
                                   "package_status": PackageEntryStatus.TRANSMISSION_SUCCESSFUL.name, },
                     "excluded_duplicate": {"expected_status": SourceFileStatus.EXCLUDED,
                                            "expected_exclusion": ExclusionReason.DUPLICATE,
                                            "expected_relationship": ParentRelationship.DUPLICATE,
                                            "package_status": PackageEntryStatus.EXCLUDED_AS_DUPLICATE.name,
                                            "exclusion_reason": "MD5_DUPLICATE",
                                            "relationship_to_parent": "MD5_DUPLICATE",
                                            "parent_file_id": "parent_123", },
                     "content_duplicate": {"expected_status": SourceFileStatus.EXCLUDED,
                                           "expected_exclusion": ExclusionReason.CONTENT_DUPLICATE,
                                           "expected_relationship": ParentRelationship.DUPLICATE,
                                           "package_status": PackageEntryStatus.EXCLUDED_AS_DUPLICATE.name,
                                           "exclusion_reason": "CONTENT_DUPLICATE",
                                           "relationship_to_parent": "CONTENT_DUPLICATE",
                                           "parent_file_id": "parent_456", },
                     "failed_transmission": {"expected_status": SourceFileStatus.PENDING, "expected_exclusion": None,
                                             "expected_relationship": None,
                                             "package_status": PackageEntryStatus.TRANSMISSION_FAILED.name, },
                     "research": {"expected_status": SourceFileStatus.RESEARCH, "expected_exclusion": None,
                                  "expected_relationship": None, "package_status": None,  # No package entry
                                  "doc_status": DocumentDataStatus.PENDING_REVIEW.name, }, }

        for scenario_name, scenario_config in scenarios.items():
            with self.subTest(scenario=scenario_name):
                doc, doc_data = self._create_document_with_data(
                    f"{scenario_name}.pdf", f"md5_{scenario_name}",
                    status=scenario_config.get("doc_status", DocumentDataStatus.VERIFIED.name),
                    received_at=datetime(2024, 6, 1, tzinfo=timezone.utc),
                    source_file_num_pages=10, source_file_size=1024,
                    exclusion_reason=scenario_config.get("exclusion_reason"),
                    parent_file_id=scenario_config.get("parent_file_id"),
                    relationship_to_parent=scenario_config.get("relationship_to_parent")
                )

                package_entry = None
                if scenario_config["package_status"]:
                    package = Package.objects.create(packager=self.packager, data_pool=self.data_pool)
                    package_entry = PackageEntry.objects.create(package=package, document_data=doc_data,
                                                                data_pool=self.data_pool,
                                                                status_in_package=scenario_config["package_status"],
                                                                filename_in_package=f"final_{scenario_name}.json",
                                                                delivered_at=datetime(2024, 6, 15, tzinfo=timezone.utc),
                                                                exclusion_reason=scenario_config.get(
                                                                    "exclusion_reason"), )

                service = CoCReportService(self.data_pool, self.start_date, self.end_date)
                report_entry = service.create_report_entry(doc_data, package_entry)

                self.assertEqual(report_entry.source_file_status, scenario_config["expected_status"])
                self.assertEqual(report_entry.exclusion_reason, scenario_config["expected_exclusion"])
                self.assertEqual(report_entry.relationship_to_parent, scenario_config["expected_relationship"])

                if scenario_config.get("parent_file_id"):
                    self.assertEqual(report_entry.parent_file_id, scenario_config["parent_file_id"])

                if package_entry:
                    package_entry.package.delete()
                doc_data.delete()
                doc.delete()

    def test_csv_report_row_validation(self):
        """Test that CSV report generates valid rows with all required data."""
        doc1, doc_data1 = self._create_document_with_data("test_csv_1.pdf", "md5_csv_test_1",
                                                          status=DocumentDataStatus.VERIFIED.name,
                                                          received_at=datetime(2024, 6, 1, 10, 0, 0,
                                                                               tzinfo=timezone.utc),
                                                          source_file_num_pages=15, source_file_size=2048,
                                                          source_file_detected_language="en",
                                                          exclusion_reason="MD5_DUPLICATE",
                                                          parent_file_id="parent_csv_1",
                                                          relationship_to_parent="MD5_DUPLICATE", )
        doc1.inbound_path = "/inbound/test_csv_1.pdf"
        doc1.save()

        doc2, doc_data2 = self._create_document_with_data("test_csv_2.pdf", "md5_csv_test_2",
                                                          status=DocumentDataStatus.PENDING_REVIEW.name,
                                                          received_at=datetime(2024, 6, 2, 14, 30, 0,
                                                                               tzinfo=timezone.utc),
                                                          source_file_num_pages=8, source_file_size=1536,
                                                          source_file_detected_language="es", )
        doc2.inbound_path = "/inbound/test_csv_2.pdf"
        doc2.save()

        package = Package.objects.create(packager=self.packager, data_pool=self.data_pool)
        PackageEntry.objects.create(package=package, document_data=doc_data1, data_pool=self.data_pool,
                                    status_in_package=PackageEntryStatus.EXCLUDED_AS_DUPLICATE.name,
                                    filename_in_package="excluded_test_1.json",
                                    delivered_at=datetime(2024, 6, 15, 11, 0, 0, tzinfo=timezone.utc),
                                    exclusion_reason="MD5_DUPLICATE", )

        service = CoCReportService(self.data_pool, self.start_date, self.end_date)
        csv_string = service.generate_csv_report()

        self.assertIsInstance(csv_string, str)
        self.assertTrue(len(csv_string) > 0)

        reader = csv.DictReader(io.StringIO(csv_string))
        rows = list(reader)

        self.assertEqual(len(rows), 2)

        expected_headers = service.get_field_names()
        for row in rows:
            for header in expected_headers:
                self.assertIn(header, row, f"Missing header: {header}")
                if header in ["SourceFileID", "SourceFileStatus"]:
                    self.assertTrue(len(row[header]) > 0, f"Empty critical field: {header}")

        csv_content = csv_string.lower()
        self.assertIn("test_csv_1.pdf", csv_content)
        self.assertIn("test_csv_2.pdf", csv_content)
        self.assertIn("excluded", csv_content)
        self.assertIn("research", csv_content)

        print(f"CSV Headers: {reader.fieldnames}")
        print(f"CSV Content: {csv_string[:500]}...")

    def test_json_report_structure_validation(self):
        """Test that JSON report generates valid structure with all required data."""
        doc, doc_data = self._create_document_with_data("test_json.pdf", "md5_json_test",
                                                        status=DocumentDataStatus.VERIFIED.name,
                                                        received_at=datetime(2024, 6, 1, tzinfo=timezone.utc),
                                                        source_file_num_pages=20, source_file_size=4096,
                                                        source_file_detected_language="fr", )

        package = Package.objects.create(packager=self.packager, data_pool=self.data_pool)
        PackageEntry.objects.create(package=package, document_data=doc_data, data_pool=self.data_pool,
                                    status_in_package=PackageEntryStatus.TRANSMISSION_SUCCESSFUL.name,
                                    filename_in_package="delivered_test.json",
                                    delivered_at=datetime(2024, 6, 15, tzinfo=timezone.utc), )

        service = CoCReportService(self.data_pool, self.start_date, self.end_date)
        json_data = service.generate_json_report()

        self.assertIsInstance(json_data, list)
        self.assertEqual(len(json_data), 1)

        entry = json_data[0]
        self.assertIsInstance(entry, dict)

        required_fields = ["ChainOfCustodyStartDate", "ChainOfCustodyEndDate", "SourceFileReceivedAt", "SourceFileID",
                           "SourceFileOriginalName", "SourceFileStatus", "DeliveredAt"]
        for field in required_fields:
            self.assertIn(field, entry, f"Missing required field: {field}")
            self.assertIsNotNone(entry[field], f"Null value for required field: {field}")

        self.assertEqual(entry["SourceFileOriginalName"], "test_json.pdf")
        self.assertEqual(entry["SourceFileStatus"], "delivered")
        self.assertEqual(entry["SourceFileNumPages"], 20)
        self.assertEqual(entry["SourceFileSize"], 4096)

    def test_comprehensive_coc_integration_with_full_lifecycle(self):
        """
        Integration test that creates a complete document lifecycle with duplicates,
        packages, and delivery to ensure all CoC fields are properly populated.
        """
        parent_doc, parent_doc_data = self._create_document_with_data("parent_document.pdf", "md5_parent_123",
                                                                      status=DocumentDataStatus.VERIFIED.name,
                                                                      received_at=datetime(2024, 6, 1, 10, 0, 0,
                                                                                           tzinfo=timezone.utc),
                                                                      source_file_num_pages=15, source_file_size=2048,
                                                                      source_file_detected_language="en",
                                                                      location_id="LOC_001", )
        parent_doc.inbound_path = "/inbound/parent_document.pdf"
        parent_doc.save()
        parent_doc_data.refresh_from_db()

        duplicate_doc, duplicate_doc_data = self._create_document_with_data(
            "duplicate_document.pdf",
            "md5_duplicate_456",
            status=DocumentDataStatus.VERIFIED.name,
            received_at=datetime(2024, 6, 2, 11, 0, 0,
                                 tzinfo=timezone.utc),
            source_file_num_pages=15,
            source_file_size=2048,
            source_file_detected_language="en",
            exclusion_reason="MD5_DUPLICATE",
            parent_file_id=parent_doc_data.source_file_id,
            relationship_to_parent="MD5_DUPLICATE",
            canonical_document_data=parent_doc_data,
            location_id="LOC_002")
        duplicate_doc.inbound_path = "/inbound/duplicate_document.pdf"
        duplicate_doc.save()
        duplicate_doc_data.refresh_from_db()

        package = Package.objects.create(packager=self.packager, data_pool=self.data_pool)

        parent_entry = PackageEntry.objects.create(package=package, document_data=parent_doc_data,
                                                   data_pool=self.data_pool,
                                                   status_in_package=PackageEntryStatus.TRANSMISSION_SUCCESSFUL.name,
                                                   filename_in_package="DOCDATA_parent_123.json",
                                                   delivered_at=datetime(2024, 6, 15, 12, 0, 0, tzinfo=timezone.utc), )

        duplicate_entry = PackageEntry.objects.create(package=package, document_data=duplicate_doc_data,
                                                      data_pool=self.data_pool,
                                                      status_in_package=PackageEntryStatus.EXCLUDED_AS_DUPLICATE.name,
                                                      filename_in_package="DOCDATA_duplicate_456.json",
                                                      delivered_at=datetime(2024, 6, 15, 12, 0, 0, tzinfo=timezone.utc),
                                                      exclusion_reason="MD5_DUPLICATE", )

        service = CoCReportService(self.data_pool, self.start_date, self.end_date)

        json_report = service.generate_json_report()
        self.assertEqual(len(json_report), 2)  # Parent + duplicate

        csv_report = service.generate_csv_report()
        self.assertTrue(len(csv_report) > 0)

        import csv
        import io
        csv_reader = csv.DictReader(io.StringIO(csv_report))
        csv_rows = list(csv_reader)
        self.assertEqual(len(csv_rows), 2)

        parent_json = next(entry for entry in json_report if entry["SourceFileID"] == parent_doc_data.source_file_id)
        duplicate_json = next(
            entry for entry in json_report if entry["SourceFileID"] == duplicate_doc_data.source_file_id)

        parent_csv = next(row for row in csv_rows if row["SourceFileID"] == parent_doc_data.source_file_id)
        duplicate_csv = next(row for row in csv_rows if row["SourceFileID"] == duplicate_doc_data.source_file_id)

        self._validate_complete_coc_entry(parent_json, parent_doc_data, parent_entry, is_duplicate=False)
        self._validate_complete_coc_csv_entry(parent_csv, parent_doc_data, parent_entry, is_duplicate=False)

        self._validate_complete_coc_entry(duplicate_json, duplicate_doc_data, duplicate_entry, is_duplicate=True)
        self._validate_complete_coc_csv_entry(duplicate_csv, duplicate_doc_data, duplicate_entry, is_duplicate=True)

    def _validate_complete_coc_entry(self, entry, doc_data, package_entry, is_duplicate=False):
        """Validate all fields in a CoC JSON entry are properly populated."""
        self.assertIsNotNone(entry.get("ChainOfCustodyStartDate"))
        self.assertIsNotNone(entry.get("ChainOfCustodyEndDate"))
        self.assertIsNotNone(entry.get("SourceFileReceivedAt"))
        self.assertIsNotNone(entry.get("SourceFileID"))
        self.assertIsNotNone(entry.get("SourceFileOriginalName"))
        self.assertIsNotNone(entry.get("SourceFileStatus"))
        self.assertIsNotNone(entry.get("DeliveredAt"))

        self.assertEqual(entry["SourceFileID"], doc_data.source_file_id)
        self.assertEqual(entry["SourceFileOriginalName"], doc_data.source_file_name)
        expected_path = doc_data.document.inbound_path or doc_data.document.label
        self.assertEqual(entry["SourceFileOriginalPath"], expected_path)
        self.assertEqual(entry["SourceFileFinalName"], package_entry.filename_in_package)
        self.assertEqual(entry["PackageID"], package_entry.package.obfuscated_id)
        self.assertEqual(entry["SourceFileNumPages"], doc_data.source_file_num_pages)
        self.assertEqual(entry["SourceFileDetectedLanguage"], doc_data.source_file_detected_language)
        self.assertEqual(entry["SourceFileSize"], doc_data.source_file_size)

        if is_duplicate:
            self.assertEqual(entry["SourceFileStatus"], "excluded")
            self.assertEqual(entry["ExclusionReason"], "duplicate")
            self.assertEqual(entry["RelationshipToParent"], "duplicate")
            self.assertIsNotNone(entry["ParentFileID"])
        else:
            self.assertEqual(entry["SourceFileStatus"], "delivered")
            self.assertIsNone(entry.get("ExclusionReason"))
            self.assertIsNone(entry.get("RelationshipToParent"))
            self.assertIsNone(entry.get("ParentFileID"))

    def _validate_complete_coc_csv_entry(self, row, doc_data, package_entry, is_duplicate=False):
        """Validate all fields in a CoC CSV row are properly populated."""
        self.assertTrue(len(row.get("ChainOfCustodyStartDate", "")) > 0)
        self.assertTrue(len(row.get("ChainOfCustodyEndDate", "")) > 0)
        self.assertTrue(len(row.get("SourceFileReceivedAt", "")) > 0)
        self.assertTrue(len(row.get("SourceFileID", "")) > 0)
        self.assertTrue(len(row.get("SourceFileOriginalName", "")) > 0)
        self.assertTrue(len(row.get("SourceFileStatus", "")) > 0)
        self.assertTrue(len(row.get("DeliveredAt", "")) > 0)

        self.assertEqual(row["SourceFileID"], doc_data.source_file_id)
        self.assertEqual(row["SourceFileOriginalName"], doc_data.source_file_name)
        expected_path = doc_data.document.inbound_path or doc_data.document.label
        self.assertEqual(row["SourceFileOriginalPath"], expected_path)
        self.assertEqual(row["SourceFileFinalName"], package_entry.filename_in_package)
        self.assertEqual(row["PackageID"], package_entry.package.obfuscated_id)
        self.assertEqual(row["SourceFileNumPages"], str(doc_data.source_file_num_pages))
        self.assertEqual(row["SourceFileDetectedLanguage"], doc_data.source_file_detected_language)
        self.assertEqual(row["SourceFileSize"], str(doc_data.source_file_size))

        if is_duplicate:
            self.assertEqual(row["SourceFileStatus"], "excluded")
            self.assertEqual(row["ExclusionReason"], "duplicate")
            self.assertEqual(row["RelationshipToParent"], "duplicate")
            self.assertTrue(len(row.get("ParentFileID", "")) > 0)
        else:
            self.assertEqual(row["SourceFileStatus"], "delivered")
            self.assertEqual(row.get("ExclusionReason", ""), "")
            self.assertEqual(row.get("RelationshipToParent", ""), "")
            self.assertEqual(row.get("ParentFileID", ""), "")

    def test_schema_compliance_validation(self):
        """Test that CoC reports are fully compliant with glynt-schemas GtdrChainOfCustodyCsv."""
        from packager.models import get_default_coc_report_fields

        doc, doc_data = self._create_document_with_data("schema_test.pdf", "md5_schema_test",
                                                        status=DocumentDataStatus.VERIFIED.name,
                                                        received_at=datetime(2024, 6, 1, 10, 0, 0, tzinfo=timezone.utc),
                                                        source_file_num_pages=25, source_file_size=3072,
                                                        source_file_detected_language="de", )
        doc.inbound_path = "/inbound/schema_test.pdf"
        doc.save()

        package = Package.objects.create(packager=self.packager, data_pool=self.data_pool)
        PackageEntry.objects.create(package=package, document_data=doc_data, data_pool=self.data_pool,
                                    status_in_package=PackageEntryStatus.TRANSMISSION_SUCCESSFUL.name,
                                    filename_in_package="DOCDATA_schema_test.json",
                                    delivered_at=datetime(2024, 6, 15, 14, 30, 0, tzinfo=timezone.utc), )

        service = CoCReportService(self.data_pool, self.start_date, self.end_date)
        json_report = service.generate_json_report()
        csv_report = service.generate_csv_report()

        schema_fields = get_default_coc_report_fields()
        service_fields = service.get_field_names()

        self.assertEqual(set(schema_fields), set(service_fields),
                         f"Service fields don't match schema. Missing: {set(schema_fields) - set(service_fields)}, "
                         f"Extra: {set(service_fields) - set(schema_fields)}")

        csv_reader = csv.DictReader(io.StringIO(csv_report))
        csv_headers = csv_reader.fieldnames
        csv_rows = list(csv_reader)

        self.assertEqual(set(schema_fields), set(csv_headers), "CSV headers don't match schema fields")

        json_entry = json_report[0]
        self.assertEqual(set(schema_fields), set(json_entry.keys()), "JSON fields don't match schema fields")

        csv_row = csv_rows[0]
        for field in schema_fields:
            if field in ["ChainOfCustodyStartDate", "ChainOfCustodyEndDate", "SourceFileReceivedAt", "SourceFileID",
                         "SourceFileOriginalName", "SourceFileStatus", "PackageID", "DeliveredAt"]:
                self.assertTrue(len(csv_row.get(field, "")) > 0, f"Required field {field} is empty in CSV")
                self.assertIsNotNone(json_entry.get(field), f"Required field {field} is null in JSON")

    def test_field_population_completeness(self):
        """Test that all available data is properly mapped to CoC fields."""
        doc, doc_data = self._create_document_with_data("complete_test.pdf", "md5_complete_test",
                                                        status=DocumentDataStatus.VERIFIED.name,
                                                        received_at=datetime(2024, 6, 1, 10, 0, 0, tzinfo=timezone.utc),
                                                        source_file_num_pages=30, source_file_size=4096,
                                                        source_file_detected_language="en",
                                                        exclusion_reason="MD5_DUPLICATE",
                                                        parent_file_id="parent_file_123",
                                                        relationship_to_parent="MD5_DUPLICATE", )
        doc.inbound_path = "/inbound/complete_test.pdf"
        doc.save()

        package = Package.objects.create(packager=self.packager, data_pool=self.data_pool)
        package_entry = PackageEntry.objects.create(package=package, document_data=doc_data, data_pool=self.data_pool,
                                                    status_in_package=PackageEntryStatus.EXCLUDED_AS_DUPLICATE.name,
                                                    filename_in_package="DOCDATA_complete_test.json",
                                                    delivered_at=datetime(2024, 6, 15, 14, 30, 0, tzinfo=timezone.utc),
                                                    exclusion_reason="MD5_DUPLICATE", )

        service = CoCReportService(self.data_pool, self.start_date, self.end_date)
        json_report = service.generate_json_report()

        entry = json_report[0]

        self.assertEqual(entry["SourceFileID"], doc_data.source_file_id)
        self.assertEqual(entry["SourceFileOriginalName"], doc_data.source_file_name)
        self.assertEqual(entry["SourceFileOriginalPath"], doc.inbound_path)
        self.assertEqual(entry["SourceFileFinalName"], package_entry.filename_in_package)
        self.assertEqual(entry["SourceFileStatus"], "excluded")  # Mapped from package entry status
        self.assertEqual(entry["ExclusionReason"], "duplicate")  # Mapped from exclusion reason
        self.assertEqual(entry["ParentFileID"], doc_data.parent_file_id)
        self.assertEqual(entry["RelationshipToParent"], "duplicate")  # Mapped from relationship
        self.assertEqual(entry["PackageID"], package.obfuscated_id)
        self.assertEqual(entry["SourceFileNumPages"], doc_data.source_file_num_pages)
        self.assertEqual(entry["SourceFileDetectedLanguage"], doc_data.source_file_detected_language)
        self.assertEqual(entry["SourceFileSize"], doc_data.source_file_size)

        self.assertEqual(entry["ChainOfCustodyStartDate"], self.start_date.isoformat())
        self.assertEqual(entry["ChainOfCustodyEndDate"], self.end_date.isoformat())
        self.assertIsNotNone(entry["SourceFileReceivedAt"])
        self.assertIsNotNone(entry["DeliveredAt"])

    def test_comprehensive_coc_integration_end_to_end(self):
        """
        End-to-end integration test that simulates a complete CoC workflow:
        1. Document creation and processing
        2. Duplicate detection and relationships
        3. Package creation and delivery
        4. CoC report generation with full field validation
        """
        original_doc, original_doc_data = self._create_document_with_data("original_bill.pdf", "md5_original_123",
                                                                          status=DocumentDataStatus.VERIFIED.name,
                                                                          received_at=datetime(2024, 6, 1, 9, 0, 0,
                                                                                               tzinfo=timezone.utc),
                                                                          source_file_num_pages=12,
                                                                          source_file_size=2048,
                                                                          source_file_detected_language="en", )
        original_doc.inbound_path = "/customer_uploads/original_bill.pdf"
        original_doc.save()
        original_doc_data.refresh_from_db()

        duplicate_doc, duplicate_doc_data = self._create_document_with_data(
            "duplicate_bill.pdf",
            "md5_duplicate_456",
            status=DocumentDataStatus.VERIFIED.name,
            received_at=datetime(2024, 6, 1, 10, 30, 0, tzinfo=timezone.utc),
            source_file_num_pages=12,
            source_file_size=2048,
            source_file_detected_language="en",
            exclusion_reason="MD5_DUPLICATE",
            parent_file_id=original_doc_data.source_file_id,
            relationship_to_parent="MD5_DUPLICATE",
            canonical_document_data=original_doc_data)

        duplicate_doc.inbound_path = "/customer_uploads/duplicate_bill.pdf"
        duplicate_doc.save()
        duplicate_doc_data.refresh_from_db()

        standalone_doc, standalone_doc_data = self._create_document_with_data(
            "standalone_doc.pdf",
            "md5_standalone_789",
            status=DocumentDataStatus.PENDING_REVIEW.name,
            received_at=datetime(2024, 6, 2, 14, 0, 0, tzinfo=timezone.utc),
            source_file_num_pages=5,
            source_file_size=1024,
            source_file_detected_language="es")
        standalone_doc.inbound_path = "/customer_uploads/standalone_doc.pdf"
        standalone_doc.save()
        standalone_doc_data.refresh_from_db()

        package = Package.objects.create(packager=self.packager, data_pool=self.data_pool)

        original_entry = PackageEntry.objects.create(package=package, document_data=original_doc_data,
                                                     data_pool=self.data_pool,
                                                     status_in_package=PackageEntryStatus.TRANSMISSION_SUCCESSFUL.name,
                                                     filename_in_package="DOCDATA_original_bill_processed.json",
                                                     delivered_at=datetime(2024, 6, 10, 15, 30, 0,
                                                                           tzinfo=timezone.utc), )

        duplicate_entry = PackageEntry.objects.create(package=package, document_data=duplicate_doc_data,
                                                      data_pool=self.data_pool,
                                                      status_in_package=PackageEntryStatus.EXCLUDED_AS_DUPLICATE.name,
                                                      filename_in_package="DOCDATA_duplicate_bill_excluded.json",
                                                      delivered_at=datetime(2024, 6, 10, 15, 30, 0,
                                                                            tzinfo=timezone.utc),
                                                      exclusion_reason="MD5_DUPLICATE", )

        service = CoCReportService(self.data_pool, self.start_date, self.end_date)

        json_report = service.generate_json_report()
        csv_report = service.generate_csv_report()

        self.assertEqual(len(json_report), 3)

        import csv
        import io
        csv_reader = csv.DictReader(io.StringIO(csv_report))
        csv_rows = list(csv_reader)
        self.assertEqual(len(csv_rows), 3)

        original_json = next(e for e in json_report if e["SourceFileID"] == original_doc_data.source_file_id)
        duplicate_json = next(e for e in json_report if e["SourceFileID"] == duplicate_doc_data.source_file_id)
        standalone_json = next(e for e in json_report if e["SourceFileID"] == standalone_doc_data.source_file_id)

        original_csv = next(r for r in csv_rows if r["SourceFileID"] == original_doc_data.source_file_id)
        duplicate_csv = next(r for r in csv_rows if r["SourceFileID"] == duplicate_doc_data.source_file_id)
        standalone_csv = next(r for r in csv_rows if r["SourceFileID"] == standalone_doc_data.source_file_id)

        self._validate_coc_entry_comprehensive(original_json, original_doc_data, original_entry,
                                               expected_status="delivered", is_duplicate=False)
        self._validate_coc_csv_comprehensive(original_csv, original_doc_data, original_entry,
                                             expected_status="delivered", is_duplicate=False)

        self._validate_coc_entry_comprehensive(duplicate_json, duplicate_doc_data, duplicate_entry,
                                               expected_status="excluded", is_duplicate=True)
        self._validate_coc_csv_comprehensive(duplicate_csv, duplicate_doc_data, duplicate_entry,
                                             expected_status="excluded", is_duplicate=True)

        self._validate_coc_entry_comprehensive(standalone_json, standalone_doc_data, None, expected_status="research",
                                               is_duplicate=False)
        self._validate_coc_csv_comprehensive(standalone_csv, standalone_doc_data, None, expected_status="research",
                                             is_duplicate=False)

        for json_entry, csv_row in zip(json_report, csv_rows):
            self._validate_json_csv_consistency(json_entry, csv_row)

    def _validate_coc_entry_comprehensive(self, entry, doc_data, package_entry, expected_status, is_duplicate):
        """Comprehensive validation of a single CoC JSON entry."""
        self.assertEqual(entry["SourceFileID"], doc_data.source_file_id)
        self.assertEqual(entry["SourceFileOriginalName"], doc_data.source_file_name)
        expected_path = doc_data.document.inbound_path or doc_data.document.label
        self.assertEqual(entry["SourceFileOriginalPath"], expected_path)

        self.assertEqual(entry["SourceFileStatus"], expected_status)

        if package_entry:
            self.assertEqual(entry["SourceFileFinalName"], package_entry.filename_in_package)
            self.assertEqual(entry["PackageID"], package_entry.package.obfuscated_id)
            self.assertIsNotNone(entry["DeliveredAt"])
        else:
            self.assertEqual(entry["SourceFileFinalName"], "")
            self.assertEqual(entry["PackageID"], "")
            self.assertIsNotNone(entry["DeliveredAt"])

        if is_duplicate:
            self.assertEqual(entry["ExclusionReason"], "duplicate")
            self.assertEqual(entry["RelationshipToParent"], "duplicate")
            self.assertIsNotNone(entry["ParentFileID"])
        else:
            self.assertIsNone(entry.get("ExclusionReason"))
            self.assertIsNone(entry.get("RelationshipToParent"))
            self.assertIsNone(entry.get("ParentFileID"))

        self.assertEqual(entry["SourceFileNumPages"], doc_data.source_file_num_pages)
        self.assertEqual(entry["SourceFileDetectedLanguage"], doc_data.source_file_detected_language)
        self.assertEqual(entry["SourceFileSize"], doc_data.source_file_size)

        self.assertEqual(entry["ChainOfCustodyStartDate"], self.start_date.isoformat())
        self.assertEqual(entry["ChainOfCustodyEndDate"], self.end_date.isoformat())
        self.assertIsNotNone(entry["SourceFileReceivedAt"])

    def _validate_coc_csv_comprehensive(self, row, doc_data, package_entry, expected_status, is_duplicate):
        """Comprehensive validation of a single CoC CSV row."""
        self.assertEqual(row["SourceFileID"], doc_data.source_file_id)
        self.assertEqual(row["SourceFileOriginalName"], doc_data.source_file_name)
        expected_path = doc_data.document.inbound_path or doc_data.document.label
        self.assertEqual(row["SourceFileOriginalPath"], expected_path)

        self.assertEqual(row["SourceFileStatus"], expected_status)

        if package_entry:
            self.assertEqual(row["SourceFileFinalName"], package_entry.filename_in_package)
            self.assertEqual(row["PackageID"], package_entry.package.obfuscated_id)
            self.assertTrue(len(row["DeliveredAt"]) > 0)
        else:
            self.assertEqual(row["SourceFileFinalName"], "")
            self.assertEqual(row["PackageID"], "")
            self.assertTrue(len(row["DeliveredAt"]) > 0)  # Default timestamp

        if is_duplicate:
            self.assertEqual(row["ExclusionReason"], "duplicate")
            self.assertEqual(row["RelationshipToParent"], "duplicate")
            self.assertTrue(len(row["ParentFileID"]) > 0)
        else:
            self.assertEqual(row.get("ExclusionReason", ""), "")
            self.assertEqual(row.get("RelationshipToParent", ""), "")
            self.assertEqual(row.get("ParentFileID", ""), "")

        self.assertEqual(row["SourceFileNumPages"], str(doc_data.source_file_num_pages))
        self.assertEqual(row["SourceFileDetectedLanguage"], doc_data.source_file_detected_language or "")
        self.assertEqual(row["SourceFileSize"], str(doc_data.source_file_size))

        self.assertTrue(len(row["ChainOfCustodyStartDate"]) > 0)
        self.assertTrue(len(row["ChainOfCustodyEndDate"]) > 0)
        self.assertTrue(len(row["SourceFileReceivedAt"]) > 0)

    def _validate_json_csv_consistency(self, json_entry, csv_row):
        """Validate that JSON and CSV entries have consistent data."""
        consistent_fields = ["SourceFileID", "SourceFileOriginalName", "SourceFileOriginalPath", "SourceFileFinalName",
                             "SourceFileStatus", "PackageID"]

        for field in consistent_fields:
            json_val = str(json_entry.get(field, "")) if json_entry.get(field) is not None else ""
            csv_val = str(csv_row.get(field, ""))
            self.assertEqual(json_val, csv_val, f"Inconsistent {field}: JSON='{json_val}', CSV='{csv_val}'")

    def test_empty_report_validation(self):
        """Test that empty reports are handled correctly."""
        future_start = date(2025, 1, 1)
        future_end = date(2025, 12, 31)
        service = CoCReportService(self.data_pool, future_start, future_end)

        csv_string = service.generate_csv_report()
        self.assertEqual(csv_string, "")

        json_data = service.generate_json_report()
        self.assertIsInstance(json_data, list)
        self.assertEqual(len(json_data), 0)

        report_data = service.generate_report_data()
        self.assertIsInstance(report_data, list)
        self.assertEqual(len(report_data), 0)

    def test_report_field_mapping_accuracy(self):
        """Test that all field mappings are accurate and complete."""
        doc, doc_data = self._create_document_with_data("mapping_test.pdf", "md5_mapping_test",
                                                        status=DocumentDataStatus.VERIFIED.name,
                                                        received_at=datetime(2024, 6, 1, 12, 30, 45,
                                                                             tzinfo=timezone.utc),
                                                        source_file_num_pages=25, source_file_size=8192,
                                                        source_file_detected_language="de",
                                                        exclusion_reason="CONTENT_DUPLICATE",
                                                        parent_file_id="parent_mapping_test",
                                                        relationship_to_parent="CONTENT_DUPLICATE", )
        doc.inbound_path = "/inbound/mapping_test.pdf"
        doc.save()

        doc_data.refresh_from_db()

        package = Package.objects.create(packager=self.packager, data_pool=self.data_pool)
        package_entry = PackageEntry.objects.create(package=package, document_data=doc_data, data_pool=self.data_pool,
                                                    status_in_package=PackageEntryStatus.EXCLUDED_AS_DUPLICATE.name,
                                                    filename_in_package="final_mapping_test.json",
                                                    delivered_at=datetime(2024, 6, 15, 16, 45, 30, tzinfo=timezone.utc),
                                                    exclusion_reason="CONTENT_DUPLICATE", )

        service = CoCReportService(self.data_pool, self.start_date, self.end_date)
        report_entry = service.create_report_entry(doc_data, package_entry)

        self.assertEqual(report_entry.source_file_id, doc_data.source_file_id)
        self.assertEqual(report_entry.source_file_original_name, doc_data.source_file_name)

        expected_path = doc.inbound_path or doc.label
        self.assertEqual(report_entry.source_file_original_path, expected_path)

        self.assertEqual(report_entry.source_file_final_name, "final_mapping_test.json")
        self.assertEqual(report_entry.source_file_status, SourceFileStatus.EXCLUDED)
        self.assertEqual(report_entry.exclusion_reason, ExclusionReason.CONTENT_DUPLICATE)
        self.assertEqual(report_entry.parent_file_id, "parent_mapping_test")
        self.assertEqual(report_entry.relationship_to_parent, ParentRelationship.DUPLICATE)
        self.assertEqual(report_entry.package_id, package.obfuscated_id)
        self.assertEqual(report_entry.delivered_at, package_entry.delivered_at)
        self.assertEqual(report_entry.num_pages, 25)
        self.assertEqual(report_entry.detected_language, "de")
        self.assertEqual(report_entry.size, 8192)
        self.assertEqual(report_entry.source_file_received_at, doc_data.received_at)

    def test_comprehensive_field_population_integration(self):
        """
        Comprehensive integration test to validate all CoC fields are properly populated.
        This test addresses the issues found in sandbox where many fields were empty.
        """
        doc, doc_data = self._create_document_with_data("comprehensive_test.pdf", "md5_comprehensive_001",
                                                        status=DocumentDataStatus.VERIFIED.name,
                                                        received_at=datetime(2024, 6, 1, 12, 30, 45,
                                                                             tzinfo=timezone.utc),
                                                        source_file_num_pages=25, source_file_size=8192000,  # 8MB
                                                        source_file_detected_language="en", )

        doc.inbound_path = "/inbound/comprehensive_test.pdf"
        doc.page_count = 25
        doc.filesize = 8192000
        doc.language = "en"
        doc.save()

        doc_data.refresh_from_db()

        package = Package.objects.create(packager=self.packager, data_pool=self.data_pool)
        package_entry = PackageEntry.objects.create(package=package, document_data=doc_data, data_pool=self.data_pool,
                                                    status_in_package=PackageEntryStatus.TRANSMISSION_SUCCESSFUL.name,
                                                    filename_in_package="comprehensive_test_final.json",
                                                    delivered_at=datetime(2024, 6, 15, 16, 45, 30,
                                                                          tzinfo=timezone.utc), )

        service = CoCReportService(self.data_pool, self.start_date, self.end_date, self.packager)

        json_report = service.generate_json_report()
        self.assertEqual(len(json_report), 1)
        json_entry = json_report[0]

        csv_report = service.generate_csv_report()
        self.assertTrue(len(csv_report) > 0)

        csv_reader = csv.DictReader(io.StringIO(csv_report))
        csv_rows = list(csv_reader)
        self.assertEqual(len(csv_rows), 1)
        csv_entry = csv_rows[0]

        self._validate_json_field_population(json_entry, doc_data, package_entry)

        self._validate_csv_field_population(csv_entry, doc_data, package_entry)

        self._validate_json_csv_field_consistency(json_entry, csv_entry)

    def _validate_json_field_population(self, json_entry, doc_data, package_entry):
        """Validate that all required fields are properly populated in JSON."""
        self.assertIsNotNone(json_entry.get("ChainOfCustodyStartDate"))
        self.assertIsNotNone(json_entry.get("ChainOfCustodyEndDate"))
        self.assertIsNotNone(json_entry.get("SourceFileReceivedAt"))
        self.assertIsNotNone(json_entry.get("DeliveredAt"))

        self.assertIsNotNone(json_entry.get("SourceFileID"))
        self.assertNotEqual(json_entry.get("SourceFileID"), "")
        self.assertIsNotNone(json_entry.get("SourceFileOriginalName"))
        self.assertNotEqual(json_entry.get("SourceFileOriginalName"), "")
        self.assertIsNotNone(json_entry.get("SourceFileOriginalPath"))
        self.assertNotEqual(json_entry.get("SourceFileOriginalPath"), "")

        self.assertIsNotNone(json_entry.get("SourceFileNumPages"))
        self.assertGreater(json_entry.get("SourceFileNumPages"), 0)
        self.assertIsNotNone(json_entry.get("SourceFileSize"))
        self.assertGreater(json_entry.get("SourceFileSize"), 0)
        self.assertIsNotNone(json_entry.get("SourceFileDetectedLanguage"))

        self.assertIsNotNone(json_entry.get("SourceFileStatus"))
        self.assertEqual(json_entry.get("SourceFileStatus"), "delivered")

        self.assertIsNotNone(json_entry.get("PackageID"))
        self.assertNotEqual(json_entry.get("PackageID"), "")
        self.assertIsNotNone(json_entry.get("SourceFileFinalName"))
        self.assertNotEqual(json_entry.get("SourceFileFinalName"), "")

        self.assertEqual(json_entry.get("SourceFileOriginalName"), doc_data.source_file_name)
        self.assertEqual(json_entry.get("SourceFileNumPages"), doc_data.source_file_num_pages)
        self.assertEqual(json_entry.get("SourceFileSize"), doc_data.source_file_size)
        self.assertEqual(json_entry.get("SourceFileDetectedLanguage"), doc_data.source_file_detected_language)
        self.assertEqual(json_entry.get("PackageID"), package_entry.package.obfuscated_id)
        self.assertEqual(json_entry.get("SourceFileFinalName"), package_entry.filename_in_package)

    def _validate_csv_field_population(self, csv_entry, doc_data, package_entry):
        """Validate that all required fields are properly populated in CSV."""
        self.assertNotEqual(csv_entry.get("ChainOfCustodyStartDate", ""), "")
        self.assertNotEqual(csv_entry.get("ChainOfCustodyEndDate", ""), "")
        self.assertNotEqual(csv_entry.get("SourceFileReceivedAt", ""), "")
        self.assertNotEqual(csv_entry.get("DeliveredAt", ""), "")

        self.assertNotEqual(csv_entry.get("SourceFileID", ""), "")
        self.assertNotEqual(csv_entry.get("SourceFileOriginalName", ""), "")
        self.assertNotEqual(csv_entry.get("SourceFileOriginalPath", ""), "")

        self.assertNotEqual(csv_entry.get("SourceFileNumPages", ""), "")
        self.assertNotEqual(csv_entry.get("SourceFileSize", ""), "")
        self.assertNotEqual(csv_entry.get("SourceFileDetectedLanguage", ""), "")

        self.assertNotEqual(csv_entry.get("SourceFileStatus", ""), "")
        self.assertEqual(csv_entry.get("SourceFileStatus"), "delivered")

        self.assertNotEqual(csv_entry.get("PackageID", ""), "")
        self.assertNotEqual(csv_entry.get("SourceFileFinalName", ""), "")

    def _validate_json_csv_field_consistency(self, json_entry, csv_entry):
        """Validate that JSON and CSV have consistent field values."""
        consistent_fields = ["SourceFileID", "SourceFileOriginalName", "SourceFileOriginalPath", "SourceFileFinalName",
                             "SourceFileStatus", "PackageID", "SourceFileNumPages", "SourceFileSize",
                             "SourceFileDetectedLanguage", ]

        for field in consistent_fields:
            json_value = str(json_entry.get(field, "")) if json_entry.get(field) is not None else ""
            csv_value = str(csv_entry.get(field, ""))
            self.assertEqual(json_value, csv_value,
                             f"Field {field} inconsistent: JSON='{json_value}', CSV='{csv_value}'")

    def test_complete_coc_workflow_validation(self):
        """Test complete CoC workflow with multiple document types and validate all report outputs."""
        test_documents = []

        doc1, doc_data1 = self._create_document_with_data("delivered_doc.pdf", "md5_delivered_workflow",
                                                          status=DocumentDataStatus.VERIFIED.name,
                                                          received_at=datetime(2024, 6, 1, tzinfo=timezone.utc),
                                                          source_file_num_pages=10, source_file_size=1024,
                                                          source_file_detected_language="en", )
        doc1.inbound_path = "/inbound/delivered_doc.pdf"
        doc1.save()

        package1 = Package.objects.create(packager=self.packager, data_pool=self.data_pool)
        entry1 = PackageEntry.objects.create(package=package1, document_data=doc_data1, data_pool=self.data_pool,
                                             status_in_package=PackageEntryStatus.TRANSMISSION_SUCCESSFUL.name,
                                             filename_in_package="delivered_final.json",
                                             delivered_at=datetime(2024, 6, 15, tzinfo=timezone.utc), )
        test_documents.append(("delivered", doc1, doc_data1, entry1))

        doc2, doc_data2 = self._create_document_with_data("md5_duplicate.pdf", "md5_duplicate_workflow",
                                                          status=DocumentDataStatus.VERIFIED.name,
                                                          received_at=datetime(2024, 6, 2, tzinfo=timezone.utc),
                                                          source_file_num_pages=5, source_file_size=512,
                                                          exclusion_reason="MD5_DUPLICATE",
                                                          parent_file_id="original_doc_id",
                                                          relationship_to_parent="MD5_DUPLICATE", )
        doc2.inbound_path = "/inbound/md5_duplicate.pdf"
        doc2.save()

        package2 = Package.objects.create(packager=self.packager, data_pool=self.data_pool)
        entry2 = PackageEntry.objects.create(package=package2, document_data=doc_data2, data_pool=self.data_pool,
                                             status_in_package=PackageEntryStatus.EXCLUDED_AS_DUPLICATE.name,
                                             filename_in_package="",
                                             delivered_at=datetime(2024, 6, 15, tzinfo=timezone.utc),
                                             exclusion_reason="MD5_DUPLICATE", )
        test_documents.append(("md5_duplicate", doc2, doc_data2, entry2))

        doc3, doc_data3 = self._create_document_with_data("content_duplicate.pdf", "md5_content_workflow",
                                                          status=DocumentDataStatus.VERIFIED.name,
                                                          received_at=datetime(2024, 6, 3, tzinfo=timezone.utc),
                                                          source_file_num_pages=8, source_file_size=768,
                                                          exclusion_reason="CONTENT_DUPLICATE",
                                                          parent_file_id="content_parent_id",
                                                          relationship_to_parent="CONTENT_DUPLICATE", )
        doc3.inbound_path = "/inbound/content_duplicate.pdf"
        doc3.save()

        package3 = Package.objects.create(packager=self.packager, data_pool=self.data_pool)
        entry3 = PackageEntry.objects.create(package=package3, document_data=doc_data3, data_pool=self.data_pool,
                                             status_in_package=PackageEntryStatus.EXCLUDED_AS_DUPLICATE.name,
                                             filename_in_package="",
                                             delivered_at=datetime(2024, 6, 15, tzinfo=timezone.utc),
                                             exclusion_reason="CONTENT_DUPLICATE", )
        test_documents.append(("content_duplicate", doc3, doc_data3, entry3))

        doc4, doc_data4 = self._create_document_with_data("failed_transmission.pdf", "md5_failed_workflow",
                                                          status=DocumentDataStatus.VERIFIED.name,
                                                          received_at=datetime(2024, 6, 4, tzinfo=timezone.utc),
                                                          source_file_num_pages=12, source_file_size=1536, )
        doc4.inbound_path = "/inbound/failed_transmission.pdf"
        doc4.save()

        package4 = Package.objects.create(packager=self.packager, data_pool=self.data_pool)
        entry4 = PackageEntry.objects.create(package=package4, document_data=doc_data4, data_pool=self.data_pool,
                                             status_in_package=PackageEntryStatus.TRANSMISSION_FAILED.name,
                                             filename_in_package="failed_final.json",
                                             delivered_at=datetime(2024, 6, 15, tzinfo=timezone.utc), )
        test_documents.append(("failed", doc4, doc_data4, entry4))

        doc5, doc_data5 = self._create_document_with_data("research_document.pdf", "md5_research_workflow",
                                                          status=DocumentDataStatus.PENDING_REVIEW.name,
                                                          received_at=datetime(2024, 6, 5, tzinfo=timezone.utc),
                                                          source_file_num_pages=15, source_file_size=2048, )
        doc5.inbound_path = "/inbound/research_document.pdf"
        doc5.save()
        test_documents.append(("research", doc5, doc_data5, None))

        service = CoCReportService(self.data_pool, self.start_date, self.end_date)

        report_data = service.generate_report_data()
        self.assertEqual(len(report_data), 5, "Should have 5 report entries")

        for entry in report_data:
            self.assertIsInstance(entry, GtdrChainOfCustodyCsv)
            self.assertIsNotNone(entry.source_file_id)
            self.assertIsNotNone(entry.source_file_original_name)
            self.assertIsNotNone(entry.source_file_status)
            self.assertIsNotNone(entry.delivered_at)

        csv_report = service.generate_csv_report()
        self.assertIsInstance(csv_report, str)
        self.assertTrue(len(csv_report) > 0)

        csv_reader = csv.DictReader(io.StringIO(csv_report))
        csv_rows = list(csv_reader)
        self.assertEqual(len(csv_rows), 5)

        expected_headers = service.get_field_names()
        for row in csv_rows:
            for header in expected_headers:
                self.assertIn(header, row)

        json_report = service.generate_json_report()
        self.assertIsInstance(json_report, list)
        self.assertEqual(len(json_report), 5)

        for json_entry in json_report:
            self.assertIsInstance(json_entry, dict)
            self.assertIn("SourceFileID", json_entry)
            self.assertIn("SourceFileOriginalName", json_entry)
            self.assertIn("SourceFileStatus", json_entry)

        status_mapping_tests = {"delivered_doc.pdf": "delivered", "md5_duplicate.pdf": "excluded",
                                "content_duplicate.pdf": "excluded", "failed_transmission.pdf": "pending",
                                "research_document.pdf": "research", }

        for json_entry in json_report:
            filename = json_entry["SourceFileOriginalName"]
            if filename in status_mapping_tests:
                expected_status = status_mapping_tests[filename]
                self.assertEqual(json_entry["SourceFileStatus"], expected_status, f"Wrong status for {filename}")

        exclusion_mapping_tests = {"md5_duplicate.pdf": ExclusionReason.DUPLICATE,
                                   "content_duplicate.pdf": ExclusionReason.CONTENT_DUPLICATE, }

        for json_entry in json_report:
            filename = json_entry["SourceFileOriginalName"]
            if filename in exclusion_mapping_tests:
                expected_exclusion = exclusion_mapping_tests[filename]
                actual_exclusion = json_entry["ExclusionReason"]

                if hasattr(expected_exclusion, 'value'):
                    expected_value = expected_exclusion.value
                else:
                    expected_value = expected_exclusion

                if hasattr(actual_exclusion, 'value'):
                    actual_value = actual_exclusion.value
                else:
                    actual_value = actual_exclusion

                self.assertEqual(actual_value, expected_value, f"Wrong exclusion reason for {filename}")

        for json_entry in json_report:
            filename = json_entry["SourceFileOriginalName"]
            if filename in ["md5_duplicate.pdf", "content_duplicate.pdf"]:
                actual_relationship = json_entry["RelationshipToParent"]
                expected_relationship = ParentRelationship.DUPLICATE

                if hasattr(expected_relationship, 'value'):
                    expected_value = expected_relationship.value
                else:
                    expected_value = expected_relationship

                if hasattr(actual_relationship, 'value'):
                    actual_value = actual_relationship.value
                else:
                    actual_value = actual_relationship

                self.assertEqual(actual_value, expected_value)
                self.assertIsNotNone(json_entry["ParentFileID"])
                self.assertTrue(len(json_entry["ParentFileID"]) > 0)

        first_entry = json_report[0]
        actual_start_date = first_entry["ChainOfCustodyStartDate"]
        actual_end_date = first_entry["ChainOfCustodyEndDate"]

        if isinstance(actual_start_date, str):
            self.assertEqual(actual_start_date, self.start_date.isoformat())
        else:
            self.assertEqual(actual_start_date, self.start_date)

        if isinstance(actual_end_date, str):
            self.assertEqual(actual_end_date, self.end_date.isoformat())
        else:
            self.assertEqual(actual_end_date, self.end_date)

    def test_api_ready_coc_report_completeness(self):
        """
        Test that ensures CoC reports are API-ready with complete field population.
        This test simulates the exact scenario a user would encounter when downloading
        CoC reports via the API - ensuring no surprises with missing or inconsistent data.
        """
        original_doc, original_doc_data = self._create_document_with_data("invoice_001.pdf", "md5_invoice_001",
                                                                          status=DocumentDataStatus.VERIFIED.name,
                                                                          received_at=datetime(2024, 6, 1, 9, 0, 0,
                                                                                               tzinfo=timezone.utc),
                                                                          source_file_num_pages=3,
                                                                          source_file_size=1024000,  # 1MB
                                                                          source_file_detected_language="en", )
        original_doc.inbound_path = "/uploads/invoices/invoice_001.pdf"
        original_doc.save()
        original_doc_data.refresh_from_db()

        duplicate_doc, duplicate_doc_data = self._create_document_with_data(
            "invoice_001_copy.pdf",
            "md5_invoice_001_dup",
            status=DocumentDataStatus.VERIFIED.name,
            received_at=datetime(2024, 6, 1, 10, 30, 0,
                                 tzinfo=timezone.utc),
            source_file_num_pages=3,
            source_file_size=1024000,
            source_file_detected_language="en",
            exclusion_reason="MD5_DUPLICATE",
            parent_file_id=original_doc_data.source_file_id,
            relationship_to_parent="MD5_DUPLICATE",
            canonical_document_data=original_doc_data)
        duplicate_doc.inbound_path = "/uploads/invoices/invoice_001_copy.pdf"
        duplicate_doc.save()
        duplicate_doc_data.refresh_from_db()

        package = Package.objects.create(packager=self.packager, data_pool=self.data_pool)

        PackageEntry.objects.create(package=package, document_data=original_doc_data, data_pool=self.data_pool,
                                    status_in_package=PackageEntryStatus.TRANSMISSION_SUCCESSFUL.name,
                                    filename_in_package="DOCDATA_invoice_001_final.json",
                                    delivered_at=datetime(2024, 6, 10, 15, 30, 0, tzinfo=timezone.utc), )

        PackageEntry.objects.create(package=package, document_data=duplicate_doc_data, data_pool=self.data_pool,
                                    status_in_package=PackageEntryStatus.EXCLUDED_AS_DUPLICATE.name,
                                    filename_in_package="",
                                    delivered_at=datetime(2024, 6, 10, 15, 30, 0, tzinfo=timezone.utc),
                                    exclusion_reason="MD5_DUPLICATE", )

        service = CoCReportService(self.data_pool, self.start_date, self.end_date)
        json_report = service.generate_json_report()
        csv_report = service.generate_csv_report()

        self.assertEqual(len(json_report), 2, "Should have 2 entries: original + duplicate")

        csv_reader = csv.DictReader(io.StringIO(csv_report))
        csv_rows = list(csv_reader)
        self.assertEqual(len(csv_rows), 2, "CSV should have 2 rows: original + duplicate")

        original_json = next(e for e in json_report if e["SourceFileID"] == original_doc_data.source_file_id)
        duplicate_json = next(e for e in json_report if e["SourceFileID"] == duplicate_doc_data.source_file_id)

        original_csv = next(r for r in csv_rows if r["SourceFileID"] == original_doc_data.source_file_id)
        duplicate_csv = next(r for r in csv_rows if r["SourceFileID"] == duplicate_doc_data.source_file_id)

        self._validate_api_ready_entry(original_json,
                                       original_csv,
                                       {
                                           "SourceFileID": original_doc_data.source_file_id,
                                           "SourceFileOriginalName": "invoice_001.pdf",
                                           "SourceFileOriginalPath": "/uploads/invoices/invoice_001.pdf",
                                           "SourceFileFinalName": "DOCDATA_invoice_001_final.json",
                                           "SourceFileStatus": "delivered",
                                           "SourceFileNumPages": 3,
                                           "SourceFileSize": 1024000,
                                           "SourceFileDetectedLanguage": "en",
                                           "PackageID": package.obfuscated_id,
                                           "ExclusionReason": None,
                                           "ParentFileID": None,
                                           "RelationshipToParent": None
                                       })

        self._validate_api_ready_entry(duplicate_json, duplicate_csv,
                                       {"SourceFileID": duplicate_doc_data.source_file_id,
                                        "SourceFileOriginalName": "invoice_001_copy.pdf",
                                        "SourceFileOriginalPath": "/uploads/invoices/invoice_001_copy.pdf",
                                        "SourceFileFinalName": "", "SourceFileStatus": "excluded",
                                        "SourceFileNumPages": 3, "SourceFileSize": 1024000,
                                        "SourceFileDetectedLanguage": "en", "PackageID": package.obfuscated_id,
                                        "ExclusionReason": "duplicate",
                                        "ParentFileID": original_doc_data.source_file_id,
                                        "RelationshipToParent": "duplicate", })

        for entry in [original_json, duplicate_json]:
            self.assertIsInstance(entry["ChainOfCustodyStartDate"], str)
            self.assertIsInstance(entry["ChainOfCustodyEndDate"], str)
            self.assertIsInstance(entry["SourceFileReceivedAt"], str)
            self.assertIsInstance(entry["DeliveredAt"], str)

            self.assertTrue(
                entry["ChainOfCustodyStartDate"].endswith("T00:00:00") or entry["ChainOfCustodyStartDate"].count(
                    "-") == 2)
            self.assertTrue(len(entry["SourceFileReceivedAt"]) > 10)
            self.assertTrue(len(entry["DeliveredAt"]) > 10)

    def _validate_api_ready_entry(self, json_entry, csv_entry, expected_values):
        """
        Validate that a CoC entry is API-ready with all expected values.
        Ensures consistency between JSON and CSV formats.
        """
        for field, expected_value in expected_values.items():
            actual_json_value = json_entry.get(field)

            if expected_value is None:
                self.assertIsNone(actual_json_value, f"JSON {field} should be None")
            else:
                self.assertEqual(actual_json_value, expected_value,
                                 f"JSON {field} mismatch: expected {expected_value}, got {actual_json_value}")

        for field, expected_value in expected_values.items():
            actual_csv_value = csv_entry.get(field, "")

            if expected_value is None:
                self.assertEqual(actual_csv_value, "", f"CSV {field} should be empty string")
            else:
                expected_str = str(expected_value)
                self.assertEqual(actual_csv_value, expected_str,
                                 f"CSV {field} mismatch: expected {expected_str}, got {actual_csv_value}")

        consistent_fields = ["SourceFileID", "SourceFileOriginalName", "SourceFileOriginalPath", "SourceFileFinalName",
                             "SourceFileStatus", "PackageID", "SourceFileNumPages", "SourceFileSize",
                             "SourceFileDetectedLanguage"]

        for field in consistent_fields:
            json_val = str(json_entry.get(field, "")) if json_entry.get(field) is not None else ""
            csv_val = str(csv_entry.get(field, ""))
            self.assertEqual(json_val, csv_val, f"Inconsistent {field}: JSON='{json_val}', CSV='{csv_val}'")

        required_fields = ["ChainOfCustodyStartDate", "ChainOfCustodyEndDate", "SourceFileReceivedAt", "SourceFileID",
                           "SourceFileOriginalName", "SourceFileOriginalPath", "SourceFileStatus", "DeliveredAt",
                           "SourceFileNumPages", "SourceFileSize"]

        for field in required_fields:
            json_val = json_entry.get(field)
            csv_val = csv_entry.get(field, "")

            self.assertIsNotNone(json_val, f"JSON {field} should not be None")
            self.assertNotEqual(str(json_val), "", f"JSON {field} should not be empty")
            self.assertNotEqual(csv_val, "", f"CSV {field} should not be empty")


@override_settings(DEBUG=False)
class CoCUtilsTests(TransactionTestCase, TestCaseMixin):
    """Tests for CoC utility functions."""

    def setUp(self):
        super().setUp()
        """Set up test data for CoC utils tests."""
        self.org, _ = create_organization()
        self.dp, _ = create_data_pool(organization=self.org)

        now = timezone.now()
        self.doc1, _ = create_document(data_pool=self.dp, label="Original", content_md5="md5_1",
                                       created_at=now - timezone.timedelta(hours=2),
                                       status=DocumentDataStatus.VERIFIED.name, )
        self.doc2, _ = create_document(data_pool=self.dp, label="Duplicate1", content_md5="md5_2",
                                       created_at=now - timezone.timedelta(hours=1),
                                       status=DocumentDataStatus.VERIFIED.name, )
        self.doc3, _ = create_document(data_pool=self.dp, label="Duplicate2", content_md5="md5_3", created_at=now,
                                       status=DocumentDataStatus.VERIFIED.name, )
        self.doc4, _ = create_document(data_pool=self.dp, label="Nested", content_md5="md5_4", created_at=now,
                                       status=DocumentDataStatus.VERIFIED.name, )

        docs_to_process = [self.doc1, self.doc2, self.doc3, self.doc4]
        for doc_instance in docs_to_process:
            DocumentData.objects.get_or_create(document=doc_instance, data_pool=doc_instance.data_pool,
                                               defaults={"status": DocumentDataStatus.CREATED.name,
                                                         "source_file_id": doc_instance.obfuscated_id,
                                                         "source_file_name": doc_instance.label,
                                                         "source_file_md5": doc_instance.content_md5,
                                                         "received_at": doc_instance.created_at,
                                                         "source_file_num_pages": doc_instance.page_count,
                                                         "source_file_size": doc_instance.filesize,
                                                         "source_file_detected_language": doc_instance.language,
                                                         "is_md5_check_completed": False,
                                                         "is_label_check_completed": False,
                                                         "is_content_check_completed": False, }, )

        self.doc_data1 = DocumentData.objects.get(document=self.doc1)
        self.doc_data2 = DocumentData.objects.get(document=self.doc2)
        self.doc_data3 = DocumentData.objects.get(document=self.doc3)
        self.doc_data4 = DocumentData.objects.get(document=self.doc4)

        self.doc_data2.canonical_document_data = self.doc_data1
        self.doc_data2.save()
        self.doc_data3.canonical_document_data = self.doc_data1
        self.doc_data3.save()

    def test_get_canonical_document_data(self):
        """Test getting canonical document data."""
        # For a duplicate, should return the canonical
        self.assertEqual(get_canonical_document_data(self.doc_data2), self.doc_data1)
        self.assertEqual(get_canonical_document_data(self.doc_data3), self.doc_data1)
        # For an original, should return self
        self.assertEqual(get_canonical_document_data(self.doc_data1), self.doc_data1)
        self.assertEqual(get_canonical_document_data(self.doc_data4), self.doc_data4)

    def test_get_duplicate_family(self):
        """Test getting all documents in the same duplicate family."""
        # Get family from canonical
        canonical_family = get_duplicate_family(self.doc_data1)
        self.assertEqual(len(canonical_family), 3)
        self.assertIn(self.doc_data1, canonical_family)
        self.assertIn(self.doc_data2, canonical_family)
        self.assertIn(self.doc_data3, canonical_family)
        # Get family from duplicate - should be same
        duplicate_family = get_duplicate_family(self.doc_data2)
        self.assertEqual(len(duplicate_family), 3)
        self.assertIn(self.doc_data1, duplicate_family)
        self.assertIn(self.doc_data2, duplicate_family)
        self.assertIn(self.doc_data3, duplicate_family)
        # Get family for standalone doc - just itself
        standalone_family = get_duplicate_family(self.doc_data4)
        self.assertEqual(len(standalone_family), 1)
        self.assertIn(self.doc_data4, standalone_family)


class AppendCoCFieldsTests(TransactionTestCase, TestCaseMixin):
    """Tests for CoC field appending functionality."""

    def setUp(self):
        """Set up test data for CoC field appending tests."""
        super().setUp()
        self.org, _ = create_organization()
        self.dp, _ = create_data_pool(organization=self.org)
        self.packager = create_packager(data_pool=self.dp)
        self.package = Package.objects.create(packager=self.packager, data_pool=self.dp)

        self.doc1, _ = create_document(data_pool=self.dp, label="Doc1")
        self.doc2, _ = create_document(data_pool=self.dp, label="Doc2")

        self.doc_data1, _ = DocumentData.objects.get_or_create(document=self.doc1, data_pool=self.dp,
                                                               defaults={"status": "PENDING_CREATION",
                                                                         "exclusion_reason": None,
                                                                         "parent_file_id": "source1",
                                                                         "relationship_to_parent": None,
                                                                         "source_file_name": "doc1.pdf",
                                                                         "source_file_md5": "md5_1", }, )
        self.doc_data1.content_hash = "content_hash_1"
        self.doc_data1.save()

        self.doc_data2, _ = DocumentData.objects.get_or_create(document=self.doc2, data_pool=self.dp,
                                                               defaults={"status": "PENDING_CREATION",
                                                                         "exclusion_reason": None,
                                                                         "parent_file_id": "source2",
                                                                         "relationship_to_parent": None,
                                                                         "source_file_name": "doc2.pdf",
                                                                         "source_file_md5": "md5_2", }, )
        self.doc_data2.content_hash = "content_hash_2"
        self.doc_data2.save()

        self.entry1 = PackageEntry.objects.create(package=self.package, document_data=self.doc_data1,
                                                  status_in_package="INCLUDED", filename_in_package="doc1.json",
                                                  data_pool=self.dp, delivered_at=timezone.now(), )

        self.entry2 = PackageEntry.objects.create(package=self.package, document_data=self.doc_data2,
                                                  status_in_package="INCLUDED", filename_in_package="doc2.json",
                                                  data_pool=self.dp, delivered_at=timezone.now(), )

    def test_render_grouped_documents_with_appended_coc_fields(self):
        """Test appending CoC fields to data output when flag is enabled."""
        self.packager.append_coc_fields_to_data = True
        self.packager.coc_field_config = ["source_file_name", "source_file_md5", "received_at"]
        self.packager.save()

        self.doc_data1.source_file_name = "doc1.pdf"
        self.doc_data1.source_file_md5 = "md5_1"
        self.doc_data1.received_at = datetime(2024, 6, 1, 10, 0, 0, tzinfo=timezone.utc)
        self.doc_data1.save()

        self.doc_data2.source_file_name = "doc2.pdf"
        self.doc_data2.source_file_md5 = "md5_2"
        self.doc_data2.received_at = datetime(2024, 6, 2, 10, 0, 0, tzinfo=timezone.utc)
        self.doc_data2.save()

        grouped_data = {"group1": [self.doc_data1], "group2": [self.doc_data2]}
        service = PackageRenderingService(self.package)
        result = service._render_grouped_documents(grouped_data, include_document_content=False)

        self.assertEqual(len(result.rendered_items), 2)
        self.assertEqual(len(result.errors), 0)

        for item in result.rendered_items:
            content_data = json.loads(item.content)
            self.assertIsInstance(content_data, dict)
            self.assertIn("data", content_data)

            documents = content_data["data"]
            self.assertIsInstance(documents, list)
            self.assertTrue(len(documents) > 0)

            doc_entry = documents[0]

            self.assertIn("source_file_name", doc_entry)
            self.assertIn("source_file_md5", doc_entry)
            self.assertIn("received_at", doc_entry)

    def test_render_grouped_documents_without_appended_coc_fields(self):
        """Test no CoC fields are appended when flag is disabled."""
        self.packager.append_coc_fields_to_data = False
        self.packager.coc_field_config = ["source_file_name", "source_file_md5", "received_at"]
        self.packager.save()

        self.doc_data1.source_file_name = "doc1.pdf"
        self.doc_data1.source_file_md5 = "md5_1"
        self.doc_data1.received_at = datetime(2024, 6, 1, 10, 0, 0, tzinfo=timezone.utc)
        self.doc_data1.save()

        self.doc_data2.source_file_name = "doc2.pdf"
        self.doc_data2.source_file_md5 = "md5_2"
        self.doc_data2.received_at = datetime(2024, 6, 2, 10, 0, 0, tzinfo=timezone.utc)
        self.doc_data2.save()

        grouped_data = {"group1": [self.doc_data1], "group2": [self.doc_data2]}
        service = PackageRenderingService(self.package)
        result = service._render_grouped_documents(grouped_data, include_document_content=False)

        self.assertEqual(len(result.rendered_items), 2)
        self.assertEqual(len(result.errors), 0)

        for item in result.rendered_items:
            content_data = json.loads(item.content)
            self.assertIsInstance(content_data, dict)
            self.assertIn("data", content_data)

            documents = content_data["data"]
            self.assertIsInstance(documents, list)
            self.assertTrue(len(documents) > 0)

            doc_entry = documents[0]  # First document in the group

            self.assertNotIn("source_file_name", doc_entry)
            self.assertNotIn("source_file_md5", doc_entry)
            self.assertNotIn("received_at", doc_entry)

    def test_render_grouped_documents_with_missing_coc_fields(self):
        """Test handling of missing CoC fields during appending."""
        self.packager.append_coc_fields_to_data = True
        self.packager.coc_field_config = ["SourceFileOriginalName", "nonexistent_field", "another_missing_field"]
        self.packager.save()

        self.doc_data1.source_file_name = "doc1.pdf"
        self.doc_data1.save()
        grouped_data = {"group1": [self.doc_data1]}

        service = PackageRenderingService(self.package)
        result = service._render_grouped_documents(grouped_data, include_document_content=False)

        self.assertEqual(len(result.rendered_items), 1)
        self.assertEqual(len(result.errors), 0)

        doc1_item = result.rendered_items[0]
        content_data = json.loads(doc1_item.content)
        self.assertIsInstance(content_data, dict)
        self.assertIn("data", content_data)

        documents = content_data["data"]
        self.assertIsInstance(documents, list)
        self.assertTrue(len(documents) > 0)

        doc_entry = documents[0]  # First document in the group

        self.assertIn("SourceFileOriginalName", doc_entry)
        self.assertEqual(doc_entry["SourceFileOriginalName"], "doc1.pdf")

        self.assertIn("nonexistent_field", doc_entry)
        self.assertIsNone(doc_entry["nonexistent_field"])
        self.assertIn("another_missing_field", doc_entry)
        self.assertIsNone(doc_entry["another_missing_field"])

    def test_render_grouped_documents_with_glynt_schemas_coc_fields(self):
        """Test appending glynt-schemas CoC field names to data output."""
        self.packager.append_coc_fields_to_data = True
        self.packager.coc_field_config = [
            "SourceFileID", "SourceFileOriginalName", "SourceFileReceivedAt",
            "SourceFileNumPages", "ExclusionReason"
        ]
        self.packager.save()

        self.doc_data1.source_file_id = "SF-12345"
        self.doc_data1.source_file_name = "test_document.pdf"
        self.doc_data1.received_at = datetime(2024, 6, 1, 10, 0, 0, tzinfo=timezone.utc)
        self.doc_data1.source_file_num_pages = 5
        self.doc_data1.exclusion_reason = "MD5_DUPLICATE"
        self.doc_data1.save()

        grouped_data = {"group1": [self.doc_data1]}
        service = PackageRenderingService(self.package)
        result = service._render_grouped_documents(grouped_data, include_document_content=False)

        self.assertEqual(len(result.rendered_items), 1)
        self.assertEqual(len(result.errors), 0)

        doc1_item = result.rendered_items[0]
        content_data = json.loads(doc1_item.content)
        self.assertIsInstance(content_data, dict)
        self.assertIn("data", content_data)

        documents = content_data["data"]
        self.assertIsInstance(documents, list)
        self.assertTrue(len(documents) > 0)

        doc_entry = documents[0]

        self.assertIn("SourceFileID", doc_entry)
        self.assertEqual(doc_entry["SourceFileID"], "SF-12345")

        self.assertIn("SourceFileOriginalName", doc_entry)
        self.assertEqual(doc_entry["SourceFileOriginalName"], "test_document.pdf")

        self.assertIn("SourceFileReceivedAt", doc_entry)
        self.assertEqual(doc_entry["SourceFileReceivedAt"], "2024-06-01T10:00:00+00:00")

        self.assertIn("SourceFileNumPages", doc_entry)
        self.assertEqual(doc_entry["SourceFileNumPages"], 5)

        self.assertIn("ExclusionReason", doc_entry)
        self.assertEqual(doc_entry["ExclusionReason"], "duplicate")  # CoC service maps MD5_DUPLICATE to 'duplicate'
