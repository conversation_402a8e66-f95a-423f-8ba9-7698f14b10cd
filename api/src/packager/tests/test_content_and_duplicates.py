"""
Consolidated content hashing and duplicate detection tests.

"""

import logging
from datetime import timed<PERSON><PERSON>
from unittest.mock import patch

from django.test import TransactionTestCase
from django.utils import timezone
from glynt_schemas.document.document_attributes import DocumentDataStatus

from documents.tests.util import create_document
from extract.tests.util import create_extraction
from organizations.tests.util import create_data_pool, create_organization
from packager.models import DocumentData, DocumentRelationship, DocumentRelationshipType, PackageEntry
from packager.pydantic_classes import PackageEntryStatus
from packager.services.duplicate_detection_service import DuplicateDetectionService
from packager.services.package_creation_service import PackageCreationService
from packager.tests.util import create_packager
from training.tests.util import create_training_set, create_training_revision

logger = logging.getLogger(__name__)


class DocumentDataContentHashTests(TransactionTestCase):
    """Tests for DocumentData.calculate_content_hash method."""

    def setUp(self):
        """Set up test data using helper functions."""
        self.org, _ = create_organization()
        self.dp, _ = create_data_pool(organization=self.org)
        self.packager = create_packager(data_pool=self.dp, deduplication_content_fields=["field1", "field2"])

    def test_calculate_content_hash_with_real_extraction(self):
        """Test content hash calculation with real extraction data."""
        # Create document and extraction
        doc, _ = create_document(data_pool=self.dp, status=DocumentDataStatus.VERIFIED.name, label="test.pdf")
        training_set, _ = create_training_set(data_pool=self.dp)
        training_revision, _ = create_training_revision(data_pool=self.dp, training_set=training_set)

        extraction, _ = create_extraction(
            data_pool=self.dp,
            document=doc,
            training_revision=training_revision,
            _raw_results={"field1": "value1", "field2": "value2", "field3": "ignored"},
        )

        doc_data = DocumentData.objects.get(document=doc, data_pool=self.dp)

        def mock_get_transformed_results(self):
            return [{"field1": "value1", "field2": "value2", "field3": "ignored"}]

        with patch("extract.models.Extraction.get_transformed_results", mock_get_transformed_results):
            content_hash = doc_data.calculate_content_hash(["field1", "field2"])

            self.assertIsNotNone(content_hash)
            self.assertEqual(len(content_hash), 32)

            content_hash2 = doc_data.calculate_content_hash(["field1", "field2"])
            self.assertEqual(content_hash, content_hash2)

            content_hash3 = doc_data.calculate_content_hash(["field2", "field1"])
            self.assertEqual(content_hash, content_hash3)

    def test_calculate_content_hash_with_missing_fields(self):
        """Test content hash calculation when some fields are missing."""
        doc, _ = create_document(data_pool=self.dp, status=DocumentDataStatus.VERIFIED.name, label="test.pdf")
        training_set, _ = create_training_set(data_pool=self.dp)
        training_revision, _ = create_training_revision(data_pool=self.dp, training_set=training_set)

        create_extraction(
            data_pool=self.dp,
            document=doc,
            training_revision=training_revision,
            _raw_results={"field1": "value1"},  # field2 is missing
        )

        doc_data = DocumentData.objects.get(document=doc, data_pool=self.dp)

        def mock_get_transformed_results(self):
            logger.debug(f"mock_get_transformed_results called for extraction {self.pk}")
            return [{"field1": "value1"}]

        with patch("extract.models.Extraction.get_transformed_results", mock_get_transformed_results):
            content_hash = doc_data.calculate_content_hash(["field1", "field2"])
            self.assertIsNotNone(content_hash)

    def test_calculate_content_hash_with_no_extraction(self):
        """Test content hash calculation when no extraction exists."""
        doc, _ = create_document(data_pool=self.dp, status=DocumentDataStatus.VERIFIED.name, label="test.pdf")

        # Get DocumentData (created automatically by signals)
        doc_data = DocumentData.objects.get(document=doc, data_pool=self.dp)

        # Should return None when no extraction exists
        content_hash = doc_data.calculate_content_hash(["field1", "field2"])
        self.assertIsNone(content_hash)

    def test_calculate_content_hash_with_raw_values(self):
        """Test content hash calculation preserves raw values."""
        doc, _ = create_document(data_pool=self.dp, status=DocumentDataStatus.VERIFIED.name, label="test.pdf")
        training_set, _ = create_training_set(data_pool=self.dp)
        training_revision, _ = create_training_revision(data_pool=self.dp, training_set=training_set)

        create_extraction(
            data_pool=self.dp,
            document=doc,
            training_revision=training_revision,
            _raw_results={
                "field1": "  Some text  with  extra  spaces  ",
                "field2": "Line1\nLine2\nLine3",
                "field3": "ignored",
            },
        )

        doc_data = DocumentData.objects.get(document=doc, data_pool=self.dp)

        def mock_get_transformed_results(self):
            logger.debug(f"mock_get_transformed_results called for extraction {self.pk}")
            return [
                {"field1": "  Some text  with  extra  spaces  ", "field2": "Line1\nLine2\nLine3", "field3": "ignored"}
            ]

        with patch("extract.models.Extraction.get_transformed_results", mock_get_transformed_results):
            content_hash = doc_data.calculate_content_hash(["field1", "field2"])

            self.assertIsNotNone(content_hash)

            content_hash2 = doc_data.calculate_content_hash(["field1", "field2"])
            self.assertEqual(content_hash, content_hash2)

    def test_calculate_content_hash_with_non_string_values(self):
        """Test content hash calculation with non-string values."""
        doc, _ = create_document(data_pool=self.dp, status=DocumentDataStatus.VERIFIED.name, label="test.pdf")
        training_set, _ = create_training_set(data_pool=self.dp)
        training_revision, _ = create_training_revision(data_pool=self.dp, training_set=training_set)

        create_extraction(
            data_pool=self.dp,
            document=doc,
            training_revision=training_revision,
            _raw_results={"field1": 123, "field2": True, "field3": {"nested": "value"}, "field4": ["list", "item"]},
        )

        doc_data = DocumentData.objects.get(document=doc, data_pool=self.dp)

        def mock_get_transformed_results(self):
            return [{"field1": 123, "field2": True, "field3": {"nested": "value"}, "field4": ["list", "item"]}]

        with patch("extract.models.Extraction.get_transformed_results", mock_get_transformed_results):
            content_hash = doc_data.calculate_content_hash(["field1", "field2", "field3", "field4"])
            self.assertIsNotNone(content_hash)

    def test_hash_deterministic_regardless_of_field_order(self):
        """Test that content hash is deterministic regardless of field order."""
        doc, _ = create_document(data_pool=self.dp, status=DocumentDataStatus.VERIFIED.name, label="test.pdf")
        training_set, _ = create_training_set(data_pool=self.dp)
        training_revision, _ = create_training_revision(data_pool=self.dp, training_set=training_set)

        create_extraction(
            data_pool=self.dp,
            document=doc,
            training_revision=training_revision,
            _raw_results={"field1": "value1", "field2": "value2", "field3": "value3"},
        )

        doc_data = DocumentData.objects.get(document=doc, data_pool=self.dp)

        def mock_get_transformed_results(self):
            logger.debug(f"mock_get_transformed_results called for extraction {self.pk}")
            return [{"field1": "value1", "field2": "value2", "field3": "value3"}]

        with patch("extract.models.Extraction.get_transformed_results", mock_get_transformed_results):
            hash1 = doc_data.calculate_content_hash(["field1", "field2", "field3"])
            hash2 = doc_data.calculate_content_hash(["field3", "field1", "field2"])
            hash3 = doc_data.calculate_content_hash(["field2", "field3", "field1"])

            self.assertEqual(hash1, hash2)
            self.assertEqual(hash2, hash3)

    def test_calculate_content_hash_with_empty_field_list(self):
        """Test content hash calculation with empty field list."""
        doc, _ = create_document(data_pool=self.dp, status=DocumentDataStatus.VERIFIED.name, label="test.pdf")
        training_set, _ = create_training_set(data_pool=self.dp)
        training_revision, _ = create_training_revision(data_pool=self.dp, training_set=training_set)

        create_extraction(
            data_pool=self.dp,
            document=doc,
            training_revision=training_revision,
            _raw_results={"field1": "Some text content"},
        )

        doc_data = DocumentData.objects.get(document=doc, data_pool=self.dp)

        def mock_get_transformed_results(self):
            return [{"field1": "Some text content"}]

        with patch("extract.models.Extraction.get_transformed_results", mock_get_transformed_results):
            hash1 = doc_data.calculate_content_hash([])
            # Should return None for empty field list
            self.assertIsNone(hash1)

    def test_hash_deterministic_with_multiple_calls(self):
        """Test that hash generation is deterministic with multiple calls."""
        doc, _ = create_document(data_pool=self.dp, status=DocumentDataStatus.VERIFIED.name, label="test.pdf")
        training_set, _ = create_training_set(data_pool=self.dp)
        training_revision, _ = create_training_revision(data_pool=self.dp, training_set=training_set)

        create_extraction(
            data_pool=self.dp,
            document=doc,
            training_revision=training_revision,
            _raw_results={"field1": "Some text content", "field2": "More content"},
        )

        doc_data = DocumentData.objects.get(document=doc, data_pool=self.dp)

        def mock_get_transformed_results(self):
            return [{"field1": "Some text content", "field2": "More content"}]

        with patch("extract.models.Extraction.get_transformed_results", mock_get_transformed_results):
            # Generate hash multiple times
            hash1 = doc_data.calculate_content_hash(["field1", "field2"])
            hash2 = doc_data.calculate_content_hash(["field1", "field2"])
            hash3 = doc_data.calculate_content_hash(["field1", "field2"])

            # All should be the same
            self.assertEqual(hash1, hash2)
            self.assertEqual(hash2, hash3)

    def test_hash_with_whitespace_differences(self):
        """Test that whitespace differences produce different hashes."""
        training_set, _ = create_training_set(data_pool=self.dp)
        training_revision, _ = create_training_revision(data_pool=self.dp, training_set=training_set)

        doc1, _ = create_document(data_pool=self.dp, status=DocumentDataStatus.VERIFIED.name, label="test1.pdf")
        create_extraction(
            data_pool=self.dp,
            document=doc1,
            training_revision=training_revision,
            _raw_results={"field1": "  Some text  with  extra  spaces  "},
        )

        doc2, _ = create_document(data_pool=self.dp, status=DocumentDataStatus.VERIFIED.name, label="test2.pdf")
        create_extraction(
            data_pool=self.dp,
            document=doc2,
            training_revision=training_revision,
            _raw_results={"field1": "Some text with extra spaces"},
        )

        doc_data1 = DocumentData.objects.get(document=doc1, data_pool=self.dp)
        doc_data2 = DocumentData.objects.get(document=doc2, data_pool=self.dp)

        def mock_get_transformed_results(self):
            if self.document.label == "test1.pdf":
                return [{"field1": "  Some text  with  extra  spaces  "}]
            else:
                return [{"field1": "Some text with extra spaces"}]

        with patch("extract.models.Extraction.get_transformed_results", mock_get_transformed_results):
            # Should generate different hashes due to whitespace differences
            hash1 = doc_data1.calculate_content_hash(["field1"])
            hash2 = doc_data2.calculate_content_hash(["field1"])
            self.assertNotEqual(hash1, hash2)

    def test_hash_with_case_sensitivity(self):
        """Test that case differences produce different hashes."""
        training_set, _ = create_training_set(data_pool=self.dp)
        training_revision, _ = create_training_revision(data_pool=self.dp, training_set=training_set)

        doc1, _ = create_document(data_pool=self.dp, status=DocumentDataStatus.VERIFIED.name, label="test1.pdf")
        create_extraction(
            data_pool=self.dp,
            document=doc1,
            training_revision=training_revision,
            _raw_results={"field1": "Some Text Content"},
        )

        doc2, _ = create_document(data_pool=self.dp, status=DocumentDataStatus.VERIFIED.name, label="test2.pdf")
        create_extraction(
            data_pool=self.dp,
            document=doc2,
            training_revision=training_revision,
            _raw_results={"field1": "some text content"},
        )

        doc_data1 = DocumentData.objects.get(document=doc1, data_pool=self.dp)
        doc_data2 = DocumentData.objects.get(document=doc2, data_pool=self.dp)

        def mock_get_transformed_results(self):
            if self.document.label == "test1.pdf":
                return [{"field1": "Some Text Content"}]
            else:
                return [{"field1": "some text content"}]

        with patch("extract.models.Extraction.get_transformed_results", mock_get_transformed_results):
            # Should generate different hashes due to case differences
            hash1 = doc_data1.calculate_content_hash(["field1"])
            hash2 = doc_data2.calculate_content_hash(["field1"])
            self.assertNotEqual(hash1, hash2)


class ContentDuplicateDetectionTests(TransactionTestCase):
    """Tests for content-based duplicate detection."""

    def setUp(self):
        """Set up test data using helper functions."""
        self.org, _ = create_organization()
        self.dp, _ = create_data_pool(organization=self.org)

        # Create packager with content deduplication fields
        self.packager = create_packager(data_pool=self.dp, deduplication_content_fields=["field1", "field2"])

        # Ensure relationship types exist
        self.relationship_types = {}
        for label in ["CONTENT_DUPLICATE", "MD5_DUPLICATE", "LABEL_DUPLICATE"]:
            rel_type, _ = DocumentRelationshipType.objects.get_or_create(
                label=label, data_pool=self.dp, defaults={"description": f"Documents with {label.lower()} relationship"}
            )
            self.relationship_types[label] = rel_type

        # Create training infrastructure
        self.training_set, _ = create_training_set(data_pool=self.dp)
        self.training_revision, _ = create_training_revision(data_pool=self.dp, training_set=self.training_set)

        # Create test documents with different timestamps for canonical ordering
        now = timezone.now()
        self.doc1, _ = create_document(
            data_pool=self.dp,
            label="doc1.pdf",
            status=DocumentDataStatus.VERIFIED.name,
            created_at=now - timedelta(minutes=2),
        )
        self.doc2, _ = create_document(
            data_pool=self.dp,
            label="doc2.pdf",
            status=DocumentDataStatus.VERIFIED.name,
            created_at=now - timedelta(minutes=1),
        )
        self.doc3, _ = create_document(
            data_pool=self.dp, label="doc3.pdf", status=DocumentDataStatus.VERIFIED.name, created_at=now
        )

        # Create extractions with different content
        self.extraction1, _ = create_extraction(
            data_pool=self.dp,
            document=self.doc1,
            training_revision=self.training_revision,
            _raw_results={"field1": "content_a", "field2": "content_b"},
        )

        def mock_get_transformed_results1():
            return [{"field1": "content_a", "field2": "content_b"}]

        self.extraction1.get_transformed_results = mock_get_transformed_results1

        self.extraction2, _ = create_extraction(
            data_pool=self.dp,
            document=self.doc2,
            training_revision=self.training_revision,
            _raw_results={"field1": "content_a", "field2": "content_b"},
            # Same as doc1
        )

        def mock_get_transformed_results2():
            return [{"field1": "content_a", "field2": "content_b"}]

        self.extraction2.get_transformed_results = mock_get_transformed_results2

        self.extraction3, _ = create_extraction(
            data_pool=self.dp,
            document=self.doc3,
            training_revision=self.training_revision,
            _raw_results={"field1": "different", "field2": "content"},
            # Different content
        )

        def mock_get_transformed_results3():
            return [{"field1": "different", "field2": "content"}]

        self.extraction3.get_transformed_results = mock_get_transformed_results3

        # Get DocumentData objects (created by signals)
        self.doc_data1 = DocumentData.objects.get(document=self.doc1, data_pool=self.dp)
        self.doc_data2 = DocumentData.objects.get(document=self.doc2, data_pool=self.dp)
        self.doc_data3 = DocumentData.objects.get(document=self.doc3, data_pool=self.dp)

        # Set up proper timestamps for canonical ordering
        self.doc_data1.received_at = now - timedelta(minutes=2)
        self.doc_data1.save()
        self.doc_data2.received_at = now - timedelta(minutes=1)
        self.doc_data2.save()
        self.doc_data3.received_at = now
        self.doc_data3.save()

        for doc_data in [self.doc_data1, self.doc_data2, self.doc_data3]:
            doc_data.canonical_document_data = None
            doc_data.parent_file_id = None
            doc_data.relationship_to_parent = None
            doc_data.is_md5_check_completed = False
            doc_data.is_label_check_completed = False
            doc_data.is_content_check_completed = False
            # Removed: doc_data.content_hash = None
            doc_data.save()

    def test_content_duplicate_detection_creates_relationship(self):
        """Test that content duplicate detection creates proper relationships."""
        # Clear any existing relationships
        DocumentRelationship.objects.filter(data_pool=self.dp).delete()

        self.doc_data1.is_content_check_completed = False
        self.doc_data1.content_hash = None
        self.doc_data1.save()
        self.doc_data2.is_content_check_completed = False
        self.doc_data2.content_hash = None
        self.doc_data2.save()

        def mock_get_transformed_results(self):
            logger.debug(f"mock_get_transformed_results called for extraction {self.pk}")
            if self.document.label == "doc1.pdf":
                return [{"field1": "content_a", "field2": "content_b"}]
            elif self.document.label == "doc2.pdf":
                return [{"field1": "content_a", "field2": "content_b"}]  # Same as doc1 for duplicate detection
            else:
                return [{"field1": "content_a", "field2": "content_b"}]

        with patch("extract.models.Extraction.get_transformed_results", mock_get_transformed_results):
            service1 = DuplicateDetectionService(self.doc_data1.pk)
            service1.run_checks(["CONTENT"])
            service2 = DuplicateDetectionService(self.doc_data2.pk)
            service2.run_checks(["CONTENT"])

        relationship = DocumentRelationship.objects.filter(
            source_document_data=self.doc_data1,
            target_document_data=self.doc_data2,
            relationship_type__label="CONTENT_DUPLICATE",
        ).first()

        self.assertIsNotNone(relationship)

        # Verify canonical pointer was set
        self.doc_data2.refresh_from_db()
        self.assertEqual(self.doc_data2.canonical_document_data, self.doc_data1)
        self.assertEqual(self.doc_data2.parent_file_id, str(self.doc_data1.document.obfuscated_id))
        self.assertEqual(self.doc_data2.relationship_to_parent, "CONTENT_DUPLICATE")

        # Verify content hash was calculated and saved
        self.assertIsNotNone(self.doc_data2.content_hash)
        self.assertTrue(self.doc_data2.is_content_check_completed)

    def test_content_duplicate_detection_no_duplicates(self):
        """Test content duplicate detection when no duplicates exist."""
        # Clear any existing relationships
        DocumentRelationship.objects.filter(data_pool=self.dp).delete()

        # Reset content check flags
        self.doc_data3.is_content_check_completed = False
        self.doc_data3.content_hash = None
        self.doc_data3.save()

        from unittest.mock import patch

        def mock_get_transformed_results(self):
            if self.document.label == "doc3.pdf":
                return [{"field1": "different", "field2": "content"}]
            else:
                return [{"field1": "different", "field2": "content"}]

        with patch("extract.models.Extraction.get_transformed_results", mock_get_transformed_results):
            service = DuplicateDetectionService(self.doc_data3.pk)
            service.run_checks(["CONTENT"])

        relationship_count = DocumentRelationship.objects.filter(
            target_document_data=self.doc_data3, relationship_type__label="CONTENT_DUPLICATE"
        ).count()

        self.assertEqual(relationship_count, 0)

        # Verify no canonical pointer was set
        self.doc_data3.refresh_from_db()
        self.assertIsNone(self.doc_data3.canonical_document_data)
        self.assertIsNone(self.doc_data3.parent_file_id)
        self.assertIsNone(self.doc_data3.relationship_to_parent)

        # Verify content hash was still calculated
        self.assertIsNotNone(self.doc_data3.content_hash)
        self.assertTrue(self.doc_data3.is_content_check_completed)

    def test_content_duplicate_detection_skips_non_verified(self):
        """Test that content duplicate detection skips non-VERIFIED documents."""
        # Set document to non-VERIFIED status
        self.doc2.status = DocumentDataStatus.CREATED.name
        self.doc2.save()

        # Clear any existing relationships
        DocumentRelationship.objects.filter(data_pool=self.dp).delete()

        # Reset content check flags
        self.doc_data2.is_content_check_completed = False
        self.doc_data2.content_hash = None
        self.doc_data2.save()

        # Run content duplicate check
        service = DuplicateDetectionService(self.doc_data2.pk)
        service.run_checks(["CONTENT"])

        # Verify check was marked as completed but no hash was calculated
        self.doc_data2.refresh_from_db()
        self.assertTrue(self.doc_data2.is_content_check_completed)
        self.assertIsNone(self.doc_data2.content_hash)

        # Verify no relationships were created
        relationship_count = DocumentRelationship.objects.filter(
            target_document_data=self.doc_data2, relationship_type__label="CONTENT_DUPLICATE"
        ).count()

        self.assertEqual(relationship_count, 0)

    def test_content_hash_based_duplicate_detection(self):
        """Test end-to-end content hash based duplicate detection."""
        # Clear any existing relationships
        DocumentRelationship.objects.filter(data_pool=self.dp).delete()

        # Create documents with same content
        doc1, _ = create_document(data_pool=self.dp, label="doc1.pdf", status=DocumentDataStatus.VERIFIED.name)
        doc2, _ = create_document(data_pool=self.dp, label="doc2.pdf", status=DocumentDataStatus.VERIFIED.name)

        extraction1, _ = create_extraction(
            data_pool=self.dp,
            document=doc1,
            training_revision=self.training_revision,
            _raw_results={"field1": "identical_content", "field2": "same_value"},
        )

        def mock_get_transformed_results1():
            return [{"field1": "identical_content", "field2": "same_value"}]

        extraction1.get_transformed_results = mock_get_transformed_results1

        extraction2, _ = create_extraction(
            data_pool=self.dp,
            document=doc2,
            training_revision=self.training_revision,
            _raw_results={"field1": "identical_content", "field2": "same_value"},
        )

        def mock_get_transformed_results2():
            return [{"field1": "identical_content", "field2": "same_value"}]

        extraction2.get_transformed_results = mock_get_transformed_results2

        # Get DocumentData objects
        doc_data1 = DocumentData.objects.get(document=doc1, data_pool=self.dp)
        doc_data2 = DocumentData.objects.get(document=doc2, data_pool=self.dp)

        # Set timestamps for canonical ordering
        now = timezone.now()
        doc_data1.received_at = now - timedelta(minutes=1)
        doc_data1.save()
        doc_data2.received_at = now
        doc_data2.save()

        # Reset content check flags
        doc_data1.is_content_check_completed = False
        doc_data1.content_hash = None
        doc_data1.save()
        doc_data2.is_content_check_completed = False
        doc_data2.content_hash = None
        doc_data2.save()

        def mock_get_transformed_results(self):
            if self.document.label == "doc1.pdf":
                return [{"field1": "identical_content", "field2": "same_value"}]
            elif self.document.label == "doc2.pdf":
                return [{"field1": "identical_content", "field2": "same_value"}]  # Same as doc1
            else:
                return [{"field1": "identical_content", "field2": "same_value"}]

        with patch("extract.models.Extraction.get_transformed_results", mock_get_transformed_results):
            service1 = DuplicateDetectionService(doc_data1.pk)
            service1.run_checks(["CONTENT"])

            service2 = DuplicateDetectionService(doc_data2.pk)
            service2.run_checks(["CONTENT"])

        # Verify both have content hashes calculated
        doc_data1.refresh_from_db()
        doc_data2.refresh_from_db()

        self.assertIsNotNone(doc_data1.content_hash)
        self.assertIsNotNone(doc_data2.content_hash)
        self.assertEqual(doc_data1.content_hash, doc_data2.content_hash)

        # Verify relationship was created
        relationship = DocumentRelationship.objects.filter(
            source_document_data=doc_data1, target_document_data=doc_data2, relationship_type__label="CONTENT_DUPLICATE"
        ).first()

        self.assertIsNotNone(relationship)

        # Verify canonical pointer
        self.assertEqual(doc_data2.canonical_document_data, doc_data1)
        self.assertEqual(doc_data2.relationship_to_parent, "CONTENT_DUPLICATE")

    def test_content_hash_with_different_values_not_duplicates(self):
        """Test that documents with different content are not marked as duplicates."""
        # Clear any existing relationships
        DocumentRelationship.objects.filter(data_pool=self.dp).delete()

        doc1, _ = create_document(
            data_pool=self.dp, label="different_content_doc1.pdf", status=DocumentDataStatus.VERIFIED.name
        )
        doc2, _ = create_document(
            data_pool=self.dp, label="different_content_doc2.pdf", status=DocumentDataStatus.VERIFIED.name
        )

        # Create extractions with different content
        extraction1, _ = create_extraction(
            data_pool=self.dp,
            document=doc1,
            training_revision=self.training_revision,
            _raw_results={"field1": "content_a", "field2": "value_a"},
        )

        def mock_get_transformed_results1():
            return [{"field1": "content_a", "field2": "value_a"}]

        extraction1.get_transformed_results = mock_get_transformed_results1

        extraction2, _ = create_extraction(
            data_pool=self.dp,
            document=doc2,
            training_revision=self.training_revision,
            _raw_results={"field1": "content_b", "field2": "value_b"},
        )

        def mock_get_transformed_results2():
            return [{"field1": "content_b", "field2": "value_b"}]

        extraction2.get_transformed_results = mock_get_transformed_results2

        # Get DocumentData objects
        doc_data1 = DocumentData.objects.get(document=doc1, data_pool=self.dp)
        doc_data2 = DocumentData.objects.get(document=doc2, data_pool=self.dp)

        for doc_data in [doc_data1, doc_data2]:
            doc_data.is_content_check_completed = False
            doc_data.content_hash = None
            doc_data.canonical_document_data = None
            doc_data.parent_file_id = None
            doc_data.relationship_to_parent = None
            doc_data.save()

        def mock_get_transformed_results(self):
            logger.error(f"mock_get_transformed_results called for extraction {self.pk} document {self.document.label}")
            # Return the appropriate data based on the document label
            if self.document.label == "different_content_doc1.pdf":
                logger.error("mock_get_transformed_results returning doc1 data")
                return [{"field1": "content_a", "field2": "value_a"}]
            elif self.document.label == "different_content_doc2.pdf":
                logger.error("mock_get_transformed_results returning doc2 data")
                return [{"field1": "content_b", "field2": "value_b"}]
            else:
                logger.error("mock_get_transformed_results returning default data")
                return [{"field1": "content_a", "field2": "value_a"}]

        with patch("extract.models.Extraction.get_transformed_results", mock_get_transformed_results):
            service1 = DuplicateDetectionService(doc_data1.pk)
            service1.run_checks(["CONTENT"])

            service2 = DuplicateDetectionService(doc_data2.pk)
            service2.run_checks(["CONTENT"])

        # Verify both have different content hashes
        doc_data1.refresh_from_db()
        doc_data2.refresh_from_db()

        self.assertIsNotNone(doc_data1.content_hash)
        self.assertIsNotNone(doc_data2.content_hash)
        self.assertNotEqual(doc_data1.content_hash, doc_data2.content_hash)

        # Verify no relationships were created
        relationship_count = DocumentRelationship.objects.filter(relationship_type__label="CONTENT_DUPLICATE").count()

        self.assertEqual(relationship_count, 0)

        # Verify no canonical pointers were set
        self.assertIsNone(doc_data1.canonical_document_data)
        self.assertIsNone(doc_data2.canonical_document_data)
        self.assertIsNone(doc_data1.relationship_to_parent)
        self.assertIsNone(doc_data2.relationship_to_parent)


class DuplicateDetectionServiceTests(TransactionTestCase):
    """Tests for DuplicateDetectionService functionality."""

    def setUp(self):
        """Set up test data using helper functions."""
        self.org, _ = create_organization()
        self.dp, _ = create_data_pool(organization=self.org)
        self.packager = create_packager(data_pool=self.dp, deduplication_content_fields=["field1", "field2"])

        # Ensure relationship types exist
        self.relationship_types = {}
        for label in ["CONTENT_DUPLICATE", "MD5_DUPLICATE", "LABEL_DUPLICATE"]:
            rel_type, _ = DocumentRelationshipType.objects.get_or_create(
                label=label, data_pool=self.dp, defaults={"description": f"Documents with {label.lower()} relationship"}
            )
            self.relationship_types[label] = rel_type

        # Create training infrastructure
        self.training_set, _ = create_training_set(data_pool=self.dp)
        self.training_revision, _ = create_training_revision(data_pool=self.dp, training_set=self.training_set)

        # Create test documents
        self.doc1, _ = create_document(data_pool=self.dp, label="doc1.pdf", status=DocumentDataStatus.VERIFIED.name)
        self.doc2, _ = create_document(data_pool=self.dp, label="doc2.pdf", status=DocumentDataStatus.VERIFIED.name)

        # Create extractions
        self.extraction1, _ = create_extraction(
            data_pool=self.dp,
            document=self.doc1,
            training_revision=self.training_revision,
            _raw_results={"field1": "value1", "field2": "value2"},
        )

        def mock_get_transformed_results1():
            return [{"field1": "value1", "field2": "value2"}]

        self.extraction1.get_transformed_results = mock_get_transformed_results1

        self.extraction2, _ = create_extraction(
            data_pool=self.dp,
            document=self.doc2,
            training_revision=self.training_revision,
            _raw_results={"field1": "value1", "field2": "value2"},  # Same content
        )

        def mock_get_transformed_results2():
            return [{"field1": "value1", "field2": "value2"}]

        self.extraction2.get_transformed_results = mock_get_transformed_results2

        # Get DocumentData objects
        self.doc_data1 = DocumentData.objects.get(document=self.doc1, data_pool=self.dp)
        self.doc_data2 = DocumentData.objects.get(document=self.doc2, data_pool=self.dp)

        for doc_data in [self.doc_data1, self.doc_data2]:
            doc_data.canonical_document_data = None
            doc_data.parent_file_id = None
            doc_data.relationship_to_parent = None
            doc_data.is_md5_check_completed = False
            doc_data.is_label_check_completed = False
            doc_data.is_content_check_completed = False
            doc_data.content_hash = None
            doc_data.save()

    def test_service_initialization(self):
        """Test that DuplicateDetectionService initializes correctly."""
        service = DuplicateDetectionService(self.doc_data1.pk)

        self.assertEqual(service.document_data, self.doc_data1)
        self.assertEqual(service.document_data.data_pool, self.dp)

    def test_run_checks_with_content_check(self):
        """Test run_checks method with content check."""
        # Clear any existing relationships and reset flags
        DocumentRelationship.objects.filter(data_pool=self.dp).delete()
        self.doc_data2.is_content_check_completed = False
        self.doc_data2.content_hash = None
        self.doc_data2.save()

        def mock_get_transformed_results(self):
            return [{"field1": "value1", "field2": "value2"}]

        with patch("extract.models.Extraction.get_transformed_results", mock_get_transformed_results):
            service = DuplicateDetectionService(self.doc_data2.pk)
            service.run_checks(["CONTENT"])

            self.doc_data2.refresh_from_db()
            self.assertTrue(self.doc_data2.is_content_check_completed)
            self.assertIsNotNone(self.doc_data2.content_hash)

    def test_run_checks_with_multiple_check_types(self):
        """Test run_checks method with multiple check types."""
        # Reset all check flags
        self.doc_data1.is_md5_check_completed = False
        self.doc_data1.is_label_check_completed = False
        self.doc_data1.is_content_check_completed = False
        self.doc_data1.save()

        service = DuplicateDetectionService(self.doc_data1.pk)
        service.run_checks(["MD5", "LABEL", "CONTENT"])

        # Verify all checks were completed
        self.doc_data1.refresh_from_db()
        self.assertTrue(self.doc_data1.is_md5_check_completed)
        self.assertTrue(self.doc_data1.is_label_check_completed)
        self.assertTrue(self.doc_data1.is_content_check_completed)

    def test_run_checks_skips_already_completed(self):
        """Test that run_checks skips already completed checks."""
        # Mark content check as already completed
        self.doc_data1.is_content_check_completed = True
        self.doc_data1.content_hash = "existing_hash"
        self.doc_data1.save()

        service = DuplicateDetectionService(self.doc_data1.pk)
        service.run_checks(["CONTENT"])

        # Verify hash wasn't recalculated
        self.doc_data1.refresh_from_db()
        self.assertEqual(self.doc_data1.content_hash, "existing_hash")

    def test_run_checks_with_empty_check_types(self):
        """Test run_checks method with empty check types list."""
        # Reset check flags
        self.doc_data1.is_md5_check_completed = False
        self.doc_data1.is_label_check_completed = False
        self.doc_data1.is_content_check_completed = False
        self.doc_data1.save()

        service = DuplicateDetectionService(self.doc_data1.pk)
        service.run_checks([])

        # Verify no checks were performed
        self.doc_data1.refresh_from_db()
        self.assertFalse(self.doc_data1.is_md5_check_completed)
        self.assertFalse(self.doc_data1.is_label_check_completed)
        self.assertFalse(self.doc_data1.is_content_check_completed)

    def test_check_md5_duplicates_with_duplicates(self):
        """Test MD5 duplicate detection when duplicates exist."""
        # Clear any existing relationships first
        DocumentRelationship.objects.filter(data_pool=self.dp).delete()

        # Create documents with same MD5 and unique labels
        doc1, _ = create_document(
            data_pool=self.dp, label="md5_test1.pdf", content_md5="same_md5", status=DocumentDataStatus.VERIFIED.name
        )
        doc2, _ = create_document(
            data_pool=self.dp, label="md5_test2.pdf", content_md5="same_md5", status=DocumentDataStatus.VERIFIED.name
        )

        # Get DocumentData objects
        doc_data1 = DocumentData.objects.get(document=doc1, data_pool=self.dp)
        doc_data2 = DocumentData.objects.get(document=doc2, data_pool=self.dp)

        # Reset all relationships and flags
        for doc_data in [doc_data1, doc_data2]:
            doc_data.canonical_document_data = None
            doc_data.parent_file_id = None
            doc_data.relationship_to_parent = None
            doc_data.is_md5_check_completed = False
            doc_data.save()

        # Set timestamps for canonical ordering
        now = timezone.now()
        doc_data1.received_at = now - timedelta(minutes=1)
        doc_data1.save()
        doc_data2.received_at = now
        doc_data2.save()

        # Run MD5 duplicate check on doc2
        service = DuplicateDetectionService(doc_data2.pk)
        service.run_checks(["MD5"])

        # Verify relationship was created
        relationship = DocumentRelationship.objects.filter(
            source_document_data=doc_data1, target_document_data=doc_data2, relationship_type__label="MD5_DUPLICATE"
        ).first()

        self.assertIsNotNone(relationship)

        # Verify canonical pointer was set
        doc_data2.refresh_from_db()
        self.assertEqual(doc_data2.canonical_document_data, doc_data1)
        self.assertEqual(doc_data2.parent_file_id, str(doc_data1.document.obfuscated_id))
        self.assertEqual(doc_data2.relationship_to_parent, "MD5_DUPLICATE")

    def test_check_md5_duplicates_without_duplicates(self):
        """Test MD5 duplicate detection when no duplicates exist."""
        doc1, _ = create_document(
            data_pool=self.dp, label="unique_doc1.pdf", content_md5="md5_1", status=DocumentDataStatus.VERIFIED.name
        )
        doc2, _ = create_document(
            data_pool=self.dp, label="unique_doc2.pdf", content_md5="md5_2", status=DocumentDataStatus.VERIFIED.name
        )

        # Get DocumentData objects
        doc_data2 = DocumentData.objects.get(document=doc2, data_pool=self.dp)

        # Clear any existing relationships
        DocumentRelationship.objects.filter(data_pool=self.dp).delete()

        # Run MD5 duplicate check on doc2
        service = DuplicateDetectionService(doc_data2.pk)
        service.run_checks(["MD5"])

        # Verify no relationships were created
        relationship_count = DocumentRelationship.objects.filter(
            target_document_data=doc_data2, relationship_type__label="MD5_DUPLICATE"
        ).count()

        self.assertEqual(relationship_count, 0)

        # Verify no canonical pointer was set
        doc_data2.refresh_from_db()
        self.assertIsNone(doc_data2.canonical_document_data)
        self.assertIsNone(doc_data2.parent_file_id)
        self.assertIsNone(doc_data2.relationship_to_parent)

    def test_check_label_duplicates_with_duplicates(self):
        """Test label duplicate detection when duplicates exist."""
        # Clear any existing relationships first
        DocumentRelationship.objects.filter(data_pool=self.dp).delete()

        # Create documents with same label (unique from setup)
        doc1, _ = create_document(
            data_pool=self.dp, label="label_dup_test.pdf", status=DocumentDataStatus.VERIFIED.name
        )
        doc2, _ = create_document(
            data_pool=self.dp, label="label_dup_test.pdf", status=DocumentDataStatus.VERIFIED.name
        )

        # Get DocumentData objects
        doc_data1 = DocumentData.objects.get(document=doc1, data_pool=self.dp)
        doc_data2 = DocumentData.objects.get(document=doc2, data_pool=self.dp)

        # Reset all relationships and flags
        for doc_data in [doc_data1, doc_data2]:
            doc_data.canonical_document_data = None
            doc_data.parent_file_id = None
            doc_data.relationship_to_parent = None
            doc_data.is_label_check_completed = False
            doc_data.save()

        # Set timestamps for canonical ordering
        now = timezone.now()
        doc_data1.received_at = now - timedelta(minutes=1)
        doc_data1.save()
        doc_data2.received_at = now
        doc_data2.save()

        # Run label duplicate check on doc2
        service = DuplicateDetectionService(doc_data2.pk)
        service.run_checks(["LABEL"])

        # Verify relationship was created
        relationship = DocumentRelationship.objects.filter(
            source_document_data=doc_data1, target_document_data=doc_data2, relationship_type__label="LABEL_DUPLICATE"
        ).first()

        self.assertIsNotNone(relationship)

        # Verify canonical pointer was set
        doc_data2.refresh_from_db()
        self.assertEqual(doc_data2.canonical_document_data, doc_data1)
        self.assertEqual(doc_data2.parent_file_id, str(doc_data1.document.obfuscated_id))
        self.assertEqual(doc_data2.relationship_to_parent, "LABEL_DUPLICATE")

    def test_check_label_duplicates_without_duplicates(self):
        """Test label duplicate detection when no duplicates exist."""
        # Clear any existing relationships first
        DocumentRelationship.objects.filter(data_pool=self.dp).delete()

        doc1, _ = create_document(data_pool=self.dp, label="unique_label1.pdf", status=DocumentDataStatus.VERIFIED.name)
        doc2, _ = create_document(data_pool=self.dp, label="unique_label2.pdf", status=DocumentDataStatus.VERIFIED.name)

        # Get DocumentData objects
        doc_data2 = DocumentData.objects.get(document=doc2, data_pool=self.dp)

        # Reset relationships and flags
        doc_data2.canonical_document_data = None
        doc_data2.parent_file_id = None
        doc_data2.relationship_to_parent = None
        doc_data2.is_label_check_completed = False
        doc_data2.save()

        # Run label duplicate check on doc2
        service = DuplicateDetectionService(doc_data2.pk)
        service.run_checks(["LABEL"])

        # Verify no relationships were created
        relationship_count = DocumentRelationship.objects.filter(
            target_document_data=doc_data2, relationship_type__label="LABEL_DUPLICATE"
        ).count()

        self.assertEqual(relationship_count, 0)

        # Verify no canonical pointer was set
        doc_data2.refresh_from_db()
        self.assertIsNone(doc_data2.canonical_document_data)
        self.assertIsNone(doc_data2.parent_file_id)
        self.assertIsNone(doc_data2.relationship_to_parent)


class ContentDuplicatePackageIntegrationTests(TransactionTestCase):
    """Integration tests for content duplicate detection with package creation."""

    def setUp(self):
        """Set up test data using helper functions."""
        self.org, _ = create_organization()
        self.dp, _ = create_data_pool(organization=self.org)

        # Create packager with content deduplication and duplicate exclusion enabled
        self.packager = create_packager(
            data_pool=self.dp, deduplication_content_fields=["field1", "field2"], exclude_duplicates_from_delivery=True
        )

        # Ensure relationship types exist
        self.relationship_types = {}
        for label in ["CONTENT_DUPLICATE", "MD5_DUPLICATE", "LABEL_DUPLICATE"]:
            rel_type, _ = DocumentRelationshipType.objects.get_or_create(
                label=label, data_pool=self.dp, defaults={"description": f"Documents with {label.lower()} relationship"}
            )
            self.relationship_types[label] = rel_type

        # Create training infrastructure
        self.training_set, _ = create_training_set(data_pool=self.dp)
        self.training_revision, _ = create_training_revision(data_pool=self.dp, training_set=self.training_set)

    def test_package_creation_excludes_content_duplicates(self):
        """Test that package creation excludes content duplicates when configured."""
        # Create documents with same content
        doc1, _ = create_document(data_pool=self.dp, label="doc1.pdf", status=DocumentDataStatus.VERIFIED.name)
        doc2, _ = create_document(data_pool=self.dp, label="doc2.pdf", status=DocumentDataStatus.VERIFIED.name)
        doc3, _ = create_document(data_pool=self.dp, label="doc3.pdf", status=DocumentDataStatus.VERIFIED.name)

        # Create extractions - doc1 and doc2 have same content, doc3 is different
        extraction1, _ = create_extraction(
            data_pool=self.dp,
            document=doc1,
            training_revision=self.training_revision,
            _raw_results={"field1": "same_content", "field2": "same_value"},
        )

        extraction2, _ = create_extraction(
            data_pool=self.dp,
            document=doc2,
            training_revision=self.training_revision,
            _raw_results={"field1": "same_content", "field2": "same_value"},  # Same as doc1
        )

        extraction3, _ = create_extraction(
            data_pool=self.dp,
            document=doc3,
            training_revision=self.training_revision,
            _raw_results={"field1": "different", "field2": "content"},  # Different
        )

        # Get DocumentData objects
        doc_data1 = DocumentData.objects.get(document=doc1, data_pool=self.dp)
        doc_data2 = DocumentData.objects.get(document=doc2, data_pool=self.dp)
        doc_data3 = DocumentData.objects.get(document=doc3, data_pool=self.dp)

        def mock_get_transformed_results(self):
            if self.document.label == "doc1.pdf":
                return [{"field1": "same_content", "field2": "same_value"}]
            elif self.document.label == "doc2.pdf":
                return [{"field1": "same_content", "field2": "same_value"}]  # Same as doc1
            elif self.document.label == "doc3.pdf":
                return [{"field1": "different", "field2": "content"}]  # Different
            else:
                return [{"field1": "same_content", "field2": "same_value"}]

        with patch("extract.models.Extraction.get_transformed_results", mock_get_transformed_results):
            # Clear any existing relationships first
            DocumentRelationship.objects.filter(data_pool=self.dp).delete()

            # Reset content check flags
            for doc_data in [doc_data1, doc_data2, doc_data3]:
                doc_data.is_content_check_completed = False
                doc_data.content_hash = None
                doc_data.canonical_document_data = None
                doc_data.parent_file_id = None
                doc_data.relationship_to_parent = None
                doc_data.save()

            service1 = DuplicateDetectionService(doc_data1.pk)
            service1.run_checks(["CONTENT"])

            service2 = DuplicateDetectionService(doc_data2.pk)
            service2.run_checks(["CONTENT"])

            doc_data2.refresh_from_db()
            self.assertEqual(doc_data2.canonical_document_data, doc_data1)
            self.assertEqual(doc_data2.relationship_to_parent, "CONTENT_DUPLICATE")

        # Create package
        package_service = PackageCreationService(self.packager)
        document_data_list = [doc_data1, doc_data2, doc_data3]
        package = package_service.create_package(document_data_list)

        # Verify package entries
        entry1 = PackageEntry.objects.get(package=package, document_data=doc_data1)
        entry2 = PackageEntry.objects.get(package=package, document_data=doc_data2)
        entry3 = PackageEntry.objects.get(package=package, document_data=doc_data3)

        self.assertIn(entry1.status_in_package, [PackageEntryStatus.INCLUDED.name, PackageEntryStatus.PENDING.name])

        # Duplicate should be excluded
        self.assertEqual(entry2.status_in_package, PackageEntryStatus.EXCLUDED_AS_DUPLICATE.name)

        self.assertIn(entry3.status_in_package, [PackageEntryStatus.INCLUDED.name, PackageEntryStatus.PENDING.name])

    def test_package_creation_includes_content_duplicates_when_disabled(self):
        """Test that package creation includes content duplicates when exclusion is disabled."""
        # Disable duplicate exclusion
        self.packager.exclude_duplicates_from_delivery = False
        self.packager.save()

        # Create documents with same content
        doc1, _ = create_document(data_pool=self.dp, label="doc1.pdf", status=DocumentDataStatus.VERIFIED.name)
        doc2, _ = create_document(data_pool=self.dp, label="doc2.pdf", status=DocumentDataStatus.VERIFIED.name)

        # Create extractions with same content
        extraction1, _ = create_extraction(
            data_pool=self.dp,
            document=doc1,
            training_revision=self.training_revision,
            _raw_results={"field1": "same_content", "field2": "same_value"},
        )

        def mock_get_transformed_results1():
            return [{"field1": "same_content", "field2": "same_value"}]

        extraction1.get_transformed_results = mock_get_transformed_results1

        extraction2, _ = create_extraction(
            data_pool=self.dp,
            document=doc2,
            training_revision=self.training_revision,
            _raw_results={"field1": "same_content", "field2": "same_value"},  # Same as doc1
        )

        def mock_get_transformed_results2():
            return [{"field1": "same_content", "field2": "same_value"}]

        extraction2.get_transformed_results = mock_get_transformed_results2

        # Get DocumentData objects
        doc_data1 = DocumentData.objects.get(document=doc1, data_pool=self.dp)
        doc_data2 = DocumentData.objects.get(document=doc2, data_pool=self.dp)

        from unittest.mock import patch

        def mock_get_transformed_results(self):
            if hasattr(self, "_raw_results"):
                return [self._raw_results]
            return [{"field1": "same_content", "field2": "same_value"}]

        with patch("extract.models.Extraction.get_transformed_results", mock_get_transformed_results):
            DocumentRelationship.objects.filter(data_pool=self.dp).delete()

            for doc_data in [doc_data1, doc_data2]:
                doc_data.is_content_check_completed = False
                doc_data.content_hash = None
                doc_data.canonical_document_data = None
                doc_data.parent_file_id = None
                doc_data.relationship_to_parent = None
                doc_data.is_md5_check_completed = True
                doc_data.is_label_check_completed = True
                doc_data.save()

            service1 = DuplicateDetectionService(doc_data1.pk)
            service1.run_checks(["CONTENT"])

            service2 = DuplicateDetectionService(doc_data2.pk)
            service2.run_checks(["CONTENT"])

            doc_data1.refresh_from_db()
            doc_data2.refresh_from_db()
            self.assertEqual(doc_data2.canonical_document_data, doc_data1)

            doc_data1.is_content_check_completed = True
            doc_data1.save()
            doc_data2.is_content_check_completed = True
            doc_data2.save()

        package_service = PackageCreationService(self.packager)
        document_data_list = [doc_data1, doc_data2]
        package = package_service.create_package(document_data_list)

        # Verify both entries are included when exclusion is disabled
        entry1 = PackageEntry.objects.get(package=package, document_data=doc_data1)
        entry2 = PackageEntry.objects.get(package=package, document_data=doc_data2)

        self.assertEqual(entry1.status_in_package, PackageEntryStatus.INCLUDED.name)
        self.assertEqual(entry2.status_in_package, PackageEntryStatus.INCLUDED.name)

    def test_content_hash_calculation_and_storage_integration(self):
        """Test that content hashes are calculated and stored correctly during duplicate detection."""
        # Create document
        doc, _ = create_document(data_pool=self.dp, label="test.pdf", status=DocumentDataStatus.VERIFIED.name)

        extraction, _ = create_extraction(
            data_pool=self.dp,
            document=doc,
            training_revision=self.training_revision,
            _raw_results={"field1": "test_content", "field2": "test_value"},
        )

        def mock_get_transformed_results():
            return [{"field1": "test_content", "field2": "test_value"}]

        extraction.get_transformed_results = mock_get_transformed_results

        doc_data = DocumentData.objects.get(document=doc, data_pool=self.dp)

        doc_data.content_hash = None
        doc_data.is_content_check_completed = False
        doc_data.save()

        self.assertIsNone(doc_data.content_hash)
        self.assertFalse(doc_data.is_content_check_completed)

        from unittest.mock import patch

        def mock_get_transformed_results(self):
            logger.debug(f"mock_get_transformed_results called for extraction {self.pk}")
            return [{"field1": "test_content", "field2": "test_value"}]

        with patch("extract.models.Extraction.get_transformed_results", mock_get_transformed_results):
            service = DuplicateDetectionService(doc_data.pk)
            service.run_checks(["CONTENT"])

            doc_data.refresh_from_db()
            self.assertIsNotNone(doc_data.content_hash)
            self.assertTrue(doc_data.is_content_check_completed)

            original_hash = doc_data.content_hash

            doc_data.content_hash = None
            doc_data.is_content_check_completed = False
            doc_data.save()

            service = DuplicateDetectionService(doc_data.pk)
            service.run_checks(["CONTENT"])

            doc_data.refresh_from_db()
            self.assertEqual(doc_data.content_hash, original_hash)
