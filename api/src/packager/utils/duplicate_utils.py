import logging
from typing import Dict, List

from django.apps import apps
from django.db import models

logger = logging.getLogger(__name__)


def apply_duplicate_flags(data_row: dict, doc_data, for_json: bool = False) -> dict:
    """
    Apply duplicate flags to a data row using only DocumentRelationship as the source of truth.

    Args:
        data_row: The data row to modify
        doc_data: The DocumentData instance
        for_json: Format output for JSON (boolean values) or CSV (string values)

    Returns:
        Modified data row with duplicate flags
    """
    DocumentData = apps.get_model("packager", "DocumentData")

    if not isinstance(doc_data, DocumentData):
        logger.error("doc_data argument must be a DocumentData instance")
        return data_row

    if doc_data.pk is not None:
        try:
            doc_data.refresh_from_db()
        except DocumentData.DoesNotExist:
            logger.warning(f"DocumentData with pk {doc_data.pk} no longer exists. Cannot apply duplicate flags.")
            return data_row
        except Exception as e:
            logger.exception(f"Error refreshing DocumentData with pk {doc_data.pk}: {str(e)}")

    if doc_data.id is None:
        logger.warning("Skipping duplicate flags for unsaved DocumentData instance")
        return data_row

    DocumentData = apps.get_model("packager", "DocumentData")
    DocumentRelationship = apps.get_model("packager", "DocumentRelationship")
    DocumentRelationshipType = apps.get_model("packager", "DocumentRelationshipType")

    # Query all duplicate relationships for this doc (as source or target)
    duplicate_types = DocumentRelationshipType.get_duplicate_types()
    rels_as_source = DocumentRelationship.objects.filter(source_document_data_id=doc_data.id,
                                                         relationship_type__in=duplicate_types).select_related(
        "target_document_data__document")
    rels_as_target = DocumentRelationship.objects.filter(target_document_data_id=doc_data.id,
                                                         relationship_type__in=duplicate_types).select_related(
        "source_document_data__document")

    # Check if this document is a duplicate (target of a relationship)
    is_duplicate = rels_as_target.exists()
    duplicate_of = None
    if is_duplicate:
        rel = rels_as_target.first()
        if rel and rel.source_document_data and rel.source_document_data.document:
            duplicate_of = rel.source_document_data.document.obfuscated_id

    # Check if this document has duplicates (source of relationships)
    has_duplicates = rels_as_source.exists()
    duplicate_doc_ids = [rel.target_document_data.document.obfuscated_id for rel in rels_as_source
                         if rel.target_document_data
                         and rel.target_document_data.document
                         and rel.target_document_data.document.obfuscated_id]
    unique_duplicate_doc_ids = sorted(set(duplicate_doc_ids))

    if duplicate_of:
        data_row["duplicate_of"] = duplicate_of
        data_row["canonical_document_data"] = duplicate_of

    if for_json:
        data_row["is_duplicate"] = is_duplicate
        data_row["has_duplicates"] = has_duplicates
    else:
        data_row["is_duplicate"] = "True" if is_duplicate else "False"
        data_row["has_duplicates"] = "True" if has_duplicates else "False"

    if has_duplicates:
        data_row["duplicate_doc_ids"] = ",".join(unique_duplicate_doc_ids)

    return data_row


def filter_duplicate_documents(doc_data_list, exclude_duplicates_config: bool, is_simulation_mode: bool = False):
    """
    Filter out duplicate documents from a list based on DocumentRelationship records.

    Args:
        doc_data_list: List of DocumentData instances to filter
        exclude_duplicates_config: Whether to exclude duplicates
        is_simulation_mode: Whether this is a simulation (respects exclude_duplicates_config)

    Returns:
        List of DocumentData instances with duplicates filtered out if configured
    """
    if not exclude_duplicates_config:
        return doc_data_list

    if not doc_data_list:
        return doc_data_list

    DocumentRelationship = apps.get_model("packager", "DocumentRelationship")
    DocumentRelationshipType = apps.get_model("packager", "DocumentRelationshipType")
    duplicate_types = DocumentRelationshipType.get_duplicate_types()

    doc_ids = [doc.id for doc in doc_data_list if doc.id is not None]
    if not doc_ids:
        return doc_data_list

    duplicate_ids = set(DocumentRelationship.objects.filter(target_document_data_id__in=doc_ids,
                                                            relationship_type__in=duplicate_types).values_list(
        'target_document_data_id', flat=True))

    filtered_docs = []
    for doc_data_item in doc_data_list:
        if doc_data_item.id not in duplicate_ids:
            filtered_docs.append(doc_data_item)
        else:
            doc_id_for_log = doc_data_item.document.obfuscated_id if doc_data_item.document else "Unknown"
            logger.debug(f"Filtering out duplicate document {doc_id_for_log} (target in duplicate relationship)")

    return filtered_docs


def get_document_relationships(document_obfuscated_ids: List[str], data_pool_id: int) -> List[Dict]:
    """
    Get document relationships for the given list of document obfuscated IDs.

    This function returns relationships (like duplicate relationships) between documents
    represented by their obfuscated IDs.

    Args:
        document_obfuscated_ids: A list of document obfuscated IDs to get relationships for.
        data_pool_id: The ID of the DataPool to scope the queries.

    Returns:
        List of dictionaries containing relationship details with source and target document IDs
    """
    from documents.models import Document

    DocumentData = apps.get_model("packager", "DocumentData")
    DocumentRelationship = apps.get_model("packager", "DocumentRelationship")

    if not document_obfuscated_ids:
        return []

    document_obfuscated_ids_set = set(document_obfuscated_ids)
    result = []

    document_ids = []
    for obfuscated_id in document_obfuscated_ids_set:
        try:
            document_id = Document.get_unobfuscated_id(obfuscated_id)
            document_ids.append(document_id)
        except Exception as e:
            logger.error(f"Error unobfuscating document ID {obfuscated_id}: {e}")

    if not document_ids:
        return []

    doc_data_queryset = DocumentData.objects.filter(
        document_id__in=document_ids, data_pool_id=data_pool_id).select_related("document",
                                                                                "canonical_document_data__document")

    doc_data_instances_list = list(doc_data_queryset)

    if not doc_data_instances_list:
        return []

    relevant_doc_data_pks = [dd.pk for dd in doc_data_instances_list if dd.pk is not None]

    if relevant_doc_data_pks:
        relationships_from_db = DocumentRelationship.objects.filter(
            models.Q(
                source_document_data_id__in=relevant_doc_data_pks) | models.Q(
                target_document_data_id__in=relevant_doc_data_pks)).select_related("source_document_data__document",
                                                                                   "target_document_data__document",
                                                                                   "relationship_type")

        for rel in relationships_from_db:
            source_doc_id_db = None
            target_doc_id_db = None

            if rel.source_document_data and rel.source_document_data.document:
                source_doc_id_db = rel.source_document_data.document.obfuscated_id

            if rel.target_document_data and rel.target_document_data.document:
                target_doc_id_db = rel.target_document_data.document.obfuscated_id

            if source_doc_id_db and target_doc_id_db:
                result.append({"source_document_id": source_doc_id_db, "target_document_id": target_doc_id_db,
                               "relationship_type": rel.relationship_type.label
                               if rel.relationship_type else "UNKNOWN_DB_REL",
                               "id": rel.id, })

    for dd_instance in doc_data_instances_list:
        if dd_instance.canonical_document_data and dd_instance.canonical_document_data_id != dd_instance.id:
            if (
                    dd_instance.document and dd_instance.document.obfuscated_id
                    and dd_instance.canonical_document_data.document
                    and dd_instance.canonical_document_data.document.obfuscated_id):
                source_id_canonical = dd_instance.document.obfuscated_id
                target_id_canonical = dd_instance.canonical_document_data.document.obfuscated_id

                result.append({"source_document_id": source_id_canonical, "target_document_id": target_id_canonical,
                               "relationship_type": "CANONICAL_POINTER", "id": None, })
            else:
                logger.warning(f"Skipping canonical relationship for DocumentData ID {dd_instance.id} due to missing "
                               f"document or obfuscated_id for itself or its canonical DocumentData "
                               f"(Canonical ID: {dd_instance.canonical_document_data_id}).")

    return result
