# Boxcars priority level constants correspond to their numeric values which is passed to boxcars.
# Note that the values must be JSON-serializable in order to pass to celery tasks.
BOXCARS_PRIORITY_DEFAULT = 0  # default priority
BOXCARS_PRIORITY_INTERACTIVE = 1  # higher priority for jobs on behalf of interactive user

# Set of all boxcars priority values
ALL_BOXCARS_PRIORITIES = {
    BOXCARS_PRIORITY_DEFAULT,
    BOXCARS_PRIORITY_INTERACTIVE
}
