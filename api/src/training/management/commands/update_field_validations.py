from django.core.management.base import BaseCommand

from organizations.models import DataPool, Organization
from training.models import Field, TrainingSet

DEFAULT_VALIDATORS = ['RequiredFieldValidator', 'CrossCheckValidator']


class Command(BaseCommand):
    """
    Admin command for forcing fields to update their relevant validation rules by calling
    the `update_validation_rules` on fields.
    `python manage.py fix_data_types [-u | --update] [--org <id>] [--datapool <id>] [--trainingset <id>]`
    """
    help = "Force fields to update their validation rules"

    def add_arguments(self, parser):
        parser.add_argument('-u', '--update', action='store_true', default=False,
                            help='Update the validation rules for the fields')
        parser.add_argument('-o', '--org', action='store', default=None,
                            help='Organization ID to filter fields by')
        parser.add_argument('-d', '--datapool', action='store', default=None,
                            help='DataPool ID to filter fields by')
        parser.add_argument('-t', '--trainingset', action='store', default=None,
                            help='TrainingSet ID to filter fields by')

    def handle(self, *args, **options):
        # Figure out which fields should have default validators
        org_id = options.get('org')
        datapool_id = options.get('datapool')
        training_set_id = options.get('trainingset')
        update = options.get('update')
        if org_id:
            org = Organization.get_object_by_obfuscated_id(org_id)
            fields = Field.objects.filter(data_pool__organization=org)
        elif datapool_id:
            datapool = DataPool.get_object_by_obfuscated_id(datapool_id)
            fields = Field.objects.filter(data_pool=datapool)
        elif training_set_id:
            training_set = TrainingSet.get_object_by_obfuscated_id(training_set_id)
            fields = Field.objects.filter(training_set=training_set)
        else:
            self.stdout.write(
                'You must specify at least one of the following: --org, --datapool, or --trainingset'
            )
            return

        # Update fields
        count = 0
        total = fields.count()
        for idx, field in enumerate(fields):
            if update:
                field.update_validation_rules(clear_rules=True, ignore_partial_docs=True)

            count += 1

            if idx > 0 and (idx % 1000) == 0:
                # Give an update on number of fields iterated if we're in update mode, which can take a while
                self.stdout.write(
                    f'[{idx+1}/{total}] Fields processed'
                )

        self.stdout.write(
            f'{count} Fields {"updated" if update else "found. Use --update to update these fields"}.'
        )
