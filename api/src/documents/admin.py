from django.contrib import admin

from organizations.admin import DataPoolAwareModelAdmin as ModelAdmin

from .models import Document, Tokenized, MimeType


# NOTE: Making the data_pool and document fields read-only is to speed up
# display when viewing on the admin page--having them as dropdowns
# overwhelms the server and/or browser.
#
# This also has the consequence that you will not be able to change the data
# pool from the admin, and that you will not be able to create them with the
# admin.
#
# Because the API (usually via GlyntUI) should be creating all of this stuff
# (not the admin), this should not affect anyone. We could do fancier stuff
# here; for example, we could change the fields to hyperlinks to link to the
# data pools/documents, or make a searchable field, but this is likely far
# beyond the scope of how we use the Django admin.


@admin.register(MimeType)
class MimeTypeAdmin(admin.ModelAdmin):
    list_display = ('mime_type', 'is_supported')
    search_fields = ('mime_type',)
    list_filter = ('is_supported',)
    ordering = ('mime_type',)

    def get_readonly_fields(self, request, obj=None):
        if obj:  # Editing an existing object
            return ['mime_type']
        return []


@admin.register(Document)
class DocumentAdmin(ModelAdmin):
    list_display = ('label', 'uuid',)
    readonly_fields = ["data_pool"]

    # make the data pool appear first on the page
    def get_fields(self, request, obj=None, **kwargs):
        fields = super().get_fields(request, obj, **kwargs)
        fields.remove("data_pool")
        fields.insert(0, "data_pool")
        return fields


@admin.register(Tokenized)
class TokenizedAdmin(ModelAdmin):
    list_display = ('document', 'uuid',)
    readonly_fields = ["document", "data_pool"]

    # make the document and data pool appear first on the page
    def get_fields(self, request, obj=None, **kwargs):
        fields = super().get_fields(request, obj, **kwargs)
        fields.remove("data_pool")
        fields.insert(0, "data_pool")
        fields.remove("document")
        fields.insert(0, "document")
        return fields
