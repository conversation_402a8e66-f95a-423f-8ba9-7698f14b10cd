import logging

from glynt_schemas.datapool.config import DocEnrichJoinConfig

from .models import DocumentEnrichments

logger = logging.getLogger(__name__)


class DocEnrichmentJoiner:
    def __init__(self, config: DocEnrichJoinConfig = None):
        self._config = config or DocEnrichJoinConfig()

    def _get_doc_key_value(self, doc):
        doc_val = getattr(doc, self._config.doc_key)
        if self._config.doc_key_split_char:
            split_char = self._config.doc_key_split_char
            split_index = self._config.doc_key_split_index
            try:
                doc_val = doc_val.split(split_char)[split_index]
            except IndexError:
                logger.warning(
                    f"Cannot split doc key value {doc_val} with config {self._config.doc_key_split_char}"
                )
                return None
        return doc_val

    def get_enrichments_row(self, document):
        doc_key = self._get_doc_key_value(document)
        if doc_key:
            # this introspective json field filtering does not work in django 2.2.  Need 3.1.
            enrichment_key_path = self._config.enrichment_key_path.replace('.', '__')
            enrichment_filter_kwargs = {
                "data_pool": document.data_pool,
                enrichment_key_path: doc_key
            }

            # this may be a pretty slow database call if the enrichment key is in the data jsonfield, beware
            enrichments_q = DocumentEnrichments.objects.filter(**enrichment_filter_kwargs)
            if not enrichments_q.exists():
                return None
            elif enrichments_q.count() != 1:
                logger.warning(
                    f"Multiple enrichment rows found for document {document.obfuscated_id} "
                    f"using enrichment key {enrichment_filter_kwargs}, using latest.")
            return enrichments_q.latest("updated_at")
        return None

    def set_enrichment(self, document):
        logger.info(f"Setting enrichment for document {document.obfuscated_id}")
        enrichments_row = self.get_enrichments_row(document)
        if enrichments_row:
            document.enrichment = enrichments_row
            document.save()
        logger.info(f"Enrichment row not found for document {document.obfuscated_id}")
        return
