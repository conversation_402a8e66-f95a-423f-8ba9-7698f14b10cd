from django.db.models import QuerySet, Count, OuterRef, Subquery, Integer<PERSON>ield, Prefetch
from django.apps import apps


class DocumentManifestQuerySet(QuerySet):
    def with_duplicate_counts(self):
        Document = apps.get_model("documents", "Document")
        duplicates_subquery = (
            Document.objects.filter(
                data_pool_id=OuterRef("data_pool_id"),
                content_md5=OuterRef("content_md5"),
            )
            .values("data_pool_id", "content_md5")
            .annotate(dup_count=Count("*"))
            .values("dup_count")
            .order_by()
        )

        return self.annotate(
            duplicate_count=Subquery(
                duplicates_subquery[:1], output_field=IntegerField()
            )
        ).order_by("-id")

    def with_extractions(self):
        Document = apps.get_model("documents", "Document")
        Extraction = Document.extractions.field.model
        extractions_prefetch = Prefetch(
            "extractions",
            queryset=Extraction.objects.order_by("-created_at").prefetch_related(
                "training_set", "extraction_batch"
            ),
        )
        return self.prefetch_related(
            extractions_prefetch,
            "data_pool",
            "data_pool__organization",
            "mime_type",
            "text_content",
        )
