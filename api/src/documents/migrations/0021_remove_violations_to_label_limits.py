# Generated by Django 2.2.24 on 2021-08-06 20:19
import logging
import unicodedata

from django.db import migrations

logger = logging.getLogger(__name__)


def forwards(apps, schema_editor):
    logger.info("Running data migration documents.0021_remove_violations_to_label_limits")
    Document = apps.get_model('documents', 'Document')
    for doc in Document.objects.only('label'):
        if not doc.label.isprintable():
            logger.info(f"Found tag with Non-printable chars: {repr(doc.label)}")
            doc.label = unicodedata.normalize(
                'NFKD',
                doc.label
            ).encode('ascii', 'ignore').decode('ascii')
            doc.save()


class Migration(migrations.Migration):

    dependencies = [
        ('documents', '0020_disable_label_validators'),
    ]

    operations = [
        migrations.RunPython(forwards, migrations.RunPython.noop)
    ]
