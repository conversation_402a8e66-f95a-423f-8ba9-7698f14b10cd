# Generated by Django 2.2.28 on 2025-04-14 21:32

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('organizations', '0019_choices_sync'),
        ('documents', '0036_remake_doc_enrichment_model'),
    ]

    operations = [
        migrations.CreateModel(
            name='DocEnrichmentJoinConfig',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('doc_key', models.<PERSON>r<PERSON>ield(default='inbound_path', max_length=225)),
                ('doc_key_split_char', models.CharField(blank=True, max_length=3, null=True)),
                ('doc_key_split_index', models.IntegerField(blank=True, null=True)),
                ('enrichment_key_path', models.CharField(blank=True, default='enrichment_key', max_length=225)),
                ('data_pool', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='doc_enrich_join_config', to='organizations.DataPool')),
            ],
        ),
    ]
