# Generated by Django 2.2.28 on 2025-04-14 21:31

from django.db import migrations, models
import django.db.models.deletion
import documents.models
import jsonfield.fields


class Migration(migrations.Migration):

    dependencies = [
        ('organizations', '0019_choices_sync'),
        ('documents', '0035_auto_20250328_1943'),
    ]

    operations = [
        migrations.CreateModel(
            name='DocumentEnrichments',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('data', jsonfield.fields.JSONField(default=dict, validators=[documents.models.is_flat_dict_of_strings])),
                ('enrichment_key', models.CharField(blank=True, max_length=350)),
                ('data_pool', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='documents_documentenrichments_related', related_query_name='documents_documentenrichmentss', to='organizations.DataPool')),
            ],
        ),
        migrations.AlterField(
            model_name='document',
            name='enrichments',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='documents', to='documents.DocumentEnrichments'),
        ),
        migrations.DeleteModel(
            name='DocEnrichments',
        ),
        migrations.AddConstraint(
            model_name='documentenrichments',
            constraint=models.UniqueConstraint(condition=models.Q(enrichment_key__isnull=False), fields=('enrichment_key', 'data_pool'), name='unique_doc_identifier_in_dp'),
        ),
    ]
