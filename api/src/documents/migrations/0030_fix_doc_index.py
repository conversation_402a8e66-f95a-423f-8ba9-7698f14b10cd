# Generated by Django 2.2.28 on 2024-11-22 14:16

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('documents', '0029_eb_buble_up_status'),
    ]

    operations = [
        migrations.SeparateDatabaseAndState(
            database_operations=[],
            state_operations=[
                migrations.RemoveIndex(
                    model_name='document',
                    name='data_pool_id_content_md5_idx',
                ),
                migrations.AddIndex(
                    model_name='document',
                    index=models.Index(fields=['data_pool', 'content_md5'], name='data_pool_id_content_md5_idx'),
                ),
            ]
        )
    ]
