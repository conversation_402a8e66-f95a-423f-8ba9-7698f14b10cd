# -*- coding: utf-8 -*-
# Generated by Django 1.11.20 on 2019-05-15 16:41
from __future__ import unicode_literals

import json

from django.db import migrations

from api_storage import storage_client

# This migration hardcodes several model properties in order to run.
# We are expecting that this data migration will not run and make it into the
# for loop on anything but the two existing environments (sandbox and prod).
# Any new envs would have no tokenizeds.


def remote_path(tokenized):
    return '/'.join([str(tokenized.uuid), 'tokenized.json'])


class Migration(migrations.Migration):

    def forwards(apps, schema_editor):
        storage = storage_client()

        Tokenized = apps.get_model('documents', 'Tokenized')

        for tokenized in Tokenized.objects.all():
            try:
                tokenized_obj = storage.download_fileobj(
                    bucket='documents.Tokenized',
                    remote_path=remote_path(tokenized)
                )
                json.loads(tokenized_obj.read().decode())
            except (FileNotFoundError, json.JSONDecodeError, ValueError):
                tokenized.status = 300
                tokenized.save()

    dependencies = [
        ('documents', '0011_remove_document_uniqueness_contraint'),
    ]

    operations = [
        migrations.RunPython(
            forwards,
            migrations.RunPython.noop
        ),
    ]
