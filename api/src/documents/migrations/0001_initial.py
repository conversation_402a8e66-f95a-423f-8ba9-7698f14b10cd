# -*- coding: utf-8 -*-
# Generated by Django 1.11.18 on 2019-02-05 23:01
from __future__ import unicode_literals

import uuid

import django.db.models.deletion
from django.db import migrations, models

import tags.managers


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('tags', '0002_auto_20190204_1950'),
        ('organizations', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Document',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('label', models.CharField(max_length=255)),
                ('content_type', models.CharField(choices=[('application/pdf', 'application/pdf'), ('image/tiff', 'image/tiff')], max_length=15)),
                ('content_md5', models.CharField(max_length=32)),
                ('data_pool', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='documents_document_related', related_query_name='documents_documents', to='organizations.DataPool')),
                ('glynt_tags', tags.managers.GlyntTaggableManager(help_text='A comma-separated list of tags.', through='tags.GlyntTaggedItem', to='tags.GlyntTag', verbose_name='Tags')),
                ('tags', tags.managers.PublicTaggableManager(help_text='A comma-separated list of tags.', through='tags.PublicTaggedItem', to='tags.PublicTag', verbose_name='Tags')),
            ],
            options={
                'ordering': ['created_at'],
            },
        ),
        migrations.AlterUniqueTogether(
            name='document',
            unique_together=set([('label', 'data_pool')]),
        ),
    ]
