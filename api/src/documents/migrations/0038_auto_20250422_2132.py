# Generated by Django 2.2.28 on 2025-04-22 21:32

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('documents', '0037_docenrichmentjoinconfig'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='docenrichmentjoinconfig',
            name='data_pool',
        ),
        migrations.RemoveConstraint(
            model_name='documentenrichments',
            name='unique_doc_identifier_in_dp',
        ),
        migrations.AddConstraint(
            model_name='documentenrichments',
            constraint=models.UniqueConstraint(fields=('enrichment_key', 'data_pool'), name='unique_key_in_dp'),
        ),
        migrations.DeleteModel(
            name='DocEnrichmentJoinConfig',
        ),
    ]
