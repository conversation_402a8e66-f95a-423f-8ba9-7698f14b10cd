# -*- coding: utf-8 -*-
# Generated by Django 2.2.19 on 2021-04-01 17:27
from __future__ import unicode_literals

import logging

from django.db import migrations

logger = logging.getLogger(__name__)


def forwards(apps, schema_editor):
    logger.info("Running data migration documents.0018_change_label_collation")
    logger.info(f"Database vendor: {schema_editor.connection.vendor}")
    if not schema_editor.connection.vendor.startswith('mysql'):
        logger.info("Skipping migration without attempting to change tables COLLATE")
        return

    with schema_editor.connection.cursor() as cursor:
        cursor.execute(
            'ALTER TABLE documents_document MODIFY label VARCHAR(255) CHARACTER SET utf8 COLLATE utf8_bin;'
        )


def reverse(apps, schema_editor):
    logger.info("Reversing data migration documents.0018_change_label_collation")
    logger.info(f"Database vendor: {schema_editor.connection.vendor}")
    if not schema_editor.connection.vendor.startswith('mysql'):
        logger.info("Skipping migration without attempting to revert tables COLLATE")
        return

    with schema_editor.connection.cursor() as cursor:
        cursor.execute(
            'ALTER TABLE documents_document MODIFY label VARCHAR(255) CHARACTER SET utf8 COLLATE utf8_general_ci;'
        )


class Migration(migrations.Migration):

    dependencies = [
        ('documents', '0017_remove_old_status_fields'),
    ]

    operations = [
        migrations.RunPython(forwards, reverse),
    ]
