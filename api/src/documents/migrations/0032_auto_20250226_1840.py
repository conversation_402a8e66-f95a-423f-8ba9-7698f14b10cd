# Generated by Django 2.2.28 on 2025-02-26 18:40

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('documents', '0031_add_target_training_set'),
    ]

    operations = [
        migrations.CreateModel(
            name='DocumentEnrichments',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('document_enrichment_col1', models.CharField(blank=True, max_length=50, null=True)),
                ('document_enrichment_col2', models.Char<PERSON>ield(blank=True, max_length=50, null=True)),
                ('document_enrichment_col3', models.Char<PERSON>ield(blank=True, max_length=50, null=True)),
                ('document_enrichment_col4', models.Char<PERSON>ield(blank=True, max_length=50, null=True)),
                ('document_enrichment_col5', models.Char<PERSON>ield(blank=True, max_length=50, null=True)),
                ('document_enrichment_col6', models.Char<PERSON>ield(blank=True, max_length=50, null=True)),
                ('document_enrichment_col7', models.CharField(blank=True, max_length=50, null=True)),
                ('document_enrichment_col8', models.CharField(blank=True, max_length=50, null=True)),
                ('document_enrichment_col9', models.CharField(blank=True, max_length=50, null=True)),
                ('document_enrichment_col10', models.CharField(blank=True, max_length=50, null=True)),
                ('document_enrichment_col11', models.CharField(blank=True, max_length=50, null=True)),
                ('document_enrichment_col12', models.CharField(blank=True, max_length=50, null=True)),
            ],
        ),
        migrations.AddField(
            model_name='document',
            name='enrichments',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='documents.DocumentEnrichments', unique=True),
        ),
    ]
