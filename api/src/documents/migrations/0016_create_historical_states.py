# -*- coding: utf-8 -*-
# Generated by Django 1.11.29 on 2020-07-10 21:55
from __future__ import unicode_literals

from django.db import migrations

from jobs import States


"""Creates new State objects to represent the status history of tokenization
jobs based on available data.  No jobs should be running during this migrations
thus all tokenized objects will received some terminal status by the end of
this migration."""


def forwards(apps, schema_editor):
    Tokenized = apps.get_model('documents', 'Tokenized')
    State = apps.get_model('jobs', 'State')

    for t in Tokenized.objects.all():
        in_progress_state = State.objects.create(
            tokenized=t,
            state=States.IN_PROGRESS.value
        )

        in_progress_state.created_at = t.created_at
        in_progress_state.save()

        if t.status == 200:
            state = States.SUCCEEDED.value
        else:
            state = States.FAILED.value

        terminal_state = State.objects.create(
            tokenized=t,
            state=state
        )

        terminal_state.created_at = t.updated_at
        terminal_state.save()


def reverse(apps, schema_editor):
    Tokenized = apps.get_model('documents', 'Tokenized')
    State = apps.get_model('jobs', 'State')

    for t in Tokenized.objects.all():
        final_state = State.objects.filter(tokenized=t).last()

        if final_state.state == States.SUCCEEDED.value:
            t.status = 200
        else:
            t.status = 300

        t.finished = True
        t.save()


class Migration(migrations.Migration):

    dependencies = [
        ('documents', '0015_doc_label_length_increase'),
        ('jobs', '0001_initial'),
    ]

    operations = [
        migrations.RunPython(forwards, reverse),
    ]
