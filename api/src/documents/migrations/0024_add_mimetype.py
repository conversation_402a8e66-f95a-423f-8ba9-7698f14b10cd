# Generated by Django 2.2.28 on 2024-02-02 22:26

from django.db import migrations, models
import django.db.models.deletion
from django.core.paginator import Paginator
import jsonfield.fields
import time
import logging

logger = logging.getLogger(__name__)

# When running the migration bulk update in batches of 1000
BATCH_SIZE = 2000

# Original content_type to mime_type mapping
CONTENT_TYPE_MAPPING = {
    100: 'application/pdf',
    200: 'image/jpeg',
    201: 'image/png',
    202: 'image/tiff',
}

LANGUAGE_CHOICES = [
    ("af", "Afrikaans"),
    ("ar", "Arabic"),
    ("bg", "Bulgarian"),
    ("bn", "Bengali"),
    ("ca", "Catalan"),
    ("cs", "Czech"),
    ("cy", "Welsh"),
    ("da", "Danish"),
    ("de", "German"),
    ("el", "Greek"),
    ("en", "English"),
    ("es", "Spanish"),
    ("et", "Estonian"),
    ("fa", "Persian"),
    ("fi", "Finnish"),
    ("fr", "French"),
    ("gu", "Gujarati"),
    ("he", "Hebrew"),
    ("hi", "Hindi"),
    ("hr", "Croatian"),
    ("hu", "Hungarian"),
    ("id", "Indonesian"),
    ("it", "Italian"),
    ("ja", "Japanese"),
    ("kn", "Kannada"),
    ("ko", "Korean"),
    ("lt", "Lithuanian"),
    ("lv", "Latvian"),
    ("mk", "Macedonian"),
    ("ml", "Malayalam"),
    ("mr", "Marathi"),
    ("ne", "Nepali"),
    ("nl", "Dutch"),
    ("no", "Norwegian"),
    ("pa", "Punjabi"),
    ("pl", "Polish"),
    ("pt", "Portuguese"),
    ("ro", "Romanian"),
    ("ru", "Russian"),
    ("sk", "Slovak"),
    ("sl", "Slovenian"),
    ("so", "Somali"),
    ("sq", "Albanian"),
    ("sv", "Swedish"),
    ("sw", "Swahili"),
    ("ta", "Tamil"),
    ("te", "Telugu"),
    ("th", "Thai"),
    ("tl", "Tagalog"),
    ("tr", "Turkish"),
    ("uk", "Ukrainian"),
    ("ur", "Urdu"),
    ("vi", "Vietnamese"),
    ("zh-cn", "Chinese (Simplified)"),
    ("zh-tw", "Chinese (Traditional)"),
]


def forward(apps, schema_editor):
    # Migrate the content_type int field to a FK to MimeType
    logger.info("Running forward migration documents.0024_add_mimetype")
    Document = apps.get_model('documents', 'Document')
    MimeType = apps.get_model('documents', 'MimeType')

    # Generate a mapping of mime_type to MimeType instance
    mime_types = {}
    for mime_type_str in CONTENT_TYPE_MAPPING.values():
        mime_type, created = MimeType.objects.get_or_create(mime_type=mime_type_str, is_supported=True)
        mime_types[mime_type_str] = mime_type

    start_time = time.time()
    last_log_time = start_time
    processed_count = 0

    paginator = Paginator(Document.objects.only('mime_type', 'content_type').all(), BATCH_SIZE)

    for page_number in paginator.page_range:
        page = paginator.page(page_number)
        updates = []

        # Log progress every N seconds
        if time.time() - last_log_time > 30:
            logger.info(f"Forward migration on page {page_number} of {paginator.num_pages}")
            last_log_time = time.time()

        for document in page.object_list:
            content_type_code = document.content_type
            mime_type_str = CONTENT_TYPE_MAPPING.get(content_type_code)
            if mime_type_str:
                document.mime_type = mime_types[mime_type_str]
                updates.append(document)
                processed_count += 1
        Document.objects.bulk_update(updates, ['mime_type'])


def reverse(apps, schema_editor):
    logger.info("Running reverse migration documents.0024_add_mimetype")
    Document = apps.get_model('documents', 'Document')
    reverse_mapping = {v: k for k, v in CONTENT_TYPE_MAPPING.items()}

    start_time = time.time()
    last_log_time = start_time
    processed_count = 0

    paginator = Paginator(Document.objects.only('mime_type', 'content_type').all(), BATCH_SIZE)

    for page_number in paginator.page_range:
        page = paginator.page(page_number)
        updates = []

        # Log progress every N seconds
        if time.time() - last_log_time > 30:
            logger.info(f"Reverse migration on page {page_number} of {paginator.num_pages}")
            last_log_time = time.time()

        # Migrate the mime_type FK back to content_type
        # NOTE: This could be problematic if the new we accumulate new mime_types that
        # are not availabe in the original hardcoded supported list.
        for document in page.object_list:
            mime_type_str = document.mime_type.mime_type
            content_type_code = reverse_mapping.get(mime_type_str)
            if content_type_code:
                document.content_type = content_type_code
                updates.append(document)
                processed_count += 1
        Document.objects.bulk_update(updates, ['content_type'])


class Migration(migrations.Migration):

    dependencies = [
        ('documents', '0023_tokenizer_version_semantic_names'),
    ]

    operations = [
        migrations.CreateModel(
            name='MimeType',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('_persistent_id_map', jsonfield.fields.JSONField(default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('mime_type', models.CharField(max_length=255, unique=True)),
                ('is_supported', models.BooleanField(default=False)),
            ],
            options={
                'abstract': False,
            },
        ),

        # Add the mime_type column to document, allow nulls initially, then we'll populate and disable nulls
        migrations.AddField(
            model_name='document',
            name='mime_type',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='documents.MimeType'),
        ),

        migrations.AddField(
            model_name='document',
            name='format_id',
            field=models.CharField(blank=True, max_length=36, null=True),
        ),
        migrations.AddField(
            model_name='document',
            name='language',
            field=models.CharField(blank=True, max_length=8, choices=LANGUAGE_CHOICES, null=True),
        ),
        migrations.AddField(
            model_name='document',
            name='token_count',
            field=models.IntegerField(default=0),
        ),
        migrations.RunPython(forward, reverse_code=reverse),
        migrations.RemoveField(
            model_name='document',
            name='content_type',
        ),
        # we've populated the mime_type field, we can disable nulls on mime_type
        migrations.AlterField(
            model_name='document',
            name='mime_type',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='documents.MimeType'),
        ),
    ]
