# Generated by Django 2.2.24 on 2021-08-23 23:36

from django.db import migrations

VERSION_MAP = {
    "4.2.1": "ABBYY12",
    "0.0.5": "Google",
    "0.0.4": "AWS",
    "0.0.3": "Azure"
}

REVERSE_MAP = {v: k for k, v in VERSION_MAP.items()}

def forwards(apps, schema_editor):
    Tokenized = apps.get_model('documents', 'Tokenized')

    objs = []
    for tokenized in Tokenized.objects.filter(tokenizer_version__in=VERSION_MAP):
        tokenized.tokenizer_version = VERSION_MAP[tokenized.tokenizer_version]
        objs.append(tokenized)

    Tokenized.objects.bulk_update(objs, ['tokenizer_version'])


def reverse(apps, schema_editor):
    Tokenized = apps.get_model('documents', 'Tokenized')

    objs = []
    for tokenized in Tokenized.objects.filter(tokenizer_version__in=REVERSE_MAP):
        tokenized.tokenizer_version = REVERSE_MAP[tokenized.tokenizer_version]
        objs.append(tokenized)

    Tokenized.objects.bulk_update(objs, ['tokenizer_version'])


class Migration(migrations.Migration):

    dependencies = [
        ('documents', '0022_enable_label_validators'),
    ]

    operations = [
        migrations.RunPython(forwards, reverse)
    ]
