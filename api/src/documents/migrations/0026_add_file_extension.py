# Generated by Django 2.2.28 on 2024-06-20 12:58
import time
import logging

from django.db import migrations, models
from django.core.paginator import Paginator

logger = logging.getLogger(__name__)


def forward(apps, schema_editor):
    """
    Populate the file_extension field based on the current mime_type field.
    """
    Document = apps.get_model('documents', 'Document')
    start_time = time.time()
    last_log_time = start_time

    paginator = Paginator(Document.objects.only('mime_type').all(), 2000)

    for page_number in paginator.page_range:
        page = paginator.page(page_number)
        updates = []

        # Log progress every N seconds
        if time.time() - last_log_time > 30:
            logger.info(f"Forward migration on page {page_number} of {paginator.num_pages}")
            last_log_time = time.time()

        for document in page.object_list:
            if document.mime_type:
                mime_type = document.mime_type.mime_type
                document.file_extension = "." + mime_type.split('/')[-1]
                updates.append(document)
        Document.objects.bulk_update(updates, ['file_extension'])

    logger.info(f"Forward migration completed in {time.time() - start_time:.2f} seconds")


def backward(apps, schema_editor):
    Document = apps.get_model('documents', 'Document')
    Document.objects.update(file_extension=None)


class Migration(migrations.Migration):

    dependencies = [
        ('documents', '0025_add_dpid_md5_doc_index'),
    ]

    operations = [
        migrations.AddField(
            model_name='document',
            name='file_extension',
            field=models.CharField(blank=True, max_length=16, null=True),
        ),
        migrations.RunPython(forward, reverse_code=backward),

    ]
