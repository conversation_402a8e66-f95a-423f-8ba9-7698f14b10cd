# Generated by Django 2.2.28 on 2025-03-28 19:43

from django.db import migrations, models
import django.db.models.deletion
import jsonfield.fields


class Migration(migrations.Migration):

    dependencies = [
        ('organizations', '0019_choices_sync'),
        ('documents', '0034_add_text_preview'),
    ]

    operations = [
        migrations.CreateModel(
            name='DocEnrichments',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('data', jsonfield.fields.J<PERSON><PERSON>ield(default=dict)),
                ('doc_identifier', models.CharField(max_length=125)),
                ('data_pool', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='documents_docenrichments_related', related_query_name='documents_docenrichmentss', to='organizations.DataPool')),
            ],
        ),
        migrations.<PERSON><PERSON><PERSON>ield(
            model_name='document',
            name='enrichments',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='documents.DocEnrichments'),
        ),
        migrations.DeleteModel(
            name='DocumentEnrichments',
        ),
        migrations.AddConstraint(
            model_name='docenrichments',
            constraint=models.UniqueConstraint(fields=('doc_identifier', 'data_pool'), name='unique_doc_identifier_in_dp'),
        ),
    ]
