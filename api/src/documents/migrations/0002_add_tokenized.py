# -*- coding: utf-8 -*-
# Generated by Django 1.11.18 on 2019-02-07 18:07
from __future__ import unicode_literals

import uuid

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('organizations', '0002_remove_datapool_slug'),
        ('documents', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Tokenized',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('tokenizer_version', models.CharField(max_length=31)),
                ('status', models.SmallIntegerField(choices=[(0, 'Pending'), (100, 'In Progress'), (200, 'Success'), (201, 'Failed')])),
                ('finished', models.BooleanField(default=False)),
                ('data_pool', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='documents_tokenized_related', related_query_name='documents_tokenizeds', to='organizations.DataPool')),
            ],
            options={
                'ordering': ['created_at'],
            },
        ),
        migrations.AddField(
            model_name='document',
            name='file_access_url',
            field=models.URLField(default='https://someurl.com'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='tokenized',
            name='document',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tokenizeds', related_query_name='tokenized', to='documents.Document'),
        ),
        migrations.AlterUniqueTogether(
            name='tokenized',
            unique_together=set([('tokenizer_version', 'data_pool', 'document')]),
        ),
    ]
