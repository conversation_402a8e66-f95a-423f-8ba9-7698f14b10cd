# Generated by Django 2.2.28 on 2025-03-19 16:37

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('documents', '0033_add_filesize'),
    ]

    operations = [
        migrations.CreateModel(
            name='TextContent',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('preview', models.TextField(blank=True, help_text='Text preview of the document', max_length=1000, null=True)),
                ('document', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='text_content', related_query_name='text_content', to='documents.Document')),
            ],
        ),
        migrations.AddIndex(
            model_name='textcontent',
            index=models.Index(fields=['document'], name='text_content_document_id_idx'),
        ),
    ]
