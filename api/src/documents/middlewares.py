from django.http import JsonResponse
from django.utils.translation import gettext_lazy as _

from glynt_api.exceptions import BoxcarsUnavailable


class BoxcarsUnavailableResponseMiddleware(object):
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        """
        Process request as usual
        """
        return self.get_response(request)

    def process_exception(self, request, exception):
        """
        Return a 503 JSON response if BoxcarsUnavailable is propagated from
        view.
        """
        if isinstance(exception, BoxcarsUnavailable):
            return JsonResponse({
                'details': _(
                    'Tokenization server is temporarily unavailable, '
                    'please try again later.'
                )
            }, status=503)
