from django.db.models.signals import post_delete, post_save, pre_save
from django.db.transaction import on_commit
from django.dispatch import receiver, Signal
from django.db import transaction
from api_storage.signals import delete_storage_artifacts

from glynt_schemas.datapool.config import NotificationEventType
from glynt_api.util import get_default_tokenizer
from .models import Document, Tokenized
from .tasks import tokenize, create_pages, send_format_id_changed_notification
from notifications.events import ClusterIDAssignment
from notifications.util import get_client_configs

# Custom signal for document creation that is used to trigger the creation
# of a document after any content has been uploaded
document_created = Signal(providing_args=["instance", "created"])


@receiver(post_save, sender=Tokenized)
def create_tokenized(sender, instance, created, **kwargs):
    """Starts a tokenized job on tokenized creation if its status is not
    terminal.

    NOTE: Due to the use of on_commit, this signal will only fire in unit tests
    if TransactionTestCase is used instead of a standard TestCase.
    """
    if created and not instance.finished and not instance._is_copying:
        _process_tokenized_post_save(instance)


def _process_tokenized_post_save(tokenized):
    """Submethod used for test mocking"""
    tokenized.set_state('PENDING')
    on_commit(lambda: tokenize.delay(
        tokenized_id=tokenized.id, boxcars_priority=getattr(tokenized, '_boxcars_priority', None)
    ))


@receiver(document_created, sender=Document)
def generate_pages(sender, instance, created, **kwargs):
    """Starts a job to create pages for a document after it has been saved."""
    # Only files that can tokenize will have pages created successfully
    if created and instance.can_tokenize():
        on_commit(lambda: create_pages.delay(document_id=instance.id))


@receiver(document_created, sender=Document)
def generate_tokenized(sender, instance, created, **kwargs):
    """
    Create a tokenized instance for a document after it has been saved.
    """
    if created and instance.can_tokenize():
        on_commit(lambda: _create_tokenized_for_document(instance))


def _create_tokenized_for_document(document):
    tokenizer_version = get_default_tokenizer()
    Tokenized.objects.create(
        data_pool=document.data_pool,
        document=document,
        tokenizer_version=tokenizer_version,
    )


@receiver(pre_save, sender=Document)
def prepare_document_format_id_notification(sender, instance: Document, **kwargs):
    if instance.pk:
        try:
            old_instance = Document.objects.get(pk=instance.pk)
            instance.__original_format_id = old_instance.format_id
        except Document.DoesNotExist:
            instance.__original_format_id = None


@receiver(post_save, sender=Document)
def handle_document_format_id_assignment(
    sender, instance: Document, created: bool, **kwargs
):
    def send_notification():
        # Get list of clients that should receive the notification (if any)
        clients = get_client_configs(
            NotificationEventType.FORMAT_ID_ASSIGNED,
            instance.data_pool.config
        )

        if not clients:
            # Don't fire task if no clients are configured
            return

        event = ClusterIDAssignment(
            data_pool_id=instance.data_pool.obfuscated_id,
            document_id=instance.obfuscated_id,
            old_format_id=getattr(instance, "__original_format_id", None),
            new_format_id=instance.format_id,
            clients=clients
        )
        # Send serializable types across celery
        send_format_id_changed_notification.delay(event.dict())

    if not created:
        # If format_id is set/changed (after creation), and there are clients configured to receive,
        # then send the notification
        original_format_id = getattr(instance, "__original_format_id", None)
        if original_format_id is None and instance.format_id:
            transaction.on_commit(send_notification)


post_delete.connect(delete_storage_artifacts, sender=Document)
post_delete.connect(delete_storage_artifacts, sender=Tokenized)
