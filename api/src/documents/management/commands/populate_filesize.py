import time
from collections import defaultdict

from django.core.management.base import BaseCommand
from django.core.paginator import Paginator
from documents.models import Document

from api_storage import storage_client


class Command(BaseCommand):
    """
    Admin command for populating the filesize field for all documents using bulk updates.

    Fetches the filesize from S3 and populates the filesize field in batches.
    Preserves NULL filesize values for documents that don't exist in storage.
    Uses bulk_update to reduce the number of database writes.
    """
    help = "Populates the filesize field for all documents"

    def add_arguments(self, parser):
        parser.add_argument(
            '--batch-size',
            type=int,
            default=250,
            help='Number of documents to process in each batch'
        )

    def handle(self, *args, **options):
        log = self.stdout.write
        batch_size = options['batch_size']

        # Query all documents with null filesize
        queryset = Document.objects.filter(
            filesize__isnull=True
        ).order_by('id')
        total_documents = queryset.count()

        id_list = list(queryset.values_list('id', flat=True))
        paginator = Paginator(id_list, batch_size)
        total_pages = paginator.num_pages

        log(f"Populating filesize for {total_documents} documents in {total_pages} pages")

        storage = storage_client()
        start = time.time()
        last_tick = start
        stats = defaultdict(int)
        processed_count = 0

        # Process each page
        for page_num in paginator.page_range:
            page = paginator.page(page_num)
            id_batch = page.object_list
            documents = Document.objects.filter(id__in=id_batch)
            updated_documents = []

            for document in documents:
                try:
                    filesize = storage.get_filesize(document.bucket_name, document.file_remote_path)
                    document.filesize = filesize
                    updated_documents.append(document)
                    stats["success"] += 1
                except Exception as e:
                    if "Not Found" in str(e):
                        # Leave filesize as NULL for non-existent documents
                        stats["not_found"] += 1
                    else:
                        stats["other_error"] += 1
                        self.stderr.write(
                            f"Error getting filesize for {document.id}: {e}"
                        )

            if updated_documents:
                Document.objects.bulk_update(updated_documents, ['filesize'])

            processed_count += len(id_batch)

            # Report progress every minute.
            if time.time() - last_tick > 60:
                elapsed = time.time() - start

                if processed_count > 0:
                    sec_per_doc = elapsed / processed_count
                    est_mins_remaining = (total_documents - processed_count) * sec_per_doc / 60
                else:
                    est_mins_remaining = 0

                log("------")
                log(f"Processed {processed_count} of {total_documents} documents")
                log(f"{processed_count/total_documents*100:.1f}% completed")
                log(f"Estimated remaining: {est_mins_remaining:.2f} mins, elapsed: {elapsed/60:.2f} mins")
                log(f"Success: {stats['success']}, not found: {stats['not_found']}, errors: {stats['other_error']}")
                last_tick = time.time()

        total_time = time.time() - start
        log(f"Completed processing {processed_count} documents in {total_time:.2f} seconds")
        log("Counts:")
        for name, count in stats.items():
            log(f"  {name}: {count}")
