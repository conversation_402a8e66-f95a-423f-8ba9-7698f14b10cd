import time
from collections import defaultdict

from django.core.management.base import BaseCommand
from django.core.paginator import Paginator
from documents.models import Document, Tokenized, TextContent
from django.db.models import Prefetch


class Command(BaseCommand):
    """
    Fetch latest tokenized per document, and save it as text_preview associated with the document.
    """

    help = "Populates the text_preview field for all documents"

    def add_arguments(self, parser):
        parser.add_argument(
            "--batch-size",
            type=int,
            default=200,
            help="Number of documents to process in each batch",
        )

    def handle(self, *args, **options):
        log = self.stdout.write
        batch_size = options["batch_size"]

        doc_ids = list(
            Document.objects.filter(text_content__isnull=True).values_list(
                "id", flat=True
            )
        )

        total_documents = len(doc_ids)

        paginator = Paginator(doc_ids, batch_size)
        total_pages = paginator.num_pages

        log(
            f"Populating text_preview for {total_documents} documents in {total_pages} pages"
        )

        start = time.time()
        last_tick = start
        stats = defaultdict(int)
        processed_count = 0

        # Process each page
        for page_num in paginator.page_range:
            page = paginator.page(page_num)
            batch_ids = page.object_list
            doc_batch = Document.objects.filter(id__in=batch_ids).prefetch_related(
                Prefetch(
                    "tokenizeds", queryset=Tokenized.objects.order_by("-created_at")
                )
            )

            processed_count += len(doc_batch)
            doc_previews = {}
            for doc in doc_batch:
                latest_tokenized = (
                    doc.tokenizeds.first()
                )  # This will be the latest one.
                if not latest_tokenized:
                    stats["no_tokenized"] += 1
                    continue

                if not latest_tokenized.succeeded:
                    stats["not_succeeded"] += 1
                    continue

                # Render the text from the tokenized JSON
                doc_previews[doc.id] = latest_tokenized.render_text_preview()

            existing = {
                tc.document_id: tc
                for tc in TextContent.objects.filter(
                    document_id__in=doc_previews.keys()
                )
            }

            to_update = []
            to_create = []
            for doc_id, text in doc_previews.items():
                if doc_id in existing:
                    tc = existing[doc_id]
                    tc.preview = text
                    to_update.append(tc)
                else:
                    tc = TextContent(document_id=doc_id, preview=text)
                    to_create.append(tc)

            if to_update:
                TextContent.objects.bulk_update(to_update, ["preview"])
                stats["updated_ok"] += len(to_update)
            if to_create:
                TextContent.objects.bulk_create(to_create)
                stats["created_ok"] += len(to_create)

            # Report progress every minute.
            if time.time() - last_tick > 60:
                elapsed = time.time() - start

                if processed_count > 0:
                    sec_per_doc = elapsed / processed_count
                    est_mins_remaining = (
                        (total_documents - processed_count) * sec_per_doc / 60
                    )
                else:
                    est_mins_remaining = 0

                log("------")
                log(f"Processed {processed_count} of {total_documents} documents")
                log(f"{processed_count / total_documents * 100:.1f}% completed")
                log(
                    f"Estimated remaining: {est_mins_remaining:.2f} mins, elapsed: {elapsed / 60:.2f} mins"
                )
                last_tick = time.time()

        total_time = time.time() - start
        log(
            f"Completed processing {processed_count} documents in {total_time:.2f} seconds"
        )
        log("Stats:")
        for k, v in stats.items():
            log(f"{k}: {v}")
