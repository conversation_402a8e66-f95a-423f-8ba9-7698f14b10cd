from django.conf.urls import url

from glynt_api.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

from . import views

router = DefaultRouter()
router.register(r'documents', views.DocumentViewSet)
router.register(r'tokenizeds', views.TokenizedViewSet)
router.register(r'document-enrichments', views.DocumentEnrichmentsViewSet)

unscoped_router = DefaultRouter()
unscoped_router.register(r'documents', views.UnscopedDocumentViewSet, basename='unscoped-document')

tokenizer_versions_urlpattern = [
    url(r'^tokenizer-versions/', views.TokenizerVersionsView.as_view(), name='tokenizer-versions'),
]

document_manifest_urlpattern = [
    url(r'^data-pools/(?P<data_pools_obfuscated_id>[^/]+)/manifest.csv$',
        views.DocumentManifestCSVView.as_view(),
        name='document-manifest-csv'),
]
