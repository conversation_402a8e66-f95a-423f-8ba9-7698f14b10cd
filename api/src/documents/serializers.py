import base64
import logging

from django.conf import settings
from rest_framework.serializers import (
    <PERSON><PERSON><PERSON><PERSON>, IntegerField, SerializerMethodField, ValidationError,
    Serializer, ModelSerializer, DateTimeField, ListField
)

from boxcars.priority import BOXCARS_PRIORITY_INTERACTIVE
from cached_reverse.urls import reverse
from glynt_api.mixins import LabelSerializerMixin
from glynt_api.serializers import HyperlinkedModelSerializer
from glynt_api.relations import Hyperlinked<PERSON><PERSON>ted<PERSON>ield
from glynt_api.util import build_absolute_uri, calculate_content_md5
from organizations.mixins import DataPoolAwareSerializerMixin
from tags.mixins import GlyntTagsSerializerMixin, TagsSerializerMixin
from .models import Document, DocumentEnrichments, MimeType, Tokenized, validate_mime_type
from .signals import document_created
from extract.models import Extraction

logger = logging.getLogger(__name__)


class DocumentEnrichmentSerializer(DataPoolAwareSerializerMixin, ModelSerializer):
    documents = SerializerMethodField(read_only=False, required=False)
    # internal serializer field to allow the 'documents' to be used to also relate docs to enrichment rows
    doc_ids = ListField(read_only=False, required=False, allow_null=True)

    class Meta:
        model = DocumentEnrichments
        exclude = ('data_pool',)

    def get_documents(self, instance):
        if instance.documents.exists():
            return [d.obfuscated_id for d in instance.documents.all().only('id')]
        return []

    def to_internal_value(self, data):
        data['doc_ids'] = data.pop('documents', None)
        return super().to_internal_value(data)

    def to_representation(self, instance):
        dict_repr = super().to_representation(instance)
        dict_repr.pop('doc_ids', None)
        return dict_repr

    def create(self, validated_data):
        doc_ids = validated_data.pop('doc_ids')
        documents = self._get_docs_from_ids(doc_ids)

        instance = super().create(validated_data)
        if doc_ids is not None:
            instance.documents.set(documents)
        return instance

    def update(self, instance, validated_data):
        doc_ids = validated_data.pop('doc_ids')
        documents = self._get_docs_from_ids(doc_ids)

        # upsert data
        if 'data' in validated_data:
            enrichment_data = instance.data
            enrichment_data.update(validated_data['data'])
            validated_data['data'] = enrichment_data

        instance = super().update(instance, validated_data)

        if doc_ids is not None:
            instance.documents.set(documents)

        return instance

    def _get_docs_from_ids(self, doc_ids: list):
        documents = []
        for doc_id in doc_ids or []:
            try:
                documents.append(Document.objects.get(id=Document.get_unobfuscated_id(doc_id)))
            except Document.DoesNotExist:
                raise ValidationError(f'Document {doc_id} does not exist')
        return documents


class BaseDocumentSerializer(Serializer):
    """
    Common fields and methods for both unscoped and scoped Document serializers.
    """
    text_preview = SerializerMethodField(read_only=True)
    enrichments = SerializerMethodField(read_only=True)

    def get_enrichments(self, instance):
        try:
            return instance.enrichments.data
        except (AttributeError, DocumentEnrichments.DoesNotExist):
            return {}

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Remove text_preview field if with_preview is not True
        if not self.context.get('with_preview'):
            self.fields.pop('text_preview', None)

    def get_file_access_url(self, obj):
        """Return the url to retrieve a temp file access url."""
        url_name = '{}:document-file'.format(self.context['request'].version)
        path = reverse(
            url_name, kwargs={
                'data_pools_obfuscated_id': obj.data_pool.obfuscated_id,
                'obfuscated_id': obj.obfuscated_id
            }
        )

        return build_absolute_uri(self.context['request'], path)

    def get_pages(self, obj):
        """Return an array of urls to access page content."""
        page_urls = []
        url_name = '{}:document-pages'.format(self.context['request'].version)
        for page_number in range(1, obj.page_count + 1):
            path = reverse(
                url_name, kwargs={
                    'data_pools_obfuscated_id': obj.data_pool.obfuscated_id,
                    'obfuscated_id': obj.obfuscated_id,
                    'page_number': page_number
                }
            )

            page_url = build_absolute_uri(self.context['request'], path)
            page_urls.append(page_url)

        return page_urls

    def get_text_preview(self, obj):
        """Get the text preview from related TextContent if it exists"""
        if hasattr(obj, 'text_content'):
            return obj.text_content.preview
        return None


class UnscopedExtractionSerializer(ModelSerializer):
    """
    Serializer for extraction models specific to the unscoped document endpoint.
    """
    id = CharField(source='obfuscated_id')
    training_set = CharField(source='training_set.obfuscated_id', allow_null=True)
    training_set_label = CharField(source='training_set.label', allow_null=True)
    extraction_batch = CharField(source='extraction_batch.obfuscated_id', allow_null=True)
    extraction_batch_label = CharField(source='extraction_batch.label', allow_null=True)
    status = SerializerMethodField(read_only=True)
    verified_at = DateTimeField(read_only=True, allow_null=True)

    def get_status(self, obj):
        return obj.get_status_display

    class Meta:
        model = Extraction
        fields = (
            'id', 'created_at', 'updated_at',
            'training_set', 'training_set_label',
            'extraction_batch', 'extraction_batch_label',
            'status', 'data_processing_status', 'finished',
            'verified', 'verified_at', 'verify_expired',
        )


class UnscopedDocumentSerializer(
    BaseDocumentSerializer,
    LabelSerializerMixin,
    TagsSerializerMixin,
    GlyntTagsSerializerMixin,
    HyperlinkedModelSerializer
):

    content_type = CharField(read_only=True)
    file_extension = CharField(read_only=True)
    token_count = IntegerField(read_only=True)
    filesize = IntegerField(read_only=True)
    language = CharField(read_only=True)
    format_id = CharField(read_only=True)
    data_pool_id = CharField(source='data_pool.obfuscated_id', read_only=True)
    data_pool_label = CharField(source='data_pool.label', read_only=True)
    org_id = CharField(source='data_pool.organization.obfuscated_id', read_only=True)
    org_label = CharField(source='data_pool.organization.label', read_only=True)
    duplicate_count = SerializerMethodField(read_only=True)
    pages = SerializerMethodField(read_only=True)
    extractions = SerializerMethodField(read_only=True)
    file_access_url = SerializerMethodField(read_only=True)
    url = SerializerMethodField(read_only=True)
    extractions = UnscopedExtractionSerializer(many=True, read_only=True)

    content_md5 = CharField(
        max_length=Document.CONTENT_MD5_MAX_LENGTH, required=False
    )
    target_training_set = CharField(
        source='target_training_set.obfuscated_id',
        read_only=True,
        allow_null=True,
    )
    target_training_set_label = CharField(
        source='target_training_set.label',
        read_only=True,
        allow_null=True,
    )

    class Meta:
        model = Document
        fields = (
            'id', 'data_pool_id', 'data_pool_label', 'org_id', 'org_label',
            'label', 'created_at', 'updated_at',
            'content_type', 'file_extension', 'inbound_path',
            'token_count', 'filesize', 'language', 'format_id', 'is_supported',
            'content_md5', 'duplicate_count', 'extractions', 'enrichments',
            'pages', 'url', 'file_access_url', 'text_preview',
            'status', 'target_training_set', 'target_training_set_label',
        )

    def get_url(self, obj):
        url_name = '{}:unscoped-document-detail'.format(self.context['request'].version)
        request = self.context['request']
        format = self.context.get('format')
        url = HyperlinkedRelatedField.get_url(obj, url_name, request, format)
        return url

    def get_duplicate_count(self, obj):
        # NOTE: Adjust duplicate count by -1 to exclude the original document.
        return max([getattr(obj, 'duplicate_count', 0) - 1, 0])


class DocumentSerializer(
    BaseDocumentSerializer,
    LabelSerializerMixin,
    TagsSerializerMixin,
    GlyntTagsSerializerMixin,
    DataPoolAwareSerializerMixin,
    HyperlinkedModelSerializer
):
    content = CharField(required=False, allow_blank=True)
    content_type = CharField(required=True)
    token_count = IntegerField(default=0, min_value=0)
    filesize = IntegerField(allow_null=True, required=False, min_value=0)
    language = CharField(max_length=8, allow_blank=True, allow_null=True, required=False)
    format_id = CharField(max_length=36, allow_blank=True, allow_null=True, required=False)
    file_extension = CharField(max_length=16, allow_blank=True, allow_null=True, required=False)
    inbound_path = CharField(max_length=255, allow_blank=True, allow_null=True, required=False)

    content_md5 = CharField(
        max_length=Document.CONTENT_MD5_MAX_LENGTH, required=False
    )
    file_access_url = SerializerMethodField(read_only=True)
    pages = SerializerMethodField(read_only=True)
    related_extractions = SerializerMethodField(read_only=True)

    class Meta:
        model = Document
        fields = (
            'id', 'created_at', 'updated_at', 'url', 'label', 'content_type', 'enrichments',
            'file_extension', 'token_count', 'filesize', 'language', 'format_id', 'is_supported',
            'content_md5', 'content', 'file_access_url', 'pages', 'tokenizeds',
            'inbound_path', 'tags', 'glynt_tags', 'file_upload_url', 'related_extractions',
            'status', 'target_training_set', 'text_preview',
        )
        read_only_fields = ('tokenizeds', 'file_upload_url', 'related_extractions', 'is_supported')
        post_only_fields = ('content', 'content_type', 'content_md5',)

    def create(self, validated_data):
        decoded_content = None
        if 'decoded_content' in validated_data:
            decoded_content = validated_data.pop('decoded_content')

        mime_type_str = validated_data.pop('content_type', None)
        if mime_type_str:
            mime_type, created = MimeType.objects.get_or_create(mime_type=mime_type_str)
            validated_data['mime_type'] = mime_type

        document = super().create(validated_data)

        if decoded_content is not None:
            if len(decoded_content) == 0:
                logger.warning(
                    f'Document content is empty for document {document.obfuscated_id}.'
                )
            try:
                document.store_file_content(decoded_content)
                # Update filesize in the database to match the actual file size
                document.filesize = len(decoded_content)
                document.save(update_fields=['filesize'])
            except Exception:
                logger.error(
                    'Exception uploading document content to storage for document '
                    f'{document.obfuscated_id}. Rolling back Document creation.'
                )
                document.delete()
                raise

        # Send signal after document creation and file storage completes
        document_created.send(sender=Document, instance=document, created=True)
        return document

    def to_representation(self, instance):
        """Extends standard implementation to remove file_upload_url when not a POST."""
        if (
            'file_upload_url' in self.fields and (
                self.context['request'].method != 'POST'
                or instance.file_exists
            )
        ):
            self.fields.pop('file_upload_url')
        if not self.context.get('show_related_extractions'):
            self.fields.pop('related_extractions', None)

        return super().to_representation(instance)

    def get_related_extractions(self, obj: Document):
        if not self.context.get('show_related_extractions'):
            return

        return [{
            'id': extraction.obfuscated_id,
            'created_at': extraction.created_at,
            'updated_at': extraction.updated_at,
            'training_set': extraction.training_set.obfuscated_id,
            'status': extraction.get_status_display,
            'data_processing_status': extraction.data_processing_status,
            'finished': extraction.finished,
            'tags': extraction.tags.names(),
            'glynt_tags': extraction.glynt_tags.names(),
        } for extraction in obj.related_extractions or []]

    def validate_content_type(self, value):
        validate_mime_type(value)
        return value

    def validate(self, data):
        if self.context['request'].method == 'POST':
            if 'content' not in data and 'content_md5' not in data:
                raise ValidationError(
                    'At least one of content or content_md5 must be included during creation.'
                )

            if 'content' in data:
                try:
                    data['decoded_content'] = base64.b64decode(data.pop('content').encode())
                except Exception:
                    raise ValidationError({
                        'content': [
                            'Error decoding content. Check that the content is Base64 encoded.'
                        ]
                    })

                content_md5 = calculate_content_md5(data['decoded_content'])
                # If content_md5 was not provided, store the calculated value in data
                if 'content_md5' not in data:
                    data['content_md5'] = content_md5
                # Otherwise, if content_md5 was provided, validate it
                elif data['content_md5'] != content_md5:
                    raise ValidationError({
                        'content_md5': [
                            f"The calculated content_md5 ({content_md5}) does not "
                            f"match the provided content_md5 ({data['content_md5']})."
                        ]
                    })

                # Always calculate filesize from decoded_content when content is provided
                # This overrides any user-provided filesize
                data['filesize'] = len(data['decoded_content'])

            # Set file_extension if not provided
            if 'file_extension' not in data or not data['file_extension']:
                content_type = data.get('content_type')
                if content_type:
                    data['file_extension'] = "." + content_type.split('/')[-1]

        return super().validate(data)


class DocumentFileSerializer(DataPoolAwareSerializerMixin, HyperlinkedModelSerializer):
    class Meta:
        model = Document
        fields = ('file_temp_url',)
        read_only_fields = ('file_temp_url',)


class DocumentTextPageSerializer(DataPoolAwareSerializerMixin, HyperlinkedModelSerializer):
    data_pool_id = CharField(source='data_pool.obfuscated_id', read_only=True)
    text_pages = SerializerMethodField(read_only=True)

    def get_text_pages(self, obj):
        tokenized = obj.tokenizeds.order_by('-created_at').first()
        if not tokenized or not tokenized.succeeded:
            return None
        return tokenized.render_text_pages()

    class Meta:
        model = Document
        fields = ('id', 'data_pool_id', 'text_pages',)
        read_only_fields = ('id', 'data_pool_id', 'text_pages',)


class DocumentPageSerializer(DataPoolAwareSerializerMixin, HyperlinkedModelSerializer):
    file_temp_url = SerializerMethodField(read_only=True)

    class Meta:
        model = Document
        fields = ('file_temp_url',)
        read_only_fields = ('file_temp_url',)

    def get_file_temp_url(self, obj):
        """Get a page's file_temp_url."""
        # TODO: check what happens if you request a bad page number. handle gracefully.
        page_number = self.context['request'].parser_context['kwargs']['page_number']
        return obj.page_temp_url(int(page_number))


class TokenizedSerializer(
    DataPoolAwareSerializerMixin,
    HyperlinkedModelSerializer
):
    tokenizer_version = CharField(
        max_length=Tokenized.TOKENIZER_VERSION_MAX_LENGTH,
        required=False
    )

    status = SerializerMethodField()

    def get_status(self, obj):
        return obj.get_status_display

    class Meta:
        model = Tokenized
        fields = (
            'id', 'created_at', 'updated_at', 'url', 'document',
            'tokenizer_version', 'status', 'finished', 'tokenized'
        )
        read_only_fields = ('status', 'finished', 'tokenized')
        detail_only_fields = ('tokenized',)

    def create(self, validated_data):
        # Use HTTP_ORIGIN request header to determine if the request comes from Glynt UI, create the Tokenized with
        # INTERACTIVE boxcars priority level if so.
        origin = self.context['request'].META.get("HTTP_ORIGIN")
        if origin and origin == settings.GLYNT_UI_URL:
            validated_data['boxcars_priority'] = BOXCARS_PRIORITY_INTERACTIVE

        tokenized = super().create(validated_data)

        return tokenized
