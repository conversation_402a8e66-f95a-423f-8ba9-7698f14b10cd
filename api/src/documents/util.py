import logging
import os
import io
import csv
import base64
from datetime import datetime
from time import sleep, time
from typing import Dict, List, Callable, Iterator
from collections import namedtuple

from django.core.exceptions import ValidationError
from django.utils.timezone import utc

from documents.models import Tokenized
from glynt_api.parameter_store import GlyntApiParameterStoreClient
from glynt_api.util import validate_tokenizer_version
from jobs import States

from .tasks import tokenize

logger = logging.getLogger(__name__)

POLL_SPEED = 10
TOKENIZED_TIMEOUT = 60 * 60 * 2  # 2 hours


def clean_tokenizer_version(obj):
    """A shared cleaning function used for any model that has a
    tokenizer_version attribute. Given a Django Model with a tokenizer_version
    and _original_tokenizer_version attribute, clean the tokenizer version. If
    tokenizer_version is changing, it must not be an obsolete version. If not
    provided, system default is used. It may be an obsolete version if not changing
    because otherwise you would not be able to update the object which has an
    obsolete version without first changing the tokenizer_version.
    """
    # if this is an update (object already has a pk) and the versions match, alls well.
    if obj.pk and obj.tokenizer_version == obj._original_tokenizer_version:
        return
    else:
        original_value = obj.tokenizer_version
        if not obj.tokenizer_version or obj.tokenizer_version == 'default':
            obj.tokenizer_version = obj.data_pool._default_tokenizer_version['name']
        else:
            validate_tokenizer_version(obj.tokenizer_version)
            logger.debug(
                f"tokenizer_version of {obj} changed from "
                f"{original_value} to {obj.tokenizer_version}"
            )


def get_or_create_tokenized(document, tokenizer_version, boxcars_priority=None):
    """Attempts to get or create a Tokenized for the given document and
    tokenizer_version.

    Returns a tuple of (Tokenized, created), where Tokenized is the Tokenized
    object that was retrieved or created, and created is a
    Boolean indicating whether it was created by this function or not.

    Accepts an optional `boxcars_priority` parameter to indicate the priority level of boxcars jobs when a new
    Tokenized is created.
    """
    try:
        tokenized = _viable_tokenized(document, tokenizer_version)
        return tokenized, False
    except Tokenized.DoesNotExist:
        tokenized = document.tokenizeds.create(
            tokenizer_version=tokenizer_version,
            data_pool=document.data_pool,
            boxcars_priority=boxcars_priority
        )
        return tokenized, True


def get_viable_tokenized(document, tokenizer_version):
    try:
        return _viable_tokenized(document, tokenizer_version)
    except Tokenized.DoesNotExist:
        return None


def _viable_tokenized(document, tokenizer_version):
    """Tokenizeds for the document/version combo that are not in a failed
    state. There should only ever be one."""
    viable_tokenized = Tokenized.state_annotated_objects.filter(
        tokenizer_version=tokenizer_version,
        document=document
    ).exclude(_latest_state=States.FAILED.value)

    if not viable_tokenized.exists():
        raise Tokenized.DoesNotExist

    if viable_tokenized.count() > 1:
        logger.warning(
            "More than one viable Tokenized object found for "
            f"Document {document} with tokenizer version {tokenizer_version}. "
            "This should be illegal and suggests a bug in Tokenized clean "
            "methods. Using the most recent."
        )

    return viable_tokenized.first()


def tokenized_ready(tokenized):
    """Waits for tokenized job to be successful and raises exceptions if
    it times out or fails.
    """
    logger.info(
        f"Verifying tokenized {tokenized.obfuscated_id} for Document "
        f"{tokenized.document.obfuscated_id} is finished and succeeded"
    )
    timeout = time() + TOKENIZED_TIMEOUT
    while True:
        if tokenized.finished:
            if tokenized.succeeded:
                logger.debug(
                    f"Tokenized {tokenized.obfuscated_id} for Document "
                    f"{tokenized.document.obfuscated_id} is finished "
                    "and succeeded"
                )
                return True
            raise Exception(
                f"Tokenized {tokenized.obfuscated_id} failed for "
                f"Document {tokenized.document.obfuscated_id}"
            )
        elif time() > timeout:
            raise TimeoutError(
                f"Tokenized {tokenized.obfuscated_id} for Document "
                f"{tokenized.document.obfuscated_id} timed out after "
                f"{TOKENIZED_TIMEOUT} seconds"
            )
        tokenized.refresh_status()
        sleep(POLL_SPEED)


app_force_retokenization = GlyntApiParameterStoreClient().app_force_retokenization
FORCE_RETOKENIZATION = app_force_retokenization or os.getenv('APP_FORCE_RETOKENIZATION', 'True') == 'True'
FORCE_RETOKENIZATION_OLDER_X_MINUTES = \
    os.getenv('APP_FORCE_RETOKENIZATION_OLDER_X_MINUTES', 7 * 24 * 60)  # Default = 1 week


def retokenize_tokenized(tokenized: Tokenized):
    if not tokenized.finished:
        return

    minutes = int((datetime.utcnow().replace(tzinfo=utc) - tokenized.updated_at).total_seconds() / 60)
    if minutes <= FORCE_RETOKENIZATION_OLDER_X_MINUTES:
        return

    tokenized.set_state('PENDING')
    tokenize.delay(tokenized_id=tokenized.id,
                   boxcars_priority=getattr(tokenized, '_boxcars_priority', None))
    tokenized.refresh_from_db()


def validate_documents_supported(documents):
    """
    Validates that the documents are supported.
    Raises a ValidationError if any document is not supported.
    """

    unsupported_docs = [
        doc for doc in documents if not doc.mime_type.is_supported
    ]

    if unsupported_docs:
        id_msg = ", ".join(str(doc.obfuscated_id) for doc in unsupported_docs)
        raise ValidationError(f'The following document(s) have unsupported mime types: {id_msg}')


def text_pages(tokenized: Dict, infer_newlines: bool = True) -> List[str]:
    """
    Render a tokenized document into a list of text pages.
    This assumes the 'tokens' are provided in reading order by the OCR system.

    If infer_newlines is True, this will attempt to infer line breaks between tokens
    based on their relative positions.
    """
    Token = namedtuple("Token", ["content", "xl", "xr", "yt", "yb"])

    def parse_token(token):
        """Extract bounding box information from a token dictionary."""
        if not token.get("bounding_box"):
            # I don't think this can happen, but just in case
            return Token(content=token.get("content", ""), xl=0, xr=0, yt=0, yb=0)

        x_vals, y_vals = zip(*[(b["x"], b["y"]) for b in token.get("bounding_box", [])])
        return Token(
            content=token.get("content", ""),
            xl=min(x_vals), xr=max(x_vals),
            yt=min(y_vals), yb=max(y_vals)
        )

    text_pages = []

    for _, page in tokenized.items():
        if not isinstance(page, dict):
            continue

        tokens = page.get("tokens", [])

        # Return space joined tokens
        if not infer_newlines:
            text_pages.append(" ".join(token.get("content", "") for token in tokens))
            continue

        # Empty page
        if not tokens:
            text_pages.append("")
            continue

        token_objects = [parse_token(token) for token in tokens]

        result = [token_objects[0].content]

        for i in range(1, len(token_objects)):
            curr, prev = token_objects[i], token_objects[i - 1]
            vertical_gap = curr.yt - prev.yb

            # Conditions for newlines (not perfect due to skew and other issues)
            # 1. Vertical gap between tokens is small
            # 2. Current token is to the left of the previous token and below it
            needs_newline = (
                (-5 <= vertical_gap <= 5) or
                (curr.xl < prev.xr and curr.yt > prev.yt)
            )

            result.append("\n" if needs_newline else " ")
            result.append(curr.content)

        text_pages.append("".join(result))

    return text_pages


def b64_to_hex(b64: str) -> str:
    """
    Convert a base64 string/md5 representation to a hex string.
    """
    return base64.b64decode(b64).hex()


def queryset_iterator(queryset, chunk_size=1000, pk_field="id"):
    """
    Wrapper around a queryset that yields rows in chunks as the standard
    .iterator() with mysql client will fetch all rows into memory.
    """
    # Handle empty queryset
    if not queryset.exists():
        return

    pk = 0
    last_pk = queryset.order_by(f'-{pk_field}')[0][pk_field]
    queryset = queryset.order_by(pk_field)

    while pk < last_pk:
        for row in queryset.filter(**{f"{pk_field}__gt": pk})[:chunk_size]:
            pk = row[pk_field]
            yield row


def stream_csv(
    queryset,
    headers,
    limit: int = None,
    batch_size: int = 1000,
    transform: Callable[[Dict], Dict] = None,
) -> Iterator[str]:
    """
    Stream a queryset as a CSV. Expects the query set to emit dicts
    with .values() call.
    """
    buffer = io.StringIO()
    writer = csv.writer(buffer)

    # Header
    writer.writerow(headers)
    yield buffer.getvalue()
    buffer.seek(0)
    buffer.truncate(0)

    # Stream rows in batches
    count = 0
    for row in queryset_iterator(queryset, chunk_size=batch_size):
        row = transform(row) if transform else row
        writer.writerow([row.get(header) for header in headers])
        count += 1
        if limit and count >= limit:
            break

        if count % batch_size == 0:
            yield buffer.getvalue()
            buffer.seek(0)
            buffer.truncate(0)

    # Flush any remaining data
    remainder = buffer.getvalue()
    if remainder:
        yield remainder
