import base64
import json
import logging
import uuid
from io import BytesIO
import re
from typing import Dict, List

from django.core.exceptions import ValidationError
from django.db import models
from jsonfield import <PERSON><PERSON><PERSON><PERSON>

from api_storage import storage_client
from documents.exceptions import DuplicateTokenizedProfileError
from glynt_api.mixins import UUIDMixin
from glynt_api.models import Model
from glynt_api.util import (
    validate_non_printable_characters, validate_whitespaces
)
from jobs import States
from jobs.mixins import JobMixin
from organizations.mixins import DataPoolModelMixin
from tags.mixins import GlyntTagsModelMixin, TagsModelMixin
from .querysets import DocumentManifestQuerySet
from .choices import LANGUAGE_CHOICES
from glynt_schemas.document.document_attributes import DocumentDataStatus
from glynt_schemas.constants import MAX_CHARFIELD_LENGTH

logger = logging.getLogger(__name__)


def validate_mime_type(value):
    if not re.match(r'^[a-z0-9]+/[a-z0-9\-.]+$', value):
        raise ValidationError('Invalid content_type format, expected "type/subtype"')


class MimeType(Model):
    mime_type = models.CharField(max_length=255, unique=True)
    is_supported = models.BooleanField(default=False)

    def __str__(self):
        return self.mime_type

    def clean(self):
        self.mime_type = self.mime_type.strip().lower()
        validate_mime_type(self.mime_type)

    def save(self, *args, **kwargs):
        self.full_clean()
        super().save(*args, **kwargs)


def is_flat_dict_of_strings(value: dict):
    if not isinstance(value, dict):
        raise ValidationError('Expected a dictionary')
    for v in value.values():
        if not isinstance(v, str):
            raise ValidationError("Enrichment data must be a flat dict with string values.")
    return True


class DocumentEnrichments(DataPoolModelMixin, models.Model):
    data = JSONField(validators=[is_flat_dict_of_strings])
    enrichment_key = models.CharField(max_length=350, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        constraints = [
            models.UniqueConstraint(fields=["enrichment_key", "data_pool"], name="unique_key_in_dp")
        ]

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)
        if not self.enrichment_key:
            self.enrichment_key = str(self.pk)
            self.save()


class Document(
    UUIDMixin,
    DataPoolModelMixin,
    TagsModelMixin,
    GlyntTagsModelMixin,
    Model
):
    objects = models.Manager()
    manifest_queryset = DocumentManifestQuerySet.as_manager()

    CONTENT_MD5_MAX_LENGTH = 32
    LANGUAGE_MAX_LENGTH = 8
    FORMAT_ID_MAX_LENGTH = 36
    DEFAULT_LABEL_FUNC = uuid.uuid4
    LABEL_ALLOW_BLANK = True
    LABEL_MAX_LENGTH = 255
    INBOUND_PATH_MAX_LENGTH = 255
    EXTENSION_MAX_LENGTH = 16

    label = models.CharField(
        max_length=LABEL_MAX_LENGTH,
        blank=LABEL_ALLOW_BLANK,
        default=DEFAULT_LABEL_FUNC,
        validators=[validate_non_printable_characters, validate_whitespaces]
    )

    mime_type = models.ForeignKey(MimeType, on_delete=models.PROTECT)

    content_md5 = models.CharField(max_length=CONTENT_MD5_MAX_LENGTH)

    page_count = models.SmallIntegerField(default=0)

    token_count = models.IntegerField(default=0)

    filesize = models.BigIntegerField(null=True, blank=True)

    # Language of the document, detected by the inbound router
    language = models.CharField(max_length=LANGUAGE_MAX_LENGTH, choices=LANGUAGE_CHOICES, blank=True, null=True)

    # A reference cluster/layout id generated by the inbound routing system.
    format_id = models.CharField(max_length=FORMAT_ID_MAX_LENGTH, blank=True, null=True)

    file_extension = models.CharField(max_length=EXTENSION_MAX_LENGTH, blank=True, null=True)

    inbound_path = models.CharField(max_length=INBOUND_PATH_MAX_LENGTH, blank=True, null=True)

    status = models.CharField(choices=DocumentDataStatus.get_choices(), max_length=MAX_CHARFIELD_LENGTH,
                              default=DocumentDataStatus.PENDING_CREATION.name)
    #  TODO see if migration is required on schema updates
    #  TODO see if there is alternative to charfield if migration is required

    target_training_set = models.ForeignKey(
        'training.TrainingSet',
        on_delete=models.SET_NULL,
        related_name='targeted_documents',
        related_query_name='targeted_document',
        null=True,
        blank=True
    )

    enrichments = models.ForeignKey(
        DocumentEnrichments,
        related_name='documents',
        on_delete=models.SET_NULL,
        null=True,
        blank=True
    )

    class Meta:
        ordering = ['created_at']
        indexes = [
            models.Index(fields=['data_pool', 'content_md5'], name='data_pool_id_content_md5_idx')
        ]

    def clean(self):
        super().clean()
        # Validate target training set is in same data pool
        if self.target_training_set and self.target_training_set.data_pool_id != self.data_pool_id:
            raise ValidationError(
                "Target training set must belong to the same data pool as the document"
            )

    @property
    def hex_md5(self):
        return base64.b64decode(self.content_md5).hex()

    @property
    def content_type(self):
        return self.mime_type.mime_type if self.mime_type else None

    def get_content_type_display(self):
        return self.content_type

    @property
    def is_supported(self):
        return self.mime_type.is_supported

    @property
    def file_remote_path(self):
        """Returns a remote_path string to be used for storing this file using
        a storage client provided by the storage app.
        """
        if self.file_extension:
            file_name = 'document_file' + self.file_extension
        else:
            file_name = 'document_file'

        return '/'.join([str(self.uuid), file_name])

    @property
    def file_temp_url(self):
        """Generates and returns a temporary presigned url to GET the Document
        file content. This is the url that is exposed to users to access their
        files."""
        storage = storage_client()
        return storage.generate_presigned_get_url(
            bucket=self.bucket_name,
            remote_path=self.file_remote_path,
            valid_for=60 * 60  # this is the public facing presigned url so modify this wisely
        )

    @property
    def file_content(self):
        """Return file content as byte string."""
        storage = storage_client()
        doc_obj = storage.download_fileobj(self.bucket_name, self.file_remote_path)
        return doc_obj.read()

    def store_file_content(self, content):
        """Store document body content using the file_storage_client(). Content
        should be a byte array.
        """
        storage = storage_client()
        storage.upload_fileobj(
            bucket=self.bucket_name,
            remote_path=self.file_remote_path,
            fileobj=BytesIO(content),
            content_md5=self.content_md5
        )

    @property
    def file_upload_url(self):
        """Return the presigned url to PUT the file content."""
        storage = storage_client()
        return storage.generate_presigned_put_url(
            bucket=self.bucket_name,
            remote_path=self.file_remote_path,
            content_type=self.get_content_type_display(),
            content_md5=self.content_md5
        )

    @property
    def file_exists(self):
        """Returns True if underlying file exists in storage. Else False."""
        storage = storage_client()
        return storage.check_object_exists(self.bucket_name, self.file_remote_path)

    def page_remote_path(self, page_number):
        """Returns a remote_path string to be used for storing a page file
        using a storage client provided by the storage app.
        """
        return '/'.join([str(self.uuid), 'pages', str(page_number), 'page_file.png'])

    def page_temp_url(self, page_number):
        """Generates and returns a temporary presigned url to GET a page's file content."""
        if type(page_number) is not int:
            raise TypeError('page_number must be type {}, got {}'.format(int, type(page_number)))
        elif page_number <= 0:
            raise ValueError('page_number must be greater than 0')
        elif page_number > self.page_count:
            raise ValueError('page_number must be <= self.page_count')

        storage = storage_client()
        return storage.generate_presigned_get_url(
            bucket=self.bucket_name,
            remote_path=self.page_remote_path(page_number)
        )

    def page_content(self, page_number):
        """Return page content as byte string."""
        storage = storage_client()
        remote_path = self.page_remote_path(page_number)
        page_obj = storage.download_fileobj(self.bucket_name, remote_path)
        return page_obj.read()

    def store_page_content(self, page_number, content):
        """Store byte string as page content in storage."""
        storage = storage_client()
        storage.upload_fileobj(
            bucket=self.bucket_name,
            remote_path=self.page_remote_path(page_number),
            fileobj=BytesIO(content)
        )

    def can_tokenize(self):
        return self.is_supported and self.file_exists and self.filesize > 0

    @property
    def full_bucket_name(self):
        storage = storage_client()
        return storage._get_bucket_name(self.bucket_name)

    def get_summarized_doc_info(self) -> Dict[str, str]:
        return {
            "id": self.obfuscated_id,
            "name": self.label,
            "key": self.file_remote_path,
            "bucket": self.full_bucket_name,
            "extension": self.file_extension,
            "created_at": self.created_at.isoformat(timespec="minutes"),
            "updated_at": self.updated_at.isoformat(timespec="minutes")
        }

    @property
    def text_preview(self):
        """Safely access the text_content preview field, returning None if not available."""
        try:
            return self.text_content.preview if self.text_content else None
        except TextContent.DoesNotExist:
            return None

    def __str__(self):
        return '{} ({})'.format(self.label, self.obfuscated_id)


class TextContent(models.Model):
    MAX_PREVIEW_LENGTH = 1000

    document = models.OneToOneField(
        'Document',
        on_delete=models.CASCADE,
        related_name='text_content',
        related_query_name='text_content'
    )

    preview = models.TextField(
        max_length=MAX_PREVIEW_LENGTH,
        null=True,
        blank=True,
        help_text="Text preview of the document"
    )

    class Meta:
        indexes = [
            models.Index(fields=['document'], name='text_content_document_id_idx')
        ]

    def __str__(self):
        return self.preview


class Tokenized(UUIDMixin, DataPoolModelMixin, JobMixin, Model):

    document = models.ForeignKey(
        'Document',
        on_delete=models.CASCADE,
        related_name='tokenizeds',
        related_query_name='tokenized'
    )

    TOKENIZER_VERSION_MAX_LENGTH = 31
    tokenizer_version = models.CharField(max_length=TOKENIZER_VERSION_MAX_LENGTH)

    # Indicates whether the Tokenized is currently being copied. See the copy action.
    _is_copying = False

    # Indicates "current" tokenizer version.
    _original_tokenizer_version = None

    class Meta:
        ordering = ['created_at']

    def __init__(self, *args, boxcars_priority=None, **kwargs):
        """
        Optional `boxcars_priority` keyword argument is accepted at initialization to indicate the requesting boxcars
        job priority level to generate the tokenized data.
        """
        super().__init__(*args, **kwargs)
        self._original_tokenizer_version = self.tokenizer_version
        if boxcars_priority is not None:
            # This variable is not persisted. It is meant to be accessed only in Tokenized post save signal.
            self._boxcars_priority = boxcars_priority

    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)
        self._original_tokenizer_version = self.tokenizer_version

    def clean(self):
        super().clean()

        if not self._is_copying:
            self._clean_document()
            from documents.util import clean_tokenizer_version
            clean_tokenizer_version(self)

            # Allow Tokenized with same tokenizer_version and document and data_pool
            # ONLY if all the previous jobs are in FAILED state.
            not_failed_jobs = self.__class__.state_annotated_objects.filter(
                tokenizer_version=self.tokenizer_version,
                document=self.document,
                data_pool=self.data_pool
            ).exclude(_latest_state=States.FAILED.value)

            if self.pk:
                not_failed_jobs = not_failed_jobs.exclude(pk=self.pk)

            if not_failed_jobs.count() > 0:
                raise DuplicateTokenizedProfileError(
                    "Non-failed Tokenized jobs with same signature exists."
                    " (tokenizer_version: {}; document: {}; data pool: {})".format(
                        self.tokenizer_version,
                        self.document.obfuscated_id,
                        self.data_pool.obfuscated_id
                    )
                )

    def _clean_document(self):
        if not self.document.file_exists:
            raise ValidationError(
                "Document '{}' not found in storage. Cannot Tokenize. "
                "Please upload document file and try again.".format(
                    self.document.obfuscated_id
                )
            )

    def render_text_preview(self) -> str:
        """
        Render a tokenized document for text preview.
        """
        pages = self.render_text_pages(infer_newlines=False)
        preview = " ".join(pages)[:TextContent.MAX_PREVIEW_LENGTH]
        return preview

    def render_text_pages(self, infer_newlines: bool = True) -> List[str]:
        """
        Render a tokenized document into a list of text pages.

        If infer_newlines is True, attempt to infer newlines
        between tokens using position data.
        """
        from documents.util import text_pages
        pages = text_pages(self.tokenized, infer_newlines)
        return pages

    @property
    def tokenized(self):
        """Get and return tokenized data as a dict."""
        storage = storage_client()
        try:
            tokenized_obj = storage.download_fileobj(
                bucket=self.bucket_name,
                remote_path=self.remote_path
            )
        except FileNotFoundError:
            logger.warning(
                f"Tokenized '{self.id}' lost its JSON")
        else:
            try:
                return json.loads(tokenized_obj.read().decode())
            except (json.JSONDecodeError, ValueError):
                logger.warning(
                    f"Tokenized '{self.id}' has invalid JSON")
        return {}

    @property
    def remote_path(self):
        """Returns a remote_path string to be used for storing this underlying
        tokenization data file using a storage client provided by the storage
        app.
        """
        return '/'.join([str(self.uuid), 'tokenized.json'])

    @property
    def temp_upload_url(self):
        """This is the long-lived presigned url used internally by boxcars to
        upload its tokenized json results"""
        storage = storage_client()
        return storage.generate_presigned_put_url(
            bucket=self.bucket_name,
            remote_path=self.remote_path,
            content_type='application/json',
            content_md5=None,
            use_vpc=True
        )

    @property
    def tokenized_temp_url(self):
        """Generates and returns a temporary presigned url to GET the tokenized json content."""
        storage = storage_client()
        return storage.generate_presigned_get_url(
            bucket=self.bucket_name,
            remote_path=self.remote_path,
        )

    @property
    def n_pages(self):
        return len(self.tokenized)

    def __str__(self):
        return "{} Tokenized `{}`".format(
            self.tokenizer_version,
            self.document
        )
