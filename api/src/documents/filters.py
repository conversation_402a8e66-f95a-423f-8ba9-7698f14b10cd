import re

from django.core.exceptions import Validation<PERSON>rror
from django_filters import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from django.db.models import Exists, OuterRef

from .models import Document
from glynt_api.filters import FilterSet, Char<PERSON>n<PERSON><PERSON><PERSON>, CharIn<PERSON>ikeFilter
from glynt_api.mixins import (
    LabelFilterMixin,
    CreatedAtUpdatedAtFilterMixin,
)


class RegexCharFilter(CharFilter):
    """
    A custom CharFilter that validates the provided regex.
    Raises a ValidationError if the regex is invalid.
    """

    def filter(self, qs, value):
        if value is None or value == '':
            return qs

        # Validate the regex before applying it.
        try:
            re.compile(value)
        except re.error as e:
            raise ValidationError({self.field_name: f"Invalid regex provided: '{value}'. Error: {str(e)}"})
        return super().filter(qs, value)


class TextSearchFilterMixin(FilterSet):
    """
    Mixin for adding text search capabilities to document filter classes.
    Supports 'contains' and 'regex' filters that can be stacked for AND operations.
    """

    text__contains = CharFilter(
        field_name='text_content__preview',
        lookup_expr='contains',
        label='Text contains'
    )

    text__icontains = CharFilter(
        field_name='text_content__preview',
        lookup_expr='icontains',
        label='Text icontains'
    )

    text__regex = RegexCharFilter(
        field_name='text_content__preview',
        lookup_expr='regex',
        label='Text regex'
    )


class DocumentManifestFilter(
    LabelFilterMixin, CreatedAtUpdatedAtFilterMixin,
    TextSearchFilterMixin, FilterSet
):
    id = CharInFilter(
        field_name="id",
        label="Document id",
        transform_func=Document.get_unobfuscated_id,
    )
    exclude_id = CharInFilter(
        field_name="id",
        exclude=True,
        label="Exclude document ids",
        transform_func=Document.get_unobfuscated_id,
    )
    organization = CharFilter(method="obfuscated_id_filter", label="Organization")
    data_pool = CharFilter(method="obfuscated_id_filter", label="Data Pool")

    # Leaving original LabelFilterMixin for backward compatibility
    name = CharInLikeFilter(field_name="label", label="Include document names")
    exclude_name = CharInLikeFilter(
        field_name="label", exclude=True, label="Exclude document names"
    )

    format_id = CharInFilter(field_name="format_id")
    exclude_format_id = CharInFilter(field_name="format_id", exclude=True)

    filesize = RangeFilter(field_name="filesize", lookup_expr="range")
    token_count = RangeFilter(field_name="token_count", lookup_expr="range")
    page_count = RangeFilter(field_name="page_count", lookup_expr="range")

    language = CharInFilter(field_name="language", label="Languages")
    exclude_language = CharInFilter(
        field_name="language", exclude=True, label="Exclude languages"
    )

    content_md5 = CharInFilter(field_name="content_md5")
    exclude_content_md5 = CharInFilter(field_name="content_md5", exclude=True)

    is_supported = BooleanFilter(method="filter_is_supported", label="Is mimetype supported")
    has_verified_extraction = BooleanFilter(method="filter_by_verified_extraction", label="Has verified extraction")

    target_training_set = CharInFilter(
        field_name='target_training_set',
        transform_func=Document.get_unobfuscated_id,
        label='Target Training Set ID'
    )

    target_training_set_label = CharInFilter(
        field_name='target_training_set__label',
        label='Target Training Set Label'
    )

    def filter_is_supported(self, queryset, name, value):
        return queryset.filter(mime_type__is_supported=value)

    def obfuscated_id_filter(self, queryset, field_name, value):
        deobfuscated_id = Document.get_unobfuscated_id(value)

        if field_name == "organization":
            return queryset.filter(data_pool__organization_id=deobfuscated_id)
        return super().obfuscated_id_filter(queryset, field_name, value)

    def filter_by_verified_extraction(self, queryset, name, value):
        Extraction = Document.extractions.field.model
        # Subquery to check for verified extractions for the current document
        verified_extractions = Extraction.objects.filter(
            document_id=OuterRef("pk"), verified=True
        )
        if value:
            # Filter documents that have at least one verified extraction
            queryset = queryset.annotate(
                has_verified=Exists(verified_extractions)
            ).filter(has_verified=True)
        else:
            # Filter documents that do not have any verified extractions
            queryset = queryset.annotate(
                has_verified=Exists(verified_extractions)
            ).filter(has_verified=False)
        return queryset
