import unittest

from django.test.testcases import TransactionTestCase
from glynt_schemas.datapool.config import DocEnrichJoinConfig

from documents.enrichment_joiner import DocEnrichmentJoiner
from documents.models import DocumentEnrichments
from documents.tests.util import create_document
from glynt_api.tests.util import TestCaseMixin
from organizations.tests.util import create_organization, create_data_pool


class TestDocEnrichmentJoiner(TransactionTestCase, TestCaseMixin):
    def setUp(self):
        self.org, _ = create_organization()
        self.dp, _ = create_data_pool(organization=self.org)
        self.doc, _ = create_document(data_pool=self.dp)

    def test_enrichment_join_by_enrichment_key(self):
        enrichment = DocumentEnrichments.objects.create(
            data_pool=self.dp, enrichment_key="remote/path/to/file.pdf", data={"foo": "bar"})

        self.doc.inbound_path = "remote/path/to/file.pdf"
        self.doc.save()

        default_config = DocEnrichJoinConfig()

        joiner = DocEnrichmentJoiner(default_config)
        joiner.set_enrichment(self.doc)
        self.doc.refresh_from_db()

        self.assertEqual(self.doc.enrichment, enrichment)

    def test_by_enrichment_key_with_split_doc_key(self):
        enrichment = DocumentEnrichments.objects.create(
            data_pool=self.dp, enrichment_key="site_1", data={"site_id": "site_1"})

        self.doc.inbound_path = "remote/path/site_1/file.pdf"
        self.doc.save()

        enrichment_config = DocEnrichJoinConfig(
            doc_key="inbound_path",
            doc_key_split_char="/",
            doc_key_split_index=2
        )

        joiner = DocEnrichmentJoiner(enrichment_config)
        joiner.set_enrichment(self.doc)
        self.doc.refresh_from_db()

        self.assertEqual(self.doc.enrichment, enrichment)

    @unittest.skip("Filtering on jsonfield introspections does not work in django 2.2")
    def test_join_by_data_introspection(self):
        enrichment = DocumentEnrichments.objects.create(data_pool=self.dp, data={"site_id": "site_1"})
        self.doc.inbound_path = "remote/path/site_1/file.pdf"
        self.doc.save()

        enrichment_config = DocEnrichJoinConfig(
            doc_key="inbound_path",
            doc_key_split_char="/",
            doc_key_split_index=2,
            enrichment_key_path="data.site_id"
        )

        joiner = DocEnrichmentJoiner(enrichment_config)
        joiner.set_enrichment(self.doc)
        self.doc.refresh_from_db()

        self.assertEqual(self.doc.enrichment, enrichment)
