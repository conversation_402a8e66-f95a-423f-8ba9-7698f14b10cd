import unittest
from unittest.mock import PropertyMock, patch

from django.core.exceptions import ValidationError
from django.test import TransactionTestCase
from pyfakefs.fake_filesystem_unittest import TestCaseMixin

from api_storage.tests.util import patch_enqueue_delete_resource_folder
from boxcars.priority import ALL_BOXCARS_PRIORITIES
from documents.models import Tokenized
from documents.tests.util import (
    create_document, mock_get_tokenizer_versions, patch_tokenize
)
from extract.tests.util import create_extraction
from organizations.tests.util import create_data_pool, create_organization
from training.tests.util import (
    create_field, create_ground_truth, create_training_revision,
    create_training_set
)


class DocumentModelTestCase(TransactionTestCase, TestCaseMixin):

    def setUp(self):
        self.setUpPyfakefs()

        self.org, self.org_path = create_organization()
        self.dp, self.dp_path = create_data_pool(organization=self.org)

    @patch_enqueue_delete_resource_folder
    def test_delete_doc_deletes_associated_data(
        self, mock_enqueue_delete_resource_folder
    ):
        doc, _ = create_document(data_pool=self.dp)
        ts, _ = create_training_set(data_pool=self.dp, documents=[doc])
        field, _ = create_field(self.dp, ts)
        gt, _ = create_ground_truth(self.dp, field, doc)
        tr, _ = create_training_revision(self.dp, ts)
        ex, _ = create_extraction(doc, training_revision=tr, data_pool=self.dp)
        t = Tokenized(data_pool=self.dp, document=doc)

        doc.delete()

        with self.assertRaises(gt.DoesNotExist):
            gt.refresh_from_db()

        with self.assertRaises(ex.DoesNotExist):
            ex.refresh_from_db()

        with self.assertRaises(t.DoesNotExist):
            t.refresh_from_db()

        tr.refresh_from_db()
        field.refresh_from_db()
        ts.refresh_from_db()


class TokenizedModelTestCase(TransactionTestCase, TestCaseMixin):

    def setUp(self):
        self.setUpPyfakefs()

        self.org, self.org_path = create_organization()
        self.dp, self.dp_path = create_data_pool(organization=self.org)
        self.doc, self.doc_path = create_document(data_pool=self.dp)

    @patch_tokenize
    def test_tokenized_validates_tokenizer_version(self, tokenize_mocks):
        tokenizer_versions = tokenize_mocks['get_tokenizer_versions']
        t = Tokenized(data_pool=self.dp, document=self.doc, tokenizer_version='not_valid')
        self.assertRaises(ValidationError, t.full_clean)

        t = Tokenized(data_pool=self.dp, document=self.doc, tokenizer_version='default')
        t.full_clean()
        self.assertEqual(t.tokenizer_version, mock_get_tokenizer_versions()['default'])

        t = Tokenized(data_pool=self.dp, document=self.doc)
        self.assertRaises(ValidationError, t.full_clean)

        # obsolete version
        t = Tokenized(data_pool=self.dp, document=self.doc, tokenizer_version=tokenizer_versions['obsolete'][0])
        self.assertRaises(ValidationError, t.full_clean)

        t = Tokenized(data_pool=self.dp, document=self.doc, tokenizer_version='AWS')
        # Returns none if full_clean is a success
        self.assertIsNone(t.full_clean())

        t.save()

        # TODO: determine if this test is still doing the right thing; the
        # organizations model (probably) does not check the default
        # tokenizer for validity
        with patch('organizations.models.get_default_tokenizer') as mock_version:
            mock_version.return_value = '5.0.0'

            # save with obsolete version if it is not changing
            t.tokenizer_version = 'AWS'
            self.assertIsNone(t.full_clean())

            # still cannot save with other obsolete version
            t.tokenizer_version = '2.0.0'
            self.assertRaises(ValidationError, t.full_clean)

            # can save with obsolete version if it is not changing with a freshly
            # queried instance (needed to be sure the original_tokenizer_version
            # attr is set on __init__.

            t = Tokenized.objects.get(id=t.id)
            t.tokenizer_version = 'AWS'
            self.assertIsNone(t.full_clean())

    @patch_tokenize
    def test_tokenized_same_signature(self, tokenize_mocks):

        t = Tokenized(data_pool=self.dp, document=self.doc, tokenizer_version='OCRTEST1')
        self.assertIsNone(t.full_clean())
        t.save()
        t.set_state('FAILED')
        assert t.in_state('FAILED')

        # create Tokenized with same signature ok if all previous ones failed
        t = Tokenized(data_pool=self.dp, document=self.doc, tokenizer_version='OCRTEST1')
        self.assertIsNone(t.full_clean())
        t.save()
        t.set_state('SUCCEEDED')
        assert t.in_state('SUCCEEDED')

        # cannot create Tokenized with same signature if not all previous ones failed
        t = Tokenized(data_pool=self.dp, document=self.doc, tokenizer_version='OCRTEST1')
        self.assertRaisesRegex(
            ValidationError,
            r'Non-failed Tokenized jobs with same signature exists.',
            t.full_clean
        )

    @patch_tokenize
    @patch('documents.models.Document.file_exists', new_callable=PropertyMock)
    def test_clean_document(self, mock_file_exists, tokenize_mocks):

        mock_file_exists.return_value = True
        t = Tokenized(data_pool=self.dp, document=self.doc, tokenizer_version='OCRTEST1')
        assert self.doc.file_exists is True
        self.assertIsNone(t.full_clean())

        mock_file_exists.return_value = False
        t = Tokenized(data_pool=self.dp, document=self.doc, tokenizer_version='OCRTEST1')
        assert self.doc.file_exists is False
        self.assertRaisesRegex(
            ValidationError,
            r"Document '{}' not found in storage. Cannot Tokenize.".format(
                self.doc.obfuscated_id),
            t.full_clean
        )

    @patch_tokenize
    def test_tokenized_boxcars_priority(self, tokenize_mocks):
        # no _boxcars_priority if boxcars_priority is not provided
        t = Tokenized(data_pool=self.dp, document=self.doc, tokenizer_version='AWS')
        self.assertFalse(hasattr(t, '_boxcars_priority'))

        # _boxcars_priority set according to provided boxcars_priority
        for boxcars_priority in ALL_BOXCARS_PRIORITIES:
            t = Tokenized(
                data_pool=self.dp,
                document=self.doc,
                tokenizer_version='AWS',
                boxcars_priority=boxcars_priority
            )
            self.assertEqual(t._boxcars_priority, boxcars_priority)

    @unittest.skip("Skip until patching test improvements from GL-401 come in")
    def test_bad_remote_tokenized_data(self):
        """Test tokenized data return from model is an empty dict when the
        remote data is either missing or not json serializable."""
        pass
