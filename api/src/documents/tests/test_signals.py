from unittest.mock import patch

from django.test import TransactionTestCase, Client
from pyfakefs.fake_filesystem_unittest import TestCaseMixin

from api_storage.tests.util import patch_enqueue_delete_resource_folder
from boxcars.priority import ALL_BOXCARS_PRIORITIES
from documents.models import Tokenized, Document
from documents.signals import _process_tokenized_post_save, document_created
from documents.tests.util import (
    create_document, create_mock_pages, patch_tokenize
)
from glynt_schemas.datapool.config import UIPathConfig
from organizations.tests.util import create_data_pool, create_organization
from users.tests.util import create_staff_user


class DocumentSignalsTestCase(TransactionTestCase, TestCaseMixin):

    def setUp(self):
        self.setUpPyfakefs()

        self.staff_user, self.staff_user_path = create_staff_user()
        self.staff_client = Client()
        self.org, self.org_path = create_organization()
        self.dp, self.dp_path = create_data_pool(organization=self.org)

    @patch_enqueue_delete_resource_folder
    def test_delete_document_file_and_pages_from_storage(
        self, mock_enqueue_delete_resource_folder
    ):
        doc, _ = create_document(data_pool=self.dp)
        create_mock_pages(doc)
        self.assertTrue(doc.file_exists)
        doc.delete()
        self.assertFalse(doc.file_exists)

        for i in range(doc.page_count):
            with self.assertRaises(FileNotFoundError):
                doc.page_content(i + 1)

    @patch_enqueue_delete_resource_folder
    def test_delete_document_artifacts_from_storage_no_exception(
        self, mock_enqueue_delete_resource_folder
    ):
        doc, _ = create_document(data_pool=self.dp, upload_doc=False)
        self.assertFalse(doc.file_exists)

        for i in range(doc.page_count):
            with self.assertRaises(FileNotFoundError):
                doc.page_content(i + 1)

        doc.delete()

        # assert delete resource folder task is triggered
        self.assertEqual(mock_enqueue_delete_resource_folder.call_count, 1)

    @patch('documents.signals._create_tokenized_for_document')
    @patch('documents.signals.create_pages.delay')
    def test_gen_pages_on_document_creation(self, mock_create_pages_delay, mock_create_tok):
        document, _ = create_document(data_pool=self.dp, patch_save_signals=False)
        document_created.send(sender=Document, instance=document, created=True)
        mock_create_pages_delay.assert_called_once_with(document_id=document.id)

    @patch('documents.signals._create_tokenized_for_document')
    @patch('documents.signals.create_pages.delay')
    def test_gen_tokenized_on_document_creation(self, mock_create_pages_delay, mock_create_tok):
        document, _ = create_document(data_pool=self.dp, patch_save_signals=False)
        document_created.send(sender=Document, instance=document, created=True)
        mock_create_tok.assert_called_once_with(document)


class TestDocumentFormatIDSignals(TransactionTestCase):
    def setUp(self):
        self.org = create_organization()[0]
        self.dp = create_data_pool(organization=self.org)[0]
        self.doc = create_document(data_pool=self.dp)[0]

    def test_prepare_document_format_id_notification_stores_original(self):
        # Set initial format_id
        self.doc.format_id = "format1"
        self.doc.save()

        # Change format_id
        self.doc.format_id = "format2"
        self.doc.save()

        # Verify original format_id was stored
        self.assertEqual(getattr(self.doc, "__original_format_id", None), "format1")

    @patch('documents.signals.send_format_id_changed_notification')
    @patch('documents.signals.get_client_configs')
    def test_notification_sent_when_format_id_first_set(
        self, mock_get_clients, mock_send_notification
    ):
        # Mock client configs
        clients = [UIPathConfig(client_type="uipath", queue_name="test-queue", priority="Normal")]
        mock_get_clients.return_value = clients

        # Set format_id for the first time
        self.doc.format_id = "new_format"
        self.doc.save()

        # Verify notification was triggered
        mock_send_notification.delay.assert_called_once()
        call_args = mock_send_notification.delay.call_args[0][0]

        # Verify event data
        self.assertEqual(call_args["document_id"], self.doc.obfuscated_id)
        self.assertEqual(call_args["old_format_id"], None)
        self.assertEqual(call_args["new_format_id"], "new_format")
        self.assertEqual(call_args["clients"], clients)

    @patch('documents.signals.send_format_id_changed_notification')
    @patch('documents.signals.get_client_configs')
    def test_notification_not_sent_when_no_clients(
        self, mock_get_clients, mock_send_notification
    ):
        # Mock no clients configured
        mock_get_clients.return_value = []

        # Set format_id
        self.doc.format_id = "new_format"
        self.doc.save()

        # Verify no notification was sent
        mock_send_notification.delay.assert_not_called()

    @patch('documents.signals.send_format_id_changed_notification')
    @patch('documents.signals.get_client_configs')
    def test_notification_not_sent_when_format_id_unchanged(
        self, mock_get_clients, mock_send_notification
    ):
        # Mock client configs
        clients = [UIPathConfig(client_type="uipath", queue_name="test-queue", priority="Normal")]
        mock_get_clients.return_value = clients

        # Set initial format_id
        self.doc.format_id = "existing_format"
        self.doc.save()

        # Reset the mock to clear the first notification
        mock_send_notification.delay.reset_mock()

        # Save again with same format_id
        self.doc.format_id = "existing_format"
        self.doc.save()

        # Verify no notification was sent
        mock_send_notification.delay.assert_not_called()


class TestCreateTokenizedSignal(TransactionTestCase, TestCaseMixin):

    def setUp(self):
        self.setUpPyfakefs()
        self.org, self.org_path = create_organization()
        self.dp, self.dp_path = create_data_pool(organization=self.org)
        self.doc, self.doc_path = create_document(data_pool=self.dp)

    @patch_tokenize
    @patch('documents.signals._process_tokenized_post_save', wraps=_process_tokenized_post_save)
    def test_process_tokenized_post_save_on_new_tokenized(self, mock_ptps, tokenize_mocks):
        # _boxcars_priority set according to provided value
        for boxcars_priority in ALL_BOXCARS_PRIORITIES:
            t = Tokenized(
                data_pool=self.dp,
                document=self.doc,
                tokenizer_version='OCRTEST1',
                boxcars_priority=boxcars_priority
            )
            t.save()
            self.assertEqual(t._boxcars_priority, boxcars_priority)
            mock_ptps.assert_called_once_with(t)
            tokenize_mocks['tokenize'].assert_called_once_with(
                tokenized_id=t.id,
                boxcars_priority=boxcars_priority
            )
            # reset for next loop
            tokenize_mocks['tokenize'].reset_mock()
            mock_ptps.reset_mock()
            t.delete()
