import base64
import hashlib
from django.test import TransactionTestCase, TestCase
from pyfakefs.fake_filesystem_unittest import TestCaseMixin

from boxcars.priority import ALL_BOXCARS_PRIORITIES
from documents import util
from documents.models import Tokenized
from documents.tests.util import (
    create_document,
    mock_get_tokenizer_versions,
    patch_tokenize,
)
from organizations.tests.util import create_data_pool, create_organization
from documents.util import text_pages


class MockQuerySet:
    """Mock queryset that simulates Django queryset behavior for testing stream_csv"""

    def __init__(self, data=None):
        self.data = data or []

    def exists(self):
        return len(self.data) > 0

    def order_by(self, field):
        new_qs = MockQuerySet(self.data)
        reverse = field.startswith('-')
        field = field[1:] if reverse else field
        new_qs.data = sorted(self.data, key=lambda x: x[field], reverse=reverse)
        return new_qs

    def filter(self, **kwargs):
        new_qs = MockQuerySet(self.data)
        # Simple filter implementation for id__gt
        if 'id__gt' in kwargs:
            new_qs.data = [item for item in self.data if item['id'] > kwargs['id__gt']]
        return new_qs

    def __getitem__(self, key):
        if isinstance(key, slice):
            return self.data[key]
        return self.data[key]

    def iterator(self, chunk_size=None):
        for item in self.data:
            yield item


class DocumentsGetOrCreateTokenizedUtilTestCase(TransactionTestCase, TestCaseMixin):
    def setUp(self):
        self.setUpPyfakefs()

        self.org, self.org_path = create_organization()
        self.dp, self.dp_path = create_data_pool(organization=self.org)
        self.doc, self.doc_path = create_document(data_pool=self.dp)

    @patch_tokenize
    def test_get_or_create_tokenized_happy(self, tokenize_mocks):
        for boxcars_priority in ALL_BOXCARS_PRIORITIES:
            # Use util successfully to create a tokenized
            tokenized, created = util.get_or_create_tokenized(
                self.doc,
                mock_get_tokenizer_versions()["default"],
                boxcars_priority=boxcars_priority,
            )
            self.assertIsInstance(tokenized, Tokenized)
            self.assertTrue(created)
            self.assertEqual(tokenized._boxcars_priority, boxcars_priority)
            # Requesting again returns same tokenized without _boxcars_priority (_boxcars_priority not persisted)
            second_tokenized, second_created = util.get_or_create_tokenized(
                self.doc, mock_get_tokenizer_versions()["default"]
            )
            self.assertEqual(tokenized.id, second_tokenized.id)
            self.assertFalse(second_created)
            self.assertFalse(hasattr(second_tokenized, "_boxcars_priority"))

            # reset for next loop
            tokenized.delete()


class TextPagesUtilTestCase(TransactionTestCase, TestCaseMixin):
    def setUp(self):
        # Create a simple tokenized document with one page and a few tokens
        self.tokenized_doc = {
            "1": {
                "tokens": [
                    {
                        "content": "Hello",
                        "bounding_box": [
                            {"x": 10, "y": 10},
                            {"x": 50, "y": 10},
                            {"x": 50, "y": 30},
                            {"x": 10, "y": 30},
                        ],
                    },
                    {
                        "content": "world",
                        "bounding_box": [
                            {"x": 60, "y": 10},
                            {"x": 100, "y": 10},
                            {"x": 100, "y": 30},
                            {"x": 60, "y": 30},
                        ],
                    },
                    {
                        "content": "example",
                        "bounding_box": [
                            {"x": 10, "y": 40},
                            {"x": 70, "y": 40},
                            {"x": 70, "y": 60},
                            {"x": 10, "y": 60},
                        ],
                    },
                ]
            }
        }

    def test_text_pages_without_newlines(self):
        """Test that text_pages joins tokens with spaces when infer_newlines=False"""
        result = text_pages(self.tokenized_doc, infer_newlines=False)
        self.assertEqual(len(result), 1)  # One page
        self.assertEqual(result[0], "Hello world example")

    def test_text_pages_with_newlines(self):
        """Test that text_pages infers newlines based on token positions"""
        result = text_pages(self.tokenized_doc, infer_newlines=True)
        self.assertEqual(len(result), 1)  # One page

        # "example" should be on a new line since it's below "Hello" and "world"
        self.assertTrue("Hello" in result[0])
        self.assertTrue("world" in result[0])
        self.assertTrue("example" in result[0])
        self.assertTrue("\n" in result[0])  # Should have at least one newline

        # The full text should have "Hello world" on first line and "example" on second line
        expected_text = "Hello world\nexample"
        self.assertEqual(result[0], expected_text)

    def test_empty_page(self):
        """Test that text_pages handles empty pages correctly"""
        empty_doc = {"page_1": {"tokens": []}}
        result = text_pages(empty_doc)
        self.assertEqual(len(result), 1)
        self.assertEqual(result[0], "")


class StreamCSVTestCase(TestCase):
    def test_stream_csv_basic(self):
        """Test basic CSV streaming without transform function"""
        # Mock data that mimics a Django queryset
        mock_data = [
            {"id": 1, "name": "Doc1", "size": 100, "type": "pdf"},
            {"id": 2, "name": "Doc2", "size": 200, "type": "docx"},
            {"id": 3, "name": "Doc3", "size": 300, "type": "txt"}
        ]

        # Headers for the CSV
        headers = ["name", "size", "type"]

        # Get the generator from stream_csv
        generator = util.stream_csv(MockQuerySet(mock_data), headers, batch_size=2)

        # Consume the generator and concatenate results
        csv_content = ''.join(chunk for chunk in generator)

        # Verify CSV structure with \r\n as the delimiter added by csv package
        lines = csv_content.strip().split("\r\n")
        self.assertEqual(len(lines), 4)  # Header + 3 data rows

        # Check header row
        self.assertEqual(lines[0], "name,size,type")

        # Check data rows
        self.assertEqual(lines[1], "Doc1,100,pdf")
        self.assertEqual(lines[2], "Doc2,200,docx")
        self.assertEqual(lines[3], "Doc3,300,txt")

    def test_stream_csv_with_transform(self):
        """Test CSV streaming with transform function"""
        # Mock data
        mock_data = [
            {"id": 1, "name": "Doc1", "size": 100},
            {"id": 2, "name": "Doc2", "size": 200}
        ]

        # Headers including transformed field
        headers = ["id", "name", "size", "size_kb"]

        # Transform function
        def transform(row):
            row["size_kb"] = f"{row['size'] / 1024:.2f}"
            return row

        # Get the generator
        generator = util.stream_csv(MockQuerySet(mock_data), headers, transform=transform)

        # Consume the generator
        csv_content = "".join(chunk for chunk in generator)

        # Verify CSV has transformed data
        lines = csv_content.strip().split("\r\n")
        self.assertEqual(len(lines), 3)  # Header + 2 data rows

        self.assertTrue("size_kb" in lines[0])
        self.assertTrue("0.10" in lines[1])
        self.assertTrue("0.20" in lines[2])

    def test_stream_csv_empty_data(self):
        """Test CSV streaming with empty data set"""
        headers = ["name", "size"]

        # Get the generator with empty data
        generator = util.stream_csv(MockQuerySet([]), headers)

        # Consume the generator
        csv_content = "".join(chunk for chunk in generator)

        # Should only have the header row
        self.assertEqual(csv_content.strip(), "name,size")


class B64ToHexTestCase(TestCase):
    def test_b64_to_hex(self):
        # Ensure the b64_to_hex function returns the correct hex
        content = "hello world"
        md5bytes = hashlib.md5(content.encode()).digest()
        b64str = base64.b64encode(md5bytes).decode()
        hexstr = md5bytes.hex()
        self.assertEqual(util.b64_to_hex(b64str), hexstr)
