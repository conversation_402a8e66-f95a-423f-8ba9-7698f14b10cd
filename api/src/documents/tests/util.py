import io
import json
import logging
import re
from contextlib import contextmanager
from functools import wraps
from unittest.mock import patch

import responses
from django.conf import settings

from api_storage import storage_client
from cached_reverse.urls import reverse
from documents.actions import create_pages, execute_tokenized_job
from documents.models import Document, Tokenized, MimeType
from glynt_api.util import (
    get_boxcars_versions, get_tokenizer_versions, patch_multiple_targets
)
from glynt_schemas.document.document_attributes import DocumentDataStatus

logger = logging.getLogger(__name__)


SAMPLE_DOCUMENT_LABEL = 'Sample Document'


def mock_create_pages_task(doc_id):
    doc = Document.objects.get(id=doc_id)
    create_pages(doc)


def create_mock_pages(doc, n_pages=3):
    for i in range(n_pages):
        page_number = i + 1
        content = "I am a mock page {} png file".format(page_number)
        doc.store_page_content(page_number, content.encode())
    doc.page_count = n_pages
    doc.save()


def create_document(
    data_pool,
    label=SAMPLE_DOCUMENT_LABEL,
    content_type='application/pdf',
    file_extension='.pdf',
    inbound_path='vendor1/project1',
    content_md5='a_content_md5',
    upload_doc=True,
    tags=None,
    content_type_supported=True,
    token_count=0,
    language=None,
    format_id=None,
    page_count=0,
    filesize=100,
    status=DocumentDataStatus.PENDING_CREATION,
    **kwargs,
):

    mime_type, created = MimeType.objects.get_or_create(
        mime_type=content_type,
        is_supported=content_type_supported
    )

    doc = Document(
        label=label,
        mime_type=mime_type,
        content_md5=content_md5,
        data_pool=data_pool,
        token_count=token_count,
        language=language,
        format_id=format_id,
        page_count=page_count,
        inbound_path=inbound_path,
        file_extension=file_extension,
        filesize=filesize,
        status=status
    )
    doc.save()

    if tags:
        doc.tags.add(*tags)
        doc.save()

    if upload_doc:
        fake_upload_document(doc)
        assert doc.file_exists
    else:
        assert not doc.file_exists

    doc_path = reverse('v6:document-detail', kwargs={
        'data_pools_obfuscated_id': doc.data_pool.obfuscated_id,
        'obfuscated_id': doc.obfuscated_id
    })

    return (doc, doc_path)


def fake_upload_document(doc):
    storage = storage_client()
    storage.upload_fileobj(
        bucket=doc.bucket_name,
        remote_path=doc.file_remote_path,
        fileobj=io.BytesIO(b'mock_file_content'),
    )


def fake_document_content():
    doc_path = settings.COMMON_FIXTURES_PATH / 'sample_doc.tif'
    with open(doc_path, 'rb') as f:
        return f.read()


def mock_is_boxcars_available(self):
    return True


def mock_tokenize_task(tokenized_id, boxcars_priority):
    """Used to mock out the `tokenize` task in tests."""
    tokenized = Tokenized.objects.get(id=tokenized_id)
    tokenized.set_state('IN_PROGRESS')
    execute_tokenized_job(tokenized, boxcars_priority)
    tokenized.set_state('FINISHING')
    tokenized.set_state('SUCCEEDED')


def fake_tokenized_json(document):
    """Returns stanard fake tokenized json for a document."""
    return json.dumps({'document': str(document)})


def mock_generate_tokenized_data(tokenized, boxcars_priority):
    """Used to cause fake tokenized data to appear in the tokenized"""
    tokenized_data = fake_tokenized_json(tokenized.document)

    storage = storage_client()
    storage.upload_fileobj(
        bucket=tokenized.bucket_name,
        remote_path=tokenized.remote_path,
        fileobj=io.BytesIO(tokenized_data.encode()),
    )


def patch_tokenize(func, *args, **kwargs):
    """Patches several parts of the tokenization process with mocks so that we
    don't have to talk to the boxcars server, and instead simply return a
    "fixture" tokenized.

    The created mocks are returned as a dict, injected into the kwargs under
    the name `tokenize_mocks`.

    Usage example:

    @patch('some.other.patch')
    @patch_tokenize
    def some_test(self, some_other_mock, tokenize_mocks):
        ...
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        with patch_multiple_targets([
            patch('documents.actions._generate_tokenized_data'),
            patch('glynt_api.util.get_tokenizer_versions'),
            patch('organizations.models.get_default_tokenizer'),
            patch('documents.signals.tokenize.delay'),
            patch('documents.signals._create_tokenized_for_document'),
        ]) as (mock_gtd, mock_gtv, mock_org_gdt, mock_tokenize, mock_ctfd):

            mock_tokenizer_versions = mock_get_tokenizer_versions()

            mock_gtd.side_effect = mock_generate_tokenized_data
            mock_gtv.return_value = mock_tokenizer_versions
            mock_org_gdt.return_value = mock_get_default_tokenizer()
            mock_tokenize.side_effect = mock_tokenize_task
            mock_ctfd.return_value = None

            mocks = {
                'generate_tokenized_data': mock_gtd,
                'get_tokenizer_versions': mock_gtv,
                'get_default_tokenizer': mock_org_gdt,
                'tokenize': mock_tokenize,
                '_create_tokenized_for_document': mock_ctfd
            }

            kwargs['tokenize_mocks'] = mocks

            return func(*args, **kwargs)

    return wrapper


def mock_get_boxcars_versions_resp():
    return {
        'active_boxcars_versions': ['AWS', 'OCRTEST1', 'OCRTEST2', 'OCRDEPR'],
        'deprecated_boxcars_versions': ['OCRDEPR'],
        'obsolete_boxcars_versions': ['OCROBS'],
        'experimental_boxcars_versions': ['OCREXP']
        # deprecated boxcars versions should also appear in the active list
    }


@contextmanager
def mocked_boxcars_responses_get(assert_all_requests_are_fired=True):

    def request_callback(request):
        path_url = request.path_url

        if re.search(r'/versions$', path_url):
            resp_body = mock_get_boxcars_versions_resp()
            return (200, {}, json.dumps(resp_body))

        match = re.search(r'status_code/(\d{3})/', path_url)
        if match:
            return (int(match.group(1)), {}, '')

        return (200, {}, '')

    with responses.RequestsMock(
        assert_all_requests_are_fired=assert_all_requests_are_fired
    ) as rsps:
        rsps._url_pattern = r'{}.*'.format(settings.BOXCARS_API_URL)
        rsps.add_callback(
            responses.GET,
            re.compile(rsps._url_pattern),
            callback=request_callback,
            content_type='application/json'
        )
        yield rsps


def mock_get_boxcars_versions():
    with mocked_boxcars_responses_get():
        return get_boxcars_versions()


def mock_get_default_tokenizer():
    return 'OCRTEST1'


@patch('glynt_api.util.get_default_tokenizer')
def mock_get_tokenizer_versions(mock_param):
    """Allows mocking of util.get_tokenizer_versions() directly. Uses the
    results of mock_get_boxcars_versions() to maintain consistency.
    """
    mock_param.return_value = mock_get_default_tokenizer()
    with mocked_boxcars_responses_get():
        return get_tokenizer_versions()


def mock_post_boxcars_job_resp(job_id):
    return {
        'job_id': job_id
    }


@contextmanager
def mocked_boxcars_responses_post(assert_all_requests_are_fired=True, job_id=0):

    def request_callback(request):
        path_url = request.path_url

        if re.search(r'/job$', path_url):
            resp_body = mock_post_boxcars_job_resp(job_id)
            return (202, {}, json.dumps(resp_body))

        match = re.search(r'status_code/(\d{3})/', path_url)
        if match:
            return (int(match.group(1)), {}, '')

        return (202, {}, '')

    with responses.RequestsMock(
        assert_all_requests_are_fired=assert_all_requests_are_fired
    ) as rsps:
        rsps._url_pattern = r'{}.*'.format(settings.BOXCARS_API_URL)
        rsps.add_callback(
            responses.POST,
            re.compile(rsps._url_pattern),
            callback=request_callback,
            content_type='application/json'
        )
        yield rsps


@patch_tokenize
def create_tokenized(
    data_pool,
    document,
    tokenizer_version=None,
    **kwargs
):
    """Utility to create a tokenized for unit tests, automatically patching as
    needed. Intended as the primary way tokenizeds are created for tests.
    """
    kwargs.pop('tokenize_mocks')

    tokenized = Tokenized(
        data_pool=data_pool,
        document=document,
        tokenizer_version=tokenizer_version or mock_get_tokenizer_versions()['default'],
        **kwargs
    )
    tokenized.save()
    tokenized.refresh_from_db()

    # If we someday want to use this util to create tokenizeds which are
    # not finished, we could add a kwarg to this util and make this check
    # able to be disabled. Generally, users of this util will want a
    # finished tokenized, and we should assert that for them. If we ever
    # add the ability to generate non-finished tokenizeds, we'll need to
    # rethink the asserts that are based on status, above, as well.

    assert tokenized.finished

    tokenized_path = reverse('v6:tokenized-detail', kwargs={
        'data_pools_obfuscated_id': tokenized.data_pool.obfuscated_id,
        'obfuscated_id': tokenized.obfuscated_id
    })

    return (tokenized, tokenized_path)
