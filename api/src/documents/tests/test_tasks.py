from unittest.mock import PropertyMock, patch

from django.test import TransactionTestCase
from pyfakefs.fake_filesystem_unittest import TestCaseMixin

from boxcars.priority import ALL_BOXCARS_PRIORITIES, BOXCARS_PRIORITY_DEFAULT
from documents.exceptions import BoxcarsJobFailed
from documents.models import Tokenized, TextContent
from documents.tasks import (
    _finish_tokenized_job, _start_tokenized_job,
    _wait_for_tokenized_job_success, send_format_id_changed_notification,
    generate_text_preview
)
from documents.tests.util import (
    create_document, create_tokenized, patch_tokenize
)
from organizations.tests.util import create_data_pool, create_organization


class TokenizeTaskTestCase(TransactionTestCase, TestCaseMixin):

    def setUp(self):
        self.setUpPyfakefs()

        self.org, _ = create_organization()
        self.dp, _ = create_data_pool(organization=self.org)
        self.doc, _ = create_document(data_pool=self.dp)
        self.tokenized, _ = create_tokenized(
            data_pool=self.dp, document=self.doc
        )

    @patch_tokenize
    @patch('documents.tasks.execute_tokenized_job')
    def test_start_tokenized_job(self, mock_etj, tokenize_mocks):
        boxcars_priority = BOXCARS_PRIORITY_DEFAULT
        mock_job_id = 'mock_job_id'
        mock_etj.return_value = mock_job_id
        job_id = _start_tokenized_job(
            tokenized_id=self.tokenized.id,
            boxcars_priority=boxcars_priority
        )
        self.assertEqual(job_id, mock_job_id)
        mock_etj.assert_called_with(self.tokenized, boxcars_priority)

        tokenized_id = self.tokenized.id
        self.tokenized.delete()
        with self.assertRaises(Tokenized.DoesNotExist):
            _start_tokenized_job(
                tokenized_id=tokenized_id,
                boxcars_priority=boxcars_priority
            )

    @patch_tokenize
    @patch('documents.tasks.execute_tokenized_job')
    def test_start_tokenized_job_boxcars_priority(self, mock_etj, tokenize_mocks):
        mock_job_id = 'mock_job_id'
        mock_etj.return_value = mock_job_id
        # execute_tokenized_job called with correct boxcars priority
        for boxcars_priority in ALL_BOXCARS_PRIORITIES:
            job_id = _start_tokenized_job(
                tokenized_id=self.tokenized.id,
                boxcars_priority=boxcars_priority
            )
            self.assertEqual(job_id, mock_job_id)
            mock_etj.assert_called_with(self.tokenized, boxcars_priority)

    @patch('documents.tasks.assert_tokenized_job_success')
    def test_wait_for_tokenized_job_complete(self, mock_ets):
        """Test that the underlying job need not still exist while we poll
        core.
        """
        tokenized_id = self.tokenized.id

        mock_ets.return_value = True
        _wait_for_tokenized_job_success('1234', tokenized_id)

        mock_ets.return_value = False
        with self.assertRaises(BoxcarsJobFailed):
            _wait_for_tokenized_job_success('1234', tokenized_id)

        self.tokenized.delete()

        mock_ets.return_value = True
        _wait_for_tokenized_job_success('1234', tokenized_id)

        mock_ets.return_value = False
        with self.assertRaises(BoxcarsJobFailed):
            _wait_for_tokenized_job_success('1234', tokenized_id)

    @patch_tokenize
    @patch('documents.models.Tokenized.tokenized', new_callable=PropertyMock)
    def test_finish_tokenized_job(self, mock_tokenized_data, tokenize_mocks):
        mock_tokenized_data.return_value = {'foo': '1'}
        self.tokenized.set_state('IN_PROGRESS')
        _finish_tokenized_job(self.tokenized.id)
        self.assertTrue(self.tokenized.in_state('SUCCEEDED'))

        mock_tokenized_data.return_value = {}
        self.tokenized.set_state('IN_PROGRESS')
        with self.assertRaises(AssertionError):
            _finish_tokenized_job(self.tokenized.id)
        self.assertTrue(self.tokenized.in_state('FINISHING'))

        tokenized_id = self.tokenized.id
        self.tokenized.delete()
        with self.assertRaises(Tokenized.DoesNotExist):
            _finish_tokenized_job(tokenized_id)


@patch('documents.tasks.get_notification_service')
class SendFormatIdChangedNotificationTestCase(TransactionTestCase):
    def setUp(self):
        self.notification_msg = {
            'document_id': '123',
            'data_pool_id': '456',
            'new_format_id': '789',
            'clients': []
        }

    def test_successful_notification(self, mock_get_service):
        mock_service = mock_get_service.return_value

        send_format_id_changed_notification(self.notification_msg)

        mock_service.notify.assert_called_once()
        event = mock_service.notify.call_args[0][0]
        self.assertEqual(event.document_id, '123')
        self.assertEqual(event.data_pool_id, '456')
        self.assertEqual(event.new_format_id, '789')

    def test_handles_notification_error(self, mock_get_service):
        mock_service = mock_get_service.return_value
        mock_service.notify.side_effect = Exception("Notification failed")

        send_format_id_changed_notification(self.notification_msg)

        mock_service.notify.assert_called_once()


@patch('documents.models.Tokenized.render_text_preview')
class GenerateTextPreviewTestCase(TransactionTestCase, TestCaseMixin):
    def setUp(self):
        self.setUpPyfakefs()
        self.org, _ = create_organization()
        self.dp, _ = create_data_pool(organization=self.org)
        self.doc, _ = create_document(data_pool=self.dp)
        self.tokenized, _ = create_tokenized(data_pool=self.dp, document=self.doc)

    def test_generate_text_preview(self, mock_render_preview):
        sample_preview = "This is a sample document preview text"
        mock_render_preview.return_value = sample_preview

        generate_text_preview(self.tokenized.id)

        mock_render_preview.assert_called_once()
        text_content = TextContent.objects.get(document=self.doc)
        self.assertEqual(text_content.preview, sample_preview)

        # Test with specific exception and error message
        error_message = "Preview generation failed"
        mock_render_preview.side_effect = ValueError(error_message)
        with self.assertRaisesRegex(ValueError, error_message):
            generate_text_preview(self.tokenized.id)
