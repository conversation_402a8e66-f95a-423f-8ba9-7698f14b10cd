import base64
import json
import logging
from http import HTTPStatus
from unittest.mock import ANY, PropertyMock, patch
from uuid import UUID

import pytest
from django.conf import settings
from django.test import (
    Client, TestCase, TransactionTestCase, override_settings
)
from django.urls import reverse
from parameterized import parameterized

from api_storage import storage_client
from boxcars.priority import BOXCARS_PRIORITY_INTERACTIVE
from documents import views
from documents.models import MimeType, DocumentEnrichments, TextContent
from documents.tests.util import (
    create_document, create_tokenized, fake_tokenized_json,
    mock_create_pages_task, mock_get_tokenizer_versions, patch_tokenize
)
from extract.tests.util import create_extraction_batch, create_extraction
from extract.models import PipelineExecution
from training.tests.util import create_training_set
from glynt_api.tests.util import TestCaseMixin, get_api_path
from glynt_api.util import (
    LEADING_WHITESPACE_ERROR_MESSAGE, NONPRINTABLE_CHARACTERS_ERROR_MESSAGE,
    TRAILING_WHITESPACE_ERROR_MESSAGE, calculate_content_md5, reverse_with_params, set_query_field
)
from organizations.models import DataPool, Organization
from organizations.tests.util import (
    DATA_POOL_AWARE_PERMISSION_CLASSES, create_data_pool, create_organization
)
from training.tests.util import (
    create_training_ready_training_set, create_training_revision
)
from users.tests.util import create_staff_user, create_user

logger = logging.getLogger(__name__)

# Omits the special `file_upload_url`
DOCUMENT_BASIC_FIELDS = sorted([
    'id', 'label', 'url', 'created_at', 'enrichments', 'updated_at', 'content_md5',
    'content_type', 'file_extension', 'file_access_url', 'inbound_path',
    'tags', 'format_id', 'glynt_tags', 'status', 'target_training_set', 'filesize',
])
DOCUMENT_ADVANCED_FIELDS = sorted(DOCUMENT_BASIC_FIELDS + [
    'tokenizeds', 'pages',
    'token_count', 'language', 'is_supported',
])
DOCUMENT_LIST_FIELDS = sorted(['count', 'next', 'previous', 'results'])


class ViewsFilterTestCase(TransactionTestCase, TestCaseMixin):

    def setUp(self):
        self.setUpPyfakefs()

        self.staff_user, self.staff_user_path = create_staff_user()

        self.staff_client = Client()
        self.staff_client.login(username=self.staff_user.username, password='12345')

        self.org, self.org_path = create_organization()
        self.dp, self.dp_path = create_data_pool(organization=self.org)
        self.doc, self.doc_path = create_document(data_pool=self.dp, tags=['im_a_tag'])
        self.tokenizer_version = 'OCRTEST1'
        self.tokenized, self.tokenized_path = create_tokenized(
            data_pool=self.dp, document=self.doc,
            tokenizer_version=self.tokenizer_version
        )

    def test_document_filter(self):
        other_doc, _ = create_document(data_pool=self.dp, label='other doc')
        url = get_api_path('v6:document-list', self.dp.obfuscated_id, advanced=True)

        # Each tuple is a query param and value combo
        cases = [
            ('label', self.doc.label),  # full label
            ('label__contains', self.doc.label[:3]),  # partial label
            ('tag', self.doc.tags.first().name),  # full tag
            ('tag__contains', self.doc.tags.first().name[:3]),  # partial tag
        ]

        for case in cases:
            filter_url = set_query_field(url, case[0], case[1])
            logger.debug(f'Attempting filter using url {filter_url}')
            response = self.staff_client.get(filter_url)
            self.assertEqual(response.status_code, 200)
            doc_ids = [doc['id'] for doc in response.json()['results']]
            self.assertIn(self.doc.obfuscated_id, doc_ids)
            self.assertNotIn(other_doc.obfuscated_id, doc_ids)

    def test_tokenized_filter(self):
        other_doc, _ = create_document(data_pool=self.dp, label='other doc')
        other_tokenized, _ = create_tokenized(
            data_pool=self.dp, document=other_doc,
            tokenizer_version=self.tokenizer_version
        )
        url = get_api_path('v6:tokenized-list', self.dp.obfuscated_id, advanced=True)
        filter_url = set_query_field(url, 'document', self.doc.obfuscated_id)
        response = self.staff_client.get(filter_url)
        self.assertEqual(response.status_code, 200)
        content = response.json()

        tokenized_ids_in_results = [tokenized['id'] for tokenized in content['results']]

        self.assertIn(
            self.tokenized.obfuscated_id,
            tokenized_ids_in_results
        )
        self.assertNotIn(
            other_tokenized.obfuscated_id,
            tokenized_ids_in_results
        )

        filter_url = set_query_field(url, 'document', other_doc.obfuscated_id)
        response = self.staff_client.get(filter_url)
        self.assertEqual(response.status_code, 200)
        content = response.json()
        self.assertTrue(self.tokenized.obfuscated_id not in [tokenized['id'] for tokenized in content['results']])
        self.assertTrue(other_tokenized.obfuscated_id in [tokenized['id'] for tokenized in content['results']])

    def test_status_filter_is_data_pool_aware(self):
        other_dp, _ = create_data_pool(organization=self.org, label="other dp")
        other_doc, _ = create_document(data_pool=other_dp, label='other doc')
        other_tokenized, _ = create_tokenized(
            data_pool=other_dp, document=other_doc,
            tokenizer_version=self.tokenizer_version
        )

        self.assertTrue(self.tokenized.in_state('SUCCEEDED'))
        self.assertTrue(other_tokenized.in_state('SUCCEEDED'))

        url = get_api_path('v6:tokenized-list', self.dp.obfuscated_id, advanced=True)
        filter_url = set_query_field(url, 'status', "Success")
        response = self.staff_client.get(filter_url)
        self.assertEqual(response.status_code, 200)
        content = response.json()

        tokenized_ids_in_results = [tokenized['id'] for tokenized in content['results']]

        self.assertIn(
            self.tokenized.obfuscated_id,
            tokenized_ids_in_results
        )

        self.assertNotIn(
            other_tokenized.obfuscated_id,
            tokenized_ids_in_results
        )


class ViewsAuthTestCase(TestCase):

    def test_documents_permissions(self):
        view_set = views.DocumentViewSet()
        permissions = view_set.get_permissions()
        t = [type(p) for p in permissions]
        self.assertCountEqual(t, DATA_POOL_AWARE_PERMISSION_CLASSES)

    def test_tokenizeds_permissions(self):
        view_set = views.TokenizedViewSet()
        permissions = view_set.get_permissions()
        t = [type(p) for p in permissions]
        self.assertCountEqual(t, DATA_POOL_AWARE_PERMISSION_CLASSES)


class DocumentViewsTestCase(TestCaseMixin, TransactionTestCase):

    @classmethod
    def setUpClass(cls):
        super().setUpClass()

    def setUp(self):
        self.setUpPyfakefs()

        self.staff_user, self.staff_user_path = create_staff_user()
        self.staff_client = Client()
        self.staff_client.login(username='staffuser', password='12345')

        self.user, self.user_path = create_user()
        self.client = Client()
        self.client.login(username='normaluser', password='12345')

        # org and data pool which normaluser can interact with
        self.org = Organization(label='Sample Org')
        self.org.save()
        self.org_url = reverse('v6:organization-detail', kwargs={'obfuscated_id': self.org.obfuscated_id})
        self.dp = DataPool(label='Sample Data Pool', organization=self.org)
        self.dp.save()
        self.dp_url = reverse('v6:datapool-detail', kwargs={'obfuscated_id': self.dp.obfuscated_id})
        self.user.organizations.set([self.org])
        self.user.data_pools.set([self.dp])
        self.user.save()
        self.ts, self.ts_path = create_training_set(data_pool=self.dp)
        self.tr, self.tr_path = create_training_revision(
            data_pool=self.dp,
            training_set=self.ts
        )

        assert self.org in self.user.organizations.all()
        assert self.dp in self.user.data_pools.all()

        # Second org and data pool. Only normal user cannot access this
        self.org2 = Organization(label='Staff Only Org')
        self.org2.save()
        self.org2_url = reverse('v6:organization-detail', kwargs={'obfuscated_id': self.org2.obfuscated_id})
        self.dp2 = DataPool(label='Staff Only Data Pool', organization=self.org2)
        self.dp2.save()
        self.dp2_url = reverse('v6:datapool-detail', kwargs={'obfuscated_id': self.dp2.obfuscated_id})

        # Create a document in the accessible data pool, and the inaccessible pool
        self.doc, self.doc_path = create_document(data_pool=self.dp)
        self.doc2, self.doc2_path = create_document(data_pool=self.dp2)

        self.pipeline_status_patch = patch('extract.tasks.track_data_pipeline_status')
        self.pipeline_status_patch.start()
        self.status_tracking_patch = patch('extract.models.PipelineExecution.trigger_pipeline_and_status_tracking')
        self.status_tracking_patch.start()
        self.boto3_patch = patch('extract.util.boto3')
        self.mock_boto3 = self.boto3_patch.start()

        self.create_event_patch = patch('extract.serializers.create_event')
        self.mock_create_event = self.create_event_patch.start()

        self.create_event_expiration_patch = patch('extract.util.create_event')
        self.mock_create_event_expiration = self.create_event_expiration_patch.start()

        self.requests_patch = patch('glynt_api.internal_api.internal_api_client.requests')

    def _update_extraction_to_successful_status(self, extraction_to_update):
        self.dp.glynt_tags.add('transformer:FLL3')
        pipeline_exec = PipelineExecution(data_pool=self.dp, extraction=extraction_to_update)
        pipeline_exec.save()
        pipeline_exec.set_state('SUCCEEDED')
        self.assertTrue(extraction_to_update.finished)
        self.assertEqual(extraction_to_update.data_processing_status, 'Success')

    @patch('documents.tasks.create_pages.delay')
    @patch('documents.models.Document.file_exists', new_callable=PropertyMock)
    def test_generate_pages(self, mock_file_exists, create_pages_mocks):
        generate_pages_url = reverse(
            'v6:document-generate-pages',
            kwargs={
                'data_pools_obfuscated_id': self.dp.obfuscated_id,
                'obfuscated_id': self.doc.obfuscated_id
            }
        )

        mock_file_exists.return_value = False
        response = self.client.post(generate_pages_url)
        self.assertEqual(response.status_code, 404)

        mock_file_exists.return_value = True

        self.doc.page_count = 2
        self.doc.save()
        response = self.client.post(generate_pages_url)
        self.assertEqual(response.status_code, 204)
        create_pages_mocks.assert_not_called()

        self.doc.page_count = 0
        self.doc.save()
        response = self.client.post(generate_pages_url)
        self.assertEqual(response.status_code, 204)
        create_pages_mocks.assert_called_with(self.doc.id)

    def test_get_list(self):
        docs_url = reverse(
            'v6:document-list',
            kwargs={'data_pools_obfuscated_id': self.dp.obfuscated_id}
        )
        response = self.client.get(docs_url)
        self.assertEqual(response.status_code, 200)
        content = response.json()
        self.assertListEqual(
            sorted(list(content.keys())),
            DOCUMENT_LIST_FIELDS
        )
        self.assertEqual(content['count'], 1)
        self.assertEqual(len(content['results']), content['count'])
        self.assertEqual(
            content['results'][0]['id'],
            str(self.doc.obfuscated_id)
        )

    def test_get_list_filter_label(self):
        more_doc_label = 'More Doc'
        more_doc, more_doc_path = create_document(
            data_pool=self.dp,
            label=more_doc_label
        )

        docs_url = reverse(
            'v6:document-list',
            kwargs={'data_pools_obfuscated_id': self.dp.obfuscated_id}
        )
        response = self.client.get(docs_url)
        self.assertEqual(response.status_code, 200)
        content = response.json()
        self.assertListEqual(
            sorted(list(content.keys())),
            DOCUMENT_LIST_FIELDS
        )
        self.assertEqual(content['count'], 2)

        docs_url_filter = '{}?label={}'.format(
            docs_url, more_doc_label.replace(' ', '+')
        )
        response = self.client.get(docs_url_filter)
        self.assertEqual(response.status_code, 200)
        content = response.json()
        self.assertListEqual(
            sorted(list(content.keys())),
            DOCUMENT_LIST_FIELDS
        )
        self.assertEqual(content['count'], 1)
        self.assertEqual(len(content['results']), content['count'])
        self.assertEqual(
            content['results'][0]['id'],
            str(more_doc.obfuscated_id)
        )

    def test_get_detail_basic(self):
        response = self.client.get(self.doc_path)
        self.assertEqual(response.status_code, 200)
        content = response.json()
        self.assertListEqual(sorted(list(content.keys())), DOCUMENT_BASIC_FIELDS)
        self.assertEqual(content['id'], str(self.doc.obfuscated_id))
        self.assertEqual(content['label'], self.doc.label)
        self.assertEqual(content['url'], 'http://testserver{}'.format(self.doc_path))
        self.assertEqual(content['content_md5'], self.doc.content_md5)
        self.assertEqual(content['content_type'], 'application/pdf')
        self.assertEqual(
            content['file_access_url'],
            'http://testserver/v6/data-pools/{}/documents/{}/file/'.format(
                self.dp.obfuscated_id, self.doc.obfuscated_id
            )
        )
        self.assertEqual(content['tags'], [])
        self.assertEqual(content['glynt_tags'], [])

        response = self.client.get(self.doc2_path)
        self.assertEqual(response.status_code, 403)

    def test_get_detail_advanced(self):
        url = set_query_field(self.doc_path, "advanced", "true")
        response = self.staff_client.get(url)
        self.assertEqual(response.status_code, 200)
        content = response.json()
        self.assertListEqual(sorted(list(content.keys())), DOCUMENT_ADVANCED_FIELDS)
        self.assertEqual(content['id'], str(self.doc.obfuscated_id))
        self.assertEqual(content['label'], self.doc.label)
        self.assertEqual(content['url'], 'http://testserver{}?advanced=true'.format(self.doc_path))
        self.assertEqual(content['content_md5'], self.doc.content_md5)
        self.assertEqual(content['content_type'], 'application/pdf')
        self.assertEqual(
            content['file_access_url'],
            'http://testserver/v6/data-pools/{}/documents/{}/file/?advanced=true'.format(
                self.dp.obfuscated_id, self.doc.obfuscated_id
            )
        )
        self.assertEqual(content['token_count'], 0)
        self.assertEqual(content['language'], None)
        self.assertEqual(content['format_id'], None)
        self.assertEqual(content['is_supported'], True)
        self.assertEqual(content['tokenizeds'], [])
        self.assertEqual(content['pages'], [])
        self.assertEqual(content['tags'], [])
        self.assertEqual(content['glynt_tags'], [])

    def test_get_file(self):
        for advanced in [True, False]:
            url = self.doc_path + 'file/'
            if advanced:
                url = set_query_field(url, 'advanced', 'true')
            response = self.client.get(url)
            self.assertEqual(response.status_code, 200)
            content = response.json()
            self.assertListEqual(list(content.keys()), ['file_temp_url'])
            # The presigned urls that the local storage client generates
            # include the phrase "presigned_url", which we use to assert that
            # it has in fact returned a presigned url.
            self.assertIn('presigned_url', content['file_temp_url'])

    def test_document_filter_by_status(self):
        doc, doc_path = create_document(data_pool=self.dp, label="default status doc")

        reviewed_doc_labels = ['REVIEWED_status_doc_2', 'REVIEWED_status_doc_3']
        doc_obfuscated_ids = []
        for doc_label in reviewed_doc_labels:
            doc, doc_path = create_document(data_pool=self.dp, label=doc_label)
            data = {'status': 'REVIEWED'}
            r = self.client.patch(doc_path, data=json.dumps(data), content_type='application/json')
            self.assertEqual(r.status_code, 201)
            content = r.json()
            self.assertEqual(content['status'], 'REVIEWED')
            doc_obfuscated_ids.append(doc.obfuscated_id)

        docs_url = reverse_with_params(
            'v6:document-list',
            kwargs={'data_pools_obfuscated_id': self.dp.obfuscated_id},
            query_params={'status': 'REVIEWED'})

        response = self.client.get(docs_url)
        content = response.json()

        self.assertListEqual(sorted(list(content.keys())), DOCUMENT_LIST_FIELDS)
        self.assertEqual(content['count'], len(reviewed_doc_labels))
        self.assertEqual(len(content['results']), content['count'])

        result_ids = [result_id['id'] for result_id in content['results']]
        self.assertEqual(set(doc_obfuscated_ids), set(result_ids))

    def test_update_updateable_fields(self):
        data = {
            'label': 'a new label',
            'tags': ['a new tag'],
        }
        r = self.client.patch(self.doc_path, data=json.dumps(data), content_type='application/json')
        self.assertEqual(r.status_code, 201)
        content = r.json()
        self.assertEqual(content['label'], 'a new label')
        self.assertEqual(content['tags'], ['a new tag'])

    def test_update_target_trainset(self):
        data = {
            'target_training_set': self.ts.obfuscated_id
        }
        r = self.client.patch(self.doc_path, data=json.dumps(data), content_type='application/json')
        self.assertEqual(r.status_code, 201)
        content = r.json()
        assert content['target_training_set'].endswith(self.ts_path)
        # Ensure we can unset as well
        data = {
            'target_training_set': None,
        }
        r = self.client.patch(self.doc_path, data=json.dumps(data), content_type='application/json')
        self.assertEqual(r.status_code, 201)
        content = r.json()
        assert content['target_training_set'] is None

    def test_default_document_status_field(self):
        more_doc_label = 'More Doc'
        create_document(data_pool=self.dp, label=more_doc_label)

        docs_url = reverse(
            'v6:document-list',
            kwargs={'data_pools_obfuscated_id': self.dp.obfuscated_id}
        )
        response = self.client.get(docs_url)
        self.assertEqual(response.status_code, 200)
        content = response.json()
        document = content['results'][0]
        self.assertEqual(document['status'], 'PENDING_CREATION')

    def test_update_document_status_field(self):
        data = {
            'status': 'REVIEWED'
        }
        r = self.client.patch(self.doc_path, data=json.dumps(data), content_type='application/json')
        self.assertEqual(r.status_code, 201)
        content = r.json()
        self.assertEqual(content['status'], 'REVIEWED')

    def test_illegal_update_document_status_field(self):
        data = {
            'status': 'NONEXISTENT_STATUS'
        }
        r = self.client.patch(self.doc_path, data=json.dumps(data), content_type='application/json')
        self.assertEqual(r.status_code, 400)

    def test_cannot_update_nonupdateable_fields(self):
        data = {'content_md5': 'a new content md5'}
        r = self.client.patch(self.doc_path, data=json.dumps(data), content_type='application/json')
        self.assertEqual(r.status_code, 400)
        content = r.json()
        self.assertEqual(content['content_md5'], ['Parameter may not be updated.'])

        data = {'content_type': 'application/jpeg'}
        r = self.client.patch(self.doc_path, data=json.dumps(data), content_type='application/json')
        self.assertEqual(r.status_code, 400)
        content = r.json()
        self.assertEqual(content['content_type'], ['Parameter may not be updated.'])

        data = {'content': 'some new content'}
        r = self.client.patch(self.doc_path, data=json.dumps(data), content_type='application/json')
        self.assertEqual(r.status_code, 400)
        content = r.json()
        self.assertEqual(content['content'], ['Parameter may not be updated.'])

    def test_non_staff_cannot_update_glynt_tags(self):
        data = {'glynt_tags': ['new_gtag']}
        r = self.client.patch(
            self.doc_path, data=json.dumps(data), content_type='application/json'
        )
        self.assertEqual(r.status_code, 400)

    def test_staff_can_update_glynt_tags(self):
        update_data = {'glynt_tags': ['new_gtag']}
        r = self.staff_client.patch(
            self.doc_path, data=json.dumps(update_data), content_type='application/json'
        )
        self.assertEqual(r.status_code, 201)
        content = r.json()
        self.assertEqual(content['glynt_tags'], update_data['glynt_tags'])

    def _assert_extraction_equal(self, first: dict, second: object):
        self.assertEqual(first['id'], second.obfuscated_id)
        self.assertEqual(first['created_at'],
                         second.created_at.isoformat().replace('+00:00', 'Z'))
        self.assertEqual(first['updated_at'],
                         second.updated_at.isoformat().replace('+00:00', 'Z'))
        self.assertEqual(first['training_set'],
                         second.training_set.obfuscated_id)
        self.assertEqual(first['status'], second.get_status_display)
        self.assertEqual(first['data_processing_status'],
                         second.data_processing_status)
        self.assertEqual(first['finished'], second.finished)
        self.assertListEqual(first['tags'], [n for n in second.tags.names()])
        self.assertListEqual(first['glynt_tags'], [
            n for n in second.glynt_tags.names()])

    def test_get_list_by_extraction_batch(self):
        training_set, _ = create_training_ready_training_set(data_pool=self.dp)
        training_revision, _ = create_training_revision(data_pool=self.dp, training_set=training_set)

        doc, _ = create_document(data_pool=self.dp)
        extraction_batch, _ = create_extraction_batch(data_pool=self.dp, documents=[doc, self.doc],
                                                      training_revision=training_revision, transform=True)
        create_extraction_batch(data_pool=self.dp, documents=[doc, self.doc],
                                training_revision=training_revision, transform=True)

        docs_url = reverse_with_params(
            'v6:document-list',
            kwargs={'data_pools_obfuscated_id': self.dp.obfuscated_id},
            query_params={'show_related_extractions': True,
                          'extraction_batch': extraction_batch.obfuscated_id})
        response = self.client.get(docs_url)
        self.assertEqual(response.status_code, HTTPStatus.OK)
        content = response.json()
        self.assertListEqual(
            sorted(list(content.keys())),
            DOCUMENT_LIST_FIELDS
        )
        self.assertEqual(content['count'], 2)
        self.assertEqual(len(content['results']), content['count'])
        self.assertIn(doc.obfuscated_id, [r['id'] for r in content['results']])
        self.assertIn(self.doc.obfuscated_id, [r['id'] for r in content['results']])

        result = [r for r in content['results'] if r['id'] == doc.obfuscated_id][0]
        self.assertIn('related_extractions', result)
        self.assertIsNotNone(result['related_extractions'])

        # assert the contents it's the expected
        self.assertEqual(result['id'], str(doc.obfuscated_id))
        expected_extraction = [e for e in extraction_batch.extractions.all() if e.document == doc][0]
        self.assertEqual(len(result['related_extractions']), 1)
        related_extraction = result['related_extractions'][0]
        self._assert_extraction_equal(related_extraction, expected_extraction)

    def test_get_list_by_extraction_batch_extraction_missing(self):
        training_set, _ = create_training_ready_training_set(data_pool=self.dp)
        training_revision, _ = create_training_revision(data_pool=self.dp, training_set=training_set)

        doc, _ = create_document(data_pool=self.dp)
        extraction_batch, _ = create_extraction_batch(data_pool=self.dp, documents=[doc, self.doc],
                                                      training_revision=training_revision, transform=True)

        docs_url = reverse_with_params(
            'v6:document-list',
            kwargs={'data_pools_obfuscated_id': self.dp.obfuscated_id},
            query_params={'show_related_extractions': True,
                          'extraction_batch': extraction_batch.obfuscated_id})
        response = self.client.get(docs_url)
        self.assertEqual(response.status_code, HTTPStatus.OK)
        content = response.json()
        self.assertEqual(content['count'], 2)
        self.assertEqual(len(content['results']), content['count'])

        extraction = extraction_batch.extractions.filter(document=doc)
        extraction.delete()
        response = self.client.get(docs_url)
        content = response.json()
        self.assertEqual(content['count'], 1)
        self.assertEqual(len(content['results']), content['count'])
        self.assertIn('related_extractions', content['results'][0])

    def test_get_list_by_extraction_batch_not_extracted_data(self):
        training_set, _ = create_training_ready_training_set(data_pool=self.dp)
        training_revision, _ = create_training_revision(data_pool=self.dp, training_set=training_set)

        doc, _ = create_document(data_pool=self.dp)
        extraction_batch, _ = create_extraction_batch(data_pool=self.dp, documents=[doc, self.doc],
                                                      training_revision=training_revision, transform=True)

        docs_url = reverse_with_params(
            'v6:document-list',
            kwargs={'data_pools_obfuscated_id': self.dp.obfuscated_id},
            query_params={'show_related_extractions': False,
                          'extraction_batch': extraction_batch.obfuscated_id})
        response = self.client.get(docs_url)
        self.assertEqual(response.status_code, HTTPStatus.OK)
        content = response.json()
        self.assertEqual(content['count'], 2)
        self.assertEqual(len(content['results']), content['count'])

        self.assertNotIn('related_extractions', content['results'][0])
        self.assertNotIn('related_extractions', content['results'][1])

    def test_get_list_by_extraction_batch_and_label_filter(self):
        training_set, _ = create_training_ready_training_set(data_pool=self.dp)
        training_revision, _ = create_training_revision(data_pool=self.dp, training_set=training_set)

        doc, _ = create_document(data_pool=self.dp, label='named document number 1')
        extraction_batch, _ = create_extraction_batch(data_pool=self.dp, documents=[doc, self.doc],
                                                      label='extraction 1',
                                                      training_revision=training_revision, transform=True)
        create_extraction_batch(data_pool=self.dp, documents=[doc, self.doc],
                                label='extraction 2',
                                training_revision=training_revision, transform=True)

        docs_url = reverse_with_params(
            'v6:document-list',
            kwargs={'data_pools_obfuscated_id': self.dp.obfuscated_id},
            query_params={'show_related_extractions': True,
                          'label__contains': 'named document number 1',
                          'extraction_batch': extraction_batch.obfuscated_id})
        response = self.client.get(docs_url)
        self.assertEqual(response.status_code, HTTPStatus.OK)
        content = response.json()
        self.assertListEqual(sorted(list(content.keys())), DOCUMENT_LIST_FIELDS)
        self.assertEqual(content['count'], 1)
        self.assertEqual(len(content['results']), content['count'])
        self.assertEqual(doc.obfuscated_id, content['results'][0]['id'])

        result = content['results'][0]
        self.assertIn('related_extractions', result)
        self.assertIsNotNone(result['related_extractions'])
        self.assertEqual(result['id'], str(doc.obfuscated_id))
        expected_extraction = [e for e in extraction_batch.extractions.all() if e.document == doc][0]

        self.assertEqual(len(result['related_extractions']), 1)
        related_extraction = result['related_extractions'][0]
        self._assert_extraction_equal(related_extraction, expected_extraction)

    def test_get_list_with_extraction_data(self):
        training_set, _ = create_training_ready_training_set(data_pool=self.dp)
        training_revision, _ = create_training_revision(data_pool=self.dp, training_set=training_set)

        doc, _ = create_document(data_pool=self.dp, label='named document number 1')
        extraction_batch_1, _ = create_extraction_batch(data_pool=self.dp, documents=[doc, self.doc],
                                                        label='extraction 1',
                                                        training_revision=training_revision, transform=True)
        expected_extraction_1 = [e for e in extraction_batch_1.extractions.all() if e.document == doc][0]
        extraction_batch_2, _ = create_extraction_batch(data_pool=self.dp, documents=[doc, self.doc],
                                                        label='extraction 2',
                                                        training_revision=training_revision, transform=True)
        expected_extraction_2 = [e for e in extraction_batch_2.extractions.all() if e.document == doc][0]

        docs_url = reverse_with_params(
            'v6:document-list',
            kwargs={'data_pools_obfuscated_id': self.dp.obfuscated_id},
            query_params={'show_related_extractions': True, 'limit': 20})
        response = self.client.get(docs_url)
        self.assertEqual(response.status_code, HTTPStatus.OK)
        content = response.json()
        self.assertListEqual(sorted(list(content.keys())), DOCUMENT_LIST_FIELDS)
        self.assertEqual(content['count'], 11)
        self.assertEqual(len(content['results']), 11)
        self.assertIn(doc.obfuscated_id, [c['id'] for c in content['results']])

        result = next(c for c in content['results'] if c['id'] == doc.obfuscated_id)
        self.assertIn('related_extractions', result)
        self.assertIsNotNone(result['related_extractions'])
        self.assertEqual(result['id'], str(doc.obfuscated_id))

        self.assertEqual(len(result['related_extractions']), 2)
        self.assertIn(expected_extraction_1.obfuscated_id, (r['id'] for r in result['related_extractions']))
        self.assertIn(expected_extraction_2.obfuscated_id, (r['id'] for r in result['related_extractions']))

    def test_verify_document(self):

        ex, self.ex_path = create_extraction(
            data_pool=self.dp, training_revision=self.tr, document=self.doc
        )

        self._update_extraction_to_successful_status(ex)

        url = get_api_path(
            'v6:extraction-verify',
            self.dp.obfuscated_id,
            advanced=True,
            obfuscated_id=ex.obfuscated_id
        )
        data = {'is_verified': True}
        response = self.staff_client.post(url, data=json.dumps(data), content_type='application/json')

        self.assertEqual(response.status_code, HTTPStatus.NO_CONTENT)
        ex.refresh_from_db()

        assert ex.document.status == 'VERIFIED'

        data = {'is_verified': False}
        response = self.staff_client.post(url, data=json.dumps(data), content_type='application/json')
        self.assertEqual(response.status_code, HTTPStatus.NO_CONTENT)
        ex.refresh_from_db()

        assert ex.document.status == "PENDING_REVIEW"

    def test_verify_documents_in_eb(self):
        extraction_batch, _ = create_extraction_batch(data_pool=self.dp, documents=[self.doc],
                                                      training_revision=self.tr, transform=True)
        extraction_batch.save()

        extraction = extraction_batch.extractions.first()
        self._update_extraction_to_successful_status(extraction)

        url = get_api_path(
            'v6:extractionbatch-verify',
            self.dp.obfuscated_id,
            advanced=True,
            obfuscated_id=extraction_batch.obfuscated_id
        )

        data = {'is_verified': True}
        response = self.staff_client.post(url, data=json.dumps(data), content_type='application/json')
        self.assertEqual(response.status_code, HTTPStatus.NO_CONTENT)

        extraction.refresh_from_db()

        assert extraction.document.status == 'VERIFIED'


class DocumentCreateViewsTestCase(TestCase, TestCaseMixin):

    @classmethod
    def setUpClass(cls):
        super().setUpClass()

        cls.staff_user, cls.staff_user_path = create_staff_user()
        cls.staff_client = Client()

        cls.user, cls.user_path = create_user()
        cls.client = Client()

        # org and data pool which normaluser can interact with
        cls.org = Organization(label='Sample Org')
        cls.org.save()
        cls.org_url = reverse('v6:organization-detail', kwargs={'obfuscated_id': cls.org.obfuscated_id})
        cls.dp = DataPool(label='Sample Data Pool', organization=cls.org)
        cls.dp.save()
        cls.dp_url = reverse('v6:datapool-detail', kwargs={'obfuscated_id': cls.dp.obfuscated_id})
        cls.user.organizations.set([cls.org])
        cls.user.data_pools.set([cls.dp])
        cls.user.save()
        assert cls.org in cls.user.organizations.all()
        assert cls.dp in cls.user.data_pools.all()

        # Second org and data pool. Only normal user cannot access this
        cls.org2 = Organization(label='Staff Only Org')
        cls.org2.save()
        cls.org2_url = reverse('v6:organization-detail', kwargs={'obfuscated_id': cls.org2.obfuscated_id})
        cls.dp2 = DataPool(label='Staff Only Data Pool', organization=cls.org2)
        cls.dp2.save()
        cls.dp2_url = reverse('v6:datapool-detail', kwargs={'obfuscated_id': cls.dp2.obfuscated_id})

        # URLs we'll use a bunch throughout these tests
        cls.list_url = reverse(
            'v6:document-list',
            kwargs={'data_pools_obfuscated_id': cls.dp.obfuscated_id}
        )
        cls.list_url_advanced = set_query_field(cls.list_url, 'advanced', 'true')

    def setUp(self):
        self.setUpPyfakefs()
        self.staff_client.login(username='staffuser', password='12345')
        self.client.login(username='normaluser', password='12345')

    def test_cannot_create_document_in_unassociated_data_pool(self):
        # basic
        url = reverse(
            'v6:document-list',
            kwargs={'data_pools_obfuscated_id': self.dp2.obfuscated_id}
        )
        response = self.client.post(url, data='foobar', content_type='application/json')
        self.assertEqual(response.status_code, 403)

        # advanced
        advanced_url = set_query_field(url, 'advanced', 'true')
        response = self.client.post(advanced_url, data='foobar', content_type='application/json')
        self.assertEqual(response.status_code, 403)

    def test_create_basic_with_content(self):
        document_content = b'some_content'
        data = {
            'label': 'Sample Created Document',
            'content_type': 'image/tiff',
            'content': base64.b64encode(document_content).decode()
        }
        response = self.client.post(self.list_url, data=json.dumps(data), content_type='application/json')

        self.assertEqual(response.status_code, 201)
        content = response.json()
        self.assertListEqual(
            sorted(list(content.keys())),
            sorted(DOCUMENT_BASIC_FIELDS)
        )
        self.assertEqual(content['label'], 'Sample Created Document')
        self.assertEqual(
            content['url'],
            'http://testserver/v6/data-pools/{}/documents/{}/'.format(self.dp.obfuscated_id, content['id'])
        )
        self.assertEqual(content['content_md5'], calculate_content_md5(document_content))
        self.assertEqual(content['content_type'], 'image/tiff')
        self.assertEqual(
            content['file_access_url'],
            'http://testserver/v6/data-pools/{}/documents/{}/file/'.format(self.dp.obfuscated_id, content['id'])
        )

        file_access_url = content['file_access_url']
        response = self.staff_client.get(file_access_url)
        file_temp_url = response.json()['file_temp_url']
        response = self.staff_client.get(file_temp_url)

        self.assertEqual(response.content, document_content)

    def test_create_basic_octet(self):
        data = {
            'label': 'Sample Created Document',
            'content_type': 'application/octet-stream',
            'content_md5': 'some_content_md5',
        }
        response = self.client.post(self.list_url, data=json.dumps(data), content_type='application/json')

        self.assertEqual(response.status_code, 201)

    def test_create_with_file_extension(self):
        document_content = b'some_content'
        # Create a msword document with docx ext and content type of application/zip
        data = {
            'label': 'Sample Created Document',
            'content_type': 'application/zip',
            'content': base64.b64encode(document_content).decode(),
            'file_extension': '.docx'
        }
        response = self.client.post(self.list_url, data=json.dumps(data), content_type='application/json')

        self.assertEqual(response.status_code, 201)
        content = response.json()
        self.assertEqual(content['content_type'], 'application/zip')
        self.assertEqual(content['file_extension'], '.docx')

    def test_create_with_inbound_path(self):
        document_content = b'some_content'
        # Create a msword document with docx ext and content type of application/zip
        data = {
            'label': 'Sample Created Document',
            'content_type': 'application/zip',
            'content': base64.b64encode(document_content).decode(),
            'inbound_path': 'vendor2/project2'
        }
        response = self.client.post(self.list_url, data=json.dumps(data), content_type='application/json')

        self.assertEqual(response.status_code, 201)
        content = response.json()
        self.assertEqual(content['content_type'], 'application/zip')
        self.assertEqual(content['inbound_path'], 'vendor2/project2')

    def test_create_invalid_content_types(self):
        document_content = b'some_content'
        content_types = [
            'application',
            'application/',
            '/html',
            'image /space',
            'text/ sp ace',
            '////abc',
            'abc////',
        ]
        for content_type in content_types:
            data = {
                'label': 'Sample Created Document',
                'content_type': content_type,
                'content': base64.b64encode(document_content).decode(),
            }
            response = self.client.post(self.list_url, data=json.dumps(data), content_type='application/json')

            self.assertEqual(response.status_code, 400)
            error_message = response.json()["content_type"][0]
            self.assertTrue("Invalid content_type" in error_message)

    def test_create_basic_with_md5(self):
        document_content = b'some_content'
        content_md5 = calculate_content_md5(document_content)
        data = {
            'label': 'Sample Created Document',
            'content_type': 'image/tiff',
            'content': base64.b64encode(document_content).decode(),
            'content_md5': content_md5
        }
        response = self.client.post(self.list_url, data=json.dumps(data), content_type='application/json')

        self.assertEqual(response.status_code, 201)
        self.assertEqual(response.json()['content_md5'], content_md5)
        self.assertEqual(response.json()['file_extension'], '.tiff')

    def test_cannot_create_basic_with_bad_md5(self):
        document_content = b'some_content'
        data = {
            'label': 'Sample Created Document',
            'content_type': 'image/tiff',
            'content': base64.b64encode(document_content).decode(),
            'content_md5': 'foobar'
        }
        response = self.client.post(self.list_url, data=json.dumps(data), content_type='application/json')

        self.assertEqual(response.status_code, 400)
        self.assertEqual(
            response.json()['content_md5'],
            [
                f'The calculated content_md5 ({calculate_content_md5(document_content)}) '
                'does not match the provided content_md5 (foobar).'
            ]
        )

    def test_cannot_create_basic_without_content_or_md5(self):
        data = {
            'label': 'Sample Created Document',
            'content_type': 'image/tiff'
        }
        response = self.client.post(self.list_url, data=json.dumps(data), content_type='application/json')

        self.assertEqual(response.status_code, 400)
        self.assertEqual(
            response.json()['non_field_errors'],
            ['At least one of content or content_md5 must be included during creation.']
        )

    def test_create_basic_with_empty_file(self):
        data = {
            'label': 'Empty Document',
            'content_type': 'application/pdf',
            'content': '',
        }
        response = self.client.post(self.list_url, data=json.dumps(data), content_type='application/json')
        doc = response.json()

        self.assertEqual(response.status_code, 201)

        # Check the content_md5 is as expected
        expected_md5 = calculate_content_md5(b'')
        self.assertEqual(doc['content_md5'], expected_md5)
        self.assertEqual(doc['label'], data['label'])
        self.assertEqual(doc['content_type'], data['content_type'])
        self.assertEqual(
            doc['file_access_url'],
            'http://testserver/v6/data-pools/{}/documents/{}/file/'.format(self.dp.obfuscated_id, doc['id'])
        )

    def test_cannot_create_basic_with_bad_content(self):
        data = {
            'label': 'Sample Created Document',
            'content_type': 'image/tiff',
            'content': 'foobar'
        }
        response = self.client.post(self.list_url, data=json.dumps(data), content_type='application/json')

        self.assertEqual(response.status_code, 400)
        self.assertEqual(
            response.json()['content'],
            ['Error decoding content. Check that the content is Base64 encoded.']
        )

    def test_create_basic_without_content(self):
        data = {
            'label': 'Sample Created Document',
            'content_type': 'image/tiff',
            'content_md5': 'some_content_md5',
        }
        response = self.client.post(self.list_url, data=json.dumps(data), content_type='application/json')

        self.assertEqual(response.status_code, 201)
        content = response.json()
        self.assertListEqual(
            sorted(list(content.keys())),
            sorted(DOCUMENT_BASIC_FIELDS + ['file_upload_url'])
        )
        self.assertEqual(content['label'], 'Sample Created Document')
        self.assertEqual(
            content['url'],
            'http://testserver/v6/data-pools/{}/documents/{}/'.format(self.dp.obfuscated_id, content['id'])
        )
        self.assertEqual(content['content_md5'], 'some_content_md5')
        self.assertEqual(content['content_type'], 'image/tiff')
        self.assertEqual(
            content['file_access_url'],
            'http://testserver/v6/data-pools/{}/documents/{}/file/'.format(self.dp.obfuscated_id, content['id'])
        )
        self.assertEqual(content['tags'], [])
        self.assertEqual(content['glynt_tags'], [])

    def test_create_advanced_without_content(self):
        # Create a mimetype so we can test that it's supported
        MimeType.objects.get_or_create(mime_type='image/tiff', is_supported=True)
        data = {
            'label': 'Sample Created Document',
            'content_type': 'image/tiff',
            'content_md5': 'some_content_md5',
            'language': 'en',
            'token_count': 42,
            'format_id': 'A4',
            'filesize': 1000,
        }
        response = self.staff_client.post(self.list_url_advanced,
                                          data=json.dumps(data),
                                          content_type='application/json')

        self.assertEqual(response.status_code, 201)
        content = response.json()
        self.assertListEqual(
            sorted(list(content.keys())),
            sorted(DOCUMENT_ADVANCED_FIELDS + ['file_upload_url'])
        )
        self.assertEqual(content['label'], 'Sample Created Document')
        self.assertEqual(
            content['url'],
            'http://testserver/v6/data-pools/{}/documents/{}/?advanced=true'.format(
                self.dp.obfuscated_id, content['id']
            )
        )
        self.assertEqual(content['content_md5'], 'some_content_md5')
        self.assertEqual(content['content_type'], 'image/tiff')
        self.assertEqual(
            content['file_access_url'],
            'http://testserver/v6/data-pools/{}/documents/{}/file/?advanced=true'.format(
                self.dp.obfuscated_id, content['id']
            )
        )
        self.assertEqual(content['pages'], [])
        self.assertEqual(content['tokenizeds'], [])
        self.assertEqual(content['tags'], [])
        self.assertEqual(content['glynt_tags'], [])
        self.assertEqual(content['language'], 'en')
        self.assertEqual(content['token_count'], 42)
        self.assertEqual(content['format_id'], 'A4')
        self.assertEqual(content['is_supported'], True)
        self.assertEqual(content['filesize'], 1000)

    def test_create_duplicate_md5(self):
        """Creating multiple documents with the same md5 should not raise any errors."""
        response = self.staff_client.get(self.list_url_advanced)
        initial_n_docs = response.json()['count']
        n_docs_created = 0

        data = {
            'label': 'Sample Created Document',
            'content_type': 'image/tiff',
            'content_md5': 'some_content_md5',
        }
        response = self.staff_client.post(self.list_url_advanced,
                                          data=json.dumps(data),
                                          content_type='application/json')
        self.assertEqual(response.status_code, 201)
        n_docs_created += 1

        # same label should not be a problem
        response = self.staff_client.post(self.list_url_advanced,
                                          data=json.dumps(data),
                                          content_type='application/json')
        self.assertEqual(response.status_code, 201)
        n_docs_created += 1

        data['label'] = 'A different label'
        response = self.staff_client.post(self.list_url_advanced,
                                          data=json.dumps(data),
                                          content_type='application/json')
        self.assertEqual(response.status_code, 201)
        n_docs_created += 1

        # Check that we can still see the list view
        response = self.staff_client.get(self.list_url_advanced)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()['count'], initial_n_docs + n_docs_created)

    def test_create_with_non_printable_characters_label(self):
        non_printable_chars = ["\t", "\r", "\n", "\xa0"]
        document_content = b'some_content'
        content_md5 = calculate_content_md5(document_content)
        for char in non_printable_chars:
            data = {
                'label': f'Non-printable {char} Extraction Batch',
                'content_type': 'image/tiff',
                'content': base64.b64encode(document_content).decode(),
                'content_md5': content_md5
            }
            post_data = json.dumps(data)

            response = self.staff_client.post(
                self.list_url,
                data=post_data,
                content_type="application/json"
            )

            self.assertEqual(response.status_code, 400)
            content = response.json()
            self.assertIn('label', content)
            self.assertListEqual(content['label'], [NONPRINTABLE_CHARACTERS_ERROR_MESSAGE])

    def test_create_with_leading_whitespace_label(self):
        document_content = b'some_content'
        content_md5 = calculate_content_md5(document_content)
        data = {
            'label': ' Leading Whitespace Document',
            'content_type': 'image/tiff',
            'content': base64.b64encode(document_content).decode(),
            'content_md5': content_md5
        }
        response = self.client.post(self.list_url, data=json.dumps(data), content_type='application/json')
        logger.debug(response.json())
        self.assertEqual(response.status_code, 400)
        content = response.json()
        self.assertIn('label', content)
        self.assertListEqual(content['label'], [LEADING_WHITESPACE_ERROR_MESSAGE])

    def test_create_with_trailing_whitespace_label(self):
        document_content = b'some_content'
        content_md5 = calculate_content_md5(document_content)
        data = {
            'label': 'Trailing Whitespace Document ',
            'content_type': 'image/tiff',
            'content': base64.b64encode(document_content).decode(),
            'content_md5': content_md5
        }
        response = self.client.post(self.list_url, data=json.dumps(data), content_type='application/json')
        self.assertEqual(response.status_code, 400)
        content = response.json()
        self.assertIn('label', content)
        self.assertListEqual(content['label'], [TRAILING_WHITESPACE_ERROR_MESSAGE])

    @patch('documents.actions.image_document_passes_file_check')
    @patch('documents.tasks.create_pages.delay')
    def test_upload_document_content_after_create(self, create_pages_mocks, mock_image_document_passes_file_check):
        mock_image_document_passes_file_check.return_value = True
        create_pages_mocks.side_effect = mock_create_pages_task

        # TODO: this only tests the happy case, add detailed tests (expired
        # file_upload_url, bad uploads, etc.)

        # pause and resume fake filesystem
        self.pause()
        doc_path = settings.COMMON_FIXTURES_PATH / 'sample_doc.tif'
        with open(doc_path, 'rb') as f:
            document_content = f.read()
        self.resume()
        # First, calculate the document Content MD5
        content_md5 = calculate_content_md5(document_content)
        self.assertEqual(content_md5, 'eYgJ7AICbTxIL3e2WLAs8Q==')

        # Second, create the document object
        data = {
            'label': 'Sample Uploaded Document',
            'content_type': 'image/tiff',
            'content_md5': content_md5,
        }
        response = self.staff_client.post(self.list_url_advanced,
                                          data=json.dumps(data),
                                          content_type='application/json')
        content = response.json()
        file_access_url = content['file_access_url']
        file_upload_url = content['file_upload_url']
        obfuscated_id = content['id']

        # Next, upload the document content
        response = self.staff_client.put(
            file_upload_url, data=document_content,
            CONTENT_TYPE='image/tiff',
            HTTP_CONTENT_MD5=content_md5
        )
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.content, b'')

        # Should be able to retrieve the content with a pair of gets
        response = self.staff_client.get(file_access_url)
        file_temp_url = response.json()['file_temp_url']
        response = self.staff_client.get(file_temp_url)

        self.assertEqual(response.content, document_content)

        # TODO: this test really doesn't care about pages - split pages to a different test
        # Generate pages for the document
        generate_pages_url = set_query_field(
            reverse(
                'v6:document-generate-pages',
                kwargs={
                    'data_pools_obfuscated_id': self.dp.obfuscated_id,
                    'obfuscated_id': obfuscated_id
                }
            ),
            'advanced', 'true'
        )
        response = self.staff_client.post(generate_pages_url)
        self.assertEqual(response.status_code, 204)

        # Check that document detail now shows links to pages
        detail_url = set_query_field(
            reverse(
                'v6:document-detail',
                kwargs={
                    'data_pools_obfuscated_id': self.dp.obfuscated_id,
                    'obfuscated_id': obfuscated_id
                }
            ),
            'advanced', 'true'
        )
        response = self.staff_client.get(detail_url)
        content = response.json()
        self.assertEqual(len(content['pages']), 1)

        get_page_url = set_query_field(
            reverse(
                'v6:document-pages',
                kwargs={
                    'data_pools_obfuscated_id': self.dp.obfuscated_id,
                    'obfuscated_id': obfuscated_id,
                    'page_number': 1
                }
            ),
            'advanced', 'true'
        )
        self.assertEqual(content['pages'][0], 'http://testserver{}'.format(get_page_url))

        # Finally, get the generated page content
        response = self.staff_client.get(get_page_url)
        self.assertEqual(response.status_code, 200)
        content = response.json()
        page_temp_url = content['file_temp_url']

        # and check that the stored content can be retrieved
        response = self.staff_client.get(page_temp_url)
        self.assertEqual(response.status_code, 200)

    def test_create_without_label(self):
        data = {
            'content_type': 'image/tiff',
            'content_md5': 'some_content_md5',
        }
        response = self.client.post(self.list_url,
                                    data=json.dumps(data),
                                    content_type='application/json')

        content = response.json()
        label = content['label']
        UUID(label, version=4)  # Will raise if label is not a valid v4 uuid

    def test_create_basic_with_content_storage_failure(self):
        """Test that we gracefully handle the case where there is an error
        storing the document content to the storage system when creating a
        document in basic mode.
        """
        # in django 2.2, the 500 exception response template is stored in a
        # file that django needs access to in order for this test to run
        # correctly (in previous versions of django the template was stored
        # directly in the module file). Thus, while maintaining the rest
        # of the fake filesystem, we add the templates dir to the fake
        # filesystem so that the Expection raised by this test can be processed
        # correctly by the view.

        response = self.client.get(self.list_url)
        initial_n_docs = response.json()['count']

        document_content = b'some_content'
        data = {
            'label': 'Sample Created Document',
            'content_type': 'image/tiff',
            'content': base64.b64encode(document_content).decode()
        }
        with patch('documents.models.Document.store_file_content') as mock_sfc:
            mock_sfc.side_effect = RuntimeError('Example error creating document')
            # If it raises, a 500 error would be served, which is correct
            with self.assertRaises(RuntimeError):
                self.client.post(
                    self.list_url,
                    data=json.dumps(data),
                    content_type='application/json'
                )

        # assert that Document instance is cleaned up after failure
        response = self.client.get(self.list_url)
        final_n_docs = response.json()['count']
        self.assertEqual(initial_n_docs, final_n_docs)


class UnscopedDocumentViewsTestCase(TestCaseMixin, TransactionTestCase):

    @classmethod
    def setUpClass(cls):
        super().setUpClass()

    def setUp(self):
        self.setUpPyfakefs()

        self.staff_user, self.staff_user_path = create_staff_user()
        self.staff_client = Client()
        self.staff_client.login(username='staffuser', password='12345')

        self.org1, _ = create_organization(label='Org A')
        self.dp1, _ = create_data_pool(organization=self.org1)
        self.doc1, self.doc1_path = create_document(
            data_pool=self.dp1, label='Doc A1', page_count=2, content_md5='aaa', language='en',
            token_count=1203, format_id="format_a1", filesize=1000
        )
        self.org2, _ = create_organization(label='Org B')
        self.dp2, _ = create_data_pool(organization=self.org2)
        self.doc2, _ = create_document(data_pool=self.dp2, label='Doc B1', page_count=1,
                                       content_md5='bbb', language='fr', token_count=500,
                                       format_id="format_b1", filesize=2000)

        self.tc1 = TextContent.objects.create(document=self.doc1, preview="Hello, world! Account: 1234-1999")
        self.tc2 = TextContent.objects.create(document=self.doc2, preview="Bonjour, le monde! Surcharge: $42.23")

        self.ts1, self.ts1_path = create_training_set(data_pool=self.dp1, label='Trainset ABC')
        self.ts2, self.ts2_path = create_training_set(data_pool=self.dp2, label='Trainset XYZ')

        # Set target training set for docs to check filters
        self.doc1.target_training_set = self.ts1
        self.doc1.enrichments = DocumentEnrichments.objects.create(
            data_pool=self.dp1, enrichment_key="foo", data={"foo": "bar"})
        self.doc2.target_training_set = self.ts2
        self.doc1.save()
        self.doc2.save()

        self.url = reverse('v6:unscoped-document-list')

    def test_get_list(self):
        response = self.staff_client.get(self.url)
        self.assertEqual(response.status_code, 200)
        content = response.json()
        self.assertListEqual(
            sorted(list(content.keys())),
            DOCUMENT_LIST_FIELDS
        )
        self.assertEqual(content['count'], 2)
        retrieved_ids = {doc['id'] for doc in content['results']}
        self.assertIn(str(self.doc1.obfuscated_id), retrieved_ids)
        self.assertIn(str(self.doc2.obfuscated_id), retrieved_ids)

        # Assert text preview is included in the response
        preview_url = f"{self.url}?with_preview=true"
        response = self.staff_client.get(preview_url)
        self.assertEqual(response.status_code, 200)
        results = response.json()["results"]
        expected = {
            self.doc1.obfuscated_id: self.tc1.preview,
            self.doc2.obfuscated_id: self.tc2.preview,
        }
        for result in results:
            self.assertEqual(result['text_preview'], expected[result['id']])

    @pytest.mark.skip(reason="Skipping text regex filter test, not supported in sqlite")
    def test_text_regex_filter(self):
        filter_url = rf"{self.url}?text__regex=Account:\s\d{4}-\d{4}"
        response = self.staff_client.get(filter_url)
        self.assertEqual(response.status_code, 200)
        content = response.json()
        self.assertEqual(content['count'], 1)
        self.assertEqual(content['results'][0]['id'], self.doc1.obfuscated_id)

    def test_text_contains_filter(self):
        # Test with contains
        filter_url = rf"{self.url}?text__contains=Account"
        response = self.staff_client.get(filter_url)
        self.assertEqual(response.status_code, 200)
        content = response.json()
        self.assertEqual(content['count'], 1)
        self.assertEqual(content['results'][0]['id'], self.doc1.obfuscated_id)

        # Test with icontains
        filter_url = rf"{self.url}?text__icontains=bonjour&text__contains=Surcharge"
        response = self.staff_client.get(filter_url)
        self.assertEqual(response.status_code, 200)
        content = response.json()
        self.assertEqual(content['count'], 1)
        self.assertEqual(content['results'][0]['id'], self.doc2.obfuscated_id)

        # Test anding with a not found text
        filter_url = rf"{self.url}?text__icontains=bonjour&text__contains=not_found"
        response = self.staff_client.get(filter_url)
        self.assertEqual(response.status_code, 200)
        content = response.json()
        self.assertEqual(content['count'], 0)

    def test_filter_format_id(self):
        format_id = "format_b1"
        filter_url = f"{self.url}?format_id={format_id}"
        response = self.staff_client.get(filter_url)
        self.assertEqual(response.status_code, 200)
        content = response.json()
        self.assertEqual(content['count'], 1)
        self.assertEqual(content['results'][0]['format_id'], format_id)
        filter_url = f"{self.url}?format_id={self.doc1.format_id},{self.doc2.format_id}"
        response = self.staff_client.get(filter_url)
        self.assertEqual(response.status_code, 200)
        content = response.json()
        self.assertEqual(content['count'], 2)

    def test_filter_target_trainset_label(self):
        label1 = self.ts1.label
        filter_url = f"{self.url}?target_training_set_label={label1}"
        response = self.staff_client.get(filter_url)
        self.assertEqual(response.status_code, 200)
        content = response.json()
        self.assertEqual(content['count'], 1)
        self.assertEqual(content['results'][0]['target_training_set_label'], label1)
        self.assertEqual(content['results'][0]['target_training_set'], self.ts1.obfuscated_id)

    def test_filter_target_trainset_id(self):
        value = self.ts2.obfuscated_id
        filter_url = f"{self.url}?target_training_set={value}"
        response = self.staff_client.get(filter_url)
        self.assertEqual(response.status_code, 200)
        content = response.json()
        self.assertEqual(content['count'], 1)
        self.assertEqual(content['results'][0]['target_training_set_label'], self.ts2.label)
        self.assertEqual(content['results'][0]['target_training_set'], self.ts2.obfuscated_id)

    def test_filter_by_language(self):
        langs = ['en', 'fr']
        for lang in langs:
            filter_url = f"{self.url}?language={lang}"
            response = self.staff_client.get(filter_url)
            self.assertEqual(response.status_code, 200)
            content = response.json()
            self.assertEqual(content['count'], 1)
            self.assertEqual(content['results'][0]['language'], lang)
        lang_csv = ','.join(langs)
        filter_url = f"{self.url}?language={lang_csv}"
        response = self.staff_client.get(filter_url)
        self.assertEqual(response.status_code, 200)
        content = response.json()
        self.assertEqual(content['count'], 2)
        langs_found = {doc['language'] for doc in content['results']}
        self.assertSetEqual(set(langs), langs_found)

    def test_filter_by_language_exclude(self):
        langs = ['en', 'fr']
        lang_csv = ','.join(langs)
        filter_url = f"{self.url}?exclude_language={lang_csv}"
        response = self.staff_client.get(filter_url)
        self.assertEqual(response.status_code, 200)
        content = response.json()
        self.assertEqual(content['count'], 0)
        # exclude en
        filter_url = f"{self.url}?exclude_language=en"
        response = self.staff_client.get(filter_url)
        self.assertEqual(response.status_code, 200)
        content = response.json()
        self.assertEqual(content['count'], 1)
        self.assertEqual(content['results'][0]['language'], 'fr')

    def test_filter_by_names(self):
        filter_url = f"{self.url}?name=a1"
        response = self.staff_client.get(filter_url)
        self.assertEqual(response.status_code, 200)
        content = response.json()
        self.assertEqual(content['count'], 1)
        # assert name is Doc A1
        self.assertEqual(content['results'][0]['label'], self.doc1.label)
        # Now exclude Doc A1
        filter_url = f"{self.url}?exclude_name=a1"
        response = self.staff_client.get(filter_url)
        self.assertEqual(response.status_code, 200)
        content = response.json()
        self.assertEqual(content['count'], 1)
        # assert name is Doc B1
        self.assertEqual(content['results'][0]['label'], self.doc2.label)

    def test_filter_by_id(self):
        filter_url = f"{self.url}?id={self.doc1.obfuscated_id}"
        response = self.staff_client.get(filter_url)
        self.assertEqual(response.status_code, 200)
        content = response.json()
        self.assertEqual(content['count'], 1)
        self.assertEqual(content['results'][0]['id'], self.doc1.obfuscated_id)
        # test exclude_id
        filter_url = f"{self.url}?exclude_id={self.doc1.obfuscated_id}"
        response = self.staff_client.get(filter_url)
        self.assertEqual(response.status_code, 200)
        content = response.json()
        self.assertEqual(content['count'], 1)
        self.assertEqual(content['results'][0]['id'], self.doc2.obfuscated_id)

    def test_filter_by_page_count_range(self):
        cases = {
            (1, 1): 1,
            (2, 2): 1,
            (0, 0): 0,
            (0, 2): 2,
        }
        for page_range, count in cases.items():
            filter_url = f"{self.url}?page_count_min={page_range[0]}&page_count_max={page_range[1]}"
            response = self.staff_client.get(filter_url)
            self.assertEqual(response.status_code, 200)
            content = response.json()
            self.assertEqual(content['count'], count)

    def test_filter_by_token_count_range(self):
        ranges = {
            (1000, 1300): 1,
            (400, 600): 1,
            (0, 100): 0,
            (0, 10000): 2,
        }
        for token_range, count in ranges.items():
            filter_url = f"{self.url}?token_count_min={token_range[0]}&token_count_max={token_range[1]}"
            response = self.staff_client.get(filter_url)
            self.assertEqual(response.status_code, 200)
            content = response.json()
            self.assertEqual(content['count'], count)

    def test_duplicate_count_within_same_data_pool(self):
        num_create_dups = 5
        dup_md5 = 'xxx'
        for _ in range(num_create_dups):
            duplicate_doc1, _ = create_document(data_pool=self.dp1, label='Doc A2 dup',
                                                content_md5=dup_md5)

        response = self.staff_client.get(self.url)
        self.assertEqual(response.status_code, 200)
        content = response.json()

        for doc in content['results']:
            if doc['content_md5'] == dup_md5:
                # -1 to exclude the original document
                self.assertEqual(doc['duplicate_count'], num_create_dups - 1)
            else:
                self.assertEqual(doc['duplicate_count'], 0)

    def test_duplicate_count_across_different_data_pools(self):
        duplicate_doc2_different_pool, _ = create_document(data_pool=self.dp1, label='Doc B1 dup diff datapool',
                                                           content_md5='bbb')

        response = self.staff_client.get(self.url)
        self.assertEqual(response.status_code, 200)
        content = response.json()

        for doc in content['results']:
            self.assertEqual(doc['duplicate_count'], 0)

    def test_no_duplicates(self):
        response = self.staff_client.get(self.url)
        self.assertEqual(response.status_code, 200)
        content = response.json()

        for doc in content['results']:
            self.assertEqual(doc['duplicate_count'], 0)

    def test_related_extractions(self):
        training_set, _ = create_training_ready_training_set(data_pool=self.dp1)
        training_revision, _ = create_training_revision(data_pool=self.dp1, training_set=training_set)

        extraction_batch, _ = create_extraction_batch(data_pool=self.dp1, documents=[self.doc1],
                                                      training_revision=training_revision, transform=True)

        docs_url = reverse_with_params(
            'v6:unscoped-document-list',
            query_params={'advanced': 'true', 'limit': 100}
        )

        response = self.staff_client.get(docs_url)
        self.assertEqual(response.status_code, 200)
        content = response.json()

        extraction = extraction_batch.extractions.first()
        for doc in content['results']:
            if doc['id'] == self.doc1.obfuscated_id:
                self.assertTrue('extractions' in doc)
                self.assertEqual(len(doc['extractions']), 1)
                self.assertEqual(doc['extractions'][0]['id'], extraction.obfuscated_id)
                self.assertEqual(doc['extractions'][0]['training_set'], training_set.obfuscated_id)
                self.assertEqual(doc['extractions'][0]['training_set_label'], training_set.label)
                self.assertEqual(doc['extractions'][0]['extraction_batch'], extraction_batch.obfuscated_id)
                self.assertEqual(doc['extractions'][0]['extraction_batch_label'], extraction_batch.label)
                break
        else:
            self.fail('Document with expected extraction not found in response')

    def test_filesize_filter(self):
        cases = (
            (800, 1200, 1),
            (1200, 1500, 0),
            (1000, 2000, 2),
        )
        for minsize, maxsize, expected_count in cases:
            filter_url = f"{self.url}?filesize_min={minsize}&filesize_max={maxsize}"
            response = self.staff_client.get(filter_url)
            self.assertEqual(response.status_code, 200)
            content = response.json()
            self.assertEqual(content['count'], expected_count)

    def test_enrichments(self):
        response = self.staff_client.get(self.doc1_path)
        self.assertEqual(response.status_code, 200)
        content = response.json()
        self.assertEqual(content['enrichments'], {'foo': 'bar'})


class TokenizerVersionsViewTestCase(TransactionTestCase, TestCaseMixin):

    def setUp(self):
        self.user, self.user_path = create_user()
        self.client = Client()
        self.client.login(username=self.user.username, password='12345')
        self.tokenizer_versions_url = reverse('v6:documents:tokenizer-versions')

    @patch('documents.views.get_tokenizer_versions')
    def test_list_tokenizer_versions_happy_case(self, mock_gtv):
        gtv = mock_get_tokenizer_versions()
        mock_gtv.return_value = gtv

        advanced_url = set_query_field(self.tokenizer_versions_url, "advanced", "true")
        resp = self.client.get(advanced_url)
        self.assertEqual(resp.status_code, 200)
        resp_data = resp.json()
        self.assertEqual(resp_data['active'], sorted(gtv['active']))
        self.assertEqual(resp_data['deprecated'], sorted(gtv['deprecated']))
        self.assertEqual(resp_data['obsolete'], sorted(gtv['obsolete']))
        self.assertEqual(resp_data['experimental'], sorted(gtv['experimental']))

    def test_basic_mode(self):
        resp = self.client.get(self.tokenizer_versions_url)
        self.assertEqual(resp.status_code, 404)

        for method in ['post', 'patch', 'delete', 'put']:
            resp = self.client.generic(method, self.tokenizer_versions_url)
            self.assertEqual(resp.status_code, 405)

    def test_other_actions_at_tokenizer_versions_endpoint(self):
        advanced_url = set_query_field(self.tokenizer_versions_url, "advanced", "true")
        for method in ['post', 'patch', 'delete', 'put']:
            resp = self.client.generic(method, advanced_url)
            self.assertEqual(resp.status_code, 405)


class TokenizedViewsTestCase(TransactionTestCase, TestCaseMixin):

    def setUp(self):
        self.setUpPyfakefs()
        self.staff_user, self.staff_user_path = create_staff_user()

        self.staff_client = Client()
        self.staff_client.login(username=self.staff_user.username, password='12345')

        self.org, self.org_path = create_organization()
        self.dp, self.dp_path = create_data_pool(organization=self.org)
        self.doc, self.doc_path = create_document(data_pool=self.dp)
        self.tokenizer_version = 'AWS'
        self.tokenized, self.tokenized_path = create_tokenized(
            data_pool=self.dp, document=self.doc, tokenizer_version=self.tokenizer_version
        )

    def test_tokenized_get_detail_basic(self):
        response = self.staff_client.get(self.tokenized_path)
        self.assertEqual(response.status_code, 404)

    @patch('documents.views.retokenize_tokenized')
    def test_tokenized_get_detail_advanced(self, retokenize_tokenized_mock):
        url = set_query_field(self.tokenized_path, "advanced", "true")
        response = self.staff_client.get(url)
        self.assertEqual(response.status_code, 200)
        content = response.json()
        self.assertListEqual(
            sorted(list(content.keys())),
            sorted([
                'id', 'url', 'created_at', 'updated_at', 'document',
                'tokenizer_version', 'status', 'finished', 'tokenized'
            ])
        )
        self.assertEqual(content['id'], str(self.tokenized.obfuscated_id))
        self.assertEqual(content['url'], 'http://testserver{}?advanced=true'.format(self.tokenized_path))
        self.assertEqual(content['document'], 'http://testserver{}?advanced=true'.format(self.doc_path))
        self.assertEqual(content['tokenizer_version'], self.tokenized.tokenizer_version)
        self.assertEqual(content['status'], 'Success')
        self.assertEqual(content['finished'], True)

        retokenize_tokenized_mock.assert_not_called()

    @patch_tokenize
    @patch('documents.views.retokenize_tokenized')
    def test_tokenized_get_detail_advanced_refreshed(self, retokenize_tokenized_mock, tokenize_mocks):
        url = set_query_field(self.tokenized_path, "advanced", "true")
        url = set_query_field(url, "refresh", "true")
        response = self.staff_client.get(url)
        self.assertEqual(response.status_code, 200)
        content = response.json()
        self.assertListEqual(
            sorted(list(content.keys())),
            sorted([
                'id', 'url', 'created_at', 'updated_at', 'document',
                'tokenizer_version', 'status', 'finished', 'tokenized'
            ])
        )
        self.assertEqual(content['id'], str(self.tokenized.obfuscated_id))
        self.assertEqual(content['url'], 'http://testserver{}?advanced=true'.format(self.tokenized_path))
        self.assertEqual(content['document'], 'http://testserver{}?advanced=true'.format(self.doc_path))
        self.assertEqual(content['tokenizer_version'], self.tokenized.tokenizer_version)
        self.assertEqual(content['status'], 'Success')
        self.assertEqual(content['finished'], True)

        retokenize_tokenized_mock.assert_called_once()

    def test_tokenized_create_basic(self):
        data = {
            'document': self.doc.obfuscated_id,
            'tokenizer_version': 'OCRTEST1'
        }
        url = get_api_path('v6:tokenized-list', self.dp.obfuscated_id, advanced=False)
        response = self.staff_client.post(
            url, data=json.dumps(data), content_type='application/json'
        )
        self.assertEqual(response.status_code, 404)

    def _generate_tokenized_data(self, tokenized):
        storage = storage_client()
        logging.info("file_storage_client is {}".format(type(storage)))

        presigned_get_url = tokenized.document.file_temp_url
        # TODO verify that this is failing due to the source document not really being present
        r = self.staff_client.get(presigned_get_url)
        logging.info("presigned_get_url -> {}".format(r.status_code))  # -> Not Found: /local-files/presigned_url/

        presigned_put_url = storage.generate_presigned_put_url(
            bucket=tokenized.bucket_name,
            remote_path=tokenized.remote_path,
            content_type='application/json',
            content_md5=None)

        payload = fake_tokenized_json(tokenized.document)

        # TODO for reference, this is what's being replaced
        """
        storage.upload_fileobj(
            bucket=tokenized.bucket_name,
            remote_path=tokenized.remote_path,
            fileobj=io.BytesIO(payload)
        )
        """
        r = self.staff_client.put(
            presigned_put_url,
            data=payload.encode(),
            content_type='application/json')
        logging.info("presigned_put_url -> {}".format(r.status_code))

    @patch_tokenize
    def test_tokenized_create_advanced(self, tokenize_mocks):
        data = {
            'document': self.doc.obfuscated_id,
            'tokenizer_version': 'OCRTEST1'
        }
        url = get_api_path('v6:tokenized-list', self.dp.obfuscated_id, advanced=True)

        response = self.staff_client.post(
            url, data=json.dumps(data), content_type='application/json'
        )

        self.assertEqual(response.status_code, 201)
        content = response.json()
        self.assertListEqual(
            sorted(list(content.keys())),
            sorted([
                'id', 'url', 'created_at', 'updated_at', 'document',
                'tokenizer_version', 'status', 'finished', 'tokenized'
            ])
        )
        self.assertEqual(
            content['url'],
            'http://testserver/v6/data-pools/{}/tokenizeds/{}/?advanced=true'.format(
                self.dp.obfuscated_id, content['id']
            )
        )
        self.assertEqual(content['document'], 'http://testserver{}?advanced=true'.format(self.doc_path))
        self.assertEqual(content['tokenizer_version'], 'OCRTEST1')

        tok_resp = self.staff_client.get(content['url']).json()
        # Prove that tokenized is present and non-empty
        self.assertEqual(tok_resp['status'], 'Success')
        self.assertEqual(tok_resp['finished'], True)
        self.assertIsNotNone(tok_resp['tokenized'])
        tokenized = tok_resp['tokenized']
        self.assertNotEqual(len(tokenized), 0)
        self.assertIsNotNone(tokenized['updatedDate'])
        self.assertIsNotNone(tokenized['expiredDate'])
        tokenized_json = json.loads(fake_tokenized_json(self.doc))
        tokenized_json['updatedDate'] = tokenized['updatedDate']
        tokenized_json['expiredDate'] = tokenized['expiredDate']
        # Prove that the contents are as expected
        self.assertEqual(
            tokenized,
            tokenized_json)

    @patch_tokenize
    def test_tokenized_create_advanced_boxcars_priority_from_glynt_ui(self, tokenize_mocks):
        """requests from Glynt UI tokenize with high boxcars priority"""
        glynt_ui_url = 'https://testserver'
        origin = glynt_ui_url
        data = {
            'document': self.doc.obfuscated_id,
            'tokenizer_version': 'OCRTEST1'
        }
        url = get_api_path('v6:tokenized-list', self.dp.obfuscated_id, advanced=True)
        with override_settings(GLYNT_UI_URL=glynt_ui_url):
            response = self.staff_client.post(
                url, data=json.dumps(data), content_type='application/json',
                HTTP_ORIGIN=origin
            )
        self.assertEqual(response.status_code, 201)
        tokenize_mocks['tokenize'].assert_called_once_with(
            boxcars_priority=BOXCARS_PRIORITY_INTERACTIVE, tokenized_id=ANY
        )

    @patch_tokenize
    def test_tokenized_create_advanced_boxcars_priority_no_origin(self, tokenize_mocks):
        """request with no HTTP_ORIGIN tokenized without special boxcars priority"""
        data = {
            'document': self.doc.obfuscated_id,
            'tokenizer_version': 'OCRTEST1'
        }
        url = get_api_path('v6:tokenized-list', self.dp.obfuscated_id, advanced=True)

        response = self.staff_client.post(
            url, data=json.dumps(data), content_type='application/json'
        )
        self.assertEqual(response.status_code, 201)
        tokenize_mocks['tokenize'].assert_called_once_with(boxcars_priority=None, tokenized_id=ANY)

    @patch_tokenize
    def test_tokenized_create_advanced_boxcars_priority_explicit_priority_in_query_is_rejected(self, tokenize_mocks):
        """
        boxcars_priority is obscure from end user, not requestable through the API
        """
        data = {
            'document': self.doc.obfuscated_id,
            'tokenizer_version': 'OCRTEST1',
            'boxcars_priority': 1
        }
        url = get_api_path('v6:tokenized-list', self.dp.obfuscated_id, advanced=True)

        response = self.staff_client.post(
            url, data=json.dumps(data), content_type='application/json'
        )
        self.assertEqual(response.status_code, 400)
        tokenize_mocks['tokenize'].assert_not_called()

    @patch_tokenize
    def test_tokenized_create_advanced_boxcars_priority_other_origin(self, tokenize_mocks):
        """request from other HTTP_ORIGIN tokenized without special boxcars priority"""
        glynt_ui_url = 'https://testserver'
        origin = 'https://example.com'
        data = {
            'document': self.doc.obfuscated_id,
            'tokenizer_version': 'OCRTEST1'
        }
        url = get_api_path('v6:tokenized-list', self.dp.obfuscated_id, advanced=True)
        with override_settings(GLYNT_UI_URL=glynt_ui_url):
            response = self.staff_client.post(
                url, data=json.dumps(data), content_type='application/json',
                HTTP_ORIGIN=origin
            )
        self.assertEqual(response.status_code, 201)
        tokenize_mocks['tokenize'].assert_called_once_with(boxcars_priority=None, tokenized_id=ANY)

    @patch_tokenize
    def test_tokenized_create_advanced_invalid_document_id(self, tokenize_mocks):
        data = {'document': 'an_invalid_id'}
        url = get_api_path('v6:tokenized-list', self.dp.obfuscated_id, advanced=True)

        response = self.staff_client.post(
            url, data=json.dumps(data), content_type='application/json'
        )

        self.assertEqual(response.status_code, 400)
        content = response.json()
        self.assertEqual(
            'Invalid id "an_invalid_id" - object does not exist.', content['document'][0]
        )

    @patch_tokenize
    def test_tokenized_create_advanced_invalid_tokenizer_version(self, tokenize_mocks):
        data = {
            'document': self.doc.obfuscated_id,
            'tokenizer_version': 'an_invalid_version'  # this is what is invalid and under test
        }
        url = get_api_path('v6:tokenized-list', self.dp.obfuscated_id, advanced=True)

        response = self.staff_client.post(
            url, data=json.dumps(data), content_type='application/json'
        )

        self.assertEqual(response.status_code, 400)
        content = response.json()
        self.assertIn(
            'Unknown tokenizer version: an_invalid_version. Current choices are: ', content['tokenizer_version'][0]
        )

    @patch_tokenize
    def test_tokenized_create_advanced_no_tokenizer_version(self, tokenize_mocks):
        """Effectively the same as the happy case, so we don't test quite as
        much. mostly, checking to see that tokenizer_version got set to the
        default version as expected.
        """
        tokenizer_versions = tokenize_mocks['get_tokenizer_versions'].return_value
        data = {'document': self.doc.obfuscated_id}
        url = get_api_path('v6:tokenized-list', self.dp.obfuscated_id, advanced=True)

        response = self.staff_client.post(
            url, data=json.dumps(data), content_type='application/json'
        )

        self.assertEqual(response.status_code, 201)
        content = response.json()
        self.assertListEqual(
            sorted(list(content.keys())),
            sorted([
                'id', 'url', 'created_at', 'updated_at', 'document',
                'tokenizer_version', 'status', 'finished', 'tokenized'
            ])
        )
        self.assertEqual(content['tokenizer_version'], tokenizer_versions['default'])

    @patch_tokenize
    def test_tokenized_create_advanced_default_tokenizer_version(self, tokenize_mocks):
        """Effectively the same as the happy case, so we don't test quite as
        much. mostly, checking to see that if tokenizer_version is set to
        "default", then it uses the default version as expected.
        """
        tokenizer_versions = tokenize_mocks['get_tokenizer_versions'].return_value
        data = {'document': self.doc.obfuscated_id, 'tokenizer_version': 'default'}
        url = get_api_path('v6:tokenized-list', self.dp.obfuscated_id, advanced=True)

        response = self.staff_client.post(
            url, data=json.dumps(data), content_type='application/json'
        )

        self.assertEqual(response.status_code, 201)
        content = response.json()
        self.assertListEqual(
            sorted(list(content.keys())),
            sorted([
                'id', 'url', 'created_at', 'updated_at', 'document',
                'tokenizer_version', 'status', 'finished', 'tokenized'
            ])
        )
        self.assertEqual(content['tokenizer_version'], tokenizer_versions['default'])

    @patch_tokenize
    def test_tokenized_create_advanced_invalid_document_param_type(self, tokenize_mocks):
        # this is what is invalid and under test - notice it is a list
        data = {'document': ['foo', 'bar']}
        url = get_api_path('v6:tokenized-list', self.dp.obfuscated_id, advanced=True)

        # There was an issue at one time where the error string was correct
        # every other time it was called, due to a state issue in the custom
        # HyperlinkRelatedField. This is for loop simple precaution against
        # that returning.
        for _ in range(0, 2):
            response = self.staff_client.post(
                url, data=json.dumps(data), content_type='application/json'
            )

            self.assertEqual(response.status_code, 400)
            content = response.json()
            self.assertEqual(
                content['document'],
                ['Incorrect type. Expected ID or URL string, received list.']
            )


class DocumentEnrichmentViewsTestCase(TransactionTestCase, TestCaseMixin):

    def setUp(self):
        self.setUpPyfakefs()
        self.staff_user, self.staff_user_path = create_staff_user()

        self.staff_client = Client()
        self.staff_client.login(username=self.staff_user.username, password='12345')

        self.org, self.org_path = create_organization()
        self.dp, self.dp_path = create_data_pool(organization=self.org)
        self.doc, self.doc_detail_url = create_document(data_pool=self.dp)
        self.list_url = reverse(
            'v6:documentenrichments-list',
            kwargs={'data_pools_obfuscated_id': self.dp.obfuscated_id}
        )

    def test_create_enrichment_no_doc(self):
        data = {
            "data": {"foo": "bar"},
            "enrichment_key": "foo"
        }
        resp = self.staff_client.post(self.list_url, data=json.dumps(data), content_type='application/json')
        self.assertEqual(resp.status_code, 201)
        content = resp.json()
        self.assertEqual(
            content,
            {
                'id': content['id'],
                'created_at': content['created_at'],
                'updated_at': content['updated_at'],
                'documents': [],
                'data': {'foo': 'bar'},
                'enrichment_key': 'foo'}
        )

        self.staff_client.get(self.doc_detail_url)

    def test_create_no_enrichment_key(self):
        data = {
            "data": {"foo": "bar"}
        }
        resp = self.staff_client.post(self.list_url, data=json.dumps(data), content_type='application/json')
        self.assertEqual(resp.status_code, 201)
        self.assertTrue(resp.json()['enrichment_key'] is not None)

    def test_already_exists_error(self):
        data = {
            "data": {"foo": "bar"},
            "enrichment_key": "foo"
        }
        self.staff_client.post(self.list_url, data=json.dumps(data), content_type='application/json')
        data['data'] = {"baz": "bat"}
        resp = self.staff_client.post(self.list_url, data=json.dumps(data), content_type='application/json')
        self.assertEqual(resp.status_code, 400)

    def test_create_by_put(self):
        data = {
            "data": {"foo": "bar"}
        }
        url = reverse(
            'v6:documentenrichments-detail',
            kwargs={'data_pools_obfuscated_id': self.dp.obfuscated_id, 'enrichment_key': 'foo'}
        )
        resp = self.staff_client.put(url, data=json.dumps(data), content_type='application/json')
        self.assertEqual(resp.status_code, 201)
        self.assertEqual(resp.json()['data'], data['data'])

    def test_update_by_put(self):
        data = {
            "data": {"foo": "bar"},
            "enrichment_key": "foo"
        }
        resp = self.staff_client.post(self.list_url, data=json.dumps(data), content_type='application/json')
        url = reverse(
            'v6:documentenrichments-detail',
            kwargs={'data_pools_obfuscated_id': self.dp.obfuscated_id, 'enrichment_key': 'foo'}
        )
        patch_data = {"data": {"patched": "data"}}
        resp = self.staff_client.put(url, data=json.dumps(patch_data), content_type='application/json')
        self.assertEqual(resp.status_code, 200)
        content = resp.json()
        self.assertEqual(
            content,
            {
                'id': content['id'],
                'created_at': content['created_at'],
                'updated_at': content['updated_at'],
                'documents': [],
                'data': {"patched": "data", "foo": "bar"},
                'enrichment_key': 'foo'}
        )

    @parameterized.expand([
        ({"foo": {"bar": "baz"}}),
        ("string"),
        ({"foo": 123}),
    ])
    def test_create_enrichments_invalid_data(self, enrichment_data):
        data = {
            "data": enrichment_data,
            "enrichment_key": "foo"
        }
        resp = self.staff_client.post(self.list_url, data=json.dumps(data), content_type='application/json')
        self.assertEqual(resp.status_code, 400)

    def test_set_multiple_docs_to_row(self):
        doc2, doc2_url = create_document(data_pool=self.dp)
        data = {
            "data": {"foo": "bar"},
            "enrichment_key": "foo",
            "documents": [self.doc.obfuscated_id, doc2.obfuscated_id]
        }
        resp = self.staff_client.post(self.list_url, data=json.dumps(data), content_type='application/json')
        self.assertEqual(resp.status_code, 201)
        self.assertEqual(resp.json()['documents'], data['documents'])

        resp = self.staff_client.get(doc2_url)
        self.assertEqual(resp.json()['enrichments'], data['data'])

    def test_set_docs_on_enrichment_row(self):
        data = {
            "data": {"foo": "bar"},
            "enrichment_key": "foo",
            "documents": [self.doc.obfuscated_id]
        }
        resp = self.staff_client.post(self.list_url, data=json.dumps(data), content_type='application/json')
        self.assertEqual(resp.status_code, 201)
        self.assertEqual(resp.json()['documents'], [self.doc.obfuscated_id])

        resp = self.staff_client.get(self.doc_detail_url)
        self.assertEqual(resp.json()['enrichments'], data['data'])

    def test_unassign_doc_from_enrichment_row(self):
        data = {
            "data": {"foo": "bar"},
            "enrichment_key": "foo",
            "documents": [self.doc.obfuscated_id]
        }
        self.staff_client.post(self.list_url, data=json.dumps(data), content_type='application/json')
        url = reverse(
            'v6:documentenrichments-detail',
            kwargs={'data_pools_obfuscated_id': self.dp.obfuscated_id, 'enrichment_key': 'foo'}
        )
        resp = self.staff_client.patch(url, data=json.dumps({'documents': []}), content_type='application/json')
        self.assertEqual(resp.status_code, 200)
        self.assertEqual(resp.json()['documents'], [])

    def test_reassign_doc_to_new_row(self):
        data = {
            "data": {"foo": "bar"},
            "documents": [self.doc.obfuscated_id]
        }
        self.staff_client.post(self.list_url, data=json.dumps(data), content_type='application/json')
        data['data'] = {"baz": "bat"}
        resp = self.staff_client.post(self.list_url, data=json.dumps(data), content_type='application/json')
        self.assertEqual(resp.status_code, 201)

        resp = self.staff_client.get(self.doc_detail_url)
        self.assertEqual(resp.json()['enrichments'], data['data'])

    def test_list(self):
        data = {
            "data": {"foo": "bar"},
        }
        for i in range(5):
            data['enrichment_key'] = str(i)
            self.staff_client.post(self.list_url, data=json.dumps(data), content_type='application/json')

        resp = self.staff_client.get(self.list_url)
        self.assertEqual(resp.status_code, 200)
        self.assertEqual(resp.json()['count'], 5)

    def test_delete(self):
        data = {
            "data": {"foo": "bar"},
            "documents": [self.doc.obfuscated_id]
        }
        resp = self.staff_client.post(self.list_url, data=json.dumps(data), content_type='application/json')
        url = reverse(
            'v6:documentenrichments-detail',
            kwargs={'data_pools_obfuscated_id': self.dp.obfuscated_id, 'enrichment_key': resp.json()['enrichment_key']}
        )
        resp = self.staff_client.delete(url)
        self.assertEqual(resp.status_code, 204)


class DocumentManifestCSVViewTestCase(TestCaseMixin, TransactionTestCase):
    def setUp(self):
        self.setUpPyfakefs()
        self.staff_user, self.staff_user_path = create_staff_user()
        self.staff_client = Client()
        self.staff_client.login(username=self.staff_user.username, password='12345')

        # Create organization and data pool
        self.org, _ = create_organization(label='Test Organization')
        self.dp, _ = create_data_pool(organization=self.org, label='Test Data Pool')

        # Create documents with different timestamps
        from datetime import datetime, timedelta
        from django.utils.timezone import utc

        # Set base time for controlled testing
        base_time = datetime(2023, 1, 1, 12, 0, 0, tzinfo=utc)

        # Create first document (older)
        self.doc1, _ = create_document(
            data_pool=self.dp,
            label='Document 1',
            content_md5='MTIzNDU2Nzg5MDEyMw==',
            language='en',
            page_count=5,
            token_count=1000,
            format_id='abc123',
            filesize=9401
        )
        # Manually set created_at time
        self.doc1.created_at = base_time
        self.doc1.save(update_fields=['created_at'])

        # Create second document (newer)
        self.doc2, _ = create_document(
            data_pool=self.dp,
            label='Document 2',
            content_md5='QUJDREVGMTIzNDU2Nzg=',
            language='fr',
            page_count=10,
            token_count=2000,
            format_id='xyz123',
            filesize=9310
        )
        # Set created_at to be precisely 1 hour newer than doc1
        self.doc2.created_at = base_time + timedelta(hours=1)
        self.doc2.save(update_fields=['created_at'])

        # Create text content for documents
        TextContent.objects.create(document=self.doc1, preview="Doc1 Gas Utility Bill Acct: 8675309")
        TextContent.objects.create(document=self.doc2, preview="Doc2 Energy Account # **********")

        # URL for manifest CSV view
        self.csv_url = reverse(
            'v6:manifest:document-manifest-csv',
            kwargs={'data_pools_obfuscated_id': self.dp.obfuscated_id}
        )

    def test_manifest_csv_format(self):
        """Test that the CSV has the correct format and content"""
        response = self.staff_client.get(self.csv_url)

        # Check response properties
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'text/csv; charset=utf-8')
        self.assertTrue('attachment; filename=' in response['Content-Disposition'])

        # Read the CSV content
        content = b''.join(response.streaming_content)
        csv_text = content.decode('utf-8')
        csv_lines = csv_text.strip().split('\n')

        # Check header row
        headers = csv_lines[0].strip().split(',')
        expected_headers = [
            'org_id', 'data_pool_id', 'id', 'created_at', 'format_id',
            'page_count', 'token_count', 'language', 'content_md5',
            'inbound_path', 'label', 'file_extension', 'filesize', 'hex_md5',
            'text_preview'
        ]

        for header in expected_headers:
            self.assertIn(header, headers)

        # Check that we have the expected number of rows (header + 2 documents)
        self.assertEqual(len(csv_lines), 3)

        # Check document data is present
        doc1_data = [row for row in csv_lines[1:] if self.doc1.label in row]
        doc2_data = [row for row in csv_lines[1:] if self.doc2.label in row]

        self.assertEqual(len(doc1_data), 1)
        self.assertEqual(len(doc2_data), 1)

    def test_manifest_csv_filter_by_since_id(self):
        """Test filtering the manifest by since_id parameter"""
        # Request CSV with filter
        filtered_url = f"{self.csv_url}?since_id={self.doc1.obfuscated_id}"
        response = self.staff_client.get(filtered_url)

        self.assertEqual(response.status_code, 200)

        # Read and parse the CSV
        content = b''.join(response.streaming_content)
        csv_text = content.decode('utf-8')
        csv_lines = csv_text.strip().split('\n')

        # Should have only header + 1 document (the later one)
        self.assertEqual(len(csv_lines), 2)

        # The remaining row should be doc2
        self.assertIn(self.doc2.label, csv_lines[1])
        self.assertNotIn(self.doc1.label, csv_lines[1])

    def test_manifest_csv_empty(self):
        """Test manifest CSV with empty data pool (no documents)"""
        # Create a new empty data pool
        empty_dp, _ = create_data_pool(organization=self.org, label='Empty Data Pool')

        # Get CSV for empty data pool
        empty_csv_url = reverse(
            'v6:manifest:document-manifest-csv',
            kwargs={'data_pools_obfuscated_id': empty_dp.obfuscated_id}
        )

        response = self.staff_client.get(empty_csv_url)

        self.assertEqual(response.status_code, 200)

        # Read CSV content
        content = b''.join(response.streaming_content)
        csv_text = content.decode('utf-8')
        csv_lines = csv_text.strip().split('\n')

        # Should only have the header row
        self.assertEqual(len(csv_lines), 1)

    def test_manifest_data_pool_404(self):
        """Test manifest CSV with non-existent data pool (404)"""
        # Create a new data pool with a different organization
        new_org, _ = create_organization(label='New Organization')
        new_dp, _ = create_data_pool(organization=new_org, label='New Data Pool')

        # Get CSV for non-existent data pool
        non_existent_url = reverse(
            'v6:manifest:document-manifest-csv',
            kwargs={'data_pools_obfuscated_id': "abc123"}
        )
        response = self.staff_client.get(non_existent_url)
        self.assertEqual(response.status_code, 404)
