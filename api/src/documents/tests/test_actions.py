import io
import logging
import os
import re
import unittest
from io import Bytes<PERSON>
from pathlib import Path
from tempfile import TemporaryDirectory
from unittest.mock import MagicMock, PropertyMock, patch

import pytest
import requests
from django.conf import settings
from django.test import Client, TestCase, TransactionTestCase
from pyfakefs.fake_filesystem_unittest import TestCase<PERSON>ix<PERSON>
from requests.exceptions import RequestException

from api_storage import storage_client
from api_storage.tests.util import mock_copy_resource_folder_task
from boxcars.priority import BOXCARS_PRIORITY_DEFAULT
from dockerized_ghostscript.dockerized_ghostscript import (
    delete_all_files_in_dir_except_with_extension, get_ghostscript_max_pages,
    get_ghostscript_timeout, parse_ghostscript_output, set_ghostscript_max_pages,
    set_ghostscript_timeout, set_up_docker_command_for_dockerized_ghostscript,
    set_up_docker_render_shellscript, GhostscriptResult
)
from documents.actions import (
    PDF_has_password, PDF_passes_file_check, _call_boxcars, _create_pdf_pages,
    _create_pngs_from_pdf, _dockerized_create_pngs_from_pdf,
    _generate_tokenized_data, copy_document, copy_pre_populated_tokenized,
    create_pages, execute_tokenized_job, get_api_page_dpi,
    image_document_content_passes_file_check, image_document_passes_file_check,
    log_PDF_fonts
)
from documents.models import Document, Tokenized
from documents.tests.util import (
    create_document, create_mock_pages, create_tokenized,
    fake_document_content, mocked_boxcars_responses_post, patch_tokenize
)
from glynt_api.exceptions import BoxcarsUnavailable
from glynt_api.tests.mixins import CopyActionTestCaseMixin
from glynt_api.tests.util import STANDARD_COPY_PROPERTIES_DIFFERENCE
from glynt_api.util import patch_multiple_targets
from jobs import TERMINAL_STATUSES
from jobs.tests.util import assert_job_object_states_copied
from organizations.tests.util import create_data_pool, create_organization
from users.tests.util import create_staff_user

logger = logging.getLogger(__name__)

FAKE_DOCUMENT_ID = "e64ea000-6b22-404a-8a25-722dddde33ec"
FAKE_INPUT_FILE_PATH = "/dev/null"


def mock_create_pngs_from_pdf(input_file_path, output_dir_path, document_obfuscated_id):
    """Creates 2 mock page files with simple string content and returns the
    file paths to them. Could be improved to return different number of
    pages if needed, or actual png content, if needed.
    """
    fp1 = Path(output_dir_path, 'mock_png1')
    with open(fp1, 'w') as f:
        f.write('I am mock_png1')
    fp2 = Path(output_dir_path, 'mock_png2')
    with open(fp2, 'w') as f:
        f.write('I am mock_png2')


def mock_dockerized_create_pngs_from_pdf(input_file_path, output_dir_path, document_obfuscated_id):
    """Creates 2 mock page files with simple string content and returns the
    file paths to them. Could be improved to return different number of
    pages if needed, or actual png content, if needed.
    """
    fp1 = Path(output_dir_path, 'mock_png1')
    with open(fp1, 'w') as f:
        f.write('I am mock_png1')
    fp2 = Path(output_dir_path, 'mock_png2')
    with open(fp2, 'w') as f:
        f.write('I am mock_png2')
    return GhostscriptResult(True, 2)


class CopyDocumentTestCase(TransactionTestCase, TestCaseMixin, CopyActionTestCaseMixin):

    def setUp(self):
        # NOTE: Other tests initialize Pyfakefs here to speed things up,
        # but this test cannot. This test uses Glynt's LocalStorageClient,
        # (found in common/python/storage) which calls shutil.copy2() for
        # copying documents. That function was optimized in Python 3.8 with
        # system calls that Pyfakefs does not support.

        self.staff_user, self.staff_user_path = create_staff_user()
        self.staff_client = Client()
        self.staff_client.login(username=self.staff_user.username, password='12345')

        self.org, self.org_path = create_organization()
        self.dp, self.dp_path = create_data_pool(organization=self.org)
        self.dp2, self.dp_path2 = create_data_pool(
            organization=self.org, label="Sample Data Pool 2"
        )

        # Prepare document
        document_content = fake_document_content()

        self.doc, self.doc_path = create_document(
            data_pool=self.dp,
            content_md5='eYgJ7AICbTxIL3e2WLAs8Q==',
            content_type='image/tiff',
            file_extension='.tiff',
            inbound_path='vendor1/project1',
            token_count=123,
            language='en',
        )

        [self.doc.tags.add("tag%d" % i) for i in range(2)]
        [self.doc.glynt_tags.add("glynt_tag%d" % i) for i in range(2)]

        # upload document file
        storage = storage_client()
        storage.upload_fileobj(
            bucket=self.doc.bucket_name,
            remote_path=self.doc.file_remote_path,
            fileobj=io.BytesIO(document_content),
        )
        assert self.doc.file_exists
        assert self.doc.file_content

        # create pages
        create_mock_pages(self.doc)

        # create tokenized
        self.tokenizer_version = 'OCRTEST1'
        self.tokenized, self.tokenized_path = create_tokenized(
            data_pool=self.dp,
            document=self.doc,
            tokenizer_version=self.tokenizer_version
        )

    @patch('organizations.actions.copy_resource_folder_task.delay')
    @patch_tokenize
    def test_copy_document_action(self, mock_crf, tokenize_mocks):
        mock_crf.side_effect = mock_copy_resource_folder_task

        cache = {}
        copy = copy_document(self.doc, self.dp2, cache=cache)

        # assert a new object is created
        self.assertObjectCopiedToDataPool(self.doc, self.dp2, cache)

        self.assertPersistentIDMapCopiedAndUpdated(self.doc, copy, cache)

        # assert the new object is created in designated data pool
        self.assertEqual(copy.data_pool, self.dp2)

        # assert properties are copied over
        self.assertAllPropertiesCopied(
            self.doc, copy,
            difference=STANDARD_COPY_PROPERTIES_DIFFERENCE.union({
                'uuid', 'data_pool_id', 'data_pool',
                'file_remote_path', 'file_temp_url',
                'file_upload_url'
            }))

        # assert the created object is the returned copy
        new_obf_id = cache[Document][self.doc.obfuscated_id][self.dp2]
        self.assertEqual(Document.get_object_by_obfuscated_id(new_obf_id), copy)

        # assert no new jobs were started
        self.assertFalse(tokenize_mocks['tokenize'].called)
        self.assertFalse(tokenize_mocks['generate_tokenized_data'].called)

        # assert tags are copied
        self.assertEqual(
            set(self.doc.tags.all()),
            set(copy.tags.all())
        )

        # assert glynt_tags are copied
        self.assertEqual(
            set(self.doc.glynt_tags.all()),
            set(copy.glynt_tags.all())
        )

        # assert pre_populated tokenizeds copied
        for tokenized in self.doc.tokenizeds.all():
            if tokenized.status in TERMINAL_STATUSES:
                self.assertObjectCopiedToDataPool(tokenized, self.dp2, cache)
                self.assertEqual(
                    Tokenized.get_object_by_obfuscated_id(
                        cache[Tokenized][tokenized.obfuscated_id][self.dp2]
                    ).document, copy
                )
            else:
                self.assertObjectNotCopied(tokenized, cache)

        # assert document file copied
        self.assertTrue(copy.file_exists)

        # assert pages copied
        storage = storage_client()
        for i in range(copy.page_count):
            page_number = i + 1
            self.assertTrue(storage.check_object_exists(
                copy.bucket_name,
                copy.page_remote_path(page_number)
            ))


class CopyTokenizedTestCase(TransactionTestCase, TestCaseMixin, CopyActionTestCaseMixin):

    def setUp(self):
        self.org, self.org_path = create_organization()
        self.dp, self.dp_path = create_data_pool(organization=self.org)
        self.dp2, self.dp_path2 = create_data_pool(
            organization=self.org, label="Sample Data Pool 2"
        )

        # Prepare document
        document_content = fake_document_content()
        self.doc, self.doc_path = create_document(
            data_pool=self.dp,
            content_md5='eYgJ7AICbTxIL3e2WLAs8Q==',
            content_type='image/tiff'
        )
        # upload document file
        storage = storage_client()
        storage.upload_fileobj(
            bucket=self.doc.bucket_name,
            remote_path=self.doc.file_remote_path,
            fileobj=io.BytesIO(document_content),
        )
        assert self.doc.file_exists

        self.doc2, self.doc_path2 = create_document(
            data_pool=self.dp2,
        )

        # create tokenized
        self.tokenizer_version = 'OCRTEST1'
        self.tokenized, self.tokenized_path = create_tokenized(
            data_pool=self.dp, document=self.doc,
            tokenizer_version=self.tokenizer_version
        )

    @patch('organizations.actions.copy_resource_folder_task.delay')
    @patch_tokenize
    def test_copy_to_different_data_pool(self, mock_crf, tokenize_mocks):
        mock_crf.side_effect = mock_copy_resource_folder_task

        cache = {}
        assert self.tokenized.finished

        copy = copy_pre_populated_tokenized(
            self.tokenized, self.dp2, self.doc2, cache=cache
        )
        copy.refresh_status()

        # assert a new object is created
        self.assertObjectCopiedToDataPool(self.tokenized, self.dp2, cache)

        self.assertPersistentIDMapCopiedAndUpdated(self.tokenized, copy, cache)

        # assert the new object is created in designated data pool
        self.assertEqual(copy.data_pool, self.dp2)

        # assert properties are copied over
        self.assertAllPropertiesCopied(
            self.tokenized, copy,
            difference=STANDARD_COPY_PROPERTIES_DIFFERENCE.union({
                'uuid', 'data_pool_id', 'data_pool',
                'document_id', 'document',
                'remote_path', 'tokenized_temp_url',
                'latest_state', 'all_states',
                'temp_upload_url'
            })
        )

        assert_job_object_states_copied(self.tokenized, copy)

        # assert the created object is the returned copy
        new_obf_id = cache[Tokenized][self.tokenized.obfuscated_id][self.dp2]
        self.assertEqual(Tokenized.get_object_by_obfuscated_id(new_obf_id), copy)

        # assert no new tokenize job was started
        self.assertFalse(tokenize_mocks['tokenize'].called)

        # assert parent document set right
        self.assertEqual(copy.document, self.doc2)

        # assert file copied
        storage = storage_client()
        self.assertTrue(storage.check_object_exists(
            copy.bucket_name,
            copy.remote_path
        ))

    @patch('organizations.actions.copy_resource_folder_task.delay')
    @patch_tokenize
    def test_copy_to_same_data_pool(self, mock_crf, tokenize_mocks):
        mock_crf.side_effect = mock_copy_resource_folder_task

        cache = {}
        assert self.tokenized.finished

        copy = copy_pre_populated_tokenized(
            self.tokenized, self.tokenized.data_pool, self.doc, cache=cache
        )

        # assert a new object is not created
        self.assertObjectNotCopied(self.tokenized, cache)

        # assert the returned object is the original object
        self.assertEqual(copy, self.tokenized)

    @patch('organizations.actions.copy_resource_folder_task.delay')
    @patch_tokenize
    def test_non_terminal_tokenized(self, mock_crf, tokenize_mocks):
        mock_crf.side_effect = mock_copy_resource_folder_task

        # create tokenized
        tokenized2, self.tokenized_path = create_tokenized(
            data_pool=self.dp, document=self.doc,
            tokenizer_version='AWS'
        )
        tokenized2.set_state('PENDING')
        assert tokenized2.status not in TERMINAL_STATUSES

        cache = {}
        copy = copy_pre_populated_tokenized(
            tokenized2, self.dp2, self.doc2, cache=cache
        )

        # assert a new object is not created
        self.assertObjectNotCopied(tokenized2, cache)

        # assert return value
        self.assertIsNone(copy)


class CreatePagesTestCase(TestCase, TestCaseMixin):

    def setUp(self):
        self.setUpPyfakefs()

        self.staff_user, self.staff_user_url = create_staff_user()
        self.staff_client = Client()
        self.staff_client.login(username=self.staff_user.username, password='12345')

        self.org, self.org_path = create_organization()
        self.dp, self.dp_path = create_data_pool(organization=self.org)

    def test_create_pages_formats(self):
        # Does a pdf document use the correct backend?
        doc, _ = create_document(
            data_pool=self.dp, content_type="application/pdf"
        )
        with patch_multiple_targets([
            patch('documents.actions._create_image_pages'),
            patch('documents.actions._create_pdf_pages'),
        ]) as (mock_cip, mock_cpp):
            create_pages(doc)
            mock_cpp.assert_called_once_with(doc)
            mock_cip.assert_not_called()

        # Does an image document use the correct backend?
        image_content_types = [
            "image/jpeg",
            "image/png",
            "image/tiff",
        ]

        for content_type in image_content_types:
            doc, _ = create_document(
                data_pool=self.dp, content_type=content_type
            )
            with patch_multiple_targets([
                patch('documents.actions._create_image_pages'),
                patch('documents.actions._create_pdf_pages'),
            ]) as (mock_cip, mock_cpp):
                create_pages(doc)
                mock_cip.assert_called_once_with(doc, content_type.split("/")[1])
                mock_cpp.assert_not_called()

    @unittest.skip('Implement when Wand is removed.')
    def test_create_image_pages(self):
        pass

    @patch('documents.actions.PDF_passes_file_check', return_value=True)
    @patch('documents.actions.PDF_has_password', return_value=False)
    @patch('documents.actions.log_PDF_fonts')
    def test_create_pdf_pages(self, mock_log_fonts, mock_has_pw, mock_file_check):
        doc, _ = create_document(data_pool=self.dp)
        # Happy Case
        with patch_multiple_targets([
            patch('documents.actions._create_pngs_from_pdf', side_effect=mock_create_pngs_from_pdf),
            patch('documents.actions._dockerized_create_pngs_from_pdf',
                  side_effect=mock_dockerized_create_pngs_from_pdf, return_value=GhostscriptResult(True, 2)),
        ]) as (mock_create, mock_dockerized_create):
            n_pages = _create_pdf_pages(doc)
        self.assertEqual(n_pages, 2)
        self.assertEqual(doc.page_content(1), b'I am mock_png1')
        self.assertEqual(doc.page_content(2), b'I am mock_png2')
        mock_has_pw.assert_called_once()
        mock_log_fonts.assert_called_once()
        # 0 pages
        # with patch('documents.actions.create_pngs_from_pdf', return_value=[]):
        with patch_multiple_targets([
            patch('documents.actions._create_pngs_from_pdf', return_value=[]),
            patch('documents.actions._dockerized_create_pngs_from_pdf', return_value=GhostscriptResult(False, 0)),
        ]) as (mock_create, mock_dockerized_create):
            n_pages = _create_pdf_pages(doc)
        self.assertEqual(n_pages, 0)


class TestCheckPDFForPassword(TestCase):
    """Test checking a PDF for password protection.
    Two small PDFs are used, one is password protected, and one is not."""

    def test_check_Password_Protected_PDF_for_password(self):
        doc_path = settings.COMMON_FIXTURES_PATH / 'SamplePDFPasswordProtected.pdf'
#       doc_path = settings.COMMON_FIXTURES_PATH / 'CamScanner.pdf'

        self.assertTrue(PDF_has_password(doc_path),
                        "test_check_Password_Protected_PDF_for_password() fails for {SamplePDFPasswordProtected.pdf}")

    def test_check_Not_Password_Protected_PDF_for_password(self):
        doc_path = settings.COMMON_FIXTURES_PATH / 'SamplePDFNotPasswordProtected.pdf'
        self.assertFalse(PDF_has_password(doc_path),
                         "test_check_Password_Protected_PDF_for_password() fails for "
                         "{SamplePDFNotPasswordProtected.pdf}")


class TestLogPDFFonts(TestCase):
    """Test the logging of fonts used by a PDF file, and certain expected failure modes."""

    def test_log_PDF_fonts(self):
        doc_path = settings.COMMON_FIXTURES_PATH / 'SamplePDFNotPasswordProtected.pdf'
        pdffonts_listed_results = log_PDF_fonts(doc_path, FAKE_DOCUMENT_ID)
        self.assertTrue(pdffonts_listed_results, "test_log_PDF_fonts() - False returned by log_PDF_fonts()")


@pytest.mark.skipif(settings.DEV_ENVIRONMENT_INSTEAD_OF_CI is not True, reason="does not run in CI")
class TestCreatePNGsFromPDF(TestCase):
    """Test the creation of PNG page files from a PDF file."""

    def test_create_pngs_from_pdf(self):
        # Test the creation of a PNG file from a PDF file. The PDF used for this test is a one page document,
        # and there should be a file created named temp-0001.png.
        with TemporaryDirectory() as td:
            doc_path = settings.COMMON_FIXTURES_PATH / 'SamplePDFNotPasswordProtected.pdf'
            output_dir_path = os.path.join(td, 'result_pages')
            os.makedirs(output_dir_path)
            self.assertTrue(_create_pngs_from_pdf(doc_path, output_dir_path, FAKE_DOCUMENT_ID),
                            "test_create_pngs_from_pdf() False returned from _create_pngs_from_pdf()")
            result_file_path = os.path.join(output_dir_path, "temp-0001.png")
            self.assertTrue(os.path.exists(result_file_path),
                            f"TestCreatePNGsFromPDF() output file {result_file_path} "
                            "not created by _create_pngs_from_pdf")


@pytest.mark.skipif(settings.DEV_ENVIRONMENT_INSTEAD_OF_CI is not True, reason="does not run in CI")
class TestDockerizedCreatePNGsFromPDF(TestCase):
    """Test the creation of PNG page files from a PDF file, and also test exceeding maximum time and maximum pages
    allowed."""

    def test_dockerized_create_png_from_pdf(self):
        # Test the creation of a PNG file from a PDF file. The PDF used for this test is a one page document,
        # and there should be a file created named temp-0001.png.
        with TemporaryDirectory() as td:
            doc_path = settings.COMMON_FIXTURES_PATH / 'SamplePDFNotPasswordProtected.pdf'
            output_dir_path = os.path.join(td, 'result_pages')
            os.makedirs(output_dir_path)
            container_result = _dockerized_create_pngs_from_pdf(doc_path, output_dir_path, FAKE_DOCUMENT_ID)
            self.assertTrue(container_result.success,
                            "test_new_create_pngs_from_pdf() failed to successfully execute dockerized Ghostscript.")
            self.assertTrue(os.path.exists(os.path.join(output_dir_path, "temp-0001.png")),
                            "test_new_create_pngs_from_pdf() failed to create output.")
            files = os.listdir(output_dir_path)
            self.assertTrue(len(files) == 1,
                            f"test_new_create_pngs_from_pdf() failed - expected 1 output file, found {len(files)}")
            file_size = os.path.getsize(os.path.join(output_dir_path, "temp-0001.png"))
            self.assertTrue(file_size > 1, "test_new_create_pngs_from_pdf() failed - temp-0001.png is zero length.")
            # PNG files may contain things like time stamps and other meta data, so we cannot depend on the output
            # file being of a certain exact length, let alone matching a hash value. So we test for a "reasonable"
            # file size. In this case, it should be around 12989 bytes.
            self.assertTrue(file_size > 10000 and file_size < 15000, "test_new_create_pngs_from_pdf() failed - "
                                                                     "temp-0001.png is not a reasonable file size "
                                                                     "length.")

    def test_dockerized_create_pngs_from_pdf_max_pages_exceeded(self):
        with TemporaryDirectory() as td:
            # Save the value for <ghostscript_max_pages> and set <ghostscript_max_pages> to a low value.
            # The PDF used for this test is an empty one thousand page document, and should cause a failure because
            # the page limit is exceeded.
            # The value for <ghostscript_max_pages> is reset after the invocation of dockerized Ghostscript.
            saved_ghostscript_max_pages = get_ghostscript_max_pages()
            set_ghostscript_max_pages(20)

            doc_path = settings.COMMON_FIXTURES_PATH / 'emptyOneThousandPages.pdf'
            output_dir_path = os.path.join(td, 'result_pages')
            os.makedirs(output_dir_path)

            container_result = _dockerized_create_pngs_from_pdf(doc_path, output_dir_path, FAKE_DOCUMENT_ID)
            set_ghostscript_max_pages(saved_ghostscript_max_pages)

            self.assertFalse(container_result.success,
                             "test_dockerized_create_pngs_from_pdf_max_pages_exceeded() did NOT get an expected "
                             "False from dockerized Ghostscript.")

            pdfinfo_log_file_path = os.path.join(output_dir_path, "pdfinfo.log")
            self.assertTrue(os.path.exists(pdfinfo_log_file_path),
                            "test_dockerized_create_pngs_from_pdf_max_pages_exceeded() pdfinfo.log not created.")
            with open(pdfinfo_log_file_path, "r") as pdfinfo_log:
                pdfinfo_log_data = pdfinfo_log.readlines()
            page_limit_exceeded = any(line.startswith("FAILURE input.pdf is bigger than page limit")
                                      for line in pdfinfo_log_data)
            self.assertTrue(page_limit_exceeded,
                            "test_dockerized_create_pngs_from_pdf_max_pages_exceeded() page limit test failed.")

    def test_dockerized_create_pngs_from_pdf_time_limit_exceeded(self):
        # Save the value for <ghostscript_max_pages> and set it to a very high value.
        # Save the value for <ghostscript_timeout> and set it to a very low value.
        # The PDF used for this test is an empty one thousand page document, and should cause a failure because the
        # time limit is exceeded.
        # The values for <ghostscript_max_pages> and <ghostscript_timeout> are reset reset after the invocation of
        # dockerized Ghostscript.
        with TemporaryDirectory() as td:
            saved_ghostscript_max_pages = get_ghostscript_max_pages()
            saved_ghostscript_timeout = get_ghostscript_timeout()
            set_ghostscript_max_pages(1000)
            set_ghostscript_timeout(1)

            doc_path = settings.COMMON_FIXTURES_PATH / 'emptyOneThousandPages.pdf'
            output_dir_path = os.path.join(td, 'result_pages')
            os.makedirs(output_dir_path)

            container_result = _dockerized_create_pngs_from_pdf(doc_path, output_dir_path, FAKE_DOCUMENT_ID)
            set_ghostscript_max_pages(saved_ghostscript_max_pages)
            set_ghostscript_timeout(saved_ghostscript_timeout)
            self.assertFalse(container_result.success,
                             "test_dockerized_create_pngs_from_pdf_time_limit_exceeded() did NOT get an expected "
                             "False from dockerized Ghostscript.")

            gs_log_file_path = os.path.join(output_dir_path, "gs.log")
            self.assertTrue(os.path.exists(gs_log_file_path),
                            "test_dockerized_create_pngs_from_pdf_time_limit_exceeded() gs.log not created.")
            with open(gs_log_file_path, "r") as gs_log:
                gs_log_data = gs_log.readlines()
            time_limit_exceeded = any(line.startswith("FAILURE gs failure: timeout - runtime exceeded")
                                      for line in gs_log_data)

            self.assertTrue(time_limit_exceeded,
                            "test_dockerized_create_pngs_from_pdf_time_limit_exceeded() time limit test failed.")


class TestExecuteTokenizedJob(TransactionTestCase, TestCaseMixin):
    """
    Test documents.actions.execute_tokenized_job and its supplemental private methods in documents.actions.
    """

    def setUp(self):
        self.setUpPyfakefs()

        self.org, _ = create_organization()
        self.dp, _ = create_data_pool(organization=self.org)
        self.doc, _ = create_document(data_pool=self.dp)
        self.tokenizer_version = 'OCRTEST1'
        self.boxcars_priority = BOXCARS_PRIORITY_DEFAULT
        self.tokenized, _ = create_tokenized(
            data_pool=self.dp, document=self.doc, tokenizer_version=self.tokenizer_version
        )

    @patch('documents.actions._generate_tokenized_data')
    def test_execute_tokenized_job(self, mock_gtd):
        execute_tokenized_job(self.tokenized, self.boxcars_priority)
        mock_gtd.assert_called_with(self.tokenized, self.boxcars_priority)

    @patch('documents.actions._call_boxcars')
    def test_generate_tokenized_data(self, mock_call_boxcars):
        _generate_tokenized_data(self.tokenized, self.boxcars_priority)
        mock_call_boxcars.assert_called_with(self.tokenized, self.boxcars_priority)

    @patch.object(Tokenized, 'temp_upload_url', new_callable=PropertyMock)
    @patch('documents.actions.requests.post', wraps=requests.post)
    @patch('documents.actions.storage_client')
    def test_call_boxcars(self, mock_storage_client, mock_post, mock_temp_upload_url):
        # storage_client is used to generate the source_url that gets passed to boxcars, and it generates the presigned
        # url with an expiration timestamp that would be different between expected value and actual value.
        # storage_client therefore is mocked to return an arbitrarty expected url for the post argement assertion,
        # instead of generating a real url with storage client.
        mock_storage = MagicMock()
        expected_presigned_source_url = 'presigned_url'
        mock_storage.generate_presigned_get_url.return_value = expected_presigned_source_url
        mock_storage_client.return_value = mock_storage
        temp_upload_url = 'temp_upload_url'
        job_id = 999
        mock_temp_upload_url.return_value = temp_upload_url
        expected_url = f'{settings.BOXCARS_API_URL}/job'
        expected_posted_json = {
            "source_url": expected_presigned_source_url,
            "source_mimetype": self.tokenized.document.get_content_type_display(),
            "boxcars_version": self.tokenizer_version,
            "result_url": temp_upload_url,
            "priority": self.boxcars_priority
        }
        with mocked_boxcars_responses_post(job_id=job_id):
            actual = _call_boxcars(self.tokenized, self.boxcars_priority)
        mock_post.assert_called_with(expected_url, json=expected_posted_json)
        self.assertEqual(actual, job_id)

    @patch.object(Tokenized, 'temp_upload_url', new_callable=PropertyMock)
    @patch('documents.actions.requests.post', wraps=requests.post)
    @patch('documents.actions.storage_client')
    def test_call_boxcars_no_priority(self, mock_storage_client, mock_post, mock_temp_upload_url):
        # storage_client is used to generate the source_url that gets passed to boxcars, and it generates the presigned
        # url with an expiration timestamp that would be different between expected value and actual value.
        # storage_client therefore is mocked to return an arbitrarty expected url for the post argement assertion,
        # instead of generating a real url with storage client.
        mock_storage = MagicMock()
        expected_presigned_source_url = 'presigned_url'
        mock_storage.generate_presigned_get_url.return_value = expected_presigned_source_url
        mock_storage_client.return_value = mock_storage
        temp_upload_url = 'temp_upload_url'
        job_id = 999
        mock_temp_upload_url.return_value = temp_upload_url
        expected_url = f'{settings.BOXCARS_API_URL}/job'
        expected_boxcars_priority = BOXCARS_PRIORITY_DEFAULT
        expected_posted_json = {
            "source_url": expected_presigned_source_url,
            "source_mimetype": self.tokenized.document.get_content_type_display(),
            "boxcars_version": self.tokenizer_version,
            "result_url": temp_upload_url,
            "priority": expected_boxcars_priority
        }
        with mocked_boxcars_responses_post(job_id=job_id):
            actual = _call_boxcars(self.tokenized)
        mock_post.assert_called_with(expected_url, json=expected_posted_json)
        self.assertEqual(actual, job_id)

    def test_call_boxcars_request_exception(self):
        with patch('documents.actions.requests.post', side_effect=RequestException):
            with self.assertRaises(BoxcarsUnavailable) as cm:
                _call_boxcars(self.tokenized, self.boxcars_priority)
            self.assertTrue(isinstance(cm.exception.__cause__, RequestException))

    def test_call_boxcars_non_202(self):
        # boxcars return non-202 status code
        with mocked_boxcars_responses_post() as rsps:
            rsps.replace('POST', re.compile(rsps._url_pattern), status=400)
            with self.assertRaises(RuntimeError):
                _call_boxcars(self.tokenized, self.boxcars_priority)


class TestPDF_passes_file_check(TestCase):
    """Test identifying a file as a PDF file from its internal structure.
    A valid PDF is tested for success.
    A non-PDF file, and a zero-length file with a .pdf extension are both tested for expected failure."""

    def test_valid_PDF_passes_PDF_file_check(self):
        doc_path = settings.COMMON_FIXTURES_PATH / 'SamplePDFNotPasswordProtected.pdf'
        self.assertTrue(PDF_passes_file_check(doc_path),
                        f"test_valid_PDF_passes_PDF_file_check() fails for doc_path = {doc_path}.")

    def test_non_PDF_fails_PDF_file_check(self):
        doc_path = settings.COMMON_FIXTURES_PATH / 'sample_doc.tif'
        self.assertFalse(PDF_passes_file_check(doc_path),
                         f"test_non_PDF_fails_PDF_file_check() fails for doc_path = {doc_path}.")

    def test_zero_length_PDF_fails_PDF_file_check(self):
        doc_path = settings.COMMON_FIXTURES_PATH / 'zeroFileSize.pdf'
        self.assertFalse(PDF_passes_file_check(doc_path),
                         f"test_zero_length_PDF_fails_PDF_file_check() fails for doc_path = {doc_path}.")


class TestImageDocumentPassesFileCheck(TestCase):
    """Test identifying a file as an allowed image file (not PDF) type from its internal structure.
    Three valid test images in jpeg, tiff, and png formats are used, and a PDF file is tested with
    an expected failure."""

    def test_TIFF_passes_image_file_check(self):
        doc_path = settings.COMMON_FIXTURES_PATH / 'sample_doc.tif'
        self.assertTrue(image_document_passes_file_check(doc_path),
                        "test_TIFF_passes_image_file_check() fails for 'sample_doc.tif'.")

    def test_PNG_passes_image_file_check(self):
        doc_path = settings.COMMON_FIXTURES_PATH / 'sample_doc.png'
        self.assertTrue(image_document_passes_file_check(doc_path),
                        "test_PNG_passes_image_file_check() fails for 'sample_doc.png'.")

    def test_JPEG_passes_image_file_check(self):
        doc_path = settings.COMMON_FIXTURES_PATH / 'sample_doc.jpg'
        self.assertTrue(image_document_passes_file_check(doc_path),
                        "test_JPEG_passes_image_file_check() fails for 'sample_doc.jpg'.")

    def test_PDF_fails_image_file_check(self):
        doc_path = settings.COMMON_FIXTURES_PATH / 'SamplePDFNotPasswordProtected.pdf'
        self.assertFalse(image_document_passes_file_check(doc_path),
                         "test_PDF_fails_image_file_check() fails for 'SamplePDFNotPasswordProtected.pdf'.")


class TestImageDocumentContentPassesImageFileCheck(TestCase):
    """Test identifying file content blob as an allowed image file (not PDF) type from its internal structure.
    Three valid test images in jpeg, tiff, and png formats are used, and a PDF file is tested with
    an expected failure."""

    def test_TIFF_image_document_content_passes_image_file_check(self):
        doc_path = settings.COMMON_FIXTURES_PATH / 'sample_doc.tif'
        with open(doc_path, 'rb') as temp_file:
            doc_content = temp_file.read()
        self.assertTrue(image_document_content_passes_file_check(doc_content, "tif"),
                        "test_TIFF_image_document_content_passes_file_check() fails for TIFF content")

    def test_JPEG_image_document_content_passes_image_file_check(self):
        doc_path = settings.COMMON_FIXTURES_PATH / 'sample_doc.jpg'
        with open(doc_path, 'rb') as temp_file:
            doc_content = temp_file.read()
        self.assertTrue(image_document_content_passes_file_check(doc_content, "jpg"),
                        "test_JPEG_image_document_content_passes_image_file_check() fails for JPEG content.")

    def test_PNG_image_document_content_passes_image_file_check(self):
        doc_path = settings.COMMON_FIXTURES_PATH / 'sample_doc.png'
        with open(doc_path, 'rb') as temp_file:
            doc_content = temp_file.read()
        self.assertTrue(image_document_content_passes_file_check(doc_content, "png"),
                        "test_PNG_image_document_content_passes_image_file_check() fails for 'PNG content.")

    def test_PDF_image_document_content_fails_image_file_check(self):
        doc_path = settings.COMMON_FIXTURES_PATH / 'SamplePDFNotPasswordProtected.pdf'
        with open(doc_path, 'rb') as temp_file:
            doc_content = temp_file.read()
        self.assertFalse(image_document_content_passes_file_check(doc_content, "pdf"),
                         "test_PDF_image_document_content_fails_image_file_check() fails for PDF content.")


class TestSetUpDockerRenderShellscript(TestCase):
    """Test putting the shell script used to render Ghostscript & do logging inside docker into a specified
    location."""

    def test_set_up_docker_render_shellscript(self):
        with TemporaryDirectory() as tmp:
            path = os.path.join(str(tmp), "render.sh")
            set_up_docker_render_shellscript(path)
            self.assertTrue(os.path.exists(path), f"test_set_up_docker_render_shellscript() - {path} not created")


class TestDockerizedGhostScriptSucceeded(TestCase):
    """Test detecting successful or lack of successful Ghostscript execution as reported in the docker container's
    logs."""

    def test_parse_ghostscript_output_with_good_stream(self):
        string = 'Executing Ghostscript...\nSUCCESS Ghostscript rendering successfully done\nPAGE_COUNT=1\n'
        stream = BytesIO(string.encode())
        result = parse_ghostscript_output(stream)
        self.assertTrue(result.success, "test_parse_ghostscript_output_with_good_stream() failed")
        self.assertEqual(result.page_count, 1,
                         "test_parse_ghostscript_output_with_good_stream() failed - page count not 1")

    def test_parse_ghostscript_output_with_bad_stream(self):
        string = 'Executing Ghostscript...\nFAILURE Ghostscript rendering failed\n'
        stream = BytesIO(string.encode())
        result = parse_ghostscript_output(stream)
        self.assertFalse(result.success, "test_parse_ghostscript_output_with_bad_stream() failed")
        self.assertEqual(result.page_count, None,
                         "test_parse_ghostscript_output_with_bad_stream() failed - page count not None")


class TestDeleteAllFilesInDirExceptWithExtension(TestCase):
    """Test deleting all files in a dir except those with a specific extension"""

    def test_delete_all_files_in_dir_except_with_extension(self):
        with TemporaryDirectory() as td:
            open(os.path.join(td, "test.log"), 'a').close()
            open(os.path.join(td, "test.png"), 'a').close()
            open(os.path.join(td, "test.sh"), 'a').close()
            open(os.path.join(td, "test2.png"), 'a').close()

            files = os.listdir(td)
            self.assertEqual(len(files), 4)
            delete_all_files_in_dir_except_with_extension(td, '.png')
            files = os.listdir(td)
            self.assertEqual(len(files), 2)
            self.assertTrue("test.png" in files,
                            "TestDeleteAllFilesInDirExceptWithExtension() failed - file test.png not found")
            self.assertTrue("test2.png" in files,
                            "TestDeleteAllFilesInDirExceptWithExtension() failed - file test2.png not found")


class TestSetUpDockerCommandForDockerizedGhostscript(TestCase):
    """Test setting up a docker container command with a file and a document ID"""

    def test_set_up_docker_command_for_dockerized_ghostscript(self):
        docker_command = set_up_docker_command_for_dockerized_ghostscript(FAKE_INPUT_FILE_PATH, FAKE_DOCUMENT_ID,
                                                                          get_ghostscript_timeout,
                                                                          get_ghostscript_max_pages, get_api_page_dpi)
        self.assertTrue('--DOCUMENT_OBFUSTICATED_ID ' + FAKE_DOCUMENT_ID in docker_command,
                        "test_set_up_docker_command_for_dockerized_ghostscript() failed - DOCUMENT_OBFUSTICATED_ID "
                        "not set in docker_command")
        self.assertTrue('--SOURCE_FILE ' + os.path.basename(FAKE_INPUT_FILE_PATH) in docker_command,
                        "test_set_up_docker_command_for_dockerized_ghostscript() failed - SOURCE_FILE "
                        "not set in docker_command")
