import json
from unittest.mock import patch

from django.test import Client, TestCase, override_settings
from django.urls import reverse

from documents.tests.util import patch_tokenize
from glynt_api.exceptions import BoxcarsUnavailable
from glynt_api.util import set_query_field
from organizations.tests.util import create_data_pool, create_organization
from users.tests.util import create_staff_user


@override_settings(BOXCARS_API_URL='http://testserver')
class BoxcarsUnavailableResponseMiddlewareTestCase(TestCase):

    def setUp(self):
        self.staff_user, self.staff_user_path = create_staff_user()
        self.staff_client = Client()
        self.staff_client.login(username=self.staff_user.username, password='12345')

        self.org, self.org_path = create_organization()
        self.dp, self.dp_path = create_data_pool(organization=self.org)
        self.url = set_query_field(
            reverse(
                'v6:trainingset-list',
                kwargs={'data_pools_obfuscated_id': self.dp.obfuscated_id}
            ),
            'advanced', 'true'
        )
        self.data = json.dumps({
            'label': 'Test TS',
        })

    @patch_tokenize
    def test_boxcars_available(self, tokenize_mocks):
        response = self.staff_client.post(
            self.url, data=self.data,
            content_type='application/json'
        )
        self.assertEqual(response.status_code, 201)

    # TODO: determine if this test is still relevant--the logic for the
    # default tokenizer uses parameter store now, not boxcars
    def test_boxcars_unavailable(self):
        with patch('organizations.models.get_default_tokenizer') as mock_gtv:
            mock_gtv.side_effect = BoxcarsUnavailable
            response = self.staff_client.post(
                self.url, data=self.data,
                content_type='application/json'
            )
            self.assertEqual(response.status_code, 503)
            content = response.json()
            self.assertIn('details', content)
            self.assertIn(
                'Tokenization server is temporarily unavailable',
                content['details']
            )
