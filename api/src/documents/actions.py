import logging
import os
import subprocess
import time
from tempfile import TemporaryDirectory

import requests
from django.conf import settings
from django.core.exceptions import ValidationError
from django.db import transaction
from wand.image import Image

from api_storage import storage_client
from boxcars.priority import BOXCARS_PRIORITY_DEFAULT
from dockerized_ghostscript.dockerized_ghostscript import (
    get_api_page_dpi, get_ghostscript_max_pages, get_ghostscript_timeout,
    run_dockerized_ghostscript_container,
    set_up_docker_command_for_dockerized_ghostscript,
    set_up_docker_render_shellscript,
    GhostscriptResult
)
from documents.exceptions import BoxcarsJobIncomplete
from glynt_api.exceptions import BoxcarsUnavailable
from obfuscate.obfuscate import obfuscate
from organizations.actions import (
    copy_and_update_persistent_id_map, copy_data_pool_aware_object
)

logger = logging.getLogger(__name__)

# Used instead of '/usr/bin/gs', in order to invoke a later
# release not yet included in the Ubuntu distro. This is for the legacy
# invocation of Ghostscript; the Docker container has its copy.
GHOSTSCRIPT_PATH = '/usr/bin/gs-9533-linux-x86_64'

# Used to upsample and then downsample in legacy Ghostscript, relative to dpi.
# This produces much higher quality output for markup in the UI; if not used,
# fine text will erode and lose stems and other features. MUST be an integer.
GHOSTSCRIPT_RESAMPLE_INT_SCALAR = 2

# <pdfinfo>  is a utility from the poppler-utils package; it allows inspection of
# useful qualities of a PDF document without actually rendering the PDF.
# Timeout in seconds.
PDFINFO_PATH = '/usr/bin/pdfinfo'
PDFINFO_TIMEOUT = 10

# <pdffonts>  is a utility from the poppler-utils package; it allows inspection of
# what fonts (if any) are used in a PDF document without actually rendering the PDF.
# Timeout in seconds.
PDFFONTS_PATH = '/usr/bin/pdffonts'
PDFFONTS_TIMEOUT = 10


# Path to the shell command <file>.
# Timeout in seconds.
FILE_COMMAND_PATH = '/usr/bin/file'
FILE_COMMAND_TIMEOUT = 10

# The allowed types of image (i.e., non-PDF) files, as returned in the shell "file" command reportage.
ALLOWED_IMAGE_TYPES = [
    "JPEG image data",
    "PNG image data",
    "TIFF image data"
]

# if the below is set to False, then the legacy invocation of Ghostscript as
# a sub-process will be used.
USE_DOCKERIZED_GHOSTSCRIPT = True


def assert_tokenized_ready(tokenized):
    """Checks if the tokenized job is in a successful state. Celery task
    that runs this action will handle retries for unfinished jobs.
    """
    logger.debug(
        f"Verifying Tokenized {tokenized.obfuscated_id} for Document "
        f"{tokenized.document.obfuscated_id} is finished and succeeded"
    )
    if tokenized.finished:
        if tokenized.succeeded:
            logger.debug(
                f"Tokenized {tokenized.obfuscated_id} for Document "
                f"{tokenized.document.obfuscated_id} is finished and succeeded"
            )
            return True
        raise Exception(
            f"Tokenized {tokenized.obfuscated_id} failed for "
            f"Document {tokenized.document.obfuscated_id}"
        )
    raise BoxcarsJobIncomplete


def _call_boxcars(tokenized, boxcars_priority=None):
    """Start a boxcars to tokenize a document, and wait for it to finish.
    Returns the boxcars job id.

    By default, BOXCARS_PRIORITY_DEFAULT is used to start the boxcars job, unless boxcars_priority is provided with
    alternative value which is then used.

    Parameters
    ==========
    :param tokenized: Tokenized instance which the Boxcars job is called for
    :param boxcars_priority: (Optional) the priority level to set for the boxcars job
    """
    if boxcars_priority is None:
        boxcars_priority = BOXCARS_PRIORITY_DEFAULT

    storage = storage_client()
    presigned_source_url = storage.generate_presigned_get_url(
        bucket=tokenized.document.bucket_name,
        remote_path=tokenized.document.file_remote_path,
        use_vpc=True
    )

    job_endpoint = f'{settings.BOXCARS_API_URL}/job'
    try:
        r = requests.post(
            job_endpoint,
            json={
                "source_url": presigned_source_url,
                "source_mimetype": tokenized.document.get_content_type_display(),
                "boxcars_version": tokenized.tokenizer_version,
                "result_url": tokenized.temp_upload_url,
                "priority": boxcars_priority
            })
    except requests.exceptions.RequestException as ex:
        logger.exception(f'Error connecting to Boxcars at {job_endpoint}')
        raise BoxcarsUnavailable('Unable to connect to Boxcars') from ex
    except Exception:
        logger.exception(f"{job_endpoint}")
        raise
    if r.status_code != 202:
        logger.warning(f"{job_endpoint} -> {r.status_code}")
        raise RuntimeError(
            f"Failed to start boxcars job for Tokenized {tokenized.obfuscated_id}"
        )

    job_id = r.json()['job_id']
    logger.info(
        f"Started boxcars job {job_id} for Tokenized {tokenized.obfuscated_id} "
        f"with priority {boxcars_priority}"
    )
    return job_id


def assert_tokenized_job_success(job_id, tokenized_id):
    """Used by the tokenized task to poll boxcars for job status"""
    tokenized_obf_id = obfuscate(tokenized_id)
    job_status_endpoint = f'{settings.BOXCARS_API_URL}/jobstate/{job_id}'
    try:
        r = requests.get(job_status_endpoint)
    except requests.exceptions.RequestException as ex:
        logger.exception(
            f"Error connecting to Boxcars at {job_status_endpoint} "
            f"for Tokenized {tokenized_obf_id}"
        )
        raise BoxcarsUnavailable('Unable to connect to Boxcars') from ex
    except Exception:
        logger.exception(
            "Unhandled exception raised while calling boxcars at "
            f"{job_status_endpoint} for Tokenized {tokenized_obf_id}"
        )
        raise
    if r.status_code != 200:
        logger.warning(f"{job_status_endpoint} -> {r.status_code}")
        raise BoxcarsUnavailable(
            f"Boxcars server responded with status code {r.status_code} "
            f"for Tokenized {tokenized_obf_id}"
        )
    job_state = r.json()['state']
    if job_state == 'DONE':
        logger.info(
            f"Boxcars job `{job_id}` for Tokenized {tokenized_obf_id} "
            f"complete; status: {job_state}"
        )
        return True
    if job_state == 'FAILED':
        logger.error(
            f"Boxcars job `{job_id}` for Tokenized {tokenized_obf_id} failed; "
            f"API response: {r.json()}"
        )
        return False
    raise BoxcarsJobIncomplete(
        f"Boxcars job `{job_id}` for Tokenized {tokenized_obf_id} "
        f"incomplete; status: {job_state}"
    )


def _generate_tokenized_data(tokenized, boxcars_priority=None):
    """Given a Tokenized and optional Boxcars priority, generate and store the Boxcars JSON for the
    tokenized's document file.

    Parameters
    ==========
    :param tokenized: Tokenized instance which the Boxcars job is called for
    :param boxcars_priority: (Optional) the priority level to set for the boxcars job
    """
    logger.debug(
        f'Calling _call_boxcars() for Tokenized {tokenized.obfuscated_id} with priority {boxcars_priority}'
    )
    return _call_boxcars(tokenized, boxcars_priority)


def execute_tokenized_job(tokenized, boxcars_priority=None):
    # Alias allows for simpler mocking in unit tests
    return _generate_tokenized_data(tokenized, boxcars_priority)


def create_pages(document):
    """Generates page files for a given document, storing them using the
    storage client.

    Raises a FileNotFoundError if the document file is not yet
    present on the storage client.
    """
    # Determines the document format by getting the display value for the
    # content_type field of the document. The display value for the
    # content_type field is the document's mimetype. The fmt is the second half
    # of the string. For example, a pdf document's mimetype is
    # 'application/pdf', so the parsed format will be 'pdf'.

    # PDF docs are re-directed since we don't want all of the pages of a huge doc
    # resident in memory; instead PDF files are handled in a way that stores
    # pages to temp files controlled by a context manager.

    # TODO: Put a PDF wrapper around multi-page TIFF docs and process to files
    # (JIRA: GL-635)

    mime_type = document.content_type
    _, subtype = mime_type.split('/')
    logger.info(f"create_pages() - document: {document.obfuscated_id} - fmt: {mime_type}")

    if subtype in ('pdf',):
        n_pages = _create_pdf_pages(document)
    elif subtype in ('jpeg', 'png', 'tiff'):
        n_pages = _create_image_pages(document, subtype)
    else:
        # Currently, only PDF and certain image documents are supported.
        n_pages = 0

    if n_pages == 0:
        logger.warning(f'document {document.obfuscated_id} has 0 pages, mime type: {mime_type}')

    # Lock and refresh the document to update the page count to ensure we have
    # the latest document state.
    with transaction.atomic():
        document = document.__class__.objects.select_for_update().get(id=document.id)
        document.page_count = n_pages
        document.save()


def _create_image_pages(document, fmt):
    """Backend for create_pages() which is responsible for image documents.
    fmt must be a format which is supported by wand.
    """
    logger.debug(f'_create_image_pages() - document: {document.obfuscated_id}')
    doc_content = document.file_content

    # First check for a valid content blob for a image file.
    if not image_document_content_passes_file_check(doc_content, fmt):
        logger.warning(
            "_create_image_pages() - image document fails file check - continuing to try to generate pages anyway. "
            f"Document ID: {document.obfuscated_id}")

    page_number = 0
    with Image(blob=doc_content, format=fmt, resolution=get_api_page_dpi()) as img:
        for image in img.sequence:
            content = Image(image).make_blob('png')
            page_number = page_number + 1
            document.store_page_content(page_number, content)
    return page_number


def _create_pdf_pages(document):
    """Backend for create_pages() which is responsible for pdf documents."""
    logger.debug(f'_create_pdf_pages() - Document: {document.obfuscated_id}')
    with TemporaryDirectory(dir="/tmp") as td:
        try:
            logger.debug(f"_create_pdf_pages() - Document: {document.obfuscated_id} - td is: {td}")

            doc_content = document.file_content
            input_file_path = os.path.join(td, 'input.pdf')
            logger.debug(f"_create_pdf_pages() writing temp file: {input_file_path}")
            with open(input_file_path, 'wb') as temp_file:
                temp_file.write(doc_content)

            output_dir_path = os.path.join(td, 'result_pages')
            os.makedirs(output_dir_path)

            if PDF_passes_file_check(input_file_path) is False:
                logger.warning(
                    "_create_pdf_pages() - PDF fails input file check - continuing to try to generate pages anyway. "
                    f"Document ID: {document.obfuscated_id}"
                )

            if PDF_has_password(input_file_path):
                logger.warning(
                    "_create_pdf_pages() - PDF is password protected - will not try to generate pages. "
                    f"Document ID: {document.obfuscated_id}"
                )
                return 0

            log_PDF_fonts(input_file_path, document.obfuscated_id)

            # As a side effect of a successful execution below,
            # the output directory will be populated with png files,
            # one for every page of the PDF.
            # Note: Either the legacy create_pngs_from_pdf() or the
            # newer dockerized_create_pngs_from_pdf() may be invoked.

            if (USE_DOCKERIZED_GHOSTSCRIPT is True):
                container_result = _dockerized_create_pngs_from_pdf(
                    input_file_path,
                    output_dir_path,
                    document.obfuscated_id
                )
            else:
                container_result = _create_pngs_from_pdf(
                    input_file_path,
                    output_dir_path,
                    document.obfuscated_id
                )

            # If the PDF exceeds the max page limit, the script will exit and not produce PNGs,
            # leading to an incorrect page count. In this case, return the page count
            # that was determined by the pdfinfo command even though no PNGs were created.
            # TODO: allow for larger page counts here as PNGs creation is fairly cheap,
            # but prevent training/extraction jobs based on page count as needed.
            if not container_result.success:
                logger.debug(
                    f"_create_pdf_pages() - Document: {document.obfuscated_id} - "
                    f"exited early with page count: {str(container_result.page_count)}"
                )
                return container_result.page_count

            page_file_names = sorted(os.listdir(output_dir_path))

            logger.debug(
                f"_create_pdf_pages() - Document: {document.obfuscated_id} - "
                f"number of pages created: {str(len(page_file_names))}"
            )
            page_number = 0
            for page_file_name in page_file_names:
                page_number += 1
                page_file_path = os.path.join(output_dir_path, page_file_name)
                with open(page_file_path, "rb") as f:
                    content = f.read()
                document.store_page_content(page_number, content)

            n_pages = page_number
            return n_pages

        except Exception as ex:
            logger.exception(
                f"_create_pdf_pages() for Document {document.obfuscated_id} "
                "encountered an unexpected error: {!r}".format(ex)
            )
            raise


def log_PDF_fonts(pdf_file_path_name, document_obfuscated_id):
    """Examines a PDF for font declarations and logs results. Returns True if
    scanned a legal PDF, otherwise False.
    """
    # This scans a PDF file for fonts, *without* invoking Ghostscript; a
    # lightweight examination of the internals of the PDF without the overhead
    # of a secure Docker container, or the risks of executing/rendering the
    # contents of a PDF document. This uses <pdffonts>, from the package
    # <poppler-utils>. This method returns a boolean indicating whether or not
    # it succeeded in scanning a legal PDF.

    # The expected content of the first line of output from a successful
    # invocation of <pdffonts>; if the file is not a valid PDF document or if
    # <pdffonts> is not installed, this will not be the first line returned by
    # the subprocess.run() invocation. A PDF that is simply a wrapper around an
    # image file or files *will* return this line but will not list any fonts
    # in following lines of output.
    pdffonts_header_line = "name                                 type              encoding         emb sub uni " \
                           "object ID"

    pdffonts_command = [
        PDFFONTS_PATH,
        pdf_file_path_name,
    ]

    # Assume failure, until otherwise succeeded.
    pdffonts_listed_results = False

    try:
        pdffonts_result = subprocess.run(pdffonts_command, stdout=subprocess.PIPE, stderr=subprocess.STDOUT,
                                         timeout=PDFFONTS_TIMEOUT)

        # Build the multi-line output for logging then log it as a single output to the logger.
        logging_packet = f"pdffonts report for Document {document_obfuscated_id}\n"
        for line in pdffonts_result.stdout.decode('UTF-8', errors='ignore').split('\n'):
            logging_packet += "  " + line + "\n"
        logger.info(logging_packet)

        # Check to see if the expected header line was returned for a successful invocation of <pdffonts> on a legal
        # PDF. Note: if the PDF is malformed, not found, or any other error is reported back that error info goes into
        # the log output.
        if pdffonts_header_line in logging_packet:
            pdffonts_listed_results = True

    except subprocess.SubprocessError:
        logger.exception("log_PDF_fonts() - pdffonts subprocess.run() exception.")
        try:
            pdffonts_result.check_returncode()
        except subprocess.CalledProcessError:
            logger.exception("log_PDF_fonts() - pdffonts_result.check_returncode() exception. "
                             "pdffonts_result.returncode = {pdffonts_result.returncode)")

    return pdffonts_listed_results


def PDF_has_password(pdf_file_path_name):
    """Returns True if the PDF is password protected, otherwise returns False."""
    # This checks a PDF file for password protection, *without* invoking Ghostscript;
    # a lightweight examination of the internals of the PDF without adding
    # the overhead of a secure Docker container, or the risks of executing/rendering the
    # contents of a PDF document. This uses <pdfinfo>, from the package <poppler-utils>.
    pdfinfo_command = [
        PDFINFO_PATH,
        pdf_file_path_name,
    ]

    try:
        pdfinfo_result = subprocess.run(
            pdfinfo_command,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            timeout=PDFINFO_TIMEOUT
        )
        # If the PDF *is* password protected, there will be a warning message in STDERR, which has been
        # redirected to STDOUT in the subprocess.run() invocation.
        for line in pdfinfo_result.stdout.decode('UTF-8', errors='ignore').split('\n'):
            if 'Incorrect password' in line:
                return True

    except subprocess.SubprocessError:
        logger.exception(
            "PDF_has_password() - PDF_has_password subprocess.run() exception.")
        try:
            pdfinfo_result.check_returncode()
        except subprocess.CalledProcessError:
            logger.exception("PDF_has_password() - pdfinfo_result.check_returncode() exception. "
                             "pdfinfo_result.returncode = {pdfinfo_result.returncode)")

    return False


def _create_pngs_from_pdf(input_file_path, output_dir_path, document_obfuscated_id):
    # Directly invoke ghostscript, reading input pdf from input_file_path and
    # outputting generated pngs to output_directory.

    logger.debug(f"_create_pngs_from_pdf():\n - input_file_path: {input_file_path}\n "
                 f"- output_dir_path:  {output_dir_path}\n "
                 f"- document_obfuscated_id: {document_obfuscated_id}\n")

    gs_command = [
        "/usr/bin/time",
        GHOSTSCRIPT_PATH,
        "-q",
        "-dBATCH",
        "-dNOPAUSE",
        "-dSAFER",
        "-sDEVICE=png16m",
        "-dTextAlphaBits=4",
        "-r" + str(get_api_page_dpi() * GHOSTSCRIPT_RESAMPLE_INT_SCALAR),
        "-dDownScaleFactor=" + str(GHOSTSCRIPT_RESAMPLE_INT_SCALAR),
        "-sOutputFile=" + os.path.join(output_dir_path, "temp-%04d.png"),
        input_file_path
    ]

    logger.debug(f"_create_pngs_from_pdf() for Document {document_obfuscated_id} - gs_command: {gs_command}")

    start_time = time.perf_counter()

    try:
        gs_result = subprocess.run(
            gs_command, stdout=subprocess.PIPE, stderr=subprocess.STDOUT,
            timeout=get_ghostscript_timeout()
        )
    except subprocess.TimeoutExpired:
        logger.error(
            "_create_pngs_from_pdf() - Ghostscript subprocess.TimeoutExpired - "
            f"document: {document_obfuscated_id} - input_file_path: {input_file_path}"
        )

    gs_result.check_returncode()

    end_time = time.perf_counter()
    elapsed_time = end_time - start_time

    for line in gs_result.stdout.decode('UTF-8').split('\n'):
        if "user" in line and "system" in line and "elapsed" in line:
            logger.debug(f"_create_pngs_from_pdf() for Document {document_obfuscated_id}"
                         f" gs subprocess's gs time stats: {line}\n"
                         f"  gs subprocess elapsed time: {elapsed_time:.4f}")
        logger.debug(line)

    return True, None


def _dockerized_create_pngs_from_pdf(input_file_path, output_dir_path, document_obfuscated_id) -> GhostscriptResult:
    # Use a secure docker container to execute Ghostscript, reading input pdf from <input_file_path> and
    # outputting generated pngs to <output_dir_path>.

    set_up_docker_render_shellscript(os.path.join(output_dir_path, "render.sh"))
    docker_command = set_up_docker_command_for_dockerized_ghostscript(input_file_path, document_obfuscated_id,
                                                                      get_ghostscript_timeout(),
                                                                      get_ghostscript_max_pages(), get_api_page_dpi())
    logger.debug(f"_dockerized_create_pngs_from_pdf() for Document {document_obfuscated_id} "
                 f"- docker_command: {docker_command} starting.")

    start_time = time.perf_counter()

    container_result = run_dockerized_ghostscript_container(
        input_file_path,
        output_dir_path,
        docker_command
    )

    logger.debug(
        f"_dockerized_create_pngs_from_pdf() for Document {document_obfuscated_id} finished - "
        f"container elapsed time: {time.perf_counter() - start_time:.4f}")

    return container_result


def copy_document(source_document, target_data_pool, **kwargs):
    """
    If source document data pool and target data pool are not the same, copy
    document to target data pool, together with its file and tokenized in
    terminal state. Returns copy of source document.

    If source document data pool is the same as target data pool, no copy is
    made. Returns source document.

    Optional kwargs:

    `label`: (str) If provided, this label will be used instead of the source
        document's label when creating new document.

    `cache`: (dict) If provided, it will be used to keep track of copied
        objects. If not provided, a local dict will be created for the purpose.
        NOTE: THE DICT PASSED IN WILL BE MUTATED AS A SIDE EFFECT.
    """
    if source_document.data_pool == target_data_pool:
        return source_document

    cache = kwargs.pop('cache', {})
    label = (
        kwargs.pop('label', None)
        or source_document.label
    )
    label = str(label)
    new_document = copy_data_pool_aware_object(
        source_document, target_data_pool,
        copy_fields=(
            'label', 'mime_type', 'content_md5', 'page_count',
            'file_extension', 'token_count', 'language', 'inbound_path',
            'filesize',
            # format_id/cluster is per org/dp so don't copy it.
        ),
        label=label,
        cache=cache
    )

    # Copy pre-populated tokenized for the new document if the
    # tokenized is in a terminal status.
    for tokenized in source_document.tokenizeds.all():
        try:
            new_tokenized = copy_pre_populated_tokenized(
                tokenized, target_data_pool,
                new_document, cache=cache
            )
        except ValidationError as ex:
            logger.error(
                "Error copying pre-populated tokenized"
                " {} to {}: {!r}".format(tokenized, target_data_pool, ex)
            )
        else:
            if new_tokenized:
                new_document.tokenizeds.add(new_tokenized)

    copy_and_update_persistent_id_map(source_document, new_document, cache)
    return new_document


def copy_pre_populated_tokenized(source_tokenized, target_data_pool, target_parent_document, **kwargs):
    """
    Copy tokenized only if it's in terminal state.
    Only copy tokenized if the tokenized data pool and target data pool are
    not the same.

    Optional arguments:

    `cache`: (dict) If provided, it will be used to keep track of copied
        objects. If not provided, a local dict will be created for the purpose.
        NOTE: THE DICT PASSED IN WILL BE MUTATED AS A SIDE EFFECT.

    Returns copy if created, source tokenized otherwise.
    """
    if source_tokenized is None:
        return None

    if not source_tokenized.finished:
        return None

    if source_tokenized.data_pool == target_data_pool:
        return source_tokenized

    cache = kwargs.pop('cache', {})

    kwargs.setdefault(
        'copy_fields', ('tokenizer_version',)
    )
    kwargs.setdefault(
        'override_fields', {'document': target_parent_document}
    )
    new_tokenized = copy_data_pool_aware_object(
        source_tokenized, target_data_pool,
        cache=cache,
        **kwargs
    )

    copy_and_update_persistent_id_map(source_tokenized, new_tokenized, cache)
    return new_tokenized


def image_document_content_passes_file_check(doc_content, format_extension):
    """Wrapper for <image_document_passes_file_check(image_file_path):> that creates a file from the passed
    <doc_content> with the passed <format_extension> and invokes <image_document_passes_file_check(image_file_path)>."""

    with TemporaryDirectory(dir="/tmp") as td:
        try:
            image_file_path = os.path.join(td, f"input_file.{format_extension}")
            logger.debug(f"image_document_content_passes_file_check() writing temp file: {image_file_path}")
            with open(image_file_path, 'wb') as temp_file:
                temp_file.write(doc_content)
            file_check_result = image_document_passes_file_check(image_file_path)

        except Exception as ex:
            logger.exception(
                f"image_document_content_passes_file_check() encountered an unexpected error: {ex}")
            raise
    logger.debug(f"image_document_content_passes_file_check() file_check_result: {file_check_result}")
    return file_check_result


def image_document_passes_file_check(image_file_path):
    """Returns True if the image file is identified by the shell command <file> as an allowed image format,
    otherwise returns False."""

    file_command = [
        FILE_COMMAND_PATH,
        image_file_path
    ]

    try:
        file_command_result = subprocess.run(
            file_command,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            timeout=FILE_COMMAND_TIMEOUT)

    except subprocess.SubprocessError:
        logger.exception(
            "image_document_passes_file_check() - image_document_passes_file_check subprocess.run() exception.")
        try:
            file_command_result.check_returncode()
        except subprocess.CalledProcessError:
            logger.exception("image_document_passes_file_check() - file_command_result.check_returncode() exception. "
                             "file_command_result.returncode = {file_command_result.returncode)")

    if any(image_type in file_command_result.stdout.decode('UTF-8') for image_type in ALLOWED_IMAGE_TYPES):
        return True
    return False


def PDF_passes_file_check(pdf_file_path_name):
    """Returns True if the PDF file is identified by the shell command <file> as an actual PDF, otherwise False."""
    file_command = [
        FILE_COMMAND_PATH,
        pdf_file_path_name
    ]

    try:
        file_command_result = subprocess.run(
            file_command,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            timeout=FILE_COMMAND_TIMEOUT
        )

    except subprocess.SubprocessError:
        logger.exception(
            "PDF_passes_file_check() - PDF_has_password subprocess.run() exception.")
        try:
            file_command_result.check_returncode()
        except subprocess.CalledProcessError:
            logger.exception("PDF_passes_file_check() - file_command_result.check_returncode() exception. "
                             "file_command_result.returncode = {file_command_result.returncode)")

    if 'PDF document, version' in file_command_result.stdout.decode('UTF-8'):
        return True
    return False
