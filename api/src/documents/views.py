import logging
from datetime import datetime, timedelta

from django.apps import apps
from django.db.models import Prefetch, Subquery, F
from django.http import Http404
from django.shortcuts import get_object_or_404
from django.utils.timezone import utc
from django.http import StreamingHttpResponse
from django_filters.filters import <PERSON><PERSON><PERSON><PERSON><PERSON>
from obfuscate.obfuscate import deobfuscate, obfuscate
from rest_framework import status
from rest_framework import mixins as stock_mixins
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.viewsets import GenericViewSet as StockGenericViewSet

from documents.util import (
    FORCE_RETOKENIZATION, FORCE_RETOKENIZATION_OLDER_X_MINUTES, retokenize_tokenized,
    stream_csv, b64_to_hex
)
from glynt_api import mixins
from glynt_api.filters import FilterSet
from glynt_api.mixins import (
    CreatedAtUpdatedAtFilterMixin, LabelFilterMixin,
    PrefetchRelatedViewSetMixin, StatusFilterMixin
)
from glynt_api.pagination import CountChangeLimitOffsetPagination
from glynt_api.util import get_tokenizer_versions, is_advanced_mode
from glynt_api.viewsets import GenericViewSet, ModelViewSet
from jobs import display_to_states
from organizations.mixins import DataPoolAwareViewSetMixin
from organizations.models import DataPool
from tags.mixins import GlyntTagFilterMixin, TagFilterMixin

from .filters import DocumentManifestFilter, TextSearchFilterMixin
from .models import Document, Tokenized, DocumentEnrichments
from .serializers import (
    DocumentFileSerializer, DocumentPageSerializer, DocumentSerializer,
    TokenizedSerializer, UnscopedDocumentSerializer, DocumentTextPageSerializer, DocumentEnrichmentSerializer
)
from .tasks import create_pages

logger = logging.getLogger(__name__)


def _include_preview(request):
    return request.query_params.get('with_preview') == 'true'


class UnscopedDocumentViewSet(
    PrefetchRelatedViewSetMixin,
    mixins.ListModelMixin,
    mixins.RetrieveModelMixin,
    GenericViewSet
):
    basic_fields = [
        'id', 'created_at', 'updated_at', 'url', 'file_access_url',
        'data_pool_id', 'data_pool_label', 'org_id', 'org_label',
        'label', 'content_type', 'file_extension', 'content_md5',
        'language', 'pages', 'token_count', 'filesize', 'format_id', 'is_supported',
        'duplicate_count', 'inbound_path', 'enrichments',
        'file_temp_url', 'extractions', 'text_preview',
        'status', 'target_training_set', 'target_training_set_label',
    ]
    basic_views = ['list', 'retrieve']
    select_related_fields = ['data_pool', 'data_pool__organization', 'mime_type', 'target_training_set', 'text_content']
    filterset_class = DocumentManifestFilter
    model_class = Document
    ordering_fields = ('id', 'created_at', 'updated_at', 'label')
    serializer_class = UnscopedDocumentSerializer
    queryset = Document.objects.all()
    pagination_class = CountChangeLimitOffsetPagination

    def list(self, request, *args, **kwargs):
        # NOTE: the default count query is slow due to the subquery used to count duplicates,
        # so we'll force the count_queryset to be the basic queryset.
        limited_queryset = self.filter_queryset(self.get_basic_queryset())

        if self.paginator is not None:
            self.paginator.count_queryset = limited_queryset

        return super().list(request, *args, **kwargs)

    def get_queryset(self):
        queryset = Document.manifest_queryset.with_duplicate_counts().with_extractions()
        return queryset

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context['with_preview'] = _include_preview(self.request)
        return context

    @action(detail=True, methods=['get'], url_path='text')
    def text(self, request, *args, **kwargs):
        """Return text pages for the document."""
        self.serializer_class = DocumentTextPageSerializer
        self.basic_fields.append('text_pages')
        return super().retrieve(request, *args, **kwargs)


class DocumentFilter(
    GlyntTagFilterMixin,
    TagFilterMixin,
    LabelFilterMixin,
    StatusFilterMixin,
    CreatedAtUpdatedAtFilterMixin,
    TextSearchFilterMixin,
    FilterSet
):
    ground_truth = CharFilter(method='obfuscated_id_filter')
    tokenized = CharFilter(method='obfuscated_id_filter')
    training_set = CharFilter(method='obfuscated_id_filter')
    training_revision = CharFilter(method='obfuscated_id_filter')
    extraction = CharFilter(method='obfuscated_id_filter')
    extraction_batch = CharFilter(method='obfuscated_id_filter')


class DocumentViewSet(
    PrefetchRelatedViewSetMixin,
    DataPoolAwareViewSetMixin,
    ModelViewSet
):
    basic_fields = [
        'id', 'created_at', 'updated_at', 'url', 'file_access_url',
        'file_upload_url', 'label', 'content_type', 'enrichments', 'file_extension', 'content_md5', 'content',
        'inbound_path', 'tags', 'glynt_tags', 'file_temp_url', 'related_extractions',
        'status', 'target_training_set', 'format_id', 'filesize',
    ]
    basic_views = ['list', 'retrieve', 'create', 'partial_update', 'destroy']
    select_related_fields = ['data_pool', 'mime_type']
    prefetch_related_fields = ['tags', 'glynt_tags', 'tokenizeds']
    filterset_class = DocumentFilter
    model_class = Document
    ordering_fields = ('created_at', 'updated_at', 'label')
    serializer_class = DocumentSerializer

    @action(detail=True)
    def file(self, request, *args, **kwargs):
        """Return a temporary file access URL which can be used to retrieve the document file."""
        self.serializer_class = DocumentFileSerializer
        return super().retrieve(request, *args, **kwargs)

    @action(detail=True, methods=['post'], url_path='pages')
    def generate_pages(self, request, data_pools_obfuscated_id, obfuscated_id):
        """Initiate a generate pages task for the document."""
        id = Document.get_unobfuscated_id(obfuscated_id)
        document = get_object_or_404(Document, id=id)
        if not document.file_exists:
            return Response({'detail': 'Document file not found.'}, status=status.HTTP_404_NOT_FOUND)

        # page_count is inited to 0 in object creation.
        # We assume that if it is now 0 then the doc has not yet been
        # processed.
        if not document.page_count:
            create_pages.delay(document.id)

        return Response(status=status.HTTP_204_NO_CONTENT)

    @action(detail=True, url_path=r'pages/(?P<page_number>\d+)')
    def pages(self, request, *args, **kwargs):
        """Retrieve page for the document."""
        self.serializer_class = DocumentPageSerializer
        return super().retrieve(request, *args, **kwargs)

    def get_queryset(self):
        queryset = super().get_queryset()
        if _include_preview(self.request):
            queryset = queryset.select_related('text_content')

        if not self._extraction_data():
            return queryset

        extraction_batch_id = self._extraction_batch_id()
        if extraction_batch_id:
            """
            No document without extraction should be returned by this endpoint when these parameters are specified.
            To achieve a similar behavior to a right join, the filter needs to be made
            against the extraction and extraction_batch relationship.
            """
            queryset = queryset.filter(extraction__extraction_batch__id=extraction_batch_id)
        return queryset.prefetch_related(self._get_prefetch_extractions(extraction_batch_id))

    def _extraction_data(self):
        return (self.request.query_params.get('show_related_extractions', '').lower() == 'true'
                and self.request.method == 'GET')

    def _extraction_batch_id(self):
        return (deobfuscate(
            self.request.query_params['extraction_batch'])
            if self.request.query_params.get('extraction_batch') else None)

    def _get_prefetch_extractions(self, extraction_batch_id: str):
        extraction_objects = apps.get_model('extract.Extraction').objects
        return Prefetch('extractions',
                        (extraction_objects.filter(extraction_batch_id=extraction_batch_id)
                         if extraction_batch_id
                         else extraction_objects.all()),
                        to_attr='related_extractions')

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context['with_preview'] = _include_preview(self.request)
        context['show_related_extractions'] = self._extraction_data()
        context['extraction_batch_id'] = self._extraction_batch_id()
        return context


class DocumentEnrichmentsViewSet(
    DataPoolAwareViewSetMixin,
    stock_mixins.ListModelMixin,
    stock_mixins.CreateModelMixin,
    mixins.AllowPUTAsCreateMixin,
    stock_mixins.RetrieveModelMixin,
    stock_mixins.DestroyModelMixin,
    StockGenericViewSet
):
    model_class = DocumentEnrichments
    serializer_class = DocumentEnrichmentSerializer
    lookup_field = 'enrichment_key'


class TokenizedFilter(CreatedAtUpdatedAtFilterMixin, FilterSet):
    document = CharFilter(method='obfuscated_id_filter')
    extraction = CharFilter(method='obfuscated_id_filter')
    status = CharFilter(method='status_filter', label='Status')

    def status_filter(self, queryset, field_name, value):
        """Filter for State choices based on their string rep"""
        filter_states = display_to_states(value)

        return queryset.annotate(
            _latest_state=Subquery(
                Tokenized.get_states_subquery().values('state')[:1]
            )
        ).filter(_latest_state__in=filter_states)


class TokenizedViewSet(
    PrefetchRelatedViewSetMixin,
    DataPoolAwareViewSetMixin,
    mixins.CreateModelMixin,
    mixins.ListModelMixin,
    mixins.RetrieveModelMixin,
    GenericViewSet
):
    select_related_fields = ['document']
    prefetch_related_fields = ['states']
    filterset_class = TokenizedFilter
    model_class = Tokenized
    serializer_class = TokenizedSerializer
    ordering_fields = ('created_at', 'updated_at')

    if FORCE_RETOKENIZATION:
        def retrieve(self, request, *args, **kwargs):
            if not ('retrieve' in self.get_basic_views() or is_advanced_mode(request)):
                raise Http404

            instance = self.get_object()
            if request.query_params.get('refresh') == 'true':
                retokenize_tokenized(instance)
            data = self.get_serializer(instance).data
            data['tokenized']['updatedDate'] = instance.updated_at
            expiration = timedelta(minutes=FORCE_RETOKENIZATION_OLDER_X_MINUTES)
            data['tokenized']['expiredDate'] = datetime.utcnow().replace(tzinfo=utc) + expiration
            return Response(data)


class TokenizerVersionsView(APIView):

    def get(self, request, *args, **kwargs):
        if not is_advanced_mode(request):
            raise Http404
        versions = get_tokenizer_versions()

        return Response({
            'default': versions['default'],
            'active': versions['active'],
            'deprecated': versions['deprecated'],
            'obsolete': versions['obsolete'],
            'experimental': versions['experimental'],
        })


class DocumentManifestCSVView(APIView):
    """
    Custom CSV manifest endpoint at /v6/data-pools/{id}/manifest.csv
    """
    MAX_LIMIT = 40000
    DEFAULT_LIMIT = 30000

    def _get_limit(self, request):
        try:
            limit = int(request.query_params.get("limit", self.DEFAULT_LIMIT))
            # Ensure limit is positive and within bounds
            return max(1, min(limit, self.MAX_LIMIT))
        except ValueError:
            return self.DEFAULT_LIMIT

    def get(self, request, *args, **kwargs):
        # Get params from request
        since_id = request.query_params.get("since_id")
        limit = self._get_limit(request)
        obfuscated_id = self.kwargs["data_pools_obfuscated_id"]
        data_pool_id = deobfuscate(obfuscated_id)

        # Get the data pool or 404
        get_object_or_404(DataPool, id=data_pool_id)

        columns = [
            "org_id", "data_pool_id", "id", "created_at", "format_id",
            "page_count", "token_count", "language", "content_md5",
            "inbound_path", "label", "file_extension", "filesize", "text_preview",
        ]

        queryset = (
            Document.objects
            .filter(data_pool_id=data_pool_id)
            .select_related("data_pool__organization", "text_content")
            # Order by id to ensure consistent ordering
            .order_by("id")
            .annotate(
                org_id=F("data_pool__organization_id"),
                text_preview=F("text_content__preview"),
            )
            .values(*columns)
        )

        # Only fetch new documents
        if since_id:
            since_id = deobfuscate(since_id)
            queryset = queryset.filter(id__gt=since_id)

        def transform(row):
            # Add the hex md5 for compatibility with the manifest
            row["org_id"] = obfuscate(row["org_id"])
            row["data_pool_id"] = obfuscate(row["data_pool_id"])
            row["id"] = obfuscate(row["id"])
            row["created_at"] = row["created_at"].isoformat()
            try:
                # Guard against invalid base64 strings manually provided via API calls
                row["hex_md5"] = b64_to_hex(row["content_md5"])
            except Exception:
                row["hex_md5"] = None
            return row

        # Include any calculated columns here that are not part of the queryset
        headers = columns + ["hex_md5"]

        # Generate streaming response
        generator = stream_csv(queryset, headers=headers, transform=transform, limit=limit)
        response = StreamingHttpResponse(
            generator,
            content_type="text/csv; charset=utf-8"
        )
        response["Content-Disposition"] = f'attachment; filename="manifest_{obfuscated_id}.csv"'
        return response
