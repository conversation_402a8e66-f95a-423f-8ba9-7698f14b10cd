import logging
import time
from typing import Dict

from celery import chain, shared_task

from glynt_api.exceptions import BoxcarsUnavailable
from jobs.actions import finish_job as finish_job_action
from jobs.tasks import JobTask
from obfuscate.obfuscate import obfuscate

from .actions import assert_tokenized_job_success
from .actions import create_pages as create_pages_action
from .actions import execute_tokenized_job
from .exceptions import BoxcarsJobFailed, BoxcarsJobIncomplete
from .models import Document, Tokenized, TextContent
from notifications.events import ClusterIDAssignment
from notifications.services import get_notification_service

logger = logging.getLogger(__name__)


class TokenizeTask(JobTask):
    job_cls = Tokenized
    kwarg_job_id_name = 'tokenized_id'


@shared_task(bind=True, base=TokenizeTask)
def tokenize(self, tokenized_id, boxcars_priority=None):
    """Execute and manages the complete tokenization process."""
    chain(
        _start_tokenized_job.si(tokenized_id=tokenized_id, boxcars_priority=boxcars_priority),
        _wait_for_tokenized_job_success.s(tokenized_id=tokenized_id),
        _finish_tokenized_job.si(tokenized_id=tokenized_id)
    ).delay()


@shared_task(
    bind=True,
    base=TokenizeTask,
    autoretry_for=(BoxcarsUnavailable,),
    default_retry_delay=5,
    retry_jitter=True,
    retry_backoff=False,
    max_retries=None  # will retry forever until boxcars is available
)
def _start_tokenized_job(self, tokenized_id, boxcars_priority=None):
    tokenized = Tokenized.objects.get(id=tokenized_id)

    logger.info(
        f'Running tokenize task for Tokenized {tokenized.obfuscated_id} with boxcars priority {boxcars_priority}'
    )
    tokenized.set_state('IN_PROGRESS')
    job_id = execute_tokenized_job(tokenized, boxcars_priority)
    return job_id


@shared_task(
    bind=True,
    base=TokenizeTask,
    autoretry_for=(BoxcarsUnavailable, BoxcarsJobIncomplete,),
    default_retry_delay=3,
    retry_jitter=True,
    retry_backoff=False,
    max_retries=None  # will retry forever until boxcars fails its job
)
def _wait_for_tokenized_job_success(self, job_id, tokenized_id):
    tokenized_succeeded = assert_tokenized_job_success(job_id, tokenized_id)
    if not tokenized_succeeded:
        raise BoxcarsJobFailed(
            f"Boxcars failed for Tokenized {obfuscate(tokenized_id)}"
        )


@shared_task(base=TokenizeTask)
def _finish_tokenized_job(tokenized_id):
    tokenized = Tokenized.objects.get(id=tokenized_id)

    tokenized.set_state('FINISHING')

    # On the suspicion that there's latency between the PUT to the
    # presigned URL that populates the actual data, and visibility
    # of the data on S3, retry 3 times with backoff if we don't
    # get data.
    tries = 3
    retry_delay = 0.5
    while tries > 0:
        if tokenized.tokenized != {}:
            finish_job_action(tokenized, 'SUCCEEDED')
            generate_text_preview.delay(tokenized_id)
            return
        logger.warn(f"Retrying data for Tokenized {tokenized.obfuscated_id}")
        tries -= 1
        time.sleep(retry_delay)
        retry_delay *= 2

    # The task will run on_failure, which fails the Tokenized job
    assert tokenized.tokenized != {}, (
        f"No data found for Tokenized {tokenized.obfuscated_id} "
        "even though jobs was otherwise successful. Failing tokenized."
    )


@shared_task(
    autoretry_for=(Exception,),
    default_retry_delay=5,
    retry_jitter=True,
    retry_backoff=True,
    max_retries=3
)
def generate_text_preview(tokenized_id):
    try:
        tokenized = Tokenized.objects.get(id=tokenized_id)
        document = tokenized.document
        preview = tokenized.render_text_preview()

        # Create or update the text preview
        TextContent.objects.update_or_create(
            document=document,
            defaults={"preview": preview}
        )
        logger.info(f"Generated text preview for Document {document.obfuscated_id}")
    except Tokenized.DoesNotExist:
        logger.info(f"Tokenized {tokenized_id} does not exist")
    except Exception:
        logger.exception(f"Error generating text preview for Tokenized {tokenized_id}")
        raise


@shared_task
def create_pages(document_id):
    """Execute and manages the complete page creation process."""
    try:
        document = Document.objects.get(id=document_id)
    except:  # noqa
        logger.exception(
            f'Task: create_pages. Error getting Document of id {document_id}'
        )
        raise
    logger.info(f'Running create_pages task for Document {document.obfuscated_id}')
    create_pages_action(document)


@shared_task
def send_format_id_changed_notification(msg: Dict):
    try:
        event = ClusterIDAssignment(**msg)
        get_notification_service().notify(event)
    except Exception:
        logger.exception(f"Error notifying for event {event.event_type}, message: {msg}")
