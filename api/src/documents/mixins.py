from rest_framework import status
from rest_framework.decorators import action
from rest_framework.response import Response

from documents.tasks import create_pages


class GeneratePagesMixin:
    @action(detail=True, methods=['post'])
    def pages(self, request, data_pools_obfuscated_id, obfuscated_id):
        instance = self.get_object()
        for document in instance.documents.all():
            if document.page_count == 0:
                create_pages.delay(document.id)

        return Response(status=status.HTTP_204_NO_CONTENT)
