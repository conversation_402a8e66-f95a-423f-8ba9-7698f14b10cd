import logging

from document_data.db_client import SqlDBClient
from document_data.operator import DocumentDataOperator
from document_data.operator_plans import SCHEMA_OPERATORS, TableOperatorPlan

logger = logging.getLogger(__name__)


def execute_post_load_operators(doc, schema_name):
    logger.info(f"Running post load operators for document {doc.id}")

    db_client = SqlDBClient()
    table_plans: TableOperatorPlan = {
        k.upper(): v for k, v in SCHEMA_OPERATORS.schema_plans.items()
    }[schema_name.upper()]  # Case-insensitive

    operator = DocumentDataOperator(table_plans, db_client)
    operator.run_operations(doc.id)
    return operator.execution.result
