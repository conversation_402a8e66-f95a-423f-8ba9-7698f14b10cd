from datetime import datetime, timezone

from document_data.models import Meter, Account

from glynt_schemas.data.output.utils import get_mat_key


def now_factory() -> datetime:
    return datetime.now(timezone.utc)


def calculate_mat_key(meter: Meter) -> str:
    try:
        account = Account.objects.get(document=meter.document, account_number=meter.account_number)
    except Account.DoesNotExist:
        return None

    vendor_name = account.normalized_vendor_name
    vendor_type = account.vendor_type
    commodity = meter.commodity
    account_number = account.normalized_account_number
    meter_number = meter.normalized_meter_number

    return get_mat_key(vendor_name, vendor_type, commodity, account_number, meter_number)
