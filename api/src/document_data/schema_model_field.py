from django.db import models

from document_data.util import translate_legacy_fields_to_schema_fields
from glynt_schemas.data.enums import Currency
from glynt_schemas.data.schema_library import SCHEMA_LIBRARY
from glynt_schemas.data.schema_types import (
    DataSchema, DataSchemaTable, SchemaTableField
)

DB_TYPE_MAP = {
    'string': 'CharField',
    'amount': 'DecimalField',
    'date': 'DateField',
    'number': 'DecimalField',
}


def parse_schema_field(field_dot_path):
    parts = field_dot_path.split('.')
    schema: DataSchema = SCHEMA_LIBRARY.schemas[parts[0]]
    table: DataSchemaTable = schema.tables[parts[1]]
    field: SchemaTableField = table.get_field(parts[2])
    return schema, table, field


class SchemaModelField(models.Field):
    """
    Implements a django model field related to a field in the SCHEMA_LIBRARY.
    You must pass an `implements` parameter that specifies the field in the SCHEMA_LIBRARY
    that this class implements. It should be specified in dot-notation as shown below.

    Example: 'UtilityBill.Acccount.AccountNumber'

    where 'UtilityBill' is the schema name, 'Account' is th table name, and 'AccountNumber' is the field type

    """
    def __init__(self, *args, **kwargs):
        self._implements = kwargs.pop('implements')
        schema, table, self._field = parse_schema_field(self._implements)

        if not self._field:
            raise Exception(f'Invalid schema field specified: {self._implements}')

        kwargs['db_column'] = kwargs.get('db_column') or self._field.title
        kwargs['max_length'] = kwargs.get('max_length', 255)
        kwargs['null'] = True

        super().__init__(*args, **kwargs)

    def deconstruct(self):
        name, path, args, kwargs = super().deconstruct()
        kwargs['implements'] = self._implements
        return name, path, args, kwargs

    def get_internal_type(self):
        return DB_TYPE_MAP[self._field.datatype.name]

    @property
    def schema_field(self):
        return self.get_schema_field()

    def get_schema_field(self, extraction=None):
        '''
        Returns the schema object for this model property,
        which contains additional schema details.

        If you pass an extraction, runtime configuration information will
        be retrieved, such as user-specified date format, and loaded into
        the schema field automatically.
        '''
        parts = self._implements.split('.')
        schema: DataSchema = SCHEMA_LIBRARY.schemas[parts[0]]
        table: DataSchemaTable = schema.tables[parts[1]]
        field = table.get_field(parts[2])

        if extraction:
            # Load configuration
            self._configure_schema_field(schema, table, field, extraction)

        return field

    def _configure_schema_field(
            self,
            schema: DataSchema,
            table: DataSchemaTable,
            field: SchemaTableField,
            extraction):
        '''
        Call this method to load any dynamic configuration information that was
        user-defined, such as a date format.
        '''

        # TODO: In the future, load the configuration from the training set, which
        # will specify configurations by schema field ID.
        #
        # For now, we have to map the legacy fields to schema fields
        # and then do a reverse-lookup on the baked field.
        self._configure_legacy_field(schema, table, field, extraction)

    def _configure_legacy_field(
            self,
            schema: DataSchema,
            table: DataSchemaTable,
            field: SchemaTableField,
            extraction):
        # Find the baked field for this field
        translations = translate_legacy_fields_to_schema_fields(schema, extraction, table.title)
        baked_fields = {
            k: v for k, v in translations.items() if self._implements in v.get('schema_fields')
        }

        if baked_fields:
            baked_field_label = next(iter(baked_fields))
            baked_field = extraction.baked_fields.filter(label=baked_field_label).first()
            if baked_field:
                field.datatype.config.from_dt_config(self._get_dt_config(baked_field))

    def _get_dt_config(self, baked_field):
        '''
        Returns the configuration for the datatype of the baked field.
        Overload this in the subclass to do anything special.
        '''
        return baked_field.data_type_config


# These subclass could potentially inherit from their congruous django field classes like models.CharField
# and use SchemaModelField as a mixin
class AmountSchemaModelField(models.DecimalField, SchemaModelField):
    def __init__(self, *args, **kwargs):
        if 'max_digits' in kwargs:
            max_digits = kwargs.pop('max_digits')
        else:
            max_digits = 10  # Change this to 14 after migration 0006 has been run
        if 'decimal_places' in kwargs:
            decimal_places = kwargs.pop('decimal_places')
        else:
            decimal_places = 2
        super().__init__(max_digits=max_digits, decimal_places=decimal_places, *args, **kwargs)

    def _get_dt_config(self, baked_field):
        config = super()._get_dt_config(baked_field) or {}

        # Get the currency from the tags if it exists
        # This can go away in the future when currency is
        # changed to a config option
        for tag in baked_field.glynt_tags.names():
            try:
                currency = Currency[tag.upper()]
                config['currency'] = currency.name
            except KeyError:
                continue
        return config


class StringSchemaModelField(SchemaModelField):
    def get_internal_type(self):
        return 'CharField'


class NumberSchemaModelField(models.DecimalField, SchemaModelField):
    def __init__(self, *args, **kwargs):
        if 'max_digits' in kwargs:
            max_digits = kwargs.pop('max_digits')
        else:
            max_digits = 10  # Change this to 14 after migration 0006 has been run
        if 'decimal_places' in kwargs:
            decimal_places = kwargs.pop('decimal_places')
        else:
            decimal_places = 2
        super().__init__(max_digits=max_digits, decimal_places=decimal_places, *args, **kwargs)


class DateSchemaModelField(SchemaModelField):
    def get_internal_type(self):
        return 'DateField'


def schema_model_field_factory(implements):
    schema, table, field = parse_schema_field(implements)
    datatype = field.datatype.name
    if datatype == 'date':
        return DateSchemaModelField(implements=implements)
    elif datatype in ['number']:
        return NumberSchemaModelField(implements=implements)
    elif datatype in ['amount']:
        return AmountSchemaModelField(implements=implements)
    else:
        return StringSchemaModelField(implements=implements)
