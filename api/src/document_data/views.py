from document_data import models as doc_data_models
from document_data.serializers import DocumentDataMeterSerializer
from glynt_api.models import Model
from django_filters.filters import <PERSON><PERSON><PERSON><PERSON><PERSON>

from rest_framework import mixins as stock_mixins
from rest_framework.viewsets import GenericViewSet
from glynt_api.filters import FilterSet


class DocumentMeterFilter(FilterSet):
    document = CharFilter(method='obfuscated_id_filter')
    data_pool = CharFilter(method='doc_data_pool_obf_id_filter')
    training_set = CharFilter(method='doc_training_set_obj_id_filter')
    extraction = CharFilter(method='doc_ex_obf_id_filter')
    extraction_batch = CharFilter(method='doc_eb_obf_id_filter')

    def doc_data_pool_obf_id_filter(self, queryset, field_name, value):
        """Filter for data pool choices based on their string rep"""
        data_pool_id = Model.get_unobfuscated_id(value)
        return queryset.filter(document__data_pool=data_pool_id)

    def doc_training_set_obj_id_filter(self, queryset, field_name, value):
        """Filter for training set choices based on their string rep"""
        training_set_id = Model.get_unobfuscated_id(value)
        return queryset.filter(document__training_set=training_set_id)

    def doc_ex_obf_id_filter(self, queryset, field_name, value):
        """Filter for extraction choices based on their string rep"""
        extraction_id = Model.get_unobfuscated_id(value)
        return queryset.filter(extraction_id=extraction_id)

    def doc_eb_obf_id_filter(self, queryset, field_name, value):
        """Filter for extraction batch choices based on their string rep"""
        extraction_batch_id = Model.get_unobfuscated_id(value)
        return queryset.filter(extraction__extraction_batch=extraction_batch_id)


class DocumentDataMeterViewSet(
    stock_mixins.ListModelMixin,
    stock_mixins.RetrieveModelMixin,
    stock_mixins.UpdateModelMixin,
    GenericViewSet
):
    model_class = doc_data_models.Meter
    serializer_class = DocumentDataMeterSerializer
    filterset_class = DocumentMeterFilter
    queryset = doc_data_models.Meter.objects.all()
