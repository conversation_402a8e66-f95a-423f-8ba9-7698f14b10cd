from typing import Optional

from rest_framework.serializers import ModelSerializer, Serializer<PERSON>ethod<PERSON>ield

from document_data import models as doc_data_models
from document_data.utils import calculate_mat_key


class DocumentDataMeterSerializer(ModelSerializer):
    data_pool = SerializerMethodField()
    document = SerializerMethodField()
    extraction = SerializerMethodField()
    extraction_batch = SerializerMethodField()
    calculated_mat_key = SerializerMethodField()

    class Meta:
        model = doc_data_models.Meter
        fields = "__all__"

    def get_calculated_mat_key(self, obj) -> Optional[str]:
        return calculate_mat_key(obj)

    def get_data_pool(self, obj) -> Optional[str]:
        return obj.document.data_pool.obfuscated_id if obj.document else None

    def get_document(self, obj) -> Optional[str]:
        return obj.document.obfuscated_id if obj.document else None

    def get_extraction(self, obj) -> Optional[str]:
        return obj.extraction.obfuscated_id if obj.extraction else None

    def get_extraction_batch(self, obj) -> Optional[str]:
        return obj.extraction.extraction_batch.obfuscated_id if obj.extraction else None
