import re
from typing import Dict, List, Optional

from extract.models import Extraction
from glynt_schemas.data.schema_types import DataSchema
from glynt_schemas.data.tags import FIELD_TAG_MAP, SCHEMA_TABLE_TAG_MAP


def translate_legacy_fields_to_schema_fields(
        schema: DataSchema,
        extraction: Extraction,
        table_filter: Optional[str] = None) -> Dict:

    '''
    Given a schema and extraction, returns a dictionary mapping
    legacy, tag-based baked fields into schema fields.

    Note that a single baked field can map to multiple schema
    fields. For example, in the case where a usage value
    populates usageamount, but the usagetype is generated from
    tags/metadata on the baked field.

    You can optionally specify a table_filter, such that only
    certain schema tables will be mapped.
    '''
    def tag_match(patterns, tags):
        matches = []
        for pattern in patterns or []:
            # If the pattern starts with '!', it's a negative match
            negate = pattern.startswith('!')
            if negate:
                pattern = pattern[1:]

            matched = False
            for tag in tags:
                match = re.search(pattern.lower(), tag.lower())
                if match:
                    if negate:
                        return False  # Not allowed to have this tag

                    matches.append(tag)
                    matched = True

            if not matched and not negate:
                return False

        return True

    translations: Dict = {}  # Dict{Dict} of baked fields to schema field details

    for baked_field in extraction.baked_fields.all():
        tags = baked_field.glynt_tags.names()
        if not tags:
            continue

        tags = list(tags)
        tags.sort()
        fields_matched: List[str] = []
        foreign_keys = []

        # Figure out which fields in the schema this baked field maps to based on tags
        for entry in FIELD_TAG_MAP.get(schema.title):
            if tag_match(entry.patterns, tags):
                for mapped_table, mapped_field in entry.fields:
                    # Has all of the right ingredients. We have a match.
                    field_id = '.'.join([
                        schema.title,
                        mapped_table,
                        mapped_field
                    ])

                    # Analyze tags for a potential row ID indicator (e.g. "meter1")
                    # If we detect an ID, it could be one of three things:
                    #
                    # 1. It's the row ID of the table for this field
                    # 2. It's a row ID of a related table
                    # 3. It's just some ID we don't care about
                    rowid = None
                    match_info = field_id
                    for tag in tags:
                        id = re.search(r'[0-9]$', tag)
                        if id:
                            matched_tables = [
                                t for p, t in SCHEMA_TABLE_TAG_MAP.items() if re.search(p, tag.lower())
                            ]
                            if matched_tables:
                                if len(matched_tables) > 1:
                                    raise ValueError(f'Tag {tag} ambiguously matches multiple schema tables')

                                _, match_tablename = matched_tables[0].get('table', '').split('.')
                                rowid = int(tag[id.start(0):]) + int(matched_tables[0].get('index_modifier', 0))
                                if rowid <= 0:
                                    raise ValueError(
                                        f'Tag {tag} has an index that results in an invalid row ID of {rowid}'
                                    )
                                if match_tablename.lower() == mapped_table.lower():
                                    # The ID refers to the row for the table
                                    # this field gets loaded into.
                                    # Append row information
                                    match_info = match_info + ':' + str(rowid)
                                else:
                                    # This ID refers to a row in a foreign table.
                                    # Only record once per table, not per field matched.
                                    foreign_row = f"{mapped_table}:{match_tablename}:{rowid}"

                                    if foreign_row not in foreign_keys:
                                        foreign_keys.append(foreign_row)

                    fields_matched.append(match_info)

        translations[baked_field.label] = {
            'schema_fields': ','.join([f for f in fields_matched]),
            'schema_related_rows': ','.join(foreign_keys),
            'metadata': list(tags)
        }

    return translations


def inject_field_ids_from_tags(schema: DataSchema, extraction: Extraction, extraction_results: Dict):
    '''
    For all legacy extractions where fields are tagged, you must run this prior to
    running the CoreMLDataLoader.

    This function injects the required schema field IDs, metadata, and any detected
    foreign row relationships into extraction results, which are required for the loader,
    based on field tags.

    This function updates the extraction results, which you must then pass to the loader.
    '''
    field_translations = translate_legacy_fields_to_schema_fields(schema, extraction)

    for label, translation in field_translations.items():
        result = extraction_results.get(label, {})

        result['schema_map'] = translation
