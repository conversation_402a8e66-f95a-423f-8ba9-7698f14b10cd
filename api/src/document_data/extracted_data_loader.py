import logging
from abc import abstractmethod
from typing import Dict

from document_data import models
from extract.models import Extraction
from glynt_schemas.data.schema_types import DataSchema, InitialValueSource

logger = logging.getLogger(__name__)


class OutofOrderError(Exception):
    pass


class DataLoader():
    """
    Base class for all loaders that load source data into a schema data model.
    """
    def __init__(self,
                 schema: DataSchema,
                 document: models.Document,
                 extraction: Extraction,
                 results: Dict):
        self._schema = schema
        self._document = document
        self._extraction = extraction
        self._extraction_results = results

    @abstractmethod
    def _load(self):
        # Implement this method in subclass loaders
        pass

    def load(self):
        # Clean up all existing data for this document in the schema tables
        for _, table in self._schema.tables.items():
            django_model: models.SchemaModel = models.MODELS_BY_SCHEMA_TABLE.get(
                self._schema.title, {}).get(table.title)

            if django_model:
                django_model.objects.filter(document_id=self._document.id).delete()

        # Clean up additional fields table
        models.AdditionalField.objects.filter(document_id=self._document.id).delete()

        # Run the subclassed loader
        self._load()


class CoreMLDataLoader(DataLoader):
    """
    Loads data that has been extracted by Core ML

    The loader will load the results, using the extraction and document only as references.

    For every entry for an extracted result, set the following keys:

    - content: the data value. If the schema specifies VALUE as its initial value source, this is loaded
            into the field.
    - metadata: any contextual metadata to use in place of the value (typically used by data logic). If
            the schema specifies METADATA as its value source, this is loaded into the field.
    - schema_fields: a comma-separated list of fields the loader should populate with the result
            in the format "SchemaName.TableName.FieldName". The loader can be instructed to load a
            result value into a specific row by appending ":rowid" after the field ID in the
            schema_fields list. For example
            "SchemaName.TableName.FieldName:1". Row indices are 1-based, not 0-based.
            The first row is row 1.
    - schema_related_rows (optional): a comma-separated list of foreign rows in the format
            "local table: foreign table:row index" that will force the result to relate to
            a specific object in a foreign table. For example, if a usage relates to the
            first meter, then you'd specific "usage:meter:1".
    """
    def _load(self):
        self._related_rows_by_obj = {}

        # Loading will happen in two stages:
        # 1. The data is loaded into each of the tables and fields with no
        #    relationship amongst tables
        # 2. Foreign keys are then assigned to create relationships amongst the data

        # Stage 1: Load data into each of the tables
        self._load_initial_table_data()

        # Stage 2: Go through all tables, searching for foreign keys and
        # figuring out what they link to
        self._populate_foreign_keys()

    def _load_result(self, label, result):
        value = result.get('content')
        if value is not None and value != '':
            schema_map = result.get('schema_map', {})
            schema_fields = schema_map.get('schema_fields')
            metadata = schema_map.get('metadata', [])
            loaded = False
            if schema_fields:
                for field_id in schema_fields.split(','):  # Multiple field matches split by ','
                    rowid = None

                    # If a row is specified, the field format will be "field:row[:row...]"
                    rowids = []
                    if ':' in field_id:
                        field_id, *rowids = field_id.split(':')  # Field ID/row ID split by ':'
                        rowids = [int(r) for r in rowids]
                        rowids.sort()

                    schema_name, table_name, column_name = field_id.split('.')

                    assert schema_name == self._schema.title

                    try:
                        col = [
                            c for c in self._schema.tables[table_name].fields if c.title == column_name.lower()
                        ][0]
                    except IndexError:
                        raise Exception(f'Invalid schema field ID: {field_id}')
                    except KeyError:
                        raise Exception(f'Invalid schema field ID: {field_id}')

                    django_model: models.SchemaModel = models.MODELS_BY_SCHEMA_TABLE.get(
                        self._schema.title, {}).get(table_name)
                    if django_model:
                        # Figure out the django column name
                        django_field = [
                            f for f in django_model._meta.fields if getattr(
                                f, '_implements', NotImplemented) == field_id
                        ]
                        if django_field:  # If not, then the model doesn't implement this field
                            django_field = django_field[0]

                            # See if this object already exists
                            objs = django_model.objects.filter(document=self._document)
                            objs_to_update = []

                            # If a row is not explicitly specified, then upsert
                            if col.upsert:
                                # Get the last object. If it's value is not None, then insert,
                                # else update.
                                if objs and getattr(objs.last(), django_field.attname, None) is None:
                                    objs_to_update = [objs.last()]  # Update the last object
                                else:
                                    objs_to_update = [None]  # Create one
                            else:
                                for rowid in rowids:
                                    if len(objs) >= rowid:
                                        objs_to_update.append(objs[rowid - 1])  # Update it
                                    else:
                                        # If the row ID is the next, sequential number, create it
                                        # Otherwise, this row ID is out of order, so let's
                                        # put it back on the stack to create
                                        # it later
                                        if rowid == len(objs) + 1:
                                            objs_to_update.append(None)  # Create it
                                        else:
                                            raise OutofOrderError()

                            if col.initial_value_source == InitialValueSource.METADATA:
                                col_value = ','.join(metadata)
                            else:
                                col_value = value

                            # objs_to_update will be a list of either objects to create or update
                            for obj in objs_to_update:
                                if obj is None:
                                    # Need to create one
                                    kwargs = {
                                        'document': self._document,
                                        'extraction': self._extraction,
                                        django_field.attname: col_value
                                    }
                                    obj = django_model.objects.create(**kwargs)
                                    loaded = True

                                    if (schema_map.get('schema_related_rows') and
                                            self._related_rows_by_obj.get(obj) is None):
                                        self._related_rows_by_obj[obj] = schema_map.get('schema_related_rows')
                                else:
                                    kwargs = {
                                        django_field.attname: col_value
                                    }
                                    try:
                                        objs.filter(id=obj.id).update(**kwargs)
                                    except Exception as e:
                                        logger.error(f'Error updating {django_model.__name__} {obj.id}: {e}')
                                        raise e

                                    loaded = True

            if not loaded:
                # No suitable table and field was identified for this.
                # Load it into the additional fields data table
                models.AdditionalField.objects.create(
                    document=self._document,
                    label=label,
                    value=value,
                    metadata=','.join(metadata)
                )
                logger.debug(
                    f'No target schema field identified for {label}. Loading into additional field table'
                )

    def _load_initial_table_data(self):
        results_to_reprocess = []
        for label, result in self._extraction_results.items():
            try:
                self._load_result(label, result)
            except OutofOrderError:
                # Row was out of order, so put it back on the stack to reprocess
                results_to_reprocess.append((label, result))

        # Reprocess the results that were not loaded
        for label, result in results_to_reprocess:
            # Reprocess the result
            self._load_result(label, result)

    def _populate_foreign_keys(self):
        for tablename, table in self._schema.tables.items():
            related_fields = [f for f in table.fields if f.relates_to]
            if related_fields:
                django_model: models.SchemaModel = models.MODELS_BY_SCHEMA_TABLE.get(
                    self._schema.title, {}).get(tablename)
                if django_model:
                    # Get all objects for this table
                    objects = django_model.objects.filter(document=self._document)

                    # Identify related fields
                    for col in related_fields:
                        # Figure out the django column
                        path = '.'.join([self._schema.title, tablename, type(col).__name__])
                        django_col = [
                            f for f in django_model._meta.fields if getattr(f, '_implements', NotImplemented) == path
                        ]
                        if django_col:  # If not, then the model doesn't implement this field
                            django_col = django_col[0]

                            for obj in objects:
                                join_parts = col.relates_to.split('.')  # Len of 1 if only table is specified
                                related_table_name = join_parts[0]
                                related_table = models.MODELS_BY_SCHEMA_TABLE.get(
                                    self._schema.title, {}).get(related_table_name)
                                if related_table:
                                    related_objects = related_table.objects.filter(document=self._document)
                                    if related_objects:
                                        related_obj = None

                                        # Figure out the django field. Special case for `id` fields,
                                        # which are implied fields
                                        related_col_name = type(col).__name__ if len(join_parts) == 1 else join_parts[1]
                                        if related_col_name == 'id':
                                            related_django_col = related_table._meta.get_field('id')
                                        else:
                                            path = '.'.join([self._schema.title, related_table_name, related_col_name])
                                            related_django_col = [
                                                f for f in related_table._meta.fields if getattr(
                                                    f, '_implements', NotImplemented) == path
                                            ]
                                            if related_django_col:
                                                # If not, then the model doesn't implement this field
                                                related_django_col = related_django_col[0]

                                        if related_django_col:
                                            related_rows = self._related_rows_by_obj.get(obj)

                                            if related_rows:
                                                for related_row in related_rows.split(','):
                                                    # This will be in the format "local:foreign:rowid"
                                                    local_table, foreign_table, index = related_row.split(':')

                                                    if (foreign_table.lower() == related_table_name.lower() and
                                                            local_table == tablename):
                                                        if len(related_objects) >= int(index):
                                                            related_obj = related_objects[int(index) - 1]
                                                            break

                                            if not related_obj:
                                                # Default to the first object.
                                                related_obj = related_objects[0]

                                            related_value = getattr(related_obj, related_django_col.attname, None)
                                            setattr(obj, django_col.attname, related_value)
                                            obj.save()
