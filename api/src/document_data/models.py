import logging

from django.db import models

from document_data.schema_model_field import schema_model_field_factory
from documents.models import Document
from extract.models import Extraction

logger = logging.getLogger(__name__)


class SchemaModel(models.Model):
    class Meta:
        abstract = True

    document = models.ForeignKey(
        Document,
        null=True,
        on_delete=models.SET_NULL
    )
    extraction = models.ForeignKey(
        Extraction,
        null=True,
        on_delete=models.SET_NULL
    )
    created_at = models.DateTimeField(auto_now_add=True)

    def get_meta_field(self, field_name):
        """
        When accessing a model's property, you get the value of the property, not the property
        itself. Call this function to get the actual property object.
        """
        return self._meta.get_field(field_name)


class Account(SchemaModel):
    statement_id = schema_model_field_factory(implements='UtilityBill.Account.StatementId')
    vendor_name = schema_model_field_factory(implements='UtilityBill.Account.VendorName')
    vendor_type = schema_model_field_factory(implements='UtilityBill.Account.VendorType')
    additional_vendor1_name = schema_model_field_factory(
        implements='UtilityBill.Account.AdditionalVendor1Name')
    additional_vendor1_type = schema_model_field_factory(
        implements='UtilityBill.Account.AdditionalVendor1Type')
    additional_vendor1_account_number = schema_model_field_factory(
        implements='UtilityBill.Account.AdditionalVendor1AccountNumber')
    additional_vendor2_name = schema_model_field_factory(
        implements='UtilityBill.Account.AdditionalVendor2Name')
    additional_vendor2_type = schema_model_field_factory(
        implements='UtilityBill.Account.AdditionalVendor2Type')
    additional_vendor2_account_number = schema_model_field_factory(
        implements='UtilityBill.Account.AdditionalVendor2AccountNumber')
    account_number = schema_model_field_factory(implements='UtilityBill.Account.AccountNumber')
    normalized_account_number = schema_model_field_factory(implements='UtilityBill.Account.NormalizedAccountNumber')
    customer_name = schema_model_field_factory(implements='UtilityBill.Account.CustomerName')
    customer_number = schema_model_field_factory(implements='UtilityBill.Account.CustomerNumber')
    account_key = schema_model_field_factory(implements='UtilityBill.Account.AccountKey')
    service_street = schema_model_field_factory(implements='UtilityBill.Account.ServiceStreet')
    service_city = schema_model_field_factory(implements='UtilityBill.Account.ServiceCity')
    service_state = schema_model_field_factory(implements='UtilityBill.Account.ServiceState')
    service_zip = schema_model_field_factory(implements='UtilityBill.Account.ServiceZip')
    service_address = schema_model_field_factory(implements='UtilityBill.Account.ServiceAddress')
    standardized_vendor_name = schema_model_field_factory(implements='UtilityBill.Account.StandardizedVendorName')
    short_vendor_abbreviation = schema_model_field_factory(implements='UtilityBill.Account.ShortVendorAbbreviation')
    normalized_vendor_name = schema_model_field_factory(implements='UtilityBill.Account.NormalizedVendorName')


class Meter(SchemaModel):
    statement_id = schema_model_field_factory(implements='UtilityBill.Meter.StatementId')
    account_number = schema_model_field_factory(implements='UtilityBill.Meter.AccountNumber')
    meter_number = schema_model_field_factory(implements='UtilityBill.Meter.MeterNumber')
    meter_pod = schema_model_field_factory(implements='UtilityBill.Meter.MeterPOD')
    meter_tariff = schema_model_field_factory(implements='UtilityBill.Meter.MeterTariff')
    meter_start_date = schema_model_field_factory(implements='UtilityBill.Meter.MeterStartDate')
    meter_end_date = schema_model_field_factory(implements='UtilityBill.Meter.MeterEndDate')
    service_street = schema_model_field_factory(implements='UtilityBill.Meter.MeterServiceStreet')
    service_city = schema_model_field_factory(implements='UtilityBill.Meter.MeterServiceCity')
    service_state = schema_model_field_factory(implements='UtilityBill.Meter.MeterServiceState')
    service_zip = schema_model_field_factory(implements='UtilityBill.Meter.MeterServiceZip')
    service_address = schema_model_field_factory(implements='UtilityBill.Meter.ServiceAddress')
    meter_key = schema_model_field_factory(implements='UtilityBill.Meter.MeterKey')
    commodity = schema_model_field_factory(implements='UtilityBill.Meter.MeterCommodity')
    subregion = schema_model_field_factory(implements='UtilityBill.Meter.Subregion')

    mat_key = models.CharField(max_length=225, null=True, blank=True)


class Statement(SchemaModel):
    current_charges = schema_model_field_factory(implements='UtilityBill.Statement.CurrentCharges')
    total_amount_due = schema_model_field_factory(implements='UtilityBill.Statement.TotalAmountDue')
    statement_date = schema_model_field_factory(implements='UtilityBill.Statement.StatementDate')
    statement_number = schema_model_field_factory(implements='UtilityBill.Statement.StatementNumber')
    bill_start_date = schema_model_field_factory(implements='UtilityBill.Statement.BillStartDate')
    bill_end_date = schema_model_field_factory(implements='UtilityBill.Statement.BillEndDate')

    calc_statement_date = schema_model_field_factory(implements='UtilityBill.Statement.CalculatedStatementDate')
    calc_current_charges = schema_model_field_factory(implements='UtilityBill.Statement.CalculatedCurrentCharges')
    calc_bill_start_date = schema_model_field_factory(implements='UtilityBill.Statement.CalculatedBillStartDate')
    calc_bill_end_date = schema_model_field_factory(implements='UtilityBill.Statement.CalculatedBillEndDate')
    currency = schema_model_field_factory(implements='UtilityBill.Statement.Currency')


class Usage(SchemaModel):
    # Related fields
    statement_id = schema_model_field_factory(implements='UtilityBill.Usage.StatementId')
    account_number = schema_model_field_factory(implements='UtilityBill.Usage.AccountNumber')
    meter_id = schema_model_field_factory(implements='UtilityBill.Usage.MeterId')

    # Document fields
    amount = schema_model_field_factory(implements='UtilityBill.Usage.UsageAmount')
    usage_type = schema_model_field_factory(implements='UtilityBill.Usage.UsageType')
    commodity = schema_model_field_factory(implements='UtilityBill.Usage.UsageCommodity')
    master_commodity = schema_model_field_factory(implements='UtilityBill.Usage.MasterUsageCommodity')
    uom = schema_model_field_factory(implements='UtilityBill.Usage.UsageUOM')
    normalized_uom = schema_model_field_factory(implements='UtilityBill.Usage.NormalizedUsageUOM')

    unified_uom = schema_model_field_factory(implements='UtilityBill.Usage.UnifiedUOM')
    unified_conversion_factor = schema_model_field_factory(implements='UtilityBill.Usage.UnifiedConversionFactor')
    unified_usage = schema_model_field_factory(implements='UtilityBill.Usage.UnifiedUsageAmount')

    emissions_factor = schema_model_field_factory(implements='UtilityBill.Usage.EmissionsFactor')
    emissions_factor_uom = schema_model_field_factory(implements='UtilityBill.Usage.EmissionsFactorUOM')
    emissions_amount = schema_model_field_factory(implements='UtilityBill.Usage.EmissionsAmount')


class Charge(SchemaModel):
    # Document fields
    amount = schema_model_field_factory(implements='UtilityBill.Charge.ChargeAmount')
    commodity = schema_model_field_factory(implements='UtilityBill.Charge.ChargeCommodity')
    charge_type = schema_model_field_factory(implements='UtilityBill.Charge.ChargeType')
    currency = schema_model_field_factory(implements='UtilityBill.Charge.Currency')

    statement_id = schema_model_field_factory(implements='UtilityBill.Charge.StatementId')


class AdditionalField(models.Model):
    # Stores all unmodeled fields from the raw data
    document = models.ForeignKey(
        Document,
        null=True,
        on_delete=models.SET_NULL
    )
    extraction = models.ForeignKey(
        Extraction,
        null=True,
        on_delete=models.SET_NULL
    )
    created_at = models.DateTimeField(auto_now_add=True)

    label = models.CharField(max_length=255)
    value = models.CharField(max_length=1024)
    metadata = models.CharField(blank=True, null=True, max_length=1024)


MODELS_BY_SCHEMA_TABLE = {
    'UtilityBill': {
        'Statement': Statement,
        'Account': Account,
        'Meter': Meter,
        'Usage': Usage,
        'Charge': Charge,
    }
}
