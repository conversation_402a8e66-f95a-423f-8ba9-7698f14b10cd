from typing import List

from pydantic import BaseModel as BasePydantic


class BaseOperatorConfig(BasePydantic):
    opname: str


class SqlOperatorConfig(BaseOperatorConfig):
    opname: str = 'execute_sql'
    name: str
    statement: str


class FindAndReplaceOperatorConfig(BaseOperatorConfig):
    opname: str = 'find_and_replace'
    table_name: str
    column_name: str
    find: List[str]
    use_default: bool = False
    default_value: str = None


class RegexSubOperatorConfig(BaseOperatorConfig):
    opname: str = 'regex_sub'
    pattern: str
    repl: str = ""
    table_name: str
    source_col: str
    dest_col: str
