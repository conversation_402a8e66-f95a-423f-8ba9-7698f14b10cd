from typing import Dict, List

import pydantic

from document_data.operator_configs import (
    BaseOperatorConfig, FindAndReplaceOperatorConfig, SqlOperatorConfig, RegexSubOperatorConfig
)
from glynt_schemas.data import enums as schema_enums


class TableOperatorConfig(pydantic.BaseModel):
    operator_name: str


class TableOperatorPlan(pydantic.BaseModel):
    table_plans: Dict[str, List[BaseOperatorConfig]]  # Table name to list of operator names


class OperatorPlan(pydantic.BaseModel):
    schema_plans: Dict[str, TableOperatorPlan]  # Schema name to table plans


# Configuration of operators to run by schema by table (we need to get this down to a field level)
SCHEMA_OPERATORS = OperatorPlan(
    schema_plans={
        'UtilityBill': TableOperatorPlan(
            table_plans={
                'Account': [
                    RegexSubOperatorConfig(
                        pattern=r"[-\s\W]",
                        table_name="document_data_account",
                        source_col="accountnumber",
                        dest_col="normalizedaccountnumber"
                    ),
                    BaseOperatorConfig(opname="normalize_vendor_name"),
                    BaseOperatorConfig(opname="generate_account_key"),
                    BaseOperatorConfig(opname="calculate_account_address"),
                    BaseOperatorConfig(opname="set_vendor_type"),
                ],
                'Meter': [
                    FindAndReplaceOperatorConfig(
                        table_name='document_data_meter', column_name='metercommodity',
                        find=[c.value for c in schema_enums.CommodityType],
                        use_default=True, default_value=None
                    ),
                    BaseOperatorConfig(opname="set_meter_service_address"),
                    BaseOperatorConfig(opname="parse_meter_zip_code"),
                    SqlOperatorConfig(
                        name="set_subregion",
                        statement=(
                            'UPDATE document_data_meter '
                            'SET '
                            'subregion = (SELECT subregion FROM static_enrichment_tables_zipsubregions zip'
                            ' WHERE zip.zipcode=document_data_meter.meterservicezip) '
                            'WHERE document_id={doc_id}'
                        )
                    ),
                    FindAndReplaceOperatorConfig(
                        table_name='document_data_usage', column_name='usagecommodity',
                        find=[c.value for c in schema_enums.CommodityType],
                        use_default=True, default_value=None
                    ),
                    BaseOperatorConfig(opname="set_usage_type_by_tags"),
                    BaseOperatorConfig(opname="generate_meter_key"),
                ],
                'Usage': [
                    BaseOperatorConfig(opname="set_master_usage_commodity"),
                    BaseOperatorConfig(opname="normalize_usage_uom"),
                    BaseOperatorConfig(opname="generate_unified_usage"),
                    BaseOperatorConfig(opname="generate_usage_emissions")
                ],
                'Charge': [
                    BaseOperatorConfig(opname="set_charge_type_by_tags"),
                    FindAndReplaceOperatorConfig(
                        table_name='document_data_charge', column_name='chargecommodity',
                        find=[c.value for c in schema_enums.CommodityType],
                        use_default=True, default_value=None
                    ),
                    BaseOperatorConfig(opname="parse_charge_currency")
                ],
                'Statement': [
                    BaseOperatorConfig(opname="calculate_current_charges"),
                    BaseOperatorConfig(opname='calculate_bill_dates'),
                    SqlOperatorConfig(
                        name="calculated_statement_date",
                        statement=(
                            'UPDATE '
                            'document_data_statement '
                            'SET '
                            'calculatedstatementdate = ifnull(statementdate, calculatedbillenddate) '
                            'WHERE document_data_statement.document_id={doc_id}'
                        )
                    ),
                    BaseOperatorConfig(opname="parse_statement_currency")
                ],
            },
        )
    }
)
