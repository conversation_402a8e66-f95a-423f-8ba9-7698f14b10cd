Document Id	Field Label	Field Glynt Tags	Returned Value
qD0OX1	VendorName	['string', 'vendorname', 'supply-delivery']	ALDERWOOD WATER & WASTEWATER
qD0OX1	AccountNumber	['accountnumber']	********-01
qD0OX1	CustomerName	['customername']	NORTHSHORE SCHOOL DIST #417
qD0OX1	CustomerNumber	['customernumber']	
qD0OX1	StatementDate	['statementdate']	2025-01-09
qD0OX1	StatementNumber	['statementnumber']	
qD0OX1	BillStartDate	['billstartdate']	
qD0OX1	BillEndDate	['billenddate']	
qD0OX1	CurrentCharges	[]	1024.91
qD0OX1	TotalAmountDue	['totalamountdue', 'USD']	1024.91
qD0OX1	TotalUsage	[]	
qD0OX1	LateFee	['USD', 'latefeecharge', 'chargeamount']	
qD0OX1	W_M1_BilledUsage	['billedusage', 'meter1', 'water']	
qD0OX1	W_M1_BilledUsageUnits	['meter1', 'billedusageuom', 'water']	
qD0OX1	W_M1_MeterNumber	['meter1', 'meternumber', 'water']	**********
qD0OX1	W_M1_MeterStartDate	['meter1', 'meterstartdate', 'water']	2024-10-29
qD0OX1	W_M1_MeterEndDate	['meter1', 'meterenddate', 'water']	2024-12-27
qD0OX1	W_M1_MeterUsage	['meter1', 'usage', 'water']	0
qD0OX1	W_Usage2	['meter1', 'usage', 'water']	0
qD0OX1	W_M1_MeterUsageUnits	['meter1', 'usageuom', 'water']	CCF
qD0OX1	ServiceAddressL1	['servicestreet']	23400 5TH AVE W SHELTON VIEW EL
qD0OX1	ServiceAddressL2	['servicecity']	
qD0OX1	ServiceAddressL3	['servicestate']	
qD0OX1	ServiceAddressL4	['servicezip']	
qD0OX1	W_M1_PODNumber	['meter1', 'pod', 'water']	
qD0OX1	W_M1_Tariff	['meter1', 'tariff', 'water']	
qD0OX1	W_ThirdPartyProviderName_1	['addvendor1', 'water']	
qD0OX1	W_ThirdPartyProviderAccountNumber_1	['addvendor1accountnumber', 'water']	
qD0OX1	W_ThirdPartyProviderName_2	['addvendor2', 'water']	
qD0OX1	W_ThirdPartyProviderAccountNumber_2	['addvendor2accountnumber', 'water']	
qD0OX1	W_TotalSupplyCharge	['USD', 'totalsupplycharge', 'water']	
qD0OX1	W_TotalDeliveryCharge	['USD', 'totaldeliverycharge', 'water']	
qD0OX1	WatBaseChrg	['USD', 'chargeamount', 'fixedcharge', 'water']	796.66
qD0OX1	WatBaseTier	['USD', 'chargeamount', 'usagecharge', 'water']	0.00
qD0OX1	WaterTier1	['USD', 'chargeamount', 'usagecharge', 'water']	
qD0OX1	WaterTier2	['USD', 'chargeamount', 'usagecharge', 'water']	
qD0OX1	WaterTier3	['USD', 'chargeamount', 'usagecharge', 'water']	
qD0OX1	WBotFraFee	['USD', 'chargeamount', 'misccharge', 'water']	47.80
qD0OX1	SewerCharges	['chargeamount', 'USD', 'sewer', 'unmetered1', 'usagecharge']	170.24
qD0OX1	SBotFraFee	['USD', 'chargeamount', 'misccharge', 'sewer']	10.21
qD0OX1	Wxxdemandcharges1	['USD', 'chargeamount', 'demandcharge', 'water']	
qD0OX1	WMiscCharge1	['USD', 'chargeamount', 'misccharge', 'water']	
qD0OX1	Wxxdemandcharges2	['USD', 'chargeamount', 'demandcharge', 'water']	
qD0OX1	Wxxfixedcharges2	['USD', 'chargeamount', 'fixedcharge', 'water']	
qD0OX1	Wxxusagecharges5	['USD', 'chargeamount', 'usagecharge', 'water']	
qD0OX1	WMiscCharge2	['USD', 'chargeamount', 'misccharge', 'water']	
qD0OX1	Wxxmisccharges3	['USD', 'chargeamount', 'misccharge', 'water']	
qD0OX1	Wxxmisccharges4	['USD', 'chargeamount', 'misccharge', 'water']	
qD0OX1	Wxxmisccharges5	['USD', 'chargeamount', 'misccharge', 'water']	
qD0OX1	Wxxmisccharges6	['USD', 'chargeamount', 'misccharge', 'water']	
qD0OX1	Wxxmisccharges7	['USD', 'chargeamount', 'misccharge', 'water']	
qD0OX1	Wxxtaxcharges1	['USD', 'chargeamount', 'taxcharge', 'water']	
qD0OX1	Wxxtaxcharges2	['USD', 'chargeamount', 'taxcharge', 'water']	
qD0OX1	Wxxtaxcharges3	['USD', 'chargeamount', 'taxcharge', 'water']	
qD0OX1	Wxxtaxcharges4	['USD', 'chargeamount', 'taxcharge', 'water']	
qD0OX1	Wxxtaxcharges5	['USD', 'chargeamount', 'taxcharge', 'water']	
qD0OX1	Wxxtaxcharges6	['USD', 'chargeamount', 'taxcharge', 'water']	
qD0OX1	Wxxtaxcharges7	['USD', 'chargeamount', 'taxcharge', 'water']	
qD0OX1	res_W_xxUsageCharge	['chargeamount', 'USD', 'usagecharge', 'water']	
qD0OX1	res_W_xx1UsageCharge	['chargeamount', 'USD', 'usagecharge', 'water']	
qD0OX1	res_W_xxMiscCharge	['chargeamount', 'USD', 'misccharge', 'water']	
qD0OX1	res_W_xx1MiscCharge	['chargeamount', 'USD', 'misccharge', 'water']	
qD0OX1	res_W_xxTaxCharge	['chargeamount', 'USD', 'taxcharge', 'water']	
qD0OX1	res_W_xx1TaxCharge	['chargeamount', 'USD', 'taxcharge', 'water']	
qD0OX1	res_W_xxFixedcharge	['chargeamount', 'USD', 'fixedcharge', 'water']	
qD0OX1	res_W_xxDemandCharge	['chargeamount', 'USD', 'demandcharge', 'water']	
qD0OX1	res_LateFee	['chargeamount', 'USD', 'latefeecharge']	
qD0OX1	SewerUsageCharge2	['chargeamount', 'USD', 'usagecharge', 'sewer', 'unmetered1']	
qD0OX1	SewerFixedCharge1	['chargeamount', 'USD', 'sewer', 'unmetered1', 'fixedcharge']	
qD0OX1	SewerUsageCharge3	['chargeamount', 'USD', 'usagecharge', 'sewer', 'unmetered1']	
qD0OX1	S_MiscCharge	['chargeamount', 'USD', 'sewer', 'unmetered1', 'misccharge']	
qD0OX1	S_MiscCharge2	['chargeamount', 'USD', 'usagecharge', 'sewer', 'unmetered1']	
qD0OX1	S_MiscCharge3	['chargeamount', 'USD', 'usagecharge', 'sewer', 'unmetered1']	
qD0OX1	S_MiscCharge4	['chargeamount', 'USD', 'usagecharge', 'sewer', 'unmetered1']	
qD0OX1	Non-Com_xxMiscCharge	['chargeamount', 'USD', 'misccharge']	
qD0OX1	Non-Com_xx1MiscCharge	['chargeamount', 'USD', 'misccharge']	
qD0OX1	Non-Com_xx2MiscCharge	['chargeamount', 'USD', 'misccharge']	
qD0OX1	Non-Com_xx3MiscCharge	['chargeamount', 'USD', 'misccharge']	
qD0OX1	Non-Com_xx4MiscCharge	['chargeamount', 'USD', 'misccharge']	
qD0OX1	Non-Com_xx5MiscCharge	['chargeamount', 'USD', 'misccharge']	
qD0OX1	Non-Com_xx6MiscCharge	['chargeamount', 'USD', 'misccharge']	
SYa402	VendorName	['vendorname']	ALDERWOOD WATER & WASTEWATER
SYa402	AccountNumber	['accountnumber']	********-01
SYa402	CustomerName	['customername']	NORTHSHORE SCHOOL DIST 417
SYa402	CustomerNumber	['customernumber']	
SYa402	StatementDate	['statementdate']	2025-01-09
SYa402	StatementNumber	['statementnumber']	
SYa402	BillStartDate	['billstartdate']	
SYa402	BillEndDate	['billenddate']	
SYa402	CurrentCharges	[]	3684.50
SYa402	TotalAmountDue	['totalamountdue', 'USD']	3684.50
SYa402	TotalUsage	[]	
SYa402	LateFee	['USD', 'latefeecharge', 'chargeamount']	
SYa402	W_M1_BilledUsage	['billedusage', 'meter1', 'water']	
SYa402	W_M1_BilledUsageUnits	['meter1', 'billedusageuom', 'water']	
SYa402	W_M1_MeterNumber	['meter1', 'meternumber', 'water']	**********
SYa402	W_M1_MeterStartDate	['meter1', 'meterstartdate', 'water']	2024-10-29
SYa402	W_M1_MeterEndDate	['meter1', 'meterenddate', 'water']	2024-12-27
SYa402	W_M1_MeterUsage	['meter1', 'usage', 'water']	47
SYa402	W_Usage2	['meter1', 'usage', 'water']	169
SYa402	W_M1_MeterUsageUnits	['meter1', 'usageuom', 'water']	CCF
SYa402	ServiceAddressL1	['servicestreet']	303 224TH ST SW
SYa402	ServiceAddressL2	['servicecity']	
SYa402	ServiceAddressL3	['servicestate']	
SYa402	ServiceAddressL4	['servicezip']	
SYa402	W_M1_PODNumber	['meter1', 'pod', 'water']	
SYa402	W_M1_Tariff	['meter1', 'tariff', 'water']	
SYa402	W_ThirdPartyProviderName_1	['addvendor1', 'water']	
SYa402	W_ThirdPartyProviderAccountNumber_1	['addvendor1accountnumber', 'water']	
SYa402	W_ThirdPartyProviderName_2	['addvendor2', 'water']	
SYa402	W_ThirdPartyProviderAccountNumber_2	['addvendor2accountnumber', 'water']	
SYa402	W_TotalSupplyCharge	['USD', 'totalsupplycharge', 'water']	
SYa402	W_TotalDeliveryCharge	['USD', 'totaldeliverycharge', 'water']	
SYa402	WatBaseChrg	['USD', 'chargeamount', 'fixedcharge', 'water']	512.82
SYa402	WatBaseTier	['USD', 'chargeamount', 'usagecharge', 'water']	0.00
SYa402	WaterTier1	['USD', 'chargeamount', 'usagecharge', 'water']	457.52
SYa402	WaterTier2	['USD', 'chargeamount', 'usagecharge', 'water']	
SYa402	WaterTier3	['USD', 'chargeamount', 'usagecharge', 'water']	
SYa402	WBotFraFee	['USD', 'chargeamount', 'misccharge', 'water']	58.22
SYa402	SewerCharges	['chargeamount', 'USD', 'sewer', 'unmetered1', 'usagecharge']	2505.60
SYa402	SBotFraFee	['USD', 'chargeamount', 'misccharge', 'sewer']	150.34
SYa402	Wxxdemandcharges1	['USD', 'chargeamount', 'demandcharge', 'water']	
SYa402	WMiscCharge1	['USD', 'chargeamount', 'misccharge', 'water']	
SYa402	Wxxdemandcharges2	['USD', 'chargeamount', 'demandcharge', 'water']	
SYa402	Wxxfixedcharges2	['USD', 'chargeamount', 'fixedcharge', 'water']	
SYa402	Wxxusagecharges5	['USD', 'chargeamount', 'usagecharge', 'water']	
SYa402	WMiscCharge2	['USD', 'chargeamount', 'misccharge', 'water']	
SYa402	Wxxmisccharges3	['USD', 'chargeamount', 'misccharge', 'water']	
SYa402	Wxxmisccharges4	['USD', 'chargeamount', 'misccharge', 'water']	
SYa402	Wxxmisccharges5	['USD', 'chargeamount', 'misccharge', 'water']	
SYa402	Wxxmisccharges6	['USD', 'chargeamount', 'misccharge', 'water']	
SYa402	Wxxmisccharges7	['USD', 'chargeamount', 'misccharge', 'water']	
SYa402	Wxxtaxcharges1	['USD', 'chargeamount', 'taxcharge', 'water']	
SYa402	Wxxtaxcharges2	['USD', 'chargeamount', 'taxcharge', 'water']	
SYa402	Wxxtaxcharges3	['USD', 'chargeamount', 'taxcharge', 'water']	
SYa402	Wxxtaxcharges4	['USD', 'chargeamount', 'taxcharge', 'water']	
SYa402	Wxxtaxcharges5	['USD', 'chargeamount', 'taxcharge', 'water']	
SYa402	Wxxtaxcharges6	['USD', 'chargeamount', 'taxcharge', 'water']	
SYa402	Wxxtaxcharges7	['USD', 'chargeamount', 'taxcharge', 'water']	
SYa402	res_W_xxUsageCharge	['chargeamount', 'USD', 'usagecharge', 'water']	
SYa402	res_W_xx1UsageCharge	['chargeamount', 'USD', 'usagecharge', 'water']	
SYa402	res_W_xxMiscCharge	['chargeamount', 'USD', 'misccharge', 'water']	
SYa402	res_W_xx1MiscCharge	['chargeamount', 'USD', 'misccharge', 'water']	
SYa402	res_W_xxTaxCharge	['chargeamount', 'USD', 'taxcharge', 'water']	
SYa402	res_W_xx1TaxCharge	['chargeamount', 'USD', 'taxcharge', 'water']	
SYa402	res_W_xxFixedcharge	['chargeamount', 'USD', 'fixedcharge', 'water']	
SYa402	res_W_xxDemandCharge	['chargeamount', 'USD', 'demandcharge', 'water']	
SYa402	res_LateFee	['chargeamount', 'USD', 'latefeecharge']	
SYa402	SewerUsageCharge2	['chargeamount', 'USD', 'usagecharge', 'sewer', 'unmetered1']	
SYa402	SewerFixedCharge1	['chargeamount', 'USD', 'sewer', 'unmetered1', 'fixedcharge']	
SYa402	SewerUsageCharge3	['chargeamount', 'USD', 'usagecharge', 'sewer', 'unmetered1']	
SYa402	S_MiscCharge	['chargeamount', 'USD', 'sewer', 'unmetered1', 'misccharge']	
SYa402	S_MiscCharge2	['chargeamount', 'USD', 'usagecharge', 'sewer', 'unmetered1']	
SYa402	S_MiscCharge3	['chargeamount', 'USD', 'usagecharge', 'sewer', 'unmetered1']	
SYa402	S_MiscCharge4	['chargeamount', 'USD', 'usagecharge', 'sewer', 'unmetered1']	
SYa402	Non-Com_xxMiscCharge	['chargeamount', 'USD', 'misccharge']	
SYa402	Non-Com_xx1MiscCharge	['chargeamount', 'USD', 'misccharge']	
SYa402	Non-Com_xx2MiscCharge	['chargeamount', 'USD', 'misccharge']	
SYa402	Non-Com_xx3MiscCharge	['chargeamount', 'USD', 'misccharge']	
SYa402	Non-Com_xx4MiscCharge	['chargeamount', 'USD', 'misccharge']	
SYa402	Non-Com_xx5MiscCharge	['chargeamount', 'USD', 'misccharge']	
SYa402	Non-Com_xx6MiscCharge	['chargeamount', 'USD', 'misccharge']	
ldGcK1	VendorName	['vendorname']	ALDERWOOD WATER & WASTEWATER
ldGcK1	AccountNumber	['accountnumber']	********-01
ldGcK1	CustomerName	['customername']	NORTHSHORE SCHOOL DIST 417
ldGcK1	CustomerNumber	['customernumber']	
ldGcK1	StatementDate	['statementdate']	2025-01-09
ldGcK1	StatementNumber	['statementnumber']	
ldGcK1	BillStartDate	['billstartdate']	
ldGcK1	BillEndDate	['billenddate']	
ldGcK1	CurrentCharges	[]	19.02
ldGcK1	TotalAmountDue	['totalamountdue', 'USD']	19.02
ldGcK1	TotalUsage	[]	
ldGcK1	LateFee	['USD', 'latefeecharge', 'chargeamount']	
ldGcK1	W_M1_BilledUsage	['billedusage', 'meter1', 'water']	
ldGcK1	W_M1_BilledUsageUnits	['meter1', 'billedusageuom', 'water']	
ldGcK1	W_M1_MeterNumber	['meter1', 'meternumber', 'water']	**********
ldGcK1	W_M1_MeterStartDate	['meter1', 'meterstartdate', 'water']	2024-10-29
ldGcK1	W_M1_MeterEndDate	['meter1', 'meterenddate', 'water']	2024-12-27
ldGcK1	W_M1_MeterUsage	['meter1', 'usage', 'water']	0
ldGcK1	W_Usage2	['meter1', 'usage', 'water']	
ldGcK1	W_M1_MeterUsageUnits	['meter1', 'usageuom', 'water']	CCF
ldGcK1	ServiceAddressL1	['servicestreet']	303 224TH ST SW D/C 1
ldGcK1	ServiceAddressL2	['servicecity']	
ldGcK1	ServiceAddressL3	['servicestate']	
ldGcK1	ServiceAddressL4	['servicezip']	
ldGcK1	W_M1_PODNumber	['meter1', 'pod', 'water']	
ldGcK1	W_M1_Tariff	['meter1', 'tariff', 'water']	
ldGcK1	W_ThirdPartyProviderName_1	['addvendor1', 'water']	
ldGcK1	W_ThirdPartyProviderAccountNumber_1	['addvendor1accountnumber', 'water']	
ldGcK1	W_ThirdPartyProviderName_2	['addvendor2', 'water']	
ldGcK1	W_ThirdPartyProviderAccountNumber_2	['addvendor2accountnumber', 'water']	
ldGcK1	W_TotalSupplyCharge	['USD', 'totalsupplycharge', 'water']	
ldGcK1	W_TotalDeliveryCharge	['USD', 'totaldeliverycharge', 'water']	
ldGcK1	WatBaseChrg	['USD', 'chargeamount', 'fixedcharge', 'water']	17.94
ldGcK1	WatBaseTier	['USD', 'chargeamount', 'usagecharge', 'water']	0.00
ldGcK1	WaterTier1	['USD', 'chargeamount', 'usagecharge', 'water']	
ldGcK1	WaterTier2	['USD', 'chargeamount', 'usagecharge', 'water']	
ldGcK1	WaterTier3	['USD', 'chargeamount', 'usagecharge', 'water']	
ldGcK1	WBotFraFee	['USD', 'chargeamount', 'misccharge', 'water']	1.08
ldGcK1	SewerCharges	['chargeamount', 'USD', 'sewer', 'unmetered1', 'usagecharge']	
ldGcK1	SBotFraFee	['USD', 'chargeamount', 'misccharge', 'sewer']	
ldGcK1	Wxxdemandcharges1	['USD', 'chargeamount', 'demandcharge', 'water']	
ldGcK1	WMiscCharge1	['USD', 'chargeamount', 'misccharge', 'water']	
ldGcK1	Wxxdemandcharges2	['USD', 'chargeamount', 'demandcharge', 'water']	
ldGcK1	Wxxfixedcharges2	['USD', 'chargeamount', 'fixedcharge', 'water']	
ldGcK1	Wxxusagecharges5	['USD', 'chargeamount', 'usagecharge', 'water']	
ldGcK1	WMiscCharge2	['USD', 'chargeamount', 'misccharge', 'water']	
ldGcK1	Wxxmisccharges3	['USD', 'chargeamount', 'misccharge', 'water']	
ldGcK1	Wxxmisccharges4	['USD', 'chargeamount', 'misccharge', 'water']	
ldGcK1	Wxxmisccharges5	['USD', 'chargeamount', 'misccharge', 'water']	
ldGcK1	Wxxmisccharges6	['USD', 'chargeamount', 'misccharge', 'water']	
ldGcK1	Wxxmisccharges7	['USD', 'chargeamount', 'misccharge', 'water']	
ldGcK1	Wxxtaxcharges1	['USD', 'chargeamount', 'taxcharge', 'water']	
ldGcK1	Wxxtaxcharges2	['USD', 'chargeamount', 'taxcharge', 'water']	
ldGcK1	Wxxtaxcharges3	['USD', 'chargeamount', 'taxcharge', 'water']	
ldGcK1	Wxxtaxcharges4	['USD', 'chargeamount', 'taxcharge', 'water']	
ldGcK1	Wxxtaxcharges5	['USD', 'chargeamount', 'taxcharge', 'water']	
ldGcK1	Wxxtaxcharges6	['USD', 'chargeamount', 'taxcharge', 'water']	
ldGcK1	Wxxtaxcharges7	['USD', 'chargeamount', 'taxcharge', 'water']	
ldGcK1	res_W_xxUsageCharge	['chargeamount', 'USD', 'usagecharge', 'water']	
ldGcK1	res_W_xx1UsageCharge	['chargeamount', 'USD', 'usagecharge', 'water']	
ldGcK1	res_W_xxMiscCharge	['chargeamount', 'USD', 'misccharge', 'water']	
ldGcK1	res_W_xx1MiscCharge	['chargeamount', 'USD', 'misccharge', 'water']	
ldGcK1	res_W_xxTaxCharge	['chargeamount', 'USD', 'taxcharge', 'water']	
ldGcK1	res_W_xx1TaxCharge	['chargeamount', 'USD', 'taxcharge', 'water']	
ldGcK1	res_W_xxFixedcharge	['chargeamount', 'USD', 'fixedcharge', 'water']	
ldGcK1	res_W_xxDemandCharge	['chargeamount', 'USD', 'demandcharge', 'water']	
ldGcK1	res_LateFee	['chargeamount', 'USD', 'latefeecharge']	
ldGcK1	SewerUsageCharge2	['chargeamount', 'USD', 'usagecharge', 'sewer', 'unmetered1']	
ldGcK1	SewerFixedCharge1	['chargeamount', 'USD', 'sewer', 'unmetered1', 'fixedcharge']	
ldGcK1	SewerUsageCharge3	['chargeamount', 'USD', 'usagecharge', 'sewer', 'unmetered1']	
ldGcK1	S_MiscCharge	['chargeamount', 'USD', 'sewer', 'unmetered1', 'misccharge']	
ldGcK1	S_MiscCharge2	['chargeamount', 'USD', 'usagecharge', 'sewer', 'unmetered1']	
ldGcK1	S_MiscCharge3	['chargeamount', 'USD', 'usagecharge', 'sewer', 'unmetered1']	
ldGcK1	S_MiscCharge4	['chargeamount', 'USD', 'usagecharge', 'sewer', 'unmetered1']	
ldGcK1	Non-Com_xxMiscCharge	['chargeamount', 'USD', 'misccharge']	
ldGcK1	Non-Com_xx1MiscCharge	['chargeamount', 'USD', 'misccharge']	
ldGcK1	Non-Com_xx2MiscCharge	['chargeamount', 'USD', 'misccharge']	
ldGcK1	Non-Com_xx3MiscCharge	['chargeamount', 'USD', 'misccharge']	
ldGcK1	Non-Com_xx4MiscCharge	['chargeamount', 'USD', 'misccharge']	
ldGcK1	Non-Com_xx5MiscCharge	['chargeamount', 'USD', 'misccharge']	
ldGcK1	Non-Com_xx6MiscCharge	['chargeamount', 'USD', 'misccharge']	
T6sEs1	VendorName	['vendorname']	ALDERWOOD WATER & WASTEWATER
T6sEs1	AccountNumber	['accountnumber']	********-00
T6sEs1	CustomerName	['customername']	NORTHSHORE SCHOOL DIST. 417
T6sEs1	CustomerNumber	['customernumber']	
T6sEs1	StatementDate	['statementdate']	2025-01-09
T6sEs1	StatementNumber	['statementnumber']	
T6sEs1	BillStartDate	['billstartdate']	
T6sEs1	BillEndDate	['billenddate']	
T6sEs1	CurrentCharges	[]	291.09
T6sEs1	TotalAmountDue	['totalamountdue', 'USD']	291.09
T6sEs1	TotalUsage	[]	
T6sEs1	LateFee	['USD', 'latefeecharge', 'chargeamount']	
T6sEs1	W_M1_BilledUsage	['billedusage', 'meter1', 'water']	
T6sEs1	W_M1_BilledUsageUnits	['meter1', 'billedusageuom', 'water']	
T6sEs1	W_M1_MeterNumber	['meter1', 'meternumber', 'water']	**********
T6sEs1	W_M1_MeterStartDate	['meter1', 'meterstartdate', 'water']	2024-10-29
T6sEs1	W_M1_MeterEndDate	['meter1', 'meterenddate', 'water']	2024-12-27
T6sEs1	W_M1_MeterUsage	['meter1', 'usage', 'water']	15
T6sEs1	W_Usage2	['meter1', 'usage', 'water']	
T6sEs1	W_M1_MeterUsageUnits	['meter1', 'usageuom', 'water']	CCF
T6sEs1	ServiceAddressL1	['servicestreet']	303 224TH ST SW MTR2
T6sEs1	ServiceAddressL2	['servicecity']	
T6sEs1	ServiceAddressL3	['servicestate']	
T6sEs1	ServiceAddressL4	['servicezip']	
T6sEs1	W_M1_PODNumber	['meter1', 'pod', 'water']	
T6sEs1	W_M1_Tariff	['meter1', 'tariff', 'water']	
T6sEs1	W_ThirdPartyProviderName_1	['addvendor1', 'water']	
T6sEs1	W_ThirdPartyProviderAccountNumber_1	['addvendor1accountnumber', 'water']	
T6sEs1	W_ThirdPartyProviderName_2	['addvendor2', 'water']	
T6sEs1	W_ThirdPartyProviderAccountNumber_2	['addvendor2accountnumber', 'water']	
T6sEs1	W_TotalSupplyCharge	['USD', 'totalsupplycharge', 'water']	
T6sEs1	W_TotalDeliveryCharge	['USD', 'totaldeliverycharge', 'water']	
T6sEs1	WatBaseChrg	['USD', 'chargeamount', 'fixedcharge', 'water']	85.56
T6sEs1	WatBaseTier	['USD', 'chargeamount', 'usagecharge', 'water']	0.00
T6sEs1	WaterTier1	['USD', 'chargeamount', 'usagecharge', 'water']	15.05
T6sEs1	WaterTier2	['USD', 'chargeamount', 'usagecharge', 'water']	
T6sEs1	WaterTier3	['USD', 'chargeamount', 'usagecharge', 'water']	
T6sEs1	WBotFraFee	['USD', 'chargeamount', 'misccharge', 'water']	6.04
T6sEs1	SewerCharges	['chargeamount', 'USD', 'sewer', 'unmetered1', 'usagecharge']	174.00
T6sEs1	SBotFraFee	['USD', 'chargeamount', 'misccharge', 'sewer']	10.44
T6sEs1	Wxxdemandcharges1	['USD', 'chargeamount', 'demandcharge', 'water']	
T6sEs1	WMiscCharge1	['USD', 'chargeamount', 'misccharge', 'water']	
T6sEs1	Wxxdemandcharges2	['USD', 'chargeamount', 'demandcharge', 'water']	
T6sEs1	Wxxfixedcharges2	['USD', 'chargeamount', 'fixedcharge', 'water']	
T6sEs1	Wxxusagecharges5	['USD', 'chargeamount', 'usagecharge', 'water']	
T6sEs1	WMiscCharge2	['USD', 'chargeamount', 'misccharge', 'water']	
T6sEs1	Wxxmisccharges3	['USD', 'chargeamount', 'misccharge', 'water']	
T6sEs1	Wxxmisccharges4	['USD', 'chargeamount', 'misccharge', 'water']	
T6sEs1	Wxxmisccharges5	['USD', 'chargeamount', 'misccharge', 'water']	
T6sEs1	Wxxmisccharges6	['USD', 'chargeamount', 'misccharge', 'water']	
T6sEs1	Wxxmisccharges7	['USD', 'chargeamount', 'misccharge', 'water']	
T6sEs1	Wxxtaxcharges1	['USD', 'chargeamount', 'taxcharge', 'water']	
T6sEs1	Wxxtaxcharges2	['USD', 'chargeamount', 'taxcharge', 'water']	
T6sEs1	Wxxtaxcharges3	['USD', 'chargeamount', 'taxcharge', 'water']	
T6sEs1	Wxxtaxcharges4	['USD', 'chargeamount', 'taxcharge', 'water']	
T6sEs1	Wxxtaxcharges5	['USD', 'chargeamount', 'taxcharge', 'water']	
T6sEs1	Wxxtaxcharges6	['USD', 'chargeamount', 'taxcharge', 'water']	
T6sEs1	Wxxtaxcharges7	['USD', 'chargeamount', 'taxcharge', 'water']	
T6sEs1	res_W_xxUsageCharge	['chargeamount', 'USD', 'usagecharge', 'water']	
T6sEs1	res_W_xx1UsageCharge	['chargeamount', 'USD', 'usagecharge', 'water']	
T6sEs1	res_W_xxMiscCharge	['chargeamount', 'USD', 'misccharge', 'water']	
T6sEs1	res_W_xx1MiscCharge	['chargeamount', 'USD', 'misccharge', 'water']	
T6sEs1	res_W_xxTaxCharge	['chargeamount', 'USD', 'taxcharge', 'water']	
T6sEs1	res_W_xx1TaxCharge	['chargeamount', 'USD', 'taxcharge', 'water']	
T6sEs1	res_W_xxFixedcharge	['chargeamount', 'USD', 'fixedcharge', 'water']	
T6sEs1	res_W_xxDemandCharge	['chargeamount', 'USD', 'demandcharge', 'water']	
T6sEs1	res_LateFee	['chargeamount', 'USD', 'latefeecharge']	
T6sEs1	SewerUsageCharge2	['chargeamount', 'USD', 'usagecharge', 'sewer', 'unmetered1']	
T6sEs1	SewerFixedCharge1	['chargeamount', 'USD', 'sewer', 'unmetered1', 'fixedcharge']	
T6sEs1	SewerUsageCharge3	['chargeamount', 'USD', 'usagecharge', 'sewer', 'unmetered1']	
T6sEs1	S_MiscCharge	['chargeamount', 'USD', 'sewer', 'unmetered1', 'misccharge']	
T6sEs1	S_MiscCharge2	['chargeamount', 'USD', 'usagecharge', 'sewer', 'unmetered1']	
T6sEs1	S_MiscCharge3	['chargeamount', 'USD', 'usagecharge', 'sewer', 'unmetered1']	
T6sEs1	S_MiscCharge4	['chargeamount', 'USD', 'usagecharge', 'sewer', 'unmetered1']	
T6sEs1	Non-Com_xxMiscCharge	['chargeamount', 'USD', 'misccharge']	
T6sEs1	Non-Com_xx1MiscCharge	['chargeamount', 'USD', 'misccharge']	
T6sEs1	Non-Com_xx2MiscCharge	['chargeamount', 'USD', 'misccharge']	
T6sEs1	Non-Com_xx3MiscCharge	['chargeamount', 'USD', 'misccharge']	
T6sEs1	Non-Com_xx4MiscCharge	['chargeamount', 'USD', 'misccharge']	
T6sEs1	Non-Com_xx5MiscCharge	['chargeamount', 'USD', 'misccharge']	
T6sEs1	Non-Com_xx6MiscCharge	['chargeamount', 'USD', 'misccharge']	
mBYmC1	VendorName	['vendorname']	ALDERWOOD WATER & WASTEWATER
mBYmC1	AccountNumber	['accountnumber']	********-01
mBYmC1	CustomerName	['customername']	NORTHSHORE SCHOOL DIST 417
mBYmC1	CustomerNumber	['customernumber']	
mBYmC1	StatementDate	['statementdate']	2025-01-09
mBYmC1	StatementNumber	['statementnumber']	
mBYmC1	BillStartDate	['billstartdate']	
mBYmC1	BillEndDate	['billenddate']	
mBYmC1	CurrentCharges	[]	19.02
mBYmC1	TotalAmountDue	['totalamountdue', 'USD']	19.02
mBYmC1	TotalUsage	[]	
mBYmC1	LateFee	['USD', 'latefeecharge', 'chargeamount']	
mBYmC1	W_M1_BilledUsage	['billedusage', 'meter1', 'water']	
mBYmC1	W_M1_BilledUsageUnits	['meter1', 'billedusageuom', 'water']	
mBYmC1	W_M1_MeterNumber	['meter1', 'meternumber', 'water']	**********
mBYmC1	W_M1_MeterStartDate	['meter1', 'meterstartdate', 'water']	2024-10-29
mBYmC1	W_M1_MeterEndDate	['meter1', 'meterenddate', 'water']	2024-12-27
mBYmC1	W_M1_MeterUsage	['meter1', 'usage', 'water']	0
mBYmC1	W_Usage2	['meter1', 'usage', 'water']	
mBYmC1	W_M1_MeterUsageUnits	['meter1', 'usageuom', 'water']	CCF
mBYmC1	ServiceAddressL1	['servicestreet']	303 224TH ST SW D/C 2
mBYmC1	ServiceAddressL2	['servicecity']	
mBYmC1	ServiceAddressL3	['servicestate']	
mBYmC1	ServiceAddressL4	['servicezip']	
mBYmC1	W_M1_PODNumber	['meter1', 'pod', 'water']	
mBYmC1	W_M1_Tariff	['meter1', 'tariff', 'water']	
mBYmC1	W_ThirdPartyProviderName_1	['addvendor1', 'water']	
mBYmC1	W_ThirdPartyProviderAccountNumber_1	['addvendor1accountnumber', 'water']	
mBYmC1	W_ThirdPartyProviderName_2	['addvendor2', 'water']	
mBYmC1	W_ThirdPartyProviderAccountNumber_2	['addvendor2accountnumber', 'water']	
mBYmC1	W_TotalSupplyCharge	['USD', 'totalsupplycharge', 'water']	
mBYmC1	W_TotalDeliveryCharge	['USD', 'totaldeliverycharge', 'water']	
mBYmC1	WatBaseChrg	['USD', 'chargeamount', 'fixedcharge', 'water']	17.94
mBYmC1	WatBaseTier	['USD', 'chargeamount', 'usagecharge', 'water']	0.00
mBYmC1	WaterTier1	['USD', 'chargeamount', 'usagecharge', 'water']	
mBYmC1	WaterTier2	['USD', 'chargeamount', 'usagecharge', 'water']	
mBYmC1	WaterTier3	['USD', 'chargeamount', 'usagecharge', 'water']	
mBYmC1	WBotFraFee	['USD', 'chargeamount', 'misccharge', 'water']	1.08
mBYmC1	SewerCharges	['chargeamount', 'USD', 'sewer', 'unmetered1', 'usagecharge']	
mBYmC1	SBotFraFee	['USD', 'chargeamount', 'misccharge', 'sewer']	
mBYmC1	Wxxdemandcharges1	['USD', 'chargeamount', 'demandcharge', 'water']	
mBYmC1	WMiscCharge1	['USD', 'chargeamount', 'misccharge', 'water']	
mBYmC1	Wxxdemandcharges2	['USD', 'chargeamount', 'demandcharge', 'water']	
mBYmC1	Wxxfixedcharges2	['USD', 'chargeamount', 'fixedcharge', 'water']	
mBYmC1	Wxxusagecharges5	['USD', 'chargeamount', 'usagecharge', 'water']	
mBYmC1	WMiscCharge2	['USD', 'chargeamount', 'misccharge', 'water']	
mBYmC1	Wxxmisccharges3	['USD', 'chargeamount', 'misccharge', 'water']	
mBYmC1	Wxxmisccharges4	['USD', 'chargeamount', 'misccharge', 'water']	
mBYmC1	Wxxmisccharges5	['USD', 'chargeamount', 'misccharge', 'water']	
mBYmC1	Wxxmisccharges6	['USD', 'chargeamount', 'misccharge', 'water']	
mBYmC1	Wxxmisccharges7	['USD', 'chargeamount', 'misccharge', 'water']	
mBYmC1	Wxxtaxcharges1	['USD', 'chargeamount', 'taxcharge', 'water']	
mBYmC1	Wxxtaxcharges2	['USD', 'chargeamount', 'taxcharge', 'water']	
mBYmC1	Wxxtaxcharges3	['USD', 'chargeamount', 'taxcharge', 'water']	
mBYmC1	Wxxtaxcharges4	['USD', 'chargeamount', 'taxcharge', 'water']	
mBYmC1	Wxxtaxcharges5	['USD', 'chargeamount', 'taxcharge', 'water']	
mBYmC1	Wxxtaxcharges6	['USD', 'chargeamount', 'taxcharge', 'water']	
mBYmC1	Wxxtaxcharges7	['USD', 'chargeamount', 'taxcharge', 'water']	
mBYmC1	res_W_xxUsageCharge	['chargeamount', 'USD', 'usagecharge', 'water']	
mBYmC1	res_W_xx1UsageCharge	['chargeamount', 'USD', 'usagecharge', 'water']	
mBYmC1	res_W_xxMiscCharge	['chargeamount', 'USD', 'misccharge', 'water']	
mBYmC1	res_W_xx1MiscCharge	['chargeamount', 'USD', 'misccharge', 'water']	
mBYmC1	res_W_xxTaxCharge	['chargeamount', 'USD', 'taxcharge', 'water']	
mBYmC1	res_W_xx1TaxCharge	['chargeamount', 'USD', 'taxcharge', 'water']	
mBYmC1	res_W_xxFixedcharge	['chargeamount', 'USD', 'fixedcharge', 'water']	
mBYmC1	res_W_xxDemandCharge	['chargeamount', 'USD', 'demandcharge', 'water']	
mBYmC1	res_LateFee	['chargeamount', 'USD', 'latefeecharge']	
mBYmC1	SewerUsageCharge2	['chargeamount', 'USD', 'usagecharge', 'sewer', 'unmetered1']	
mBYmC1	SewerFixedCharge1	['chargeamount', 'USD', 'sewer', 'unmetered1', 'fixedcharge']	
mBYmC1	SewerUsageCharge3	['chargeamount', 'USD', 'usagecharge', 'sewer', 'unmetered1']	
mBYmC1	S_MiscCharge	['chargeamount', 'USD', 'sewer', 'unmetered1', 'misccharge']	
mBYmC1	S_MiscCharge2	['chargeamount', 'USD', 'usagecharge', 'sewer', 'unmetered1']	
mBYmC1	S_MiscCharge3	['chargeamount', 'USD', 'usagecharge', 'sewer', 'unmetered1']	
mBYmC1	S_MiscCharge4	['chargeamount', 'USD', 'usagecharge', 'sewer', 'unmetered1']	
mBYmC1	Non-Com_xxMiscCharge	['chargeamount', 'USD', 'misccharge']	
mBYmC1	Non-Com_xx1MiscCharge	['chargeamount', 'USD', 'misccharge']	
mBYmC1	Non-Com_xx2MiscCharge	['chargeamount', 'USD', 'misccharge']	
mBYmC1	Non-Com_xx3MiscCharge	['chargeamount', 'USD', 'misccharge']	
mBYmC1	Non-Com_xx4MiscCharge	['chargeamount', 'USD', 'misccharge']	
mBYmC1	Non-Com_xx5MiscCharge	['chargeamount', 'USD', 'misccharge']	
mBYmC1	Non-Com_xx6MiscCharge	['chargeamount', 'USD', 'misccharge']	
Wfshu1	VendorName	['vendorname']	ALDERWOOD WATER & WASTEWATER
Wfshu1	AccountNumber	['accountnumber']	********-01
Wfshu1	CustomerName	['customername']	NORTHSHORE SCHOOL DIST #417
Wfshu1	CustomerNumber	['customernumber']	
Wfshu1	StatementDate	['statementdate']	2025-01-09
Wfshu1	StatementNumber	['statementnumber']	
Wfshu1	BillStartDate	['billstartdate']	
Wfshu1	BillEndDate	['billenddate']	
Wfshu1	CurrentCharges	[]	8500.08
Wfshu1	TotalAmountDue	['totalamountdue', 'USD']	8500.08
Wfshu1	TotalUsage	[]	
Wfshu1	LateFee	['USD', 'latefeecharge', 'chargeamount']	
Wfshu1	W_M1_BilledUsage	['billedusage', 'meter1', 'water']	
Wfshu1	W_M1_BilledUsageUnits	['meter1', 'billedusageuom', 'water']	
Wfshu1	W_M1_MeterNumber	['meter1', 'meternumber', 'water']	**********
Wfshu1	W_M1_MeterStartDate	['meter1', 'meterstartdate', 'water']	2024-10-30
Wfshu1	W_M1_MeterEndDate	['meter1', 'meterenddate', 'water']	2024-12-31
Wfshu1	W_M1_MeterUsage	['meter1', 'usage', 'water']	405
Wfshu1	W_Usage2	['meter1', 'usage', 'water']	126
Wfshu1	W_M1_MeterUsageUnits	['meter1', 'usageuom', 'water']	CCF
Wfshu1	ServiceAddressL1	['servicestreet']	23400 5TH AVE W MTR 2 SHELTON
Wfshu1	ServiceAddressL2	['servicecity']	
Wfshu1	ServiceAddressL3	['servicestate']	
Wfshu1	ServiceAddressL4	['servicezip']	
Wfshu1	W_M1_PODNumber	['meter1', 'pod', 'water']	
Wfshu1	W_M1_Tariff	['meter1', 'tariff', 'water']	
Wfshu1	W_ThirdPartyProviderName_1	['addvendor1', 'water']	
Wfshu1	W_ThirdPartyProviderAccountNumber_1	['addvendor1accountnumber', 'water']	
Wfshu1	W_ThirdPartyProviderName_2	['addvendor2', 'water']	
Wfshu1	W_ThirdPartyProviderAccountNumber_2	['addvendor2accountnumber', 'water']	
Wfshu1	W_TotalSupplyCharge	['USD', 'totalsupplycharge', 'water']	
Wfshu1	W_TotalDeliveryCharge	['USD', 'totaldeliverycharge', 'water']	
Wfshu1	WatBaseChrg	['USD', 'chargeamount', 'fixedcharge', 'water']	796.66
Wfshu1	WatBaseTier	['USD', 'chargeamount', 'usagecharge', 'water']	0.00
Wfshu1	WaterTier1	['USD', 'chargeamount', 'usagecharge', 'water']	752.50
Wfshu1	WaterTier2	['USD', 'chargeamount', 'usagecharge', 'water']	658.84
Wfshu1	WaterTier3	['USD', 'chargeamount', 'usagecharge', 'water']	
Wfshu1	WBotFraFee	['USD', 'chargeamount', 'misccharge', 'water']	132.48
Wfshu1	SewerCharges	['chargeamount', 'USD', 'sewer', 'unmetered1', 'usagecharge']	6159.60
Wfshu1	SBotFraFee	['USD', 'chargeamount', 'misccharge', 'sewer']	
Wfshu1	Wxxdemandcharges1	['USD', 'chargeamount', 'demandcharge', 'water']	
Wfshu1	WMiscCharge1	['USD', 'chargeamount', 'misccharge', 'water']	
Wfshu1	Wxxdemandcharges2	['USD', 'chargeamount', 'demandcharge', 'water']	
Wfshu1	Wxxfixedcharges2	['USD', 'chargeamount', 'fixedcharge', 'water']	
Wfshu1	Wxxusagecharges5	['USD', 'chargeamount', 'usagecharge', 'water']	
Wfshu1	WMiscCharge2	['USD', 'chargeamount', 'misccharge', 'water']	
Wfshu1	Wxxmisccharges3	['USD', 'chargeamount', 'misccharge', 'water']	
Wfshu1	Wxxmisccharges4	['USD', 'chargeamount', 'misccharge', 'water']	
Wfshu1	Wxxmisccharges5	['USD', 'chargeamount', 'misccharge', 'water']	
Wfshu1	Wxxmisccharges6	['USD', 'chargeamount', 'misccharge', 'water']	
Wfshu1	Wxxmisccharges7	['USD', 'chargeamount', 'misccharge', 'water']	
Wfshu1	Wxxtaxcharges1	['USD', 'chargeamount', 'taxcharge', 'water']	
Wfshu1	Wxxtaxcharges2	['USD', 'chargeamount', 'taxcharge', 'water']	
Wfshu1	Wxxtaxcharges3	['USD', 'chargeamount', 'taxcharge', 'water']	
Wfshu1	Wxxtaxcharges4	['USD', 'chargeamount', 'taxcharge', 'water']	
Wfshu1	Wxxtaxcharges5	['USD', 'chargeamount', 'taxcharge', 'water']	
Wfshu1	Wxxtaxcharges6	['USD', 'chargeamount', 'taxcharge', 'water']	
Wfshu1	Wxxtaxcharges7	['USD', 'chargeamount', 'taxcharge', 'water']	
Wfshu1	res_W_xxUsageCharge	['chargeamount', 'USD', 'usagecharge', 'water']	
Wfshu1	res_W_xx1UsageCharge	['chargeamount', 'USD', 'usagecharge', 'water']	
Wfshu1	res_W_xxMiscCharge	['chargeamount', 'USD', 'misccharge', 'water']	
Wfshu1	res_W_xx1MiscCharge	['chargeamount', 'USD', 'misccharge', 'water']	
Wfshu1	res_W_xxTaxCharge	['chargeamount', 'USD', 'taxcharge', 'water']	
Wfshu1	res_W_xx1TaxCharge	['chargeamount', 'USD', 'taxcharge', 'water']	
Wfshu1	res_W_xxFixedcharge	['chargeamount', 'USD', 'fixedcharge', 'water']	
Wfshu1	res_W_xxDemandCharge	['chargeamount', 'USD', 'demandcharge', 'water']	
Wfshu1	res_LateFee	['chargeamount', 'USD', 'latefeecharge']	
Wfshu1	SewerUsageCharge2	['chargeamount', 'USD', 'usagecharge', 'sewer', 'unmetered1']	
Wfshu1	SewerFixedCharge1	['chargeamount', 'USD', 'sewer', 'unmetered1', 'fixedcharge']	
Wfshu1	SewerUsageCharge3	['chargeamount', 'USD', 'usagecharge', 'sewer', 'unmetered1']	
Wfshu1	S_MiscCharge	['chargeamount', 'USD', 'sewer', 'unmetered1', 'misccharge']	
Wfshu1	S_MiscCharge2	['chargeamount', 'USD', 'usagecharge', 'sewer', 'unmetered1']	
Wfshu1	S_MiscCharge3	['chargeamount', 'USD', 'usagecharge', 'sewer', 'unmetered1']	
Wfshu1	S_MiscCharge4	['chargeamount', 'USD', 'usagecharge', 'sewer', 'unmetered1']	
Wfshu1	Non-Com_xxMiscCharge	['chargeamount', 'USD', 'misccharge']	
Wfshu1	Non-Com_xx1MiscCharge	['chargeamount', 'USD', 'misccharge']	
Wfshu1	Non-Com_xx2MiscCharge	['chargeamount', 'USD', 'misccharge']	
Wfshu1	Non-Com_xx3MiscCharge	['chargeamount', 'USD', 'misccharge']	
Wfshu1	Non-Com_xx4MiscCharge	['chargeamount', 'USD', 'misccharge']	
Wfshu1	Non-Com_xx5MiscCharge	['chargeamount', 'USD', 'misccharge']	
Wfshu1	Non-Com_xx6MiscCharge	['chargeamount', 'USD', 'misccharge']	