Training Set Id,Training Set Label,Document Id,Document Label,Field Label,Field Tags,Field Glynt Tags,Returned Value,Review URL
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,SAPCoCode,[],"['string', 'assign:SAPCoCode']",,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,Vendor<PERSON>ip,[],"['string', 'assign:VendorZip']",71646-0431,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,<PERSON><PERSON><PERSON>_G_420,OZ6o82,<PERSON><PERSON><PERSON><PERSON> ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,VendorName,[],['vendorname'],AVISTA,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,AccountNumber,[],['accountnumber'],123456789123456789,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,CustomerName,[],['customername'],Gas Transmission Northwest Corp,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,CustomerNumber,[],['customernumber'],,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,StatementDate,[],['statementdate'],2022-07-12,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,StatementNumber,[],['statementnumber'],,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,BillStartDate,[],['billstartdate'],,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,BillEndDate,[],['billenddate'],,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,CurrentCharges,[],"['currentcharges', 'USD']",,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,TotalAmountDue,[],"['USD', 'totalamountdue']",521.75,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,CreditBalance,[],['assign:CreditBalance'],0.00,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,G_M1_BilledUsage,[],"['meter1', 'gas', 'billedusage']",,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,G_M1_BilledUsageUnits,[],"['meter1', 'gas', 'billedusageuom']",,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,G_M1_BilledDemandUsage,[],"['meter1', 'gas', 'billeddemandusage']",,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,G_M1_BilledDemandUsageUnits,[],"['billeddemanduom', 'meter1', 'gas']",,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,G_M1_MeterNumber,[],"['meter1', 'gas', 'meternumber']",06449241,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,G_M1_MeterStartDate,[],"['meterstartdate', 'meter1', 'gas']",2022-06-09,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,G_M1_MeterEndDate,[],"['meter1', 'gas', 'meterenddate']",2022-07-11,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,G_M1_MeterUsage,[],"['usage', 'meter1', 'gas']",442.196,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,G_M1_MeterUsageUnits,[],"['meter1', 'gas', 'usageuom']",,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,G_M1_MeterDemandUsage,[],"['demandusage', 'meter1', 'gas']",,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,G_M1_MeterDemandUsageUnits,[],"['meter1', 'gas', 'demanduom']",,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,M1_ServiceAddressL1,[],"['meter1', 'servicestreet']","Gas Transmission Northwest Corp, 7235 KELLER RD",https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,M1_ServiceAddressL2,[],"['servicecity', 'meter1']",KLAMATH FALLS OR 97603,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,M1_ServiceAddressL3,[],"['meter1', 'servicestate']",,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,M1_ServiceAddressL4,[],"['meter1', 'servicezip']",,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,G_M1_PODNumber,[],"['meter1', 'pod', 'gas']",,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,G_M1_Tariff,[],"['meter1', 'gas', 'tariff']",420,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,G_ThirdPartyProviderName_1,[],"['gas', 'addvendor1']",,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,G_ThirdPartyProviderAccountNumber_1,[],"['addvendor1accountnumber', 'gas']",,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,G_ThirdPartyProviderName_2,[],"['gas', 'addvendor2']",,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,G_ThirdPartyProviderAccountNumber_2,[],"['addvendor2accountnumber', 'gas']",,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,G_TotalSupplyCharge,[],"['USD', 'gas', 'totalsupplycharge']",,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,G_TotalDeliveryCharge,[],"['USD', 'gas', 'totaldeliverycharge']",,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,LatePayCha,[],"['latefeecharge', 'USD', 'chargeamount']",521.75,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,BasCha_1,[],"['USD', 'gas', 'chargeamount', 'misccharge']",17.00,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,EneUsa_1,[],"['usagecharge', 'USD', 'gas', 'chargeamount']",504.75,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,BasCha_2,[],"['USD', 'gas', 'chargeamount', 'misccharge']",,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,EneUsa_2,[],"['usagecharge', 'USD', 'gas', 'chargeamount']",,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,Gxxdemandcharges1,[],"['USD', 'gas', 'chargeamount', 'demandcharge']",,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,Gxxdemandcharges2,[],"['USD', 'gas', 'chargeamount', 'demandcharge']",,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,Gxxfixedcharges1,[],"['USD', 'gas', 'chargeamount', 'fixedcharge']",,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,Gxxfixedcharges2,[],"['USD', 'gas', 'chargeamount', 'fixedcharge']",,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,Gxxusagecharges3,[],"['usagecharge', 'USD', 'gas', 'chargeamount']",,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,Gxxusagecharges4,[],"['usagecharge', 'USD', 'gas', 'chargeamount']",,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,Gxxusagecharges5,[],"['usagecharge', 'USD', 'gas', 'chargeamount']",,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,Gxxmisccharges3,[],"['USD', 'gas', 'chargeamount', 'misccharge']",,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,Gxxmisccharges4,[],"['USD', 'gas', 'chargeamount', 'misccharge']",,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,Gxxmisccharges5,[],"['USD', 'gas', 'chargeamount', 'misccharge']",,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,Gxxmisccharges6,[],"['USD', 'gas', 'chargeamount', 'misccharge']",,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,Gxxmisccharges7,[],"['USD', 'gas', 'chargeamount', 'misccharge']",,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,Gxxtaxcharges1,[],"['USD', 'gas', 'taxcharge', 'chargeamount']",,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,Gxxtaxcharges2,[],"['USD', 'gas', 'taxcharge', 'chargeamount']",,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,Gxxtaxcharges3,[],"['USD', 'gas', 'taxcharge', 'chargeamount']",,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,Gxxtaxcharges4,[],"['USD', 'gas', 'taxcharge', 'chargeamount']",,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,Gxxtaxcharges5,[],"['USD', 'gas', 'taxcharge', 'chargeamount']",,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,Gxxtaxcharges6,[],"['USD', 'gas', 'taxcharge', 'chargeamount']",,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,Gxxtaxcharges7,[],"['USD', 'gas', 'taxcharge', 'chargeamount']",,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,res_G_xxUsageCharge,[],"['usagecharge', 'USD', 'gas', 'chargeamount']",,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,res_G_xx1UsageCharge,[],"['usagecharge', 'USD', 'gas', 'chargeamount']",,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,res_G_xxMiscCharge,[],"['USD', 'gas', 'chargeamount', 'misccharge']",,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,res_G_xx1MiscCharge,[],"['USD', 'gas', 'chargeamount', 'misccharge']",,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,res_G_xxTaxCharge,[],"['USD', 'gas', 'taxcharge', 'chargeamount']",,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,res_G_xx1TaxCharge,[],"['USD', 'gas', 'taxcharge', 'chargeamount']",,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,res_G_xxFixedcharge,[],"['USD', 'gas', 'chargeamount', 'fixedcharge']",,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
gLN6k1,Avista_G_420,OZ6o82,AVISTA ACCT ********** DATE 07.12.22-1f37622895528895ec64ee993840d421.pdf,res_G_xxDemandCharge,[],"['USD', 'gas', 'chargeamount', 'demandcharge']",,https://ui.glynt.ai/data-pools/8S1FX1/review/9UHtP1/extraction/OKZtm1
