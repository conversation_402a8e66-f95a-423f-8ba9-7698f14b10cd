"Training Set Id","Training Set Label","Document Id","Document Label","Field Label","Field Tags","Field Glynt Tags","Returned Value","Review URL"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","VendorName","[]","['string', 'vendorname']","nevergy","https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","AccountNumber","[]","['accountnumber', 'string']","1234567","https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","CustomerName","[]","['string', 'customername']","BOGUSINC-BANGKOK LLC","https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","CustomerNumber","[]","['customernumber', 'string']",,"https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","StatementDate","[]","['statementdate', 'date']","2023-10-11","https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","StatementNumber","[]","['statementnumber', 'string']",,"https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","BillStartDate","[]","['billstartdate', 'date']",,"https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","BillEndDate","[]","['billenddate', 'date']",,"https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","CurrentCharges","[]","['currentcharges', 'USD', 'amount']","9919.40","https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","TotalAmountDue","[]","['USD', 'totalamountdue', 'amount']","10002.69","https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","E_BilledUsage","[]","['meter1', 'electric', 'billedusage', 'number']",,"https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","E_BilledUsageUnits","[]","['meter1', 'electric', 'billedusageuom', 'string']",,"https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","E_BilledDemandUsage","[]","['meter1', 'electric', 'billeddemandusage', 'number']","21.5208","https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","E_BilledDemandUsageUnits","[]","['billeddemanduom', 'meter1', 'electric', 'string']","kW","https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","E_MeterNumber","[]","['meter1', 'electric', 'meternumber', 'string']","40415266","https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","E_MeterStartDate","[]","['meterstartdate', 'meter1', 'electric', 'date']","2023-09-12","https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","E_MeterEndDate","[]","['meter1', 'electric', 'meterenddate', 'date']","2023-10-11","https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","E_MeterUsage","[]","['usage', 'meter1', 'electric', 'number']","13581.71","https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","E_MeterUsageUnits","[]","['meter1', 'electric', 'string', 'usageuom']","kWh","https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","E_MeterDemandUsage","[]","['demandusage', 'meter1', 'electric', 'number']","21.5208","https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","E_MeterDemandUsageUnits","[]","['meter1', 'electric', 'string', 'demanduom']","kW","https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","ServiceStreet","[]","['meter1', 'string', 'servicestreet']","55 Soy 7","https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","ServiceCity","[]","['servicecity', 'meter1', 'string']","Pattaya, TH 123456","https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","ServiceState","[]","['meter1', 'string', 'servicestate']",,"https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","E_PODNumber","[]","['meter1', 'pod', 'electric', 'string']","1223931000","https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","ServiceZip","[]","['meter1', 'servicezip', 'string']",,"https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","E_Tariff","[]","['meter1', 'tariff', 'electric', 'string']","WSSGS","https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","E_AdditionalVendor1","[]","['electric', 'string', 'addvendor1']",,"https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","E_AdditionalVendor1AccountNumber","[]","['addvendor1accountnumber', 'electric', 'string']",,"https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","E_AdditionalVendor2","[]","['addvendor2', 'electric', 'string']",,"https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","E_AdditionalVendor2AccountNumber","[]","['addvendor2accountnumber', 'electric', 'string']",,"https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","E_TotalSupplyCharge","[]","['USD', 'electric', 'amount', 'totalsupplycharge']",,"https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","E_TotalDeliveryCharge","[]","['USD', 'totaldeliverycharge', 'electric', 'amount']",,"https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","E_TotalCommodityCost","[]","['USD', 'totalcommoditycost', 'electric', 'amount']",,"https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","E_BilledUsage2","[]","['meter2', 'electric', 'billedusage', 'number']",,"https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","E_BilledUsageUnits2","[]","['meter2', 'electric', 'billedusageuom', 'string']",,"https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","E_BilledDemandUsage2","[]","['meter2', 'billeddemandusage', 'electric', 'number']","143.616","https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","E_BilledDemandUsageUnits2","[]","['meter2', 'electric', 'billeddemanduom', 'string']","kW","https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","E_MeterNumber2","[]","['meter2', 'electric', 'meternumber', 'string']","40438714","https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","E_MeterStartDate2","[]","['meterstartdate', 'meter2', 'electric', 'date']","2023-09-12","https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","E_MeterEndDate2","[]","['meterenddate', 'meter2', 'electric', 'date']","2023-10-11","https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","E_MeterUsage2","[]","['usage', 'meter2', 'electric', 'number']","84617.4","https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","E_MeterUsageUnits2","[]","['meter2', 'electric', 'string', 'usageuom']","kWh","https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","E_MeterDemandUsage2","[]","['demandusage', 'meter2', 'electric', 'number']","143.616","https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","E_MeterDemandUsageUnits2","[]","['meter2', 'electric', 'string', 'demanduom']","kW","https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","ServiceStreet2","[]","['meter2', 'string', 'servicestreet']","56 Soy 7","https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","ServiceCity2","[]","['servicecity', 'meter2', 'string']","Pattaya, TH 78901","https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","ServiceState2","[]","['servicestate', 'meter2', 'string']",,"https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","ServiceZip2","[]","['servicezip', 'meter2', 'string']",,"https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","E_PODNumber2","[]","['pod', 'meter2', 'electric', 'string']","5758251000","https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","E_Tariff2","[]","['meter2', 'tariff', 'electric', 'string']","WSSGS","https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","E_BilledUsageUnits3","[]","['meter3', 'electric', 'billedusageuom', 'string']",,"https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","E_BilledUsage3","[]","['meter3', 'electric', 'billedusage', 'number']",,"https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","E_BilledDemandUsage3","[]","['meter3', 'electric', 'billeddemandusage', 'number']",,"https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","E_BilledDemandUsageUnits3","[]","['billeddemanduom', 'meter3', 'electric', 'string']",,"https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","E_MeterNumber3","[]","['meter3', 'electric', 'meternumber', 'string']","40136955","https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","E_MeterStartDate3","[]","['meterstartdate', 'meter3', 'electric', 'date']","2023-09-12","https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","E_MeterEndDate3","[]","['meterenddate', 'meter3', 'electric', 'date']","2023-10-11","https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","E_MeterUsage3","[]","['usage', 'meter3', 'electric', 'number']",,"https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","E_MeterUsageUnits3","[]","['meter3', 'electric', 'string', 'usageuom']",,"https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","E_MeterDemandUsage3","[]","['demandusage', 'meter3', 'electric', 'number']",,"https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","E_MeterDemandUsageUnits3","[]","['meter3', 'electric', 'string', 'demanduom']",,"https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","ServiceStreet3","[]","['meter3', 'string', 'servicestreet']","57 Soy 7","https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","ServiceCity3","[]","['servicecity', 'meter3', 'string']","Pattaya, TH 23456","https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","ServiceState3","[]","['servicestate', 'meter3', 'string']",,"https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","ServiceZip3","[]","['servicezip', 'meter3', 'string']",,"https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","E_PODNumber3","[]","['pod', 'meter3', 'electric', 'string']","8894151000","https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","E_Tariff3","[]","['tariff', 'meter3', 'electric', 'string']","WSSGS","https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","ChargeAmount","[]","['USD', 'chargeamount', 'electric', 'amount']",,"https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","BasSerFee_1","[]","['USD', 'chargeamount', 'electric', 'fixedcharge']","22.73","https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","BasSerFee_2","[]","['USD', 'chargeamount', 'electric', 'fixedcharge']","22.73","https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","BasSerFee_3","[]","['USD', 'chargeamount', 'electric', 'fixedcharge']","22.73","https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","EneUse_1","[]","['USD', 'chargeamount', 'electric', 'misccharge']","668.29","https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","EneUse_2","[]","['USD', 'chargeamount', 'electric', 'misccharge']","4051.79","https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","Dem_1","[]","['USD', 'chargeamount', 'demandcharge', 'electric']","117.89","https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","Dem_2","[]","['USD', 'chargeamount', 'demandcharge', 'electric']","989.15","https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","FueUseIn_1","[]","['USD', 'chargeamount', 'electric', 'misccharge']","311.29","https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","FueUseIn_2","[]","['USD', 'chargeamount', 'electric', 'misccharge']","1939.43","https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","ProTaxSur_1","[]","['USD', 'chargeamount', 'electric', 'taxcharge']","22.95","https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","ProTaxSur_2","[]","['USD', 'chargeamount', 'electric', 'taxcharge']","143.00","https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","Tra_1","[]","['USD', 'chargeamount', 'electric', 'misccharge']","219.45","https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","Tra_2","[]","['USD', 'chargeamount', 'electric', 'misccharge']","1367.25","https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","EneEff_1","[]","['USD', 'chargeamount', 'electric', 'misccharge']","2.87","https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","EneEff_2","[]","['USD', 'chargeamount', 'electric', 'misccharge']","17.85","https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","CitFraFee","[]","['USD', 'chargeamount', 'electric', 'misccharge']","83.29","https://ui.glynt.ai/data-pools/cfgFV1/review/9zUGD1/extraction/PfvQh1"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","PreviousBalance","[]","['amount', 'assign']","10151.92","https://ui.glynt.ai/data-pools/0Clzo1/review/ik4Ud1/extraction/yQVe72"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","PaymentReceived1","[]","['amount', 'assign']","-10151.92","https://ui.glynt.ai/data-pools/0Clzo1/review/ik4Ud1/extraction/yQVe72"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","PaymentReceived2","[]","['amount', 'assign']",,"https://ui.glynt.ai/data-pools/0Clzo1/review/ik4Ud1/extraction/yQVe72"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","BalanceForward","[]","['amount', 'assign']","0.00","https://ui.glynt.ai/data-pools/0Clzo1/review/ik4Ud1/extraction/yQVe72"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","StatementDueDate","[]","['date', 'assign']","2023-10-27","https://ui.glynt.ai/data-pools/0Clzo1/review/ik4Ud1/extraction/yQVe72"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","OtherCharges","[]","['amount', 'assign']",,"https://ui.glynt.ai/data-pools/0Clzo1/review/ik4Ud1/extraction/yQVe72"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","Adjustments","[]","['amount', 'assign']",,"https://ui.glynt.ai/data-pools/0Clzo1/review/ik4Ud1/extraction/yQVe72"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","Credits","[]","['amount', 'assign']",,"https://ui.glynt.ai/data-pools/0Clzo1/review/ik4Ud1/extraction/yQVe72"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","RemitStreet","[]","['string', 'assign']","PO BOX 12345","https://ui.glynt.ai/data-pools/0Clzo1/review/ik4Ud1/extraction/yQVe72"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","RemitCity","[]","['string', 'assign']","JOMTIEN CITY","https://ui.glynt.ai/data-pools/0Clzo1/review/ik4Ud1/extraction/yQVe72"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","RemitState","[]","['string', 'assign']","TH","https://ui.glynt.ai/data-pools/0Clzo1/review/ik4Ud1/extraction/yQVe72"
"x5YY22","Evergy_E_3M","hKat32","hKat32_label","RemitZIP","[]","['string', 'assign']","1234-5678","https://ui.glynt.ai/data-pools/0Clzo1/review/ik4Ud1/extraction/yQVe72"
