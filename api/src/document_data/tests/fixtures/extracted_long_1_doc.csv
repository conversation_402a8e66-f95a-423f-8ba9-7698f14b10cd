Document Id	Field Label	Field Glynt Tags	Returned Value
bPyCR1	VendorName	['string', 'vendorname', 'supply-delivery']	INDIANA MICHIGAN POWER
bPyCR1	AccountNumber	['string', 'accountnumber']	#042-248-192-1-1
bPyCR1	CustomerName	['string', 'customername']	UMH PROPERTIES LLC
bPyCR1	CustomerNumber	['string', 'customernumber']	
bPyCR1	StatementDate	['date', 'statementdate']	2024-03-12
bPyCR1	StatementNumber	['string', 'statementnumber']	
bPyCR1	BillStartDate	['date', 'billstartdate']	2024-02-09
bPyCR1	BillEndDate	['date', 'billenddate']	2024-03-11
bPyCR1	CurrentCharges	['amount', 'USD', 'currentcharges']	1132.78
bPyCR1	TotalAmountDue	['amount', 'USD', 'totalamountdue']	1284.84
bPyCR1	E_BilledUsage	['number', 'meter1', 'billedusage', 'electric']	853
bPyCR1	E_BilledUsageUnits	['string', 'meter1', 'billedusageuom', 'electric']	kWh
bPyCR1	E_BilledDemandUsage	['number', 'meter1', 'billeddemandusage', 'electric']	3
bPyCR1	E_BilledDemandUsageUnits	['string', 'meter1', 'billeddemanduom', 'electric']	kW
bPyCR1	E_MeterNumber	['string', 'meter1', 'meternumber', 'electric']	#*********
bPyCR1	E_MeterStartDate	['date', 'meter1', 'meterstartdate', 'electric']	2024-02-08
bPyCR1	E_MeterEndDate	['date', 'meter1', 'meterenddate', 'electric']	2024-02-19
bPyCR1	E_MeterUsage	['number', 'meter1', 'usage', 'electric']	509
bPyCR1	E_MeterUsageUnits	['string', 'meter1', 'usageuom', 'electric']	kWh
bPyCR1	E_MeterDemandUsage	['number', 'meter1', 'demandusage', 'electric']	2.523
bPyCR1	E_MeterDemandUsageUnits	['string', 'meter1', 'demanduom', 'electric']	kW
bPyCR1	E_ServiceStreet	['string', 'meter1', 'servicestreet']	1350 COUNTY ROAD 3
bPyCR1	E_ServiceCity	['string', 'meter1', 'servicecity']	ELKHART, IN 46514-8475
bPyCR1	E_ServiceState	['string', 'meter1', 'servicestate']	
bPyCR1	E_ServiceZip	['string', 'meter1', 'servicezip']	
bPyCR1	E_PODNumber	['string', 'meter1', 'pod', 'electric']	
bPyCR1	E_Tariff	['string', 'meter1', 'tariff', 'electric']	215 - General Service
bPyCR1	E_AdditionalVendor1	['electric', 'string', 'addvendor1']	
bPyCR1	E_AdditionalVendor1AccountNumber	['electric', 'string', 'addvendor1accountnumber']	
bPyCR1	E_AdditionalVendor2	['electric', 'string', 'addvendor2']	
bPyCR1	E_AdditionalVendor2AccountNumber	['electric', 'string', 'addvendor2accountnumber']	
bPyCR1	E_TotalSupplyCharge	['amount', 'USD', 'totalsupplycharge', 'electric']	
bPyCR1	E_TotalDeliveryCharge	['amount', 'USD', 'totaldeliverycharge', 'electric']	
bPyCR1	E_TotalCommodityCost	['amount', 'USD', 'totalcommoditycost', 'electric']	
bPyCR1	L_BilledUsage1	['billedusage', 'number', 'lighting', 'meter2']	3225
bPyCR1	L_BilledUsageUnits1	['billedusageuom', 'string', 'lighting', 'meter2']	kWh
bPyCR1	L_BilledDemandUsage1	['billeddemandusage', 'number', 'lighting', 'meter2']	
bPyCR1	L_BilledDemandUsageUnits1	['billeddemanduom', 'string', 'lighting', 'meter2']	
bPyCR1	L_MeterNumber1	['meternumber', 'string', 'lighting', 'meter2']	
bPyCR1	L_MeterStartDate1	['meterstartdate', 'date', 'lighting', 'meter2']	
bPyCR1	L_MeterEndDate1	['meterenddate', 'date', 'lighting', 'meter2']	
bPyCR1	L_MeterUsage1	['usage', 'number', 'lighting', 'meter2']	
bPyCR1	L_MeterUsageUnits1	['string', 'usageuom', 'lighting', 'meter2']	
bPyCR1	L_MeterDemandUsage1	['demandusage', 'number', 'lighting', 'meter2']	
bPyCR1	L_MeterDemandUsageUnits1	['string', 'demanduom', 'lighting', 'meter2']	
bPyCR1	L_ServiceStreet1	['string', 'servicestreet', 'meter2']	1350 COUNTY ROAD 3
bPyCR1	L_ServiceCity1	['servicecity', 'string', 'meter2']	ELKHART, IN 46514-8475
bPyCR1	L_ServiceState1	['string', 'servicestate', 'meter2']	
bPyCR1	L_PODNumber1	['pod', 'string', 'lighting', 'meter2']	
bPyCR1	L_ServiceZip1	['servicezip', 'string', 'meter2']	
bPyCR1	L_Tariff1	['tariff', 'string', 'lighting', 'meter2']	094 - Outdoor Light
bPyCR1	L_AdditionalVendor1	['string', 'addvendor1', 'lighting']	
bPyCR1	L_AdditionalVendor1AccountNumber	['string', 'addvendor1accountnumber', 'lighting']	
bPyCR1	L_BilledUsage2	['billedusage', 'number', 'lighting', 'meter3']	
bPyCR1	L_BilledUsageUnits2	['billedusageuom', 'string', 'lighting', 'meter3']	
bPyCR1	L_BilledDemandUsage2	['billeddemandusage', 'number', 'lighting', 'meter3']	
bPyCR1	L_BilledDemandUsageUnits2	['billedusageuom', 'string', 'lighting', 'meter3']	
bPyCR1	L_MeterNumber2	['meternumber', 'string', 'lighting', 'meter3']	
bPyCR1	L_MeterStartDate2	['meterstartdate', 'date', 'lighting', 'meter3']	
bPyCR1	L_MeterEndDate2	['meterenddate', 'date', 'lighting', 'meter3']	
bPyCR1	L_MeterUsage2	['usage', 'number', 'lighting', 'meter3']	
bPyCR1	L_MeterUsageUnits2	['string', 'usageuom', 'lighting', 'meter3']	
bPyCR1	L_MeterDemandUsage2	['demandusage', 'number', 'lighting', 'meter3']	
bPyCR1	L_MeterDemandUsageUnits2	['string', 'demanduom', 'lighting', 'meter3']	
bPyCR1	L_ServiceStreet2	['string', 'servicestreet', 'meter3']	1350 COUNTY ROAD 3
bPyCR1	L_ServiceCity2	['servicecity', 'string', 'meter3']	ELKHART, IN 46514-8475
bPyCR1	L_ServiceState2	['servicestate', 'string', 'meter3']	
bPyCR1	L_ServiceZip2	['servicezip', 'string', 'meter3']	
bPyCR1	L_PODNumber2	['pod', 'string', 'lighting', 'meter3']	
bPyCR1	L_Tariff2	['tariff', 'string', 'lighting', 'meter3']	
bPyCR1	L_TotalSupplyCharge	['USD', 'amount', 'totalsupplycharge', 'lighting']	
bPyCR1	L_TotalDeliveryCharge	['USD', 'totaldeliverycharge', 'amount', 'lighting']	
bPyCR1	L_TotalCommodityCost	['USD', 'totalcommoditycost', 'amount', 'lighting']	
bPyCR1	MonSerCha	['chargeamount', 'electric', 'USD', 'fixedcharge']	24.65
bPyCR1	CurBil	['chargeamount', 'electric', 'USD', 'misccharge']	88.40
bPyCR1	FueAdj	['chargeamount', 'electric', 'USD', 'usagecharge']	-0.16
bPyCR1	OffSalMar	['chargeamount', 'electric', 'USD', 'misccharge']	21.44
bPyCR1	EneEffPro	['chargeamount', 'electric', 'USD', 'misccharge']	5.98
bPyCR1	CooNucPla	['chargeamount', 'electric', 'USD', 'misccharge']	
bPyCR1	SolPowRid	['chargeamount', 'electric', 'USD', 'misccharge']	
bPyCR1	PhaInAdj	['chargeamount', 'electric', 'USD', 'misccharge']	
bPyCR1	EnvCosRid	['chargeamount', 'electric', 'USD', 'misccharge']	
bPyCR1	ResAdeRid	['chargeamount', 'electric', 'USD', 'misccharge']	
bPyCR1	StaSalTax	['chargeamount', 'electric', 'USD', 'taxcharge']	9.95
bPyCR1	RatBil_1	['chargeamount', 'USD', 'usagecharge', 'lighting']	1040.49
bPyCR1	FueAdj_1	['chargeamount', 'USD', 'usagecharge', 'lighting']	
bPyCR1	OffSalMar_1	['chargeamount', 'USD', 'misccharge', 'lighting']	
bPyCR1	CooNucPla_1	['chargeamount', 'USD', 'misccharge', 'lighting']	
bPyCR1	SolPowRid_1	['chargeamount', 'USD', 'misccharge', 'lighting']	
bPyCR1	PhaInAdj_1	['chargeamount', 'USD', 'misccharge', 'lighting']	
bPyCR1	EnvCosRid_1	['chargeamount', 'USD', 'misccharge', 'lighting']	
bPyCR1	ResAdeRid_1	['chargeamount', 'USD', 'misccharge', 'lighting']	
bPyCR1	StaSalTax_1	['chargeamount', 'USD', 'taxcharge', 'lighting']	
bPyCR1	RatBil	['chargeamount', 'USD', 'usagecharge', 'lighting']	
bPyCR1	FueAdj_2	['chargeamount', 'USD', 'usagecharge', 'lighting']	
bPyCR1	OffSalMarSha_2	['chargeamount', 'USD', 'lighting', 'misccharge']	
bPyCR1	PhaInAdj_2	['chargeamount', 'USD', 'lighting', 'misccharge']	
bPyCR1	EnvCosRid_2	['chargeamount', 'USD', 'lighting', 'misccharge']	
bPyCR1	ResAdeRid_2	['chargeamount', 'USD', 'lighting', 'misccharge']	
bPyCR1	StaSalTAx_2	['chargeamount', 'USD', 'lighting', 'taxcharge']	
bPyCR1	E_xxUsageCharge	['chargeamount', 'USD', 'usagecharge', 'electric']	
bPyCR1	E_xx1UsageCharge	['chargeamount', 'USD', 'electric', 'usagecharge']	
bPyCR1	E_xxMiscCharge	['chargeamount', 'USD', 'electric', 'misccharge']	
bPyCR1	E_xx1MiscCharge	['chargeamount', 'USD', 'electric', 'misccharge']	
bPyCR1	E_xxTaxCharge	['chargeamount', 'USD', 'electric', 'taxcharge']	
bPyCR1	E_xx1TaxCharge	['chargeamount', 'USD', 'electric', 'taxcharge']	
bPyCR1	E_xxFixedcharge	['chargeamount', 'USD', 'fixedcharge', 'electric']	
bPyCR1	E_xxDemandCharge	['chargeamount', 'USD', 'demandcharge', 'electric']	
bPyCR1	E_LateFee	['chargeamount', 'USD', 'electric', 'latefeecharge']	
bPyCR1	L_xxUsageCharge	['chargeamount', 'USD', 'usagecharge', 'lighting']	
bPyCR1	L_xx1UsageCharge	['chargeamount', 'USD', 'usagecharge', 'lighting']	
bPyCR1	L_xxMiscCharge	['chargeamount', 'USD', 'misccharge', 'lighting']	
bPyCR1	L_xx1MiscCharge	['chargeamount', 'USD', 'misccharge', 'lighting']	
bPyCR1	L_xxTaxCharge	['chargeamount', 'USD', 'taxcharge', 'lighting']	
bPyCR1	L_xx1TaxCharge	['chargeamount', 'USD', 'taxcharge', 'lighting']	
bPyCR1	L_xxFixedcharge	['chargeamount', 'USD', 'fixedcharge', 'lighting']	
bPyCR1	L_xxDemandCharge	['chargeamount', 'USD', 'demandcharge', 'lighting']	
bPyCR1	L_LateFee	['chargeamount', 'USD', 'latefeecharge', 'lighting']	
bPyCR1	SomeField	[]	Some value
bPyCR1	SomeOtherField	['a_tag']	Some other value