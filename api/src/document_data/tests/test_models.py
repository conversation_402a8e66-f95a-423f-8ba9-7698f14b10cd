import ast
import csv
from pathlib import Path
from unittest.mock import PropertyMock, patch

from django.db import connection
from django.test import TransactionTestCase
from django.urls import reverse
from pyfakefs.fake_filesystem_unittest import TestCaseMixin

from document_data import models
from document_data.actions import execute_post_load_operators
from document_data.db_client import SqlDBClient
from document_data.extracted_data_loader import CoreMLDataLoader
from document_data.operator import OperatorResult
from document_data.operator_configs import SqlOperatorConfig
from document_data.operators import SqlOperator
from document_data.util import inject_field_ids_from_tags
from documents.tests.util import create_document
from extract.models import Extraction
from extract.tests.util import create_extraction
from extract.transformers.views.view_fll3 import QUERY as FLL3_QUERY
from extract.transformers.views.view_fll3_expanded import QUERY as FLL3_EXPANDED_QUERY
from glynt_api.util import set_query_field
from glynt_schemas.data.schema_library import SCHEMA_LIBRARY
from glynt_schemas.data.schema_types import DataSchema
from organizations.tests.util import create_data_pool, create_organization
from static_enrichment_tables.actions import StaticEnrichmentTablesManager
from static_enrichment_tables.table_configs import TABLE_CONFIGS
from training.tests.util import (
    create_baked_field, create_training_ready_training_set,
    create_training_revision
)


def dictfetchall(cursor):
    """
    Return all rows from a cursor as a dict.
    Assume the column names are unique.
    """
    columns = [col[0] for col in cursor.description]
    return [dict(zip(columns, row)) for row in cursor.fetchall()]


def execute_query(q):
    with connection.cursor() as cursor:
        cursor.execute(q)
        try:
            return dictfetchall(cursor)
        except TypeError:
            return None


class TestDocumentData(TransactionTestCase, TestCaseMixin):

    def setUp(self):
        self._mock_normalized_results = {}
        self.org, _ = create_organization()
        self.dp, _ = create_data_pool(organization=self.org)
        self.ts, _ = create_training_ready_training_set(data_pool=self.dp)

        self.ts.document_schema = 'UtilityBill'

        fields_path = reverse('v6:field-list', kwargs={
            'data_pools_obfuscated_id': self.ts.data_pool.obfuscated_id
        })
        self.fields_url = set_query_field(fields_path, 'advanced', 'true')
        self.tr, _ = create_training_revision(data_pool=self.dp, training_set=self.ts)

    def load_fixture(self, filename, delimiter='\t'):
        self.docs = []
        fixture_path = Path(__file__).parent / 'fixtures' / filename
        with open(fixture_path, 'r') as f:
            reader = csv.DictReader(f, delimiter=delimiter)
            docid = None
            doc = None
            for row in reader:
                next_docid = row["Document Id"]
                label = row["Field Label"]
                tags = ast.literal_eval(row["Field Glynt Tags"])
                value = row["Returned Value"]

                if next_docid != docid:
                    docid = next_docid
                    doc, _ = create_document(data_pool=self.dp)
                    self._mock_normalized_results[doc.id] = {}
                    self.docs.append(doc)
                    ex, _ = create_extraction(
                        data_pool=self.dp, training_revision=self.tr, document=doc
                    )

                baked_field = create_baked_field(
                    data_pool=self.dp, label=label, is_additional_field=True)
                baked_field._glynt_tags.set(*tags)

                baked_field.extractions.add(ex)

                self._mock_normalized_results[doc.id][label] = {
                    'content': value,
                    'tokens': []
                }

    @patch('extract.models.Extraction.normalized_results', new_callable=PropertyMock)
    def test_data_load(self, mock_normalized_results):
        self.load_fixture('extracted_long_1_doc.csv')
        schema: DataSchema = SCHEMA_LIBRARY.schemas[self.ts.document_schema]

        doc = self.docs[0]
        mock_normalized_results.return_value = self._mock_normalized_results[doc.id]
        ex = Extraction.objects.filter(document=doc).first()
        results = ex.normalized_results
        inject_field_ids_from_tags(schema, ex, results)
        loader = CoreMLDataLoader(schema, doc, ex, results)
        loader.load()

        stmt = models.Statement.objects.filter(document=doc).first()
        meter = models.Meter.objects.filter(document=doc).first()
        accounts = models.Account.objects.filter(document=doc)
        additional_fields = models.AdditionalField.objects.filter(document=doc)

        assert stmt.statement_date is not None
        assert meter.meter_number is not None
        assert len(accounts) == 1
        assert additional_fields.count() == 2
        assert additional_fields[0].label == 'SomeField'
        assert additional_fields[0].value == 'Some value'
        assert additional_fields[1].label == 'SomeOtherField'
        assert additional_fields[1].value == 'Some other value'
        assert additional_fields[1].metadata == 'a_tag'

        usage_ids = []
        for usage in models.Usage.objects.all():
            usage_ids.append(usage.id)
            assert usage.account_number is not None
            assert usage.meter_id is not None

        # Ensure reloading is working fine
        usage_count = models.Usage.objects.count()
        assert usage_count > 0

        # Load again
        loader.load()

        new_usage_count = models.Usage.objects.count()
        new_usage_ids = [u.id for u in models.Usage.objects.all()]

        assert new_usage_count == usage_count
        assert new_usage_ids != usage_ids

    @patch('extract.models.Extraction.normalized_results', new_callable=PropertyMock)
    def test_dynamic_configuration(self, mock_normalized_results):
        self.load_fixture('extracted_long_1_doc.csv')
        schema: DataSchema = SCHEMA_LIBRARY.schemas[self.ts.document_schema]

        doc = self.docs[0]
        mock_normalized_results.return_value = self._mock_normalized_results[doc.id]
        ex = Extraction.objects.filter(document=doc).first()
        results = ex.normalized_results
        inject_field_ids_from_tags(schema, ex, results)
        loader = CoreMLDataLoader(schema, doc, ex, results)
        loader.load()

        # Get total amount due BakedField. Set currency to another type.
        # Confirm that the currency for the schema field is correct.
        total_amount_due = ex.baked_fields.filter(label='TotalAmountDue').first()
        statement_date = ex.baked_fields.filter(label='StatementDate').first()

        glynt_tags = list(total_amount_due.glynt_tags.names() or [])
        glynt_tags = ['ILS' if tag == 'USD' else tag for tag in glynt_tags]
        total_amount_due.glynt_tags.set(*glynt_tags)
        total_amount_due.save()

        date_config = statement_date.data_type_config or {}
        date_config['order'] = 'YMD'
        statement_date._data_type_config = date_config
        statement_date.save()

        stmt = models.Statement.objects.filter(document=doc).first()

        schema_field = stmt.get_meta_field('total_amount_due').get_schema_field(ex)
        assert schema_field.datatype.config.currency == 'ILS'

        schema_field = stmt.get_meta_field('statement_date').get_schema_field(ex)
        assert schema_field.datatype.config.order == 'YMD'

    @patch('extract.models.Extraction.normalized_results', new_callable=PropertyMock)
    def test_raw_sql(self, mock_normalized_results):
        # Load the data
        self.load_fixture('extracted_long_1_doc.csv')
        schema: DataSchema = SCHEMA_LIBRARY.schemas[self.ts.document_schema]
        doc = self.docs[0]
        mock_normalized_results.return_value = self._mock_normalized_results[doc.id]

        ex = Extraction.objects.filter(document=doc).first()
        results = ex.normalized_results
        inject_field_ids_from_tags(schema, ex, results)
        loader = CoreMLDataLoader(schema, doc, ex, results)
        loader.load()

        stmt = f"SELECT * FROM {models.Charge._meta.db_table}"
        rows = self._sql_query_to_dict(stmt)
        assert rows

        sql_stmt = f"SELECT SUM(chargeamount) total_amount FROM {models.Charge._meta.db_table}"
        rows = self._sql_query_to_dict(sql_stmt)
        assert rows

    def _sql_query_to_dict(self, stmt):
        with connection.cursor() as cursor:
            cursor.execute(stmt)
            columns = [col[0] for col in cursor.description]
            rows = [dict(zip(columns, row)) for row in cursor.fetchall()]
            return rows

    def _load_static_enrichment_data(self):
        for static_table_config in TABLE_CONFIGS:
            fixture_path = Path(__file__).parent / 'fixtures' / 'enrichment' / static_table_config.s3_remote_path
            setm = StaticEnrichmentTablesManager()
            try:
                with open(fixture_path, 'r') as f:
                    reader = csv.DictReader(f)
                    setm.update_or_create_from_dict_rows(reader, static_table_config)
            except FileNotFoundError:
                continue

    @patch('extract.models.Extraction.normalized_results', new_callable=PropertyMock)
    def test_transformed_output(self, mock_normalized_results):
        fixtures = [
            (
                'transformed/revantage/PROD_SummerEnergy_Summary_4Block_2_7_25-extraction-long-report-rTB5r1.csv',
                'transformed/revantage/PROD_SummerEnergy_Summary_4Block_2_7_25-extraction-transformed-rTB5r1.csv'
            ),
            (
                'transformed/revantage/Prod_PGE_E_B19_A_042525-extraction-long-report-mz2Vi1.csv',
                'transformed/revantage/Prod_PGE_E_B19_A_042525-extraction-transformed-mz2Vi1.csv',
            ),
            (
                'transformed/altura/Prod_208903_CityOfLeander_W_3M_S_012925_A-extraction-long-report-2F6qp1.csv',
                'transformed/altura/Prod_208903_CityOfLeander_W_3M_S_012925_A-extraction-transformed-2F6qp1.csv'
            ),
            (
                'transformed/gilbane/Prod_27032025_NationalGrid_G_G-43B-extraction-long-report-6UxnQ1.csv',
                'transformed/gilbane/Prod_27032025_NationalGrid_G_G-43B-extraction-transformed-6UxnQ1.csv'
            ),
            (
                'transformed/gilbane/PROD_LuckyEnergy_D_04012025-2-extraction-long-report-fHD0k1.csv',
                'transformed/gilbane/PROD_LuckyEnergy_D_04012025-2-extraction-transformed-fHD0k1.csv'
            ),
            (
                'transformed/gilbane/Prod_04012025_NationalGrid_E_G-3-extraction-long-report-BUmk72.csv',
                'transformed/gilbane/Prod_04012025_NationalGrid_E_G-3-extraction-transformed-BUmk72.csv'
            ),
            (
                'transformed/benchmark/Prod_CityOfWestChicago_W_S_A_041025-extraction-long-report-fqdkR1.csv',
                'transformed/benchmark/Prod_CityOfWestChicago_W_S_A_041025-extraction-transformed-fqdkR1.csv'
            ),
            (
                'transformed/benchmark/Prod_ComEd_E_5M_Del_A_042225-extraction-long-report-AdmpM1.csv',
                'transformed/benchmark/Prod_ComEd_E_5M_Del_A_042225-extraction-transformed-AdmpM1.csv'
            ),
            (
                'transformed/greenview/Prod_Aps_E_2M_f2_A_042225-extraction-long-report-u67l42.csv',
                'transformed/greenview/Prod_Aps_E_2M_f2_A_042225-extraction-transformed-u67l42.csv'
            ),
            (
                'transformed/greenview/Prod_SFWPS_F_S_A_042825-extraction-long-report-DhiVT1.csv',
                'transformed/greenview/Prod_SFWPS_F_S_A_042825-extraction-transformed-DhiVT1.csv'
            ),
            (
                'transformed/tc_energy/Prod_PacPower_E_3SA_24_36_36_031425-extraction-long-report-jm3Q72.csv',
                'transformed/tc_energy/Prod_PacPower_E_3SA_24_36_36_031425-extraction-transformed-jm3Q72.csv'
            ),
            (
                'transformed/wp_carey/Prod_Freepoint_E_022725_C-extraction-long-report-ylvOL1.csv',
                'transformed/wp_carey/Prod_Freepoint_E_022725_C-extraction-transformed-ylvOL1.csv'
            ),
            (
                'transformed/wp_carey/Prod_DukeEnergy_L_OL_030425_B-extraction-long-report-IfIEs1.csv',
                'transformed/wp_carey/Prod_DukeEnergy_L_OL_030425_B-extraction-transformed-IfIEs1.csv'
            ),
            (
                'transformed/disney/Prod_ConEd_G_A_041825-extraction-long-report-GDiVg1.csv',
                'transformed/disney/Prod_ConEd_G_A_041825-extraction-transformed-GDiVg1.csv',
            ),
            (
                'transformed/disney/Prod_Eversource_E_Unmetered_A_042225-extraction-long-report-FDWbZ1.csv',
                'transformed/disney/Prod_Eversource_E_Unmetered_A_042225-extraction-transformed-FDWbZ1.csv'
            ),
            (
                'transformed/edo/PROD_WoodinvilleWater_W_2M_S_3_4_25-extraction-long-report-q6msD1.csv',
                'transformed/edo/PROD_WoodinvilleWater_W_2M_S_3_4_25-extraction-transformed-q6msD1.csv'
            ),
            (
                'transformed/edo/PROD_SCE_E_TOU-GS1DE_Summary_4_22_25-extraction-long-report-4VltH1.csv',
                'transformed/edo/PROD_SCE_E_TOU-GS1DE_Summary_4_22_25-extraction-transformed-4VltH1.csv'
            ),
            (
                'transformed/true_north/Aqua_W_Nometer_04_03_25-extraction-long-report-zNQw71.csv',
                'transformed/true_north/Aqua_W_Nometer_04_03_25-extraction-transformed-zNQw71.csv'
            ),
            (
                'transformed/true_north/Eversource_G_07_04_21_24-extraction-long-report-Koari1.csv',
                'transformed/true_north/Eversource_G_07_04_21_24-extraction-transformed-Koari1.csv'
            ),
            (
                'transformed/true_north/DE_E_M2_UnknownMeter_04_22_24-extraction-long-report-S71A81.csv',
                'transformed/true_north/DE_E_M2_UnknownMeter_04_22_24-extraction-transformed-S71A81.csv'
            ),
            (
                'transformed/true_north/Duke_OPT_Test1-zd7fq7-extraction-long-report-oulOb1.csv',
                'transformed/true_north/Duke_OPT_Test1-zd7fq7-extraction-transformed-oulOb1.csv'
            ),
            (
                'transformed/true_north/hKat32.long.csv',
                'transformed/true_north/hKat32.transformed.csv'
            ),
            (
                'transformed/true_north/ComEd_E_M2_Format2_03_25_25-extraction-long-report-ajInH1.csv',
                'transformed/true_north/ComEd_E_M2_Format2_03_25_25-extraction-transformed-ajInH1.csv'
            ),
            (
                'transformed/tc_energy/avista.9UHtP.long.csv',
                'transformed/tc_energy/avista.9UHtP.transformed.csv'
            ),
        ]
        for long_input, transformed_output in fixtures:
            self.load_fixture(long_input, delimiter=',')

            schema: DataSchema = SCHEMA_LIBRARY.schemas[self.ts.document_schema]
            db_client = SqlDBClient()
            transformed_results = []
            for doc in self.docs:
                mock_normalized_results.return_value = self._mock_normalized_results[doc.id]
                ex = Extraction.objects.filter(document=doc).first()
                results = ex.normalized_results
                inject_field_ids_from_tags(schema, ex, results)
                loader = CoreMLDataLoader(schema, doc, ex, results)
                loader.load()

                result = execute_post_load_operators(doc, schema.title)
                assert result == OperatorResult.SUCCESS
                query = FLL3_QUERY.replace('{doc_id}', str(doc.id))
                results = SqlOperator(SqlOperatorConfig(name='produce_fll3', statement=query), db_client).run()
                transformed_results.extend(results.output_data)

            # with open("actual_transformed.csv", 'w') as f:
            #     import csv
            #     writer = csv.DictWriter(f, fieldnames=list(transformed_results[0].keys()))
            #     writer.writeheader()
            #     writer.writerows(transformed_results)

            # Confirm that the results match expectations
            fixture_path = Path(__file__).parent / 'fixtures' / transformed_output
            with open(fixture_path, 'r') as f:
                reader = csv.reader(f, delimiter=',')

                # Read each expected row
                for idx, expected_values in enumerate(reader):
                    if idx > 0:
                        produced_values = [v for v in transformed_results[idx - 1].values()]

                        # Compare each expected column value
                        for vidx, expected_val in enumerate(expected_values):
                            try:
                                produced_val = produced_values[vidx] if produced_values[vidx] is not None else ''
                            except IndexError:
                                raise AssertionError(f'Missing produced value for row {idx}, column {vidx+1}')

                            # SQLite doesn't enforce decimal precision
                            if isinstance(produced_val, float) or isinstance(produced_val, int):
                                produced_val = "{:.2f}".format(produced_val)
                                expected_val = "{:.2f}".format(float(expected_val)) if expected_val else ''

                            # Convert to string in case of other types (like datetime)
                            produced_val = str(produced_val)
                            expected_val = str(expected_val)

                            assert produced_val == expected_val, \
                                f'Mismatched value in row {idx}, column {vidx + 1} for {long_input}'

    @patch('extract.models.Extraction.normalized_results', new_callable=PropertyMock)
    def test_expanded_output(self, mock_normalized_results):
        fixtures = [
            (
                'transformed/revantage/Prod_PGE_E_B19_A_042525-extraction-long-report-mz2Vi1.csv',
                'transformed/revantage/Prod_PGE_E_B19_A_042525-extraction-expanded-mz2Vi1.csv',
            )
        ]
        self._load_static_enrichment_data()
        for long_input, transformed_output in fixtures:
            self.load_fixture(long_input, delimiter=',')

            schema: DataSchema = SCHEMA_LIBRARY.schemas[self.ts.document_schema]
            db_client = SqlDBClient()
            transformed_results = []
            for doc in self.docs:
                mock_normalized_results.return_value = self._mock_normalized_results[doc.id]
                ex = Extraction.objects.filter(document=doc).first()
                results = ex.normalized_results
                inject_field_ids_from_tags(schema, ex, results)
                loader = CoreMLDataLoader(schema, doc, ex, results)
                loader.load()

                result = execute_post_load_operators(doc, schema.title)
                assert result == OperatorResult.SUCCESS
                query = FLL3_EXPANDED_QUERY.replace('{doc_id}', str(doc.id))
                query = query.replace('format', 'round')  # SQLite doesn't support format()
                results = SqlOperator(SqlOperatorConfig(name='produce_fll3', statement=query), db_client).run()
                transformed_results.extend(results.output_data)

            # Confirm that the results match expectations
            fixture_path = Path(__file__).parent / 'fixtures' / transformed_output
            with open(fixture_path, 'r') as f:
                reader = csv.reader(f, delimiter=',')

                # Read each expected row
                for idx, expected_values in enumerate(reader):
                    if idx > 0:
                        produced_values = [v for v in transformed_results[idx - 1].values()]

                        # Compare each expected column value
                        for vidx, expected_val in enumerate(expected_values):
                            try:
                                produced_val = produced_values[vidx] if produced_values[vidx] is not None else ''
                            except IndexError:
                                raise AssertionError(f'Missing produced value for row {idx}, column {vidx+1}')

                            # SQLite doesn't enforce decimal precision
                            if isinstance(produced_val, float) or isinstance(produced_val, int):
                                produced_val = "{:.2f}".format(produced_val)
                                expected_val = "{:.2f}".format(float(expected_val)) if expected_val else ''

                            # Convert to string in case of other types (like datetime)
                            produced_val = str(produced_val)
                            expected_val = str(expected_val)

                            assert produced_val == expected_val, \
                                f'Mismatched value in row {idx}, column {vidx + 1} for {long_input}'
