from django.test import Client, TransactionTestCase
from django.urls import reverse
from documents.tests.util import create_document
from document_data import models as doc_data_models
from extract.tests.util import create_extraction_batch
from glynt_api.tests.util import TestCaseMixin
from glynt_api.util import set_query_field
from organizations.tests.util import create_organization, create_data_pool
from training.tests.util import create_training_ready_training_set, create_training_revision
from users.tests.util import create_staff_user


class TestDocumentDataMeterViewSet(TransactionTestCase, TestCaseMixin):
    def setUp(self):
        self.setUpPyfakefs()
        self.staff_user, self.staff_user_path = create_staff_user()

        self.staff_client = Client()
        self.staff_client.login(username=self.staff_user.username, password='12345')

        self.org, self.org_path = create_organization()
        self.dp, self.dp_path = create_data_pool(organization=self.org)
        self.doc, _ = create_document(data_pool=self.dp)
        self.doc_2, _ = create_document(data_pool=self.dp)
        self.ts, self.ts_path = create_training_ready_training_set(data_pool=self.dp)
        self.tr, self.tr_path = create_training_revision(
            data_pool=self.dp,
            training_set=self.ts
        )
        self.eb, self.eb_path = create_extraction_batch(
            data_pool=self.dp,
            documents=[self.doc],
            training_revision=self.tr
        )
        self.list_url = reverse(
            'v6:document-data-meter-list',
            kwargs={'data_pools_obfuscated_id': self.dp.obfuscated_id}
        )

    def test_list_meters(self):
        # Create test meters
        doc_data_models.Meter.objects.create(
            document=self.doc,
            meter_number="123",
        )
        doc_data_models.Meter.objects.create(
            document=self.doc,
            meter_number="456",
        )

        # Make API request
        response = self.staff_client.get(self.list_url)

        # Verify response
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()['count'], 2)

    def test_filter_meters_by_document(self):
        doc_data_models.Meter.objects.create(
            document=self.doc,
            meter_number="123",
        )
        doc_data_models.Meter.objects.create(
            document=self.doc_2,
            meter_number="123",
        )
        doc_filter_url = set_query_field(self.list_url, 'document', self.doc.obfuscated_id)
        response = self.staff_client.get(doc_filter_url)

        # Verify response
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()['count'], 1)

    def test_filter_meters_by_extraction_batch(self):
        # Create test data
        doc3, _ = create_document(data_pool=self.dp)
        eb2, _ = create_extraction_batch(
            data_pool=self.dp,
            documents=[doc3],
            training_revision=self.tr
        )

        doc_data_models.Meter.objects.create(
            document=self.doc,
            extraction=self.eb.extractions.first(),
            meter_number="123",
        )
        doc_data_models.Meter.objects.create(
            document=doc3,
            extraction=eb2.extractions.first(),
            meter_number="456",
        )

        eb_filter_url = set_query_field(self.list_url, 'extraction_batch', self.eb.obfuscated_id)
        response = self.staff_client.get(eb_filter_url)

        # Verify response
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()['count'], 1)

    def test_update_meter_mat_key(self):
        meter = doc_data_models.Meter.objects.create(
            document=self.doc,
            extraction=self.eb.extractions.first(),
            meter_number="123",
            mat_key=None
        )
        update_url = reverse(
            'v6:document-data-meter-detail',
            kwargs={
                'data_pools_obfuscated_id': self.dp.obfuscated_id,
                'pk': meter.id
            }
        )
        response = self.staff_client.patch(update_url, {'mat_key': 'ABC123'}, content_type='application/json')

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()['mat_key'], 'ABC123')
        meter.refresh_from_db()
        self.assertEqual(meter.mat_key, 'ABC123')
        self.assertEqual(response.json()['document'], meter.document.obfuscated_id)
        self.assertEqual(response.json()['extraction'], meter.extraction.obfuscated_id)
        self.assertEqual(response.json()['extraction_batch'], meter.extraction.extraction_batch.obfuscated_id)
        self.assertEqual(response.json()['data_pool'], meter.document.data_pool.obfuscated_id)
