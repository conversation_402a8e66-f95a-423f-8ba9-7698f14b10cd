from django.db import migrations
import document_data.schema_model_field


class Migration(migrations.Migration):

    dependencies = [
        ('document_data', '0005_additional_vendor_fields'),
    ]

    operations = [
        migrations.AlterField(
            model_name='charge',
            name='amount',
            field=document_data.schema_model_field.AmountSchemaModelField(max_digits=14, decimal_places=2, db_column='chargeamount', implements='UtilityBill.Charge.ChargeAmount', max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='Usage',
            name='amount',
            field=document_data.schema_model_field.NumberSchemaModelField(max_digits=14, decimal_places=4, db_column='usageamount', implements='UtilityBill.Usage.UsageAmount', max_length=255, null=True)
        ),
        migrations.AlterField(
            model_name='Usage',
            name='unified_conversion_factor',
            field=document_data.schema_model_field.NumberSchemaModelField(max_digits=14, decimal_places=4, db_column='unifiedconversionfactor', implements='UtilityBill.Usage.UnifiedConversionFactor', max_length=255, null=True)
        ),
        migrations.AlterField(
            model_name='Usage',
            name='unified_usage',
            field=document_data.schema_model_field.NumberSchemaModelField(max_digits=14, decimal_places=4, db_column='unifiedusageamount', implements='UtilityBill.Usage.UnifiedUsageAmount', max_length=255, null=True)
        ),
        migrations.AlterField(
            model_name='Usage',
            name='emissions_factor',
            field=document_data.schema_model_field.NumberSchemaModelField(max_digits=14, decimal_places=4, db_column='emissionsfactor', implements='UtilityBill.Usage.EmissionsFactor', max_length=255, null=True)
        ),
        migrations.AlterField(
            model_name='Usage',
            name='emissions_amount',
            field=document_data.schema_model_field.NumberSchemaModelField(max_digits=14, decimal_places=4, db_column='emissionsamount', implements='UtilityBill.Usage.EmissionsAmount', max_length=255, null=True)
        ),
        migrations.AlterField(
            model_name='Statement',
            name='current_charges',
            field=document_data.schema_model_field.AmountSchemaModelField(max_digits=14, decimal_places=2, db_column='currentcharges', implements='UtilityBill.Statement.CurrentCharges', max_length=255, null=True)
        ),
        migrations.AlterField(
            model_name='Statement',
            name='total_amount_due',
            field=document_data.schema_model_field.AmountSchemaModelField(max_digits=14, decimal_places=2, db_column='totalamountdue', implements='UtilityBill.Statement.TotalAmountDue', max_length=255, null=True)
        ),
        migrations.AlterField(
            model_name='Statement',
            name='calc_current_charges',
            field=document_data.schema_model_field.AmountSchemaModelField(max_digits=14, decimal_places=2, db_column='calculatedcurrentcharges', implements='UtilityBill.Statement.CalculatedCurrentCharges', max_length=255, null=True)
        )
    ]
