# Generated by Django 2.2.28 on 2025-02-19 00:44

from django.db import migrations
import document_data.schema_model_field


class Migration(migrations.Migration):

    dependencies = [
        ('document_data', '0002_update_field_types'),
    ]

    operations = [
        migrations.AddField(
            model_name='account',
            name='statement_id',
            field=document_data.schema_model_field.StringSchemaModelField(db_column='statementid', implements='UtilityBill.Account.StatementId', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='charge',
            name='statement_id',
            field=document_data.schema_model_field.StringSchemaModelField(db_column='statementid', implements='UtilityBill.Charge.StatementId', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='meter',
            name='statement_id',
            field=document_data.schema_model_field.StringSchemaModelField(db_column='statementid', implements='UtilityBill.Meter.StatementId', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='usage',
            name='statement_id',
            field=document_data.schema_model_field.StringSchemaModelField(db_column='statementid', implements='UtilityBill.Usage.StatementId', max_length=255, null=True),
        ),
    ]
