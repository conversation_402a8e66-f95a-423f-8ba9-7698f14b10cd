# Generated by Django 2.2.28 on 2025-05-15 23:03

from django.db import migrations
import document_data.schema_model_field


class Migration(migrations.Migration):

    dependencies = [
        ('document_data', '0007_add_mat_key_to_meter'),
    ]

    operations = [
        migrations.AddField(
            model_name='account',
            name='normalized_account_number',
            field=document_data.schema_model_field.StringSchemaModelField(db_column='normalizedaccountnumber', implements='UtilityBill.Account.NormalizedAccountNumber', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='account',
            name='normalized_vendor_name',
            field=document_data.schema_model_field.StringSchemaModelField(db_column='normalizedvendorname', implements='UtilityBill.Account.NormalizedVendorName', max_length=255, null=True),
        )
    ]
