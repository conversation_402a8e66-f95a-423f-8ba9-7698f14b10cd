# Generated by Django 2.2.28 on 2025-01-24 23:55

from django.db import migrations, models
import django.db.models.deletion
import document_data.schema_model_field


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('documents', '0031_add_target_training_set'),
    ]

    operations = [
        migrations.CreateModel(
            name='Usage',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('account_number', document_data.schema_model_field.StringSchemaModelField(db_column='accountnumber', implements='UtilityBill.Usage.AccountNumber', max_length=255, null=True)),
                ('meter_id', document_data.schema_model_field.StringSchemaModelField(db_column='meterid', implements='UtilityBill.Usage.MeterId', max_length=255, null=True)),
                ('amount', document_data.schema_model_field.NumberSchemaModelField(db_column='usageamount', implements='UtilityBill.Usage.UsageAmount', max_length=255, null=True)),
                ('usage_type', document_data.schema_model_field.StringSchemaModelField(db_column='usagetype', implements='UtilityBill.Usage.UsageType', max_length=255, null=True)),
                ('commodity', document_data.schema_model_field.StringSchemaModelField(db_column='usagecommodity', implements='UtilityBill.Usage.UsageCommodity', max_length=255, null=True)),
                ('master_commodity', document_data.schema_model_field.StringSchemaModelField(db_column='masterusagecommodity', implements='UtilityBill.Usage.MasterUsageCommodity', max_length=255, null=True)),
                ('uom', document_data.schema_model_field.StringSchemaModelField(db_column='usageuom', implements='UtilityBill.Usage.UsageUOM', max_length=255, null=True)),
                ('normalized_uom', document_data.schema_model_field.StringSchemaModelField(db_column='normalizedusageuom', implements='UtilityBill.Usage.NormalizedUsageUOM', max_length=255, null=True)),
                ('unified_uom', document_data.schema_model_field.StringSchemaModelField(db_column='unifieduom', implements='UtilityBill.Usage.UnifiedUOM', max_length=255, null=True)),
                ('unified_conversion_factor', document_data.schema_model_field.NumberSchemaModelField(db_column='unifiedconversionfactor', implements='UtilityBill.Usage.UnifiedConversionFactor', max_length=255, null=True)),
                ('unified_usage', document_data.schema_model_field.NumberSchemaModelField(db_column='unifiedusageamount', implements='UtilityBill.Usage.UnifiedUsageAmount', max_length=255, null=True)),
                ('emissions_factor', document_data.schema_model_field.NumberSchemaModelField(db_column='emissionsfactor', implements='UtilityBill.Usage.EmissionsFactor', max_length=255, null=True)),
                ('emissions_factor_uom', document_data.schema_model_field.StringSchemaModelField(db_column='emissionsfactoruom', implements='UtilityBill.Usage.EmissionsFactorUOM', max_length=255, null=True)),
                ('emissions_amount', document_data.schema_model_field.NumberSchemaModelField(db_column='emissionsamount', implements='UtilityBill.Usage.EmissionsAmount', max_length=255, null=True)),
                ('document', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='documents.Document')),
                ('extraction', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='extract.Extraction')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='Statement',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('current_charges', document_data.schema_model_field.NumberSchemaModelField(db_column='currentcharges', implements='UtilityBill.Statement.CurrentCharges', max_length=255, null=True)),
                ('total_amount_due', document_data.schema_model_field.NumberSchemaModelField(db_column='totalamountdue', implements='UtilityBill.Statement.TotalAmountDue', max_length=255, null=True)),
                ('statement_date', document_data.schema_model_field.DateSchemaModelField(db_column='statementdate', implements='UtilityBill.Statement.StatementDate', max_length=255, null=True)),
                ('statement_number', document_data.schema_model_field.StringSchemaModelField(db_column='statementnumber', implements='UtilityBill.Statement.StatementNumber', max_length=255, null=True)),
                ('bill_start_date', document_data.schema_model_field.DateSchemaModelField(db_column='billstartdate', implements='UtilityBill.Statement.BillStartDate', max_length=255, null=True)),
                ('bill_end_date', document_data.schema_model_field.DateSchemaModelField(db_column='billenddate', implements='UtilityBill.Statement.BillEndDate', max_length=255, null=True)),
                ('calc_statement_date', document_data.schema_model_field.DateSchemaModelField(db_column='calculatedstatementdate', implements='UtilityBill.Statement.CalculatedStatementDate', max_length=255, null=True)),
                ('calc_current_charges', document_data.schema_model_field.NumberSchemaModelField(db_column='calculatedcurrentcharges', implements='UtilityBill.Statement.CalculatedCurrentCharges', max_length=255, null=True)),
                ('calc_bill_start_date', document_data.schema_model_field.DateSchemaModelField(db_column='calculatedbillstartdate', implements='UtilityBill.Statement.CalculatedBillStartDate', max_length=255, null=True)),
                ('calc_bill_end_date', document_data.schema_model_field.DateSchemaModelField(db_column='calculatedbillenddate', implements='UtilityBill.Statement.CalculatedBillEndDate', max_length=255, null=True)),
                ('currency', document_data.schema_model_field.StringSchemaModelField(db_column='currency', implements='UtilityBill.Statement.Currency', max_length=255, null=True)),
                ('document', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='documents.Document')),
                ('extraction', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='extract.Extraction')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='Meter',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('account_number', document_data.schema_model_field.StringSchemaModelField(db_column='accountnumber', implements='UtilityBill.Meter.AccountNumber', max_length=255, null=True)),
                ('meter_number', document_data.schema_model_field.StringSchemaModelField(db_column='meternumber', implements='UtilityBill.Meter.MeterNumber', max_length=255, null=True)),
                ('meter_pod', document_data.schema_model_field.StringSchemaModelField(db_column='meterpod', implements='UtilityBill.Meter.MeterPOD', max_length=255, null=True)),
                ('meter_tariff', document_data.schema_model_field.StringSchemaModelField(db_column='metertariff', implements='UtilityBill.Meter.MeterTariff', max_length=255, null=True)),
                ('meter_start_date', document_data.schema_model_field.StringSchemaModelField(db_column='meterstartdate', implements='UtilityBill.Meter.MeterStartDate', max_length=255, null=True)),
                ('meter_end_date', document_data.schema_model_field.StringSchemaModelField(db_column='meterenddate', implements='UtilityBill.Meter.MeterEndDate', max_length=255, null=True)),
                ('service_street', document_data.schema_model_field.StringSchemaModelField(db_column='meterservicestreet', implements='UtilityBill.Meter.MeterServiceStreet', max_length=255, null=True)),
                ('service_city', document_data.schema_model_field.StringSchemaModelField(db_column='meterservicecity', implements='UtilityBill.Meter.MeterServiceCity', max_length=255, null=True)),
                ('service_state', document_data.schema_model_field.StringSchemaModelField(db_column='meterservicestate', implements='UtilityBill.Meter.MeterServiceState', max_length=255, null=True)),
                ('service_zip', document_data.schema_model_field.StringSchemaModelField(db_column='meterservicezip', implements='UtilityBill.Meter.MeterServiceZip', max_length=255, null=True)),
                ('meter_service_address', document_data.schema_model_field.StringSchemaModelField(db_column='serviceaddress', implements='UtilityBill.Meter.ServiceAddress', max_length=255, null=True)),
                ('meter_key', document_data.schema_model_field.StringSchemaModelField(db_column='meterkey', implements='UtilityBill.Meter.MeterKey', max_length=255, null=True)),
                ('commodity', document_data.schema_model_field.StringSchemaModelField(db_column='metercommodity', implements='UtilityBill.Meter.MeterCommodity', max_length=255, null=True)),
                ('subregion', document_data.schema_model_field.StringSchemaModelField(db_column='subregion', implements='UtilityBill.Meter.Subregion', max_length=255, null=True)),
                ('document', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='documents.Document')),
                ('extraction', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='extract.Extraction')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='Charge',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('amount', document_data.schema_model_field.NumberSchemaModelField(db_column='chargeamount', implements='UtilityBill.Charge.ChargeAmount', max_length=255, null=True)),
                ('commodity', document_data.schema_model_field.StringSchemaModelField(db_column='chargecommodity', implements='UtilityBill.Charge.ChargeCommodity', max_length=255, null=True)),
                ('charge_type', document_data.schema_model_field.StringSchemaModelField(db_column='chargetype', implements='UtilityBill.Charge.ChargeType', max_length=255, null=True)),
                ('currency', document_data.schema_model_field.StringSchemaModelField(db_column='currency', implements='UtilityBill.Charge.Currency', max_length=255, null=True)),
                ('document', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='documents.Document')),
                ('extraction', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='extract.Extraction')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='AdditionalField',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('label', models.CharField(max_length=255)),
                ('value', models.CharField(max_length=1024)),
                ('metadata', models.CharField(blank=True, max_length=1024, null=True)),
                ('document', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='documents.Document')),
                ('extraction', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='extract.Extraction')),
            ],
        ),
        migrations.CreateModel(
            name='Account',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('vendor_name', document_data.schema_model_field.StringSchemaModelField(db_column='vendorname', implements='UtilityBill.Account.VendorName', max_length=255, null=True)),
                ('account_number', document_data.schema_model_field.StringSchemaModelField(db_column='accountnumber', implements='UtilityBill.Account.AccountNumber', max_length=255, null=True)),
                ('customer_name', document_data.schema_model_field.StringSchemaModelField(db_column='customername', implements='UtilityBill.Account.CustomerName', max_length=255, null=True)),
                ('customer_number', document_data.schema_model_field.StringSchemaModelField(db_column='customernumber', implements='UtilityBill.Account.CustomerNumber', max_length=255, null=True)),
                ('account_key', document_data.schema_model_field.StringSchemaModelField(db_column='accountkey', implements='UtilityBill.Account.AccountKey', max_length=255, null=True)),
                ('service_street', document_data.schema_model_field.StringSchemaModelField(db_column='servicestreet', implements='UtilityBill.Account.ServiceStreet', max_length=255, null=True)),
                ('service_city', document_data.schema_model_field.StringSchemaModelField(db_column='servicecity', implements='UtilityBill.Account.ServiceCity', max_length=255, null=True)),
                ('service_state', document_data.schema_model_field.StringSchemaModelField(db_column='servicestate', implements='UtilityBill.Account.ServiceState', max_length=255, null=True)),
                ('service_zip', document_data.schema_model_field.StringSchemaModelField(db_column='servicezip', implements='UtilityBill.Account.ServiceZip', max_length=255, null=True)),
                ('service_address', document_data.schema_model_field.StringSchemaModelField(db_column='serviceaddress', implements='UtilityBill.Account.ServiceAddress', max_length=255, null=True)),
                ('standardized_vendor_name', document_data.schema_model_field.StringSchemaModelField(db_column='standardizedvendorname', implements='UtilityBill.Account.StandardizedVendorName', max_length=255, null=True)),
                ('short_vendor_abbreviation', document_data.schema_model_field.StringSchemaModelField(db_column='shortvendorabbreviation', implements='UtilityBill.Account.ShortVendorAbbreviation', max_length=255, null=True)),
                ('document', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='documents.Document')),
                ('extraction', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='extract.Extraction')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
