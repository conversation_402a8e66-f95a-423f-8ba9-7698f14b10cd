# Generated by Django 2.2.28 on 2025-02-18 18:08

from django.db import migrations
import document_data.schema_model_field


class Migration(migrations.Migration):

    dependencies = [
        ('document_data', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='account',
            name='vendor_type',
            field=document_data.schema_model_field.StringSchemaModelField(db_column='vendortype', implements='UtilityBill.Account.VendorType', max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='charge',
            name='amount',
            field=document_data.schema_model_field.AmountSchemaModelField(db_column='chargeamount', implements='UtilityBill.Charge.ChargeAmount', max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='meter',
            name='meter_end_date',
            field=document_data.schema_model_field.DateSchemaModelField(db_column='meterenddate', implements='UtilityBill.Meter.MeterEndDate', max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='meter',
            name='meter_start_date',
            field=document_data.schema_model_field.DateSchemaModelField(db_column='meterstartdate', implements='UtilityBill.Meter.MeterStartDate', max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='statement',
            name='calc_current_charges',
            field=document_data.schema_model_field.AmountSchemaModelField(db_column='calculatedcurrentcharges', implements='UtilityBill.Statement.CalculatedCurrentCharges', max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='statement',
            name='current_charges',
            field=document_data.schema_model_field.AmountSchemaModelField(db_column='currentcharges', implements='UtilityBill.Statement.CurrentCharges', max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='statement',
            name='total_amount_due',
            field=document_data.schema_model_field.AmountSchemaModelField(db_column='totalamountdue', implements='UtilityBill.Statement.TotalAmountDue', max_length=255, null=True),
        ),
    ]
