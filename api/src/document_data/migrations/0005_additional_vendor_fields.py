# Generated by Django 2.2.28 on 2025-04-25 00:00

from django.db import migrations
import document_data.schema_model_field


class Migration(migrations.Migration):

    dependencies = [
        ('document_data', '0004_rename_service_address'),
    ]

    operations = [
        migrations.AddField(
            model_name='account',
            name='additional_vendor1_account_number',
            field=document_data.schema_model_field.StringSchemaModelField(db_column='additionalvendor1accountnumber', implements='UtilityBill.Account.AdditionalVendor1AccountNumber', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='account',
            name='additional_vendor1_name',
            field=document_data.schema_model_field.StringSchemaModelField(db_column='additionalvendor1name', implements='UtilityBill.Account.AdditionalVendor1Name', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='account',
            name='additional_vendor1_type',
            field=document_data.schema_model_field.StringSchemaModelField(db_column='additionalvendor1type', implements='UtilityBill.Account.AdditionalVendor1Type', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='account',
            name='additional_vendor2_account_number',
            field=document_data.schema_model_field.StringSchemaModelField(db_column='additionalvendor2accountnumber', implements='UtilityBill.Account.AdditionalVendor2AccountNumber', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='account',
            name='additional_vendor2_name',
            field=document_data.schema_model_field.StringSchemaModelField(db_column='additionalvendor2name', implements='UtilityBill.Account.AdditionalVendor2Name', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='account',
            name='additional_vendor2_type',
            field=document_data.schema_model_field.StringSchemaModelField(db_column='additionalvendor2type', implements='UtilityBill.Account.AdditionalVendor2Type', max_length=255, null=True),
        ),
    ]
