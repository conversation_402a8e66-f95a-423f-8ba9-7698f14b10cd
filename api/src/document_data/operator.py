import logging
from datetime import datetime
from enum import Enum
from typing import List, Optional

from pydantic import BaseModel as PydanticBase
from pydantic import Field

from document_data.operator_plans import TableOperatorPlan
from document_data.operators import (
    OPERATION_CLASSES_BY_NAME, OperationExecution, OperationResult
)
from document_data.utils import now_factory


class OperatorResult(str, Enum):
    PENDING = "PENDING"
    SUCCESS = "SUCCESS"
    FAIL = "FAIL"


class OperatorExecution(PydanticBase):
    op_plan: TableOperatorPlan
    started_at: datetime = Field(default_factory=now_factory)
    finished_at: Optional[datetime]
    result: OperatorResult = OperatorResult.PENDING
    result_message: Optional[str]
    executed_operations: List[OperationExecution] = Field(default_factory=list)


class DocumentDataOperator:
    def __init__(self, table_plans: TableOperatorPlan, db_client, logger=None):
        self.logger = logger or logging.getLogger(__name__)
        self.table_plans = table_plans
        self._db_client = db_client
        self.execution: Optional[OperatorExecution] = None

    def run_operations(self, doc_id):
        self.execution = OperatorExecution(op_plan=self.table_plans)
        for _, op_configs in self.table_plans.table_plans.items():
            for op_config in op_configs:
                self.logger.info(
                    f"Running operator {op_config.opname} for document {doc_id} with config {op_config}"
                )
                operator_cls = OPERATION_CLASSES_BY_NAME[op_config.opname]
                operator = operator_cls(op_config, self._db_client, self.logger)
                op_output = operator.run(doc_id)

                self.execution.executed_operations.append(op_output)

                if op_output.result != OperationResult.SUCCESS:
                    result_message = f"{op_output.config.opname} {op_output.result}: {op_output.result_message}"
                    self.logger.error(result_message)
                    self.execution.result = OperatorResult.FAIL
                    self.execution.result_message = result_message

        if self.execution.result == OperationResult.PENDING:
            self.execution.result = OperatorResult.SUCCESS
            self.execution.result_message = f"Operations for doc {doc_id} succeeded"
