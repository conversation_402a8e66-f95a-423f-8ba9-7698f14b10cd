import logging
import re
from abc import ABC, abstractmethod
from datetime import datetime
from decimal import Decimal
from enum import Enum
from typing import Any, Optional

from pydantic import BaseModel as PydanticBase
from pydantic import Field

import usaddress
from document_data import models
from document_data.db_client import GenericDBClient
from document_data.models import Account, Meter, Usage
from document_data.operator_configs import BaseOperatorConfig
from document_data.schema_model_field import SchemaModelField
from document_data.utils import now_factory
from glynt_schemas.data import enums as schema_enums
from glynt_schemas.data import tags
from static_enrichment_tables.models import (
    CommodityEmissionsFactor, EgridSubregionsEmissionsFactor,
    SiteVendorNormalizedUom
)

logger = logging.getLogger(__name__)


class OperationResult(str, Enum):
    PENDING = "PENDING"
    SUCCESS = "SUCCESS"
    FAIL = "FAIL"


class OperationExecution(PydanticBase):
    config: BaseOperatorConfig
    executed_at: datetime = Field(default_factory=now_factory)
    result: OperationResult = OperationResult.PENDING
    result_message: Optional[str]
    output_data: Optional[Any]


class BaseDocumentDataOperator(ABC):
    OPNAME = None
    """ Base abstract class for all operators that operate on a document"""
    def __init__(self, config: BaseOperatorConfig, db: GenericDBClient, logger=None):
        self.logger = logger or logging.getLogger(__name__)
        self._db = db
        assert self.OPNAME is not None, 'You must implement OPNAME in your subclass'
        self._config = config

        self.operation_execution = None

    def new_operation_execution(self) -> OperationExecution:
        return OperationExecution(
            config=self._config,
            result=OperationResult.PENDING,
            result_message=None
        )

    @abstractmethod
    def _run(self, doc_id):
        pass

    def run(self, doc_id=None):
        self.operation_execution = self.new_operation_execution()

        try:
            output = self._run(doc_id)
        except Exception as e:
            self.logger.exception(e, stack_info=True)
            self.operation_execution.result = OperationResult.FAIL
            self.operation_execution.result_message = str(e)
            return self.operation_execution

        self.operation_execution.output_data = output
        if self.operation_execution.result == OperationResult.PENDING:
            self.operation_execution.result = OperationResult.SUCCESS
        return self.operation_execution


class RegexSubOperator(BaseDocumentDataOperator):
    OPNAME = 'regex_sub'

    def _run(self, doc_id):
        source_vals = self._db.execute(
            f"""
            SELECT {self._config.source_col}, id
            FROM {self._config.table_name}
            WHERE document_id = {doc_id}
            """
        )
        for input in source_vals:
            value = input.get(self._config.source_col)
            if not value:
                continue
            id = input['id']
            output_val = re.sub(self._config.pattern, self._config.repl, value)
            self._db.execute(
                f"""
                UPDATE {self._config.table_name}
                SET {self._config.dest_col} = "{output_val}"
                WHERE id = {id}
                """
            )


class SqlOperator(BaseDocumentDataOperator):
    OPNAME = 'execute_sql'

    def _run(self, doc_id):
        query = self._config.statement.format(doc_id=doc_id)
        return self._db.execute(query)


class FindAndReplaceOperator(BaseDocumentDataOperator):
    OPNAME = 'find_and_replace'

    def _run(self, doc_id):

        table_name = self._config.table_name
        column_name = self._config.column_name
        find = self._config.find
        query = f"""
            SELECT id, {column_name}
            FROM {table_name}
            WHERE {table_name}.document_id = {doc_id}
        """
        rows = self._db.execute(query)

        for row in rows:
            value = row[column_name]
            if not value:
                continue

            meta_tags = value.split(',')
            if len(meta_tags) == 1:
                continue

            match = None
            for meta_tag in meta_tags:
                if meta_tag in find:
                    match = meta_tag

                    self._db.execute(f"UPDATE {table_name} SET {column_name} = '{match}' WHERE id = {row['id']}")
                    break

            # Update to default value if specified
            if not match and self._config.use_default:
                logger.warning(f"Could not find a replacement for {value}. Using default value")
                default = f'"{self._config.default_value}"' if self._config.default_value else "NULL"
                self._db.execute(
                    f"UPDATE {table_name} SET {column_name} = {default} WHERE id = {row['id']}"
                )


class CalcStatementCurrencyOperator(BaseDocumentDataOperator):
    OPNAME = 'parse_statement_currency'

    def _run(self, doc_id):
        stmt = models.Statement.objects.filter(document=doc_id).first()
        if stmt:
            # Get currency from the total amount due field metadata
            total_amount_due_model_field: SchemaModelField = stmt._meta.get_field('total_amount_due')
            stmt.currency = total_amount_due_model_field.get_schema_field(stmt.extraction).datatype.config.currency
            stmt.save()


class CalcChargeCurrencyOperator(BaseDocumentDataOperator):
    OPNAME = 'parse_charge_currency'

    def _run(self, doc_id):
        charges = models.Charge.objects.filter(document=doc_id)
        for charge in charges:
            charge.currency = charge._meta.get_field('amount').schema_field.datatype.config.currency
            charge.save()


class AccountKeyOperator(BaseDocumentDataOperator):
    OPNAME = 'generate_account_key'

    def _run(self, doc_id):
        # Need to retrieve the vendorname from the very first statement and account
        # which may be the only one that actually has a valid vendor name.
        primary_account_data = self._db.execute(
            f"""
            SELECT
                tacct.vendorname,
                sv.standardized_vendor_name,
                sv.short_vendor_abbr
            FROM
                document_data_statement tstmt
            JOIN
                document_data_account tacct
            ON
                tstmt.document_id = tacct.document_id
            AND
                tstmt.id = tacct.statementid
            LEFT JOIN static_enrichment_tables_standardizedvendorname sv
            ON tacct.vendorname = sv.printed_vendor_name
            WHERE
                tstmt.id IN
                (
                    SELECT min(id) from document_data_statement
                    where document_id = {doc_id}
                    group by document_id
                )
            ORDER BY tstmt.id
            """
        )

        accounts_data = self._db.execute(
            f"""
            SELECT
            acct.id, acct.document_id, acct.accountnumber, acct.vendorname,
            sv.standardized_vendor_name, sv.short_vendor_abbr
            FROM document_data_account acct
            LEFT JOIN static_enrichment_tables_standardizedvendorname sv
            ON acct.vendorname = sv.printed_vendor_name
            WHERE document_id = {doc_id}
            """
        )

        # Calculate account key. Not possible using SQL, so we'll use some code for this.
        for acct in accounts_data:
            account_number = acct.get('accountnumber', '')
            vendor_name = (
                acct.get('standardized_vendor_name') or
                acct.get('vendorname') or
                primary_account_data[0].get('standardized_vendor_name') or
                primary_account_data[0].get('vendorname', '')
            )
            abbr_vendor_name = (
                acct.get('short_vendor_abbr') or
                primary_account_data[0].get('short_vendor_abbr') or
                self._generate_abbreviated_vendor_name(vendor_name)
            )
            account_number = re.sub(r'\W+', ' ', account_number) if account_number else ''
            acct_key = f'{abbr_vendor_name}_{account_number}'.replace(" ", "")

            self._db.execute(f"UPDATE document_data_account SET accountkey = '{acct_key}' WHERE id = {acct['id']}")

    def _generate_abbreviated_vendor_name(self, vendor_name):
        # maybe move this to another operator that runs before generating account key
        if not vendor_name:
            return ''

        stop_words = ['the', 'of']
        vendor_name_first_constituent_length = 6
        vn_remaining_constituent_length = 3
        total_vendorname_constituents = 4

        vendor_name = re.sub(r'\W+', ' ', vendor_name).lower()
        vendor_name_constituents = vendor_name.split(' ')
        vendor_name_constituents = [constituent for constituent in vendor_name_constituents if
                                    constituent not in stop_words]
        abbr_vendor_name = vendor_name_constituents[0][:vendor_name_first_constituent_length]
        for constituent_index in range(1, total_vendorname_constituents + 1):
            if len(vendor_name_constituents) > constituent_index:
                abbr_vendor_name += vendor_name_constituents[constituent_index][:vn_remaining_constituent_length]
        return abbr_vendor_name


class CalculatedAccountAddressOperator(BaseDocumentDataOperator):
    OPNAME = 'calculate_account_address'

    def _run(self, doc_id):
        accounts_data = self._db.execute(
            f"""
            SELECT
            a.id, a.servicestreet, a.servicecity, a.servicestate, a.servicezip,
            m.meterservicestreet, m.meterservicecity, m.meterservicestate, m.meterservicezip,
            a.accountnumber acctnum, m.accountnumber macctnum
            FROM document_data_account a
            LEFT JOIN document_data_meter m on m.document_id=a.document_id and
            (
                m.accountnumber=a.accountnumber
                OR (m.accountnumber IS NULL AND a.accountnumber IS NULL)
            )
            WHERE a.document_id = {doc_id}
            """
        )

        accounts_processed = set()

        # Set each account address to the address of the first meter with an address
        for row in accounts_data:
            account_id = row.get('id')
            if account_id not in accounts_processed:
                street = row['servicestreet'] or row['meterservicestreet']
                city = row['servicecity'] or row['meterservicecity']
                state = row['servicestate'] or row['meterservicestate']
                zip = row['servicezip'] or row['meterservicezip']
                if street or city or state or zip:
                    service_address = ' '.join(
                        [x for x in [street, city, state, zip] if x]
                    )
                    self._db.execute(
                        'UPDATE document_data_account '
                        'SET '
                        f"serviceaddress = '{service_address}',"
                        f"servicestreet = '{street}',"
                        f"servicecity = '{city}',"
                        f"servicestate = '{state}',"
                        f"servicezip = '{zip}'"
                        f"WHERE id={account_id}"
                    )

                    accounts_processed.add(account_id)


class MeterKeyOperator(BaseDocumentDataOperator):
    OPNAME = 'generate_meter_key'

    def _run(self, doc_id):
        # Calculate the service address and commodity for each meter
        meters = models.Meter.objects.filter(document=doc_id)
        for meter in meters:
            meter_number = re.sub(r'\W+', ' ', meter.meter_number) if meter.meter_number else ''

            account = models.Account.objects.filter(document=meter.document, account_key__isnull=False).first()

            meter_key = f'{account.account_key}_{meter_number}'.replace(" ", "")
            meter.meter_key = meter_key
            meter.save()


class ZipCodeParseOperator(BaseDocumentDataOperator):
    OPNAME = 'parse_meter_zip_code'

    def _run(self, doc_id):
        meters_data = self._db.execute(
            f"""
            SELECT id, serviceaddress
            FROM document_data_meter
            WHERE document_id = {doc_id}
            """
        )
        for meter in meters_data:
            zipcode = [
                part for part, label in usaddress.parse(meter['serviceaddress'] or '') if label == 'ZipCode'
            ] or ''
            if zipcode:
                zipcode = zipcode[-1][:5].strip()

                if self._zip_code_valid(zipcode):
                    self._db.execute(
                        f"UPDATE document_data_meter SET meterservicezip = '{zipcode}' WHERE id = {meter['id']}"
                    )

    def _zip_code_valid(self, zip_code):
        valid = True
        if not len(zip_code) == 5:
            valid = False
        if not zip_code.isdigit():
            valid = False
        return valid


class SetUsageTypeByTagsOperator(BaseDocumentDataOperator):
    OPNAME = "set_usage_type_by_tags"

    def _run(self, doc_id):
        # Fix up usages so that they have valid usage_type values. Cannot be down with the 'find and replace'
        # operator because of the mismatching of usage tag values and actual usage type strings.
        usages = models.Usage.objects.filter(document=doc_id)
        for usage in usages:
            usage.usage_type = self._classify_usage_by_tag(usage.usage_type)
            logger.info(f'Update usage type to {usage.usage_type}')
            usage.save()

    def _classify_usage_by_tag(self, usage_string):
        '''
        Checks the value of the field against known usage tag strings
        and converts to an actual usage type.
        '''
        usage = usage_string
        for val in usage_string.split(',') if usage_string else []:
            try:
                usagetype = tags.UsageTypeTag[val.upper()].value
                return schema_enums.UsageType[usagetype]  # Convert from a usage tage to a usage type
            except KeyError:
                pass
        return usage


class SetMasterCommodityOperator(BaseDocumentDataOperator):
    OPNAME = "set_master_usage_commodity"

    def _get_reduced_commodity_type(self, commodity: schema_enums.CommodityType):
        reduced_commodity_map = {
            schema_enums.CommodityType.GAS: schema_enums.CommodityType.GAS,
            schema_enums.CommodityType.WATER: schema_enums.CommodityType.WATER,
            schema_enums.CommodityType.ELECTRIC: schema_enums.CommodityType.ELECTRIC,
            schema_enums.CommodityType.SEWER: schema_enums.CommodityType.WATER,
            schema_enums.CommodityType.STORMWATER: schema_enums.CommodityType.WATER,
            schema_enums.CommodityType.LIGHTING: schema_enums.CommodityType.ELECTRIC,
            schema_enums.CommodityType.FIRELINE: schema_enums.CommodityType.WATER,
            schema_enums.CommodityType.IRRIGATION: schema_enums.CommodityType.WATER,
            schema_enums.CommodityType.GARBAGE: schema_enums.CommodityType.WASTE,
            schema_enums.CommodityType.SPRINKLER: schema_enums.CommodityType.WATER,
            schema_enums.CommodityType.PROPANE: schema_enums.CommodityType.PROPANE,
        }
        return reduced_commodity_map.get(commodity, commodity)

    def _run(self, doc_id):
        for usage in Usage.objects.filter(document=doc_id):
            usage.master_commodity = self._get_reduced_commodity_type(usage.commodity)
            usage.save()


class UnifiedUsageOperator(BaseDocumentDataOperator):
    OPNAME = "generate_unified_usage"

    def _run(self, doc_id):
        usages = self._db.execute(
            'SELECT *'
            ' FROM document_data_usage'
            f' WHERE document_id={doc_id}'
        )

        for usage in usages:
            commodity = usage['masterusagecommodity'] or usage['usagecommodity']
            uom = usage['normalizedusageuom'] or usage['usageuom']

            if not commodity or not uom:
                self.logger.warning(
                    f"Missing commodity and/or uom data for Usage {usage['id']}"
                    " to generate unified usage fields. Skipping.")
                return

            try:
                unified_commodity_uom = self._db.execute(
                    f'SELECT ucu.unified_uom '
                    f'FROM static_enrichment_tables_unifiedcommodityuom ucu '
                    f'WHERE ucu.commodity="{commodity}"'
                )[0]['unified_uom']
            except IndexError:
                self.logger.warning(f"Unified commodity UOM not found for commodity {commodity}, skipping.")
                return

            try:
                conversion_factor = self._db.execute(
                    f"""
                    SELECT uomc.conversion_factor
                    FROM static_enrichment_tables_uomconversionfactor uomc
                    WHERE uomc.commodity="{commodity}"
                    AND uomc.from_uom="{uom}"
                    AND uomc.to_uom="{unified_commodity_uom}"
                    """
                )[0]["conversion_factor"]
            except IndexError:
                self.logger.warning(
                    f"Unified conversion factor not found for {commodity} from {uom} to {unified_commodity_uom}")
                return

            self._db.execute(
                'UPDATE document_data_usage '
                'SET '
                f'unifieduom = "{unified_commodity_uom}", '
                f'unifiedconversionfactor = "{conversion_factor}", '
                f'unifiedusageamount = usageamount * {conversion_factor} '
                f'WHERE id = "{usage["id"]}"'
            )


class NormalizeUsageUomOperator(BaseDocumentDataOperator):
    OPNAME = "normalize_usage_uom"

    def _run(self, doc_id):
        account = Account.objects.filter(document_id=doc_id).first()
        for usage in Usage.objects.filter(document_id=doc_id):
            meter = Meter.objects.get(id=usage.meter_id)

            vendor_name = account.standardized_vendor_name or account.vendor_name
            commodity = usage.master_commodity or usage.commodity
            if not vendor_name or not commodity or not meter.subregion:
                self.logger.warning(f"Required data to look up normalized UOM not found - "
                                    f"vendor_name: {vendor_name}, commodity: {commodity}, subregion: {meter.subregion}")
                return

            try:
                normalized_uom = SiteVendorNormalizedUom.objects.get(
                    vendor_name=vendor_name,
                    site_id=meter.subregion,
                    commodity=commodity
                ).normal_uom
            except SiteVendorNormalizedUom.DoesNotExist:
                self.logger.warning(
                    f"Normalized Uom not found for key: {vendor_name} - {meter.subregion} - {commodity}")
                return
            usage.normalized_uom = normalized_uom
            usage.save()


class UsageEmissionsOperator(BaseDocumentDataOperator):
    OPNAME = "generate_usage_emissions"

    def _run(self, doc_id):
        for usage in Usage.objects.filter(document=doc_id):
            if usage.master_commodity != schema_enums.CommodityType.ELECTRIC:
                try:
                    emissions_factor_info = CommodityEmissionsFactor.objects.get(
                        commodity=usage.master_commodity,
                        usage_uom=usage.normalized_uom or usage.uom
                    )
                except CommodityEmissionsFactor.DoesNotExist:
                    self.logger.warning(f"No emissions factor found for commodity: {usage.commodity}, uom: {usage.uom}")
                    return
                emissions_factor = emissions_factor_info.emissions_factor
                emissions_factor_uom = emissions_factor_info.emissions_factor_uom
            else:
                meter = Meter.objects.get(id=usage.meter_id)
                try:
                    emissions_factor_info = EgridSubregionsEmissionsFactor.objects.get(subregion=meter.subregion)
                except EgridSubregionsEmissionsFactor.DoesNotExist:
                    self.logger.warning(f"No electric emissions factor found for subregion: {meter.subregion}")
                    return
                emissions_factor = emissions_factor_info.electric_lb_co2e_per_MWH
                emissions_factor = self._convert_lb_to_MT(emissions_factor)
                emissions_factor_uom = "MT CO2e"

            usage.emissions_factor = emissions_factor
            usage.emissions_factor_uom = emissions_factor_uom

            if usage.unified_usage:
                usage.emissions_amount = usage.unified_usage * emissions_factor

            usage.save()

    def _convert_lb_to_MT(self, lb: Decimal):
        KG_PER_POUND = Decimal(0.453592)
        return lb * KG_PER_POUND / Decimal(1000)


class SetChargeTypeByTagsOperator(BaseDocumentDataOperator):
    OPNAME = "set_charge_type_by_tags"

    def _run(self, doc_id):
        # Fix up charges so that they have valid charge_type values.
        charges = models.Charge.objects.filter(document=doc_id)
        for charge in charges:
            charge.charge_type = self._classify_by_tag(charge.charge_type)
            logger.info(f'Update charge type to {charge.charge_type}')
            charge.save()

    def _classify_by_tag(self, charge_string):
        '''
        Checks the value of the field against known charge tag strings
        and converts to an actual charge type.
        '''
        charge = charge_string
        for val in charge_string.split(',') if charge_string else []:
            try:
                chargetype = tags.ChargeTypeTag[val.upper()].value
                return schema_enums.ChargeType[chargetype]  # Convert from a charge tage to a usage type
            except KeyError:
                pass
        return charge


class NormalizeVendorNameOperator(BaseDocumentDataOperator):
    OPNAME = "normalize_vendor_name"

    def _run(self, doc_id):
        # Get accounts for this doc
        accounts = models.Account.objects.filter(document_id=doc_id)
        for acct in accounts:
            # For now, just make it all uppercase
            if acct.vendor_name:
                acct.normalized_vendor_name = acct.vendor_name.upper()
                logger.debug(f'Update vendor name to {acct.vendor_name}')
                acct.save()


class CalculateBillDates(BaseDocumentDataOperator):
    OPNAME = "calculate_bill_dates"

    def _run(self, doc_id):
        # Get statements for this doc
        statements = models.Statement.objects.filter(document_id=doc_id)

        # Get meters for this doc
        meters = models.Meter.objects.filter(document_id=doc_id)

        # Find the min/max meter dates
        min_date = min([meter.meter_start_date for meter in meters if meter.meter_start_date], default=None)
        max_date = max([meter.meter_end_date for meter in meters if meter.meter_end_date], default=None)

        for statement in statements:
            statement.calc_bill_start_date = statement.bill_start_date or min_date
            statement.calc_bill_end_date = statement.bill_end_date or max_date
            statement.save()


class CalculateCurrentCharges(BaseDocumentDataOperator):
    OPNAME = "calculate_current_charges"

    def _run(self, doc_id):
        # Get statements for this doc
        statements = models.Statement.objects.filter(document_id=doc_id)

        for s in statements:
            if s.current_charges:
                s.calc_current_charges = s.current_charges
            else:
                charges = models.Charge.objects.filter(document_id=doc_id)
                s.calc_current_charges = sum(
                    [
                        c.amount for c in charges
                        if c.amount and c.charge_type != 'total_supply'
                        and c.charge_type != 'total_delivery'
                    ]
                )

            s.save()


class SetMeterServiceAddress(BaseDocumentDataOperator):
    OPNAME = "set_meter_service_address"

    def _run(self, doc_id):
        # Get meters for this doc
        meters = models.Meter.objects.filter(document_id=doc_id)

        for m in meters:
            m.service_address = (
                (m.service_street + ' ' if m.service_street else '') +
                (m.service_city + ' ' if m.service_city else '') +
                (m.service_state + ' ' if m.service_state else '') +
                (m.service_zip if m.service_zip else '')
            ).strip()
            if m.service_address == '':
                m.service_address = None
            m.save()


class SetVendorType(BaseDocumentDataOperator):
    OPNAME = "set_vendor_type"

    def _run(self, doc_id):
        accounts = models.Account.objects.filter(document_id=doc_id)

        for acct in accounts:
            save = False
            if acct.vendor_type:
                acct.vendor_type = self._classify_by_tag(acct.vendor_type)
                logger.debug(f'Update vendor type to {acct.vendor_type}')
                save = True
            if acct.additional_vendor1_type:
                acct.additional_vendor1_type = self._classify_by_tag(acct.additional_vendor1_type)
                logger.debug(f'Update additional vendor 1 type to {acct.additional_vendor1_type}')
                save = True
            if acct.additional_vendor2_type:
                acct.additional_vendor2_type = self._classify_by_tag(acct.additional_vendor2_type)
                logger.debug(f'Update additional vendor 1 type to {acct.additional_vendor2_type}')
                save = True
            if save:
                acct.save()

    def _classify_by_tag(self, type_string):
        '''
        Checks the value of the field against known vendor types
        and converts to an actual vendor type.

        If no, valid vendor type was found, None is returned because it means
        there is no type.
        '''
        for val in type_string.split(','):
            vendor_type = [vt.value for vt in schema_enums.VendorType if vt.value.upper() == val.upper()]
            if vendor_type and len(vendor_type) == 1:
                vendor_type = vendor_type[0]
                break
        return vendor_type or None


def all_subclasses(cls):
    return set(cls.__subclasses__()).union(
        [s for c in cls.__subclasses__() for s in all_subclasses(c)])


OPERATION_CLASSES_BY_NAME = {op.OPNAME: op for op in all_subclasses(BaseDocumentDataOperator)}
