from abc import ABC, abstractmethod

from django.db import connection


class GenericDBClient(ABC):
    """Implement this as a generic database client to use in your operators.
     Defined where the database client is used."""
    @abstractmethod
    def execute(self, query):
        pass


class SqlDBClient(GenericDBClient):
    def dictfetchall(self, cursor):
        """
        Return all rows from a cursor as a dict.
        Assume the column names are unique.
        """
        columns = [col[0] for col in cursor.description]
        return [dict(zip(columns, row)) for row in cursor.fetchall()]

    def execute(self, q):
        with connection.cursor() as cursor:
            cursor.execute(q)
            try:
                return self.dictfetchall(cursor)
            except TypeError:
                return None
