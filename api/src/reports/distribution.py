import csv
import itertools
import logging
from io import StringIO

from django.conf import settings
from django.db.models import Q
from django.http import Http404
from rest_framework.response import Response

from documents.models import Document
from extract.models import Extraction, ExtractionBatch
from training.models import TrainingSet

from .util import (
    get_report_format_from_view, get_report_type_from_view,
    report_field_serializer
)

logger = logging.getLogger(__name__)


def field_distribution_report_view_handler(view):
    report_format = get_report_format_from_view(view)

    default_report_type = 'stats'
    report_type_options = ['stats', 'wide', 'long']
    report_type = get_report_type_from_view(view, default_report_type, report_type_options)

    if not view.request.user.is_staff and report_type == 'long':
        raise Http404

    instance = view.get_object()

    training_set_ids = view.request.query_params.get('training_sets')
    if training_set_ids:
        training_set_ids = training_set_ids.split(',')

    response = Response(get_dist_report(
        instance, report_format, report_type, training_set_ids, view.request.user.is_staff
    ))
    response['Content-Disposition'] = (f'attachment; '
                                       f'filename="{instance.obfuscated_id}_field_distribution.{report_format}"')
    return response


def get_dist_report(instance, report_format, report_type, training_set_ids=None, is_staff=False):
    """Returns distribution report data in the given format and report type.
    Can create reports for TrainingSets, ExtractionBatches or single
    Extractions (treated as a batch with a single extraction)."""
    assert report_format in ['json', 'csv']

    dist_report_data = {}
    if isinstance(instance, TrainingSet):
        dist_report_data['documents'] = instance.ground_truths_by_document
        dist_report_data['training_set_id'] = instance.obfuscated_id
        dist_report_data['training_set_label'] = instance.label
        ui_url_info = {'display': 'Marking URL', 'data_key': 'marking_url'}
    elif isinstance(instance, ExtractionBatch):
        dist_report_data['documents'] = _get_dist_report_document_data(
            instance.extractions.all(), training_set_ids
        )
        ui_url_info = {'display': 'Review URL', 'data_key': 'review_url'}
    elif isinstance(instance, Extraction):
        dist_report_data['documents'] = _get_dist_report_document_data(
            Extraction.objects.filter(id=instance.id), training_set_ids
        )
        ui_url_info = {'display': 'Review URL', 'data_key': 'review_url'}
    else:
        raise ValueError('object of view must be one of TrainingSet, Extraction, or ExtractionBatch')

    dist_report_data['document_count'] = len(dist_report_data['documents'])
    dist_report_data['fields'] = field_responses(dist_report_data['documents'])

    if report_format == 'csv':
        return _get_dist_report_csv(ui_url_info, dist_report_data, report_type)
    elif report_format == 'json':
        _manipulate_json_dist_report(dist_report_data['documents'], is_staff)
        return dist_report_data


def _manipulate_json_dist_report(documents, is_staff):
    for doc_name in documents:
        fields = documents[doc_name]['fields']
        report_field_serializer(fields, is_staff)


def _get_dist_report_document_data(extractions, training_set_ids=None):
    """Populates a dictionary of document ids each containing a dictionary
    of field results collected from the extracted data and from
    corrections. New fields introduced by a correction are considered null
    for documents that do not have a correction for that field. This
    property is the origin of all data in distribution reports so be sure
    that all documents have an entry for all fields.

    If training_set_ids is a list of Training Set obfuscated ids, filters out
    Extractions which are not associated with those training sets. Also,
    Fields which are not associated with any included in any Extraction will
    also not be included. A special 'None' string value may be included in the
    training_set_ids list which will include Extractions which are not
    associated with any Training Set.

    example:
    {
        "hg8rr1": {
            "label": "Sample Document 1",
            "training_set_id": "8fnadk",
            "training_set_label": "Sample Training Set",
            "review_url": "https://ui.glynt.ai/data-pools/Dnsia5/review/jJtg51/extraction/nQkaL1",
            "fields": {
                "Account Number": {
                    "content": "abc123",
                    "tags": ["account id"],
                    "glynt_tags: []
                },
                "Line Item One": {
                    "content": "",
                    "tags": [],
                    "glynt_tags: []
                },
                "Line Item Two": {
                    "content": "555.4",
                    "tags": [],
                    "glynt_tags: []
                },
                "Line Item Three": {
                    "content": "",
                    "tags": [],
                    "glynt_tags: []
                }
            }
        },
        "nQkaL1": {
            "label": "Sample Document 2",
            "training_set_id": "8fnadk",
            "training_set_label": "Sample Training Set",
            "review_url": "https://ui.glynt.ai/data-pools/Dnsia5/review/jJtg51/extraction/nQkaL1",
            "fields": {
                "Account Number": {
                    "content": "foo",
                    "tags": ["foo tag"],
                    "glynt_tags: []
                },
                "Line Item One": {
                    "content": "",
                    "tags": [],
                    "glynt_tags: []
                },
                "Line Item Two": {
                    "content": "20.25",
                    "tags": [],
                    "glynt_tags: []
                },
                "Line Item Three": {
                    "content": "bar",
                    "tags": [],
                    "glynt_tags: ["bar glynt tag"]
                }
            }
        }
    }
    """
    doc_stats = {}

    extractions, fields = _get_extractions_and_fields(extractions, training_set_ids)
    for extraction in extractions:
        doc = extraction.document
        doc_id = doc.obfuscated_id
        doc_stats[doc_id] = {}

        training_set = extraction.training_set
        doc_stats[doc_id]['training_set_id'] = (training_set.obfuscated_id
                                                if training_set else None)
        doc_stats[doc_id]['training_set_label'] = (training_set.label
                                                   if training_set else None)
        # will be an empty dict if extraction failed
        results = extraction.results

        doc_stats[doc_id]['label'] = doc.label

        doc_stats[doc_id]['review_url'] = (
            f'{settings.GLYNT_UI_URL}/data-pools/'
            f'{extraction.data_pool.obfuscated_id}/'
            f'review/{extraction.extraction_batch.obfuscated_id}/'
            f'extraction/{extraction.obfuscated_id}'
        ) if extraction.extraction_batch_id else None

        doc_stats[doc_id]['fields'] = {}

        for field in fields:
            field_result = results.get(field.label, {})
            # if field not in results, handle as null
            doc_stats[doc_id]['fields'][field.label] = {
                'content': field_result.get('content', ''),
                'tags': list(field.tags.names()),
                'glynt_tags': list(field.glynt_tags.names())
            }

    return doc_stats


def _get_extractions_and_fields(extractions, training_set_ids=None):
    """Encapsulates the process of generating the lists of extractions and fields
    which are to be included in the extraction batch dist report, as filtered
    by a list of Training Set obfuscated ids, with the possible special 'None'
    string value to represent extractions which have no associated Training
    Set.
    """
    if training_set_ids:
        filter_statement = Q(pk__in=[])
        for obf_id in training_set_ids:
            # Handles the special 'None' value
            if obf_id == 'None':
                filter_statement |= Q(training_set=None)
            else:
                try:
                    training_set = TrainingSet.get_object_by_obfuscated_id(obf_id)
                except TrainingSet.DoesNotExist:
                    pass
                else:
                    filter_statement |= Q(training_set=training_set)

        extractions = extractions.filter(filter_statement)

    # creates a set of all fields that maintains the fields orders as they are
    # saved on the extractions; an ordered set
    fields = list(dict.fromkeys(
        itertools.chain.from_iterable(
            [
                list(
                    extraction.baked_fields.order_by(
                        'field_order'
                    )
                ) for extraction in extractions
            ]
        )
    ))
    return extractions, fields


def _get_dist_report_csv(ui_url_info, dist_data, report_type):
    assert report_type in ['stats', 'wide', 'long']
    if report_type == 'stats':
        return _dist_report_stats_csv(dist_data)
    elif report_type == 'wide':
        return _dist_report_wide_csv(ui_url_info, dist_data)
    elif report_type == 'long':
        return _dist_report_long_csv(ui_url_info, dist_data)


def field_responses(fields_data_by_document):
    """
    Sums the number of responses of each field present in the given collection
    (extraction batch, training set) and calculates the precentage of responses
    throughout the collection.  Makes no assumptions about which fields to include
    in it's output, all fields to include must be set in the data_by_document property
    on the model being processed.

    example output:
    {
        "Account Number": {
             "responses": 2,
             "response_rate": 100
        },
        "Line Item One": {
             "responses": 1,
             "response_rate": 50
        }
    }
    """
    stats_by_field = dict()
    for _doc, doc_data in fields_data_by_document.items():
        for field, field_data in doc_data['fields'].items():
            if field not in stats_by_field:
                stats_by_field[field] = {'responses': 0}
            if field_data['content']:
                stats_by_field[field]['responses'] += 1
    for field, data in stats_by_field.items():
        response_rate = (data['responses'] / len(fields_data_by_document)) * 100
        stats_by_field[field]['response_rate'] = response_rate
    return stats_by_field


def _dist_report_long_csv(ui_url_info, dist_report_data):
    header = ['Training Set Id', 'Training Set Label', 'Document Id', 'Document Label', 'Field Label', 'Field Tags',
              'Field Glynt Tags', 'Returned Value', ui_url_info['display']]

    with StringIO() as csvfile:
        csv_writer = csv.DictWriter(csvfile, fieldnames=header)
        csv_writer.writeheader()
        training_set_id = dist_report_data.get('training_set_id', None)
        training_set_label = dist_report_data.get('training_set_label', None)
        for doc_obf_id, doc_data in dist_report_data['documents'].items():
            for field, field_data in doc_data['fields'].items():
                row = {
                    'Training Set Id': training_set_id if training_set_id else doc_data['training_set_id'],
                    'Training Set Label': training_set_label if training_set_label else doc_data['training_set_label'],
                    'Document Id': doc_obf_id,
                    'Document Label': Document.get_object_by_obfuscated_id(doc_obf_id).label,
                    'Field Label': field,
                    'Field Tags': field_data['tags'],
                    'Field Glynt Tags': field_data['glynt_tags'],
                    'Returned Value': field_data['content'],
                    ui_url_info['display']: doc_data[ui_url_info['data_key']]
                }
                csv_writer.writerow(row)
        return csvfile.getvalue()


def _dist_report_wide_csv(ui_url_info, dist_report_data):
    header = ['Training Set Id', 'Training Set Label', 'Document Id', 'Document Label',
              *dist_report_data['fields'].keys(), ui_url_info['display']]

    with StringIO() as csvfile:
        csv_writer = csv.DictWriter(csvfile, fieldnames=header)
        csv_writer.writeheader()
        training_set_id = dist_report_data.get('training_set_id', None)
        training_set_label = dist_report_data.get('training_set_label', None)
        for doc_obf_id, doc_data in dist_report_data['documents'].items():
            row = {
                'Training Set Id': training_set_id if training_set_id else doc_data['training_set_id'],
                'Training Set Label': training_set_label if training_set_label else doc_data['training_set_label'],
                'Document Id': doc_obf_id,
                'Document Label': Document.get_object_by_obfuscated_id(doc_obf_id).label,
                ui_url_info['display']: doc_data[ui_url_info['data_key']]
            }
            for field, field_data in doc_data['fields'].items():
                row[field] = field_data['content']
            csv_writer.writerow(row)

        totals_row = {
            header[0]: 'Total Responses',
        }

        for field, field_stat in dist_report_data['fields'].items():
            totals_row[field] = field_stat['responses']
        csv_writer.writerow(totals_row)

        doc_count = dist_report_data['document_count']
        total_bills_row = {
            field: doc_count for field in dist_report_data['fields'].keys()
        }
        total_bills_row[header[0]] = 'Total Documents'
        csv_writer.writerow(total_bills_row)

        return csvfile.getvalue()


def _dist_report_stats_csv(dist_report_data):
    header = ['Field Label', 'Responses', 'Response Rate', 'Total Documents']
    with StringIO() as csvfile:
        csv_writer = csv.DictWriter(csvfile, fieldnames=header)
        csv_writer.writeheader()

        doc_count = dist_report_data['document_count']
        for field, stat in dist_report_data['fields'].items():
            row = {
                'Field Label': field,
                'Responses': stat['responses'],
                'Response Rate': stat['response_rate'],
                'Total Documents': doc_count
            }
            csv_writer.writerow(row)
        return csvfile.getvalue()
