import csv
import logging
from typing import Any, Dict
from tempfile import NamedTemporaryFile

from django.core.paginator import Paginator

from documents.models import Document
from documents.filters import DocumentManifestFilter


logger = logging.getLogger(__name__)
MANIFEST_DATE_FORMAT = "%m/%d/%Y %I:%M:%S %p"


def _generate_manifest_row(document: Document) -> Dict[str, Any]:
    """
    Return details for a documents related extractions
    """

    def _safe_value(obj, attr, default=None):
        if obj is None:
            return default

        value = getattr(obj, attr)
        if value is None:
            return default

        return value

    def _format_date(obj, attr):
        value = _safe_value(obj, attr)
        return value.strftime(MANIFEST_DATE_FORMAT) if value else None

    extractions = list(document.extractions.all())
    num_extractions = len(extractions)
    num_verified = sum([e.verified for e in extractions])
    latest_extraction = extractions[0] if extractions else None
    trainset = latest_extraction.training_set if latest_extraction else None
    batch = latest_extraction.extraction_batch if latest_extraction else None

    return {
        "org_id": document.data_pool.organization.obfuscated_id,
        "org_label": document.data_pool.organization.label,
        "data_pool_id": document.data_pool.obfuscated_id,
        "data_pool_label": document.data_pool.label,
        "document_id": document.obfuscated_id,
        "document_label": document.label,
        "inbound_path": document.inbound_path,
        "duplicate_count": max([document.duplicate_count - 1, 0]),
        "page_count": document.page_count,
        "token_count": document.token_count,
        "filesize": document.filesize,
        "format_id": document.format_id,
        "content_md5": document.content_md5,
        "content_type": document.content_type,
        "language": document.language,
        "created_at": _format_date(document, "created_at"),
        "extraction_count": num_extractions,
        "verified_extraction_count": num_verified,
        # Some of the related items may be None, so we use _safe_value()
        "latest_extraction_id": _safe_value(latest_extraction, "obfuscated_id"),
        "latest_extraction_created_at": _format_date(latest_extraction, "created_at"),
        "latest_extraction_verified": _safe_value(
            latest_extraction, "verified", default=False
        ),
        "latest_extraction_verified_at": _format_date(latest_extraction, "verified_at"),
        "latest_extraction_verify_expired": _safe_value(
            latest_extraction, "verify_expired", default=False
        ),
        "latest_extraction_training_set_id": _safe_value(trainset, "obfuscated_id"),
        "latest_extraction_training_set_label": _safe_value(trainset, "label"),
        "latest_extraction_batch_id": _safe_value(batch, "obfuscated_id"),
        "latest_extraction_batch_label": _safe_value(batch, "label"),
        "text_preview": document.text_preview,
    }


def _write_csv(queryset, csvfile, row_func):
    paginator = Paginator(queryset, 2000)
    writer = None

    for page_number in paginator.page_range:
        page = paginator.page(page_number)
        for record in page:
            row = row_func(record)
            if not writer:
                writer = csv.DictWriter(csvfile, fieldnames=row.keys())
                writer.writeheader()
            writer.writerow(row)


def generate_document_manifest_report(report):
    """Generate a manifest report for the given configuration."""

    # Filter the queryset using the provided filters
    queryset = Document.manifest_queryset.with_duplicate_counts().with_extractions()
    filterset = DocumentManifestFilter(data=report.filters, queryset=queryset).qs

    # Iterate over the queryset and write the data to a CSV file
    with NamedTemporaryFile(mode="w+", suffix=".csv", prefix="manifest_") as csvfile:
        _write_csv(filterset, csvfile, row_func=_generate_manifest_row)
        csvfile.seek(0)
        report.store_file_content(csvfile.read().encode("utf-8"))
