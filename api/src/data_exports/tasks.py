import logging

from celery import shared_task

from jobs.actions import finish_job as finish_job_action
from jobs.tasks import JobTask
from .models import Report, REPORT_TYPE_FUNCTIONS


logger = logging.getLogger(__name__)


class ReportJobTask(JobTask):
    job_cls = Report
    kwarg_job_id_name = "report_id"


@shared_task(bind=True, base=ReportJobTask)
def generate_report(self, report_id):
    logger.info(f"Generating report for {report_id}")
    report = Report.objects.get(id=report_id)

    report_function = REPORT_TYPE_FUNCTIONS.get(report.report_type)
    if not report_function:
        finish_job_action(report, "FAILED")
        raise Exception(f"No report function found for {report}")

    report.set_state("IN_PROGRESS")
    try:
        report_function(report)
        finish_job_action(report, "SUCCEEDED")
    except Exception as e:
        logger.exception(f"{report} failed with exception: {e}")
        finish_job_action(report, "FAILED")
