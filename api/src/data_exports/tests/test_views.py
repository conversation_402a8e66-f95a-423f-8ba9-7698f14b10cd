import json
from unittest import skip

from django.db.models.signals import post_save
from django.test import Client, TransactionTestCase
from django.urls import reverse

from users.tests.util import create_user, create_staff_user
from data_exports.models import Report
from data_exports.signals import create_report
from data_exports.tests.util import create_default_report
from organizations.tests.util import create_data_pool, create_organization


class ReportViewsTesetCase(TransactionTestCase):

    def setUp(self):
        super().setUp()
        self.staff_client = Client()
        self.staff_user, self.staff_user_path = create_staff_user()
        self.staff_client.login(username=self.staff_user.username, password="12345")

        self.client = Client()
        self.user, self.user_path = create_user()
        self.client.login(username=self.user.username, password="12345")

        post_save.disconnect(create_report, sender=Report)
        report_type = "document_manifest"
        self.report1, _ = create_default_report(
            report_type=report_type, filters={"data_pool": "abc123"}, requester={"id": "ABC"}
        )
        org, _ = create_organization(label="Test Org")
        self.dp, _ = create_data_pool(organization=org)
        self.report2, _ = create_default_report(
            report_type=report_type, filters={"data_pool": str(self.dp.obfuscated_id)}, requester={"id": "XYZ"}
        )

    def tearDown(self):
        post_save.connect(create_report, sender=Report)
        super().tearDown()

    def test_list_reports(self):
        url = reverse("v6:report-list")
        response = self.staff_client.get(url)
        self.assertEqual(response.status_code, 200)
        results = response.json()["results"]
        self.assertEqual(len(results), 2)

    def test_retrieve_report(self):
        report = Report.objects.first()
        url = reverse(
            "v6:report-detail", kwargs={"obfuscated_id": report.obfuscated_id}
        )
        response = self.staff_client.get(url)
        self.assertEqual(response.status_code, 200)
        results = response.json()
        self.assertEqual(results["report_type"], "document_manifest")
        self.assertEqual(results["filters"], {"data_pool": str(self.dp.obfuscated_id)})
        self.assertEqual(results["file_access_url"], None)
        self.assertEqual(results["data_pool_label"], self.dp.label)
        # Ensure that the file access url is returned when the report is in a success state
        report.set_state("SUCCEEDED")
        response = self.staff_client.get(url)
        self.assertEqual(response.status_code, 200)
        results = response.json()
        self.assertTrue(results["file_access_url"] is not None)

    def test_create_report(self):
        url = reverse("v6:report-list")
        data = {"report_type": "document_manifest", "filters": {"data_pool": "abc123"}}
        response = self.staff_client.post(
            url, data=json.dumps(data), content_type="application/json"
        )
        self.assertEqual(response.status_code, 201)
        self.assertEqual(Report.objects.count(), 3)

    def test_create_report_fails_with_no_datapool(self):
        url = reverse("v6:report-list")
        data = {"report_type": "document_manifest", "filters": {"data_pool": ""}}
        response = self.staff_client.post(
            url, data=json.dumps(data), content_type="application/json"
        )
        data = {"report_type": "document_manifest", "filters": {}}
        response = self.staff_client.post(
            url, data=json.dumps(data), content_type="application/json"
        )
        self.assertEqual(response.status_code, 400)

    def test_destroy_report(self):
        report = Report.objects.first()
        url = reverse(
            "v6:report-detail", kwargs={"obfuscated_id": report.obfuscated_id}
        )
        response = self.staff_client.delete(url)
        self.assertEqual(response.status_code, 204)
        self.assertEqual(Report.objects.count(), 1)

    @skip("sqlite doesn't support the JSON_UNQUOTE function")
    def test_user_filter(self):
        # TODO: adjust filtering approach to be compatible
        url = reverse("v6:report-list") + "?user=ABC"
        response = self.staff_client.get(url)
        self.assertEqual(response.status_code, 200)
        results = response.json()["results"]
        self.assertTrue(len(results) == 1)

    def test_staff_only_actions(self):
        # list not allowed
        url = reverse("v6:report-list")
        data = {"report_type": "document_manifest", "filters": {}}
        response = self.client.post(
            url, data=json.dumps(data), content_type="application/json"
        )
        self.assertEqual(response.status_code, 404)

        # create not allowed
        url = reverse("v6:report-list")
        data = {"report_type": "document_manifest", "filters": {"data_pool": "EE2718"}}
        response = self.client.post(
            url, data=json.dumps(data), content_type="application/json"
        )
        self.assertEqual(response.status_code, 404)

        # destroy report 1
        url = reverse(
            "v6:report-detail", kwargs={"obfuscated_id": self.report1.obfuscated_id}
        )
        response = self.client.delete(url)
        self.assertEqual(response.status_code, 404)
