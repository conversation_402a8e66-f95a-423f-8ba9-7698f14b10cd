from unittest import TestCase
from unittest.mock import patch, Mock

from data_exports.tasks import generate_report


class TestGenerateReportTask(TestCase):

    @patch("data_exports.tasks.Report")
    @patch("data_exports.tasks.REPORT_TYPE_FUNCTIONS")
    @patch("data_exports.tasks.finish_job_action")
    @patch("data_exports.tasks.ReportJobTask")
    def test_generate_report_success(
        self,
        mock_task,
        mock_finish_job_action,
        mock_report_type_functions,
        mock_report_model,
    ):
        mock_report_instance = Mock()
        mock_report_instance.report_type = "document_manifest"
        mock_report_function = Mock()
        mock_report_model.objects.get.return_value = mock_report_instance
        mock_report_type_functions.get.return_value = mock_report_function
        generate_report(report_id=1)
        mock_report_model.objects.get.assert_called_once_with(id=1)
        mock_report_type_functions.get.assert_called_once_with("document_manifest")
        mock_report_function.assert_called_once_with(mock_report_instance)
        mock_finish_job_action.assert_called_once_with(
            mock_report_instance, "SUCCEEDED"
        )

    @patch("data_exports.tasks.Report")
    @patch("data_exports.tasks.REPORT_TYPE_FUNCTIONS")
    @patch("data_exports.tasks.finish_job_action")
    def test_generate_report_failure_no_function_found(
        self, mock_finish_job_action, mock_report_type_functions, mock_report_model
    ):
        # Setting up mocks
        mock_report_instance = Mock()
        mock_report_instance.report_type = "UNKNOWN"
        mock_report_model.objects.get.return_value = mock_report_instance
        mock_report_type_functions.get.return_value = None
        # Expecting an exception due to no report function found
        with self.assertRaises(Exception) as context:
            generate_report(report_id=2)
        self.assertTrue("No report function" in str(context.exception))
        mock_finish_job_action.assert_called_with(mock_report_instance, "FAILED")
