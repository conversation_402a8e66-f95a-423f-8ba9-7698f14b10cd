from unittest.mock import patch

from django.test import TransactionTestCase

from api_storage.tests.util import patch_enqueue_delete_resource_folder
from data_exports.tests.util import create_default_report


class TestReportSignals(TransactionTestCase):
    @patch('data_exports.signals.generate_report.delay')
    @patch('data_exports.signals._process_report_post_save')
    def test_create_report_signal(self, mock_process, mock_delay):
        report, _ = create_default_report()
        mock_process.assert_called_once_with(report)

    @patch('data_exports.signals.generate_report.delay')
    def test_generate_report_task(self, mock_delay):
        report, _ = create_default_report()
        mock_delay.assert_called_once_with(report_id=report.id)

    @patch_enqueue_delete_resource_folder
    def test_delete_report_signal(self, mock_enqueue_delete_resource_folder):
        report, _ = create_default_report()
        report.delete()
        self.assertEqual(mock_enqueue_delete_resource_folder.call_count, 1)
