from typing import Optional

from cached_reverse.urls import reverse
from data_exports.models import Report


def create_default_report(
    report_type: str = "document_manifest",
    filters: Optional[dict] = None,
    requester: Optional[dict] = None,
):
    report = Report.objects.create(
        report_type=report_type, filters=filters or {}, _requester=requester or {}
    )
    report.save()
    path = reverse("v6:report-detail", kwargs={"obfuscated_id": report.obfuscated_id})

    return report, path
