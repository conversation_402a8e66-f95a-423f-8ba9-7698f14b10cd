import io
from datetime import datetime
from django.test import Test<PERSON>ase
import unittest
from unittest.mock import Mock, patch, MagicMock, PropertyMock

from data_exports.actions import (
    generate_document_manifest_report,
    _write_csv,
    _generate_manifest_row,
    MANIFEST_DATE_FORMAT,
)
from glynt_api.tests.util import MockQueryset, TestCaseMixin


class ReportActionsTestCase(TestCase, TestCaseMixin):
    def setUp(self):
        self.setUpPyfakefs()
        return super().setUp()

    def _make_document(self, text_preview="Electric bill for 1234-56291"):
        document = Mock()
        document.data_pool.organization.obfuscated_id = "org_id"
        document.data_pool.organization.label = "org_label"
        document.data_pool.obfuscated_id = "pool_id"
        document.data_pool.label = "pool_label"
        document.obfuscated_id = "doc_id"
        document.label = "doc_label"
        document.duplicate_count = 5
        document.page_count = 42
        document.token_count = 1000
        document.filesize = 1234
        document.format_id = "format"
        document.content_md5 = "md5"
        document.content_type = "content_type"
        document.language = "EN"
        document.created_at = datetime(2024, 2, 13, 3, 4, 30)
        document.inbound_path = "abc/def/123"
        document.text_content = Mock(preview=text_preview)
        document.text_preview = text_preview
        return document

    @patch("data_exports.actions.Document")
    @patch("data_exports.actions.DocumentManifestFilter")
    @patch("data_exports.actions.NamedTemporaryFile")
    @patch("data_exports.actions._write_csv")
    def test_generate_document_manifest_report(
        self, mock_write_csv, mock_temp_file, mock_filter, mock_document
    ):
        mock_qs = MagicMock()
        mock_document.manifest_queryset.with_duplicate_counts.return_value = mock_qs
        mock_qs.with_extractions.return_value = mock_qs

        mock_report = MagicMock()
        mock_report.filters = {"some_filter": "value"}

        mock_filter.return_value.qs = mock_qs

        mock_file_instance = mock_temp_file.return_value.__enter__.return_value
        mock_file_instance.read.return_value = "test,content"

        generate_document_manifest_report(mock_report)

        # Assert the calls
        mock_document.manifest_queryset.with_duplicate_counts.assert_called_once_with()
        mock_qs.with_extractions.assert_called_once_with()

        mock_filter.assert_called_once_with(data=mock_report.filters, queryset=mock_qs)
        mock_write_csv.assert_called_once_with(
            mock_qs, mock_file_instance, row_func=unittest.mock.ANY
        )
        mock_report.store_file_content.assert_called_once_with(b"test,content")

    def test_write_csv(self):
        # Setup a mock queryset
        mock_records = [Mock() for _ in range(10)]
        queryset = MockQueryset(mock_records)

        csvfile = io.StringIO()

        row_func = Mock()
        row_func.return_value = {"id": "123", "label": "utility-bill.pdf"}

        _write_csv(queryset, csvfile, row_func)

        # Assert the contents
        csvfile.seek(0)
        lines = csvfile.readlines()
        self.assertEqual(len(lines), 11)
        # Assert the header
        self.assertTrue(
            "id,label" in lines[0],
            'Expected header to be "id,label" but got "{}"'.format(lines[0]),
        )
        self.assertTrue(
            "123,utility-bill.pdf" in lines[1],
            'Expected row to be "123,utility-bill.pdf" but got "{}"'.format(lines[1]),
        )

    def test_generate_manifest_row(self):
        document = self._make_document()
        batch = Mock(obfuscated_id="batch_id", label="batch_label")
        trainset = Mock(obfuscated_id="trainset_id", label="trainset_label")

        doc_created_at = datetime(2024, 2, 13, 3, 4, 30)

        ext1_created_at = datetime(2024, 2, 14, 5, 6, 31)
        ext1_verified_at = datetime(2024, 2, 14, 5, 6, 55)

        ext2_created_at = datetime(2024, 2, 15, 5, 6, 31)
        ext2_verified_at = datetime(2024, 2, 15, 5, 6, 55)

        ext3_created_at = datetime(2024, 2, 16, 5, 6, 31)
        ext3_verified_at = datetime(2024, 2, 16, 5, 6, 55)

        ext1 = Mock(
            verified=True,
            obfuscated_id="ext_id1",
            created_at=ext1_created_at,
            verified_at=ext1_verified_at,
            verify_expired=False,
        )
        ext2 = Mock(
            verified=True,
            obfuscated_id="ext_id2",
            created_at=ext2_created_at,
            verified_at=ext2_verified_at,
            verify_expired=True,
        )
        # Most recent, but not verified
        ext3 = Mock(
            verified=False,
            obfuscated_id="ext_id3",
            created_at=ext3_created_at,
            verified_at=ext3_verified_at,
            verify_expired=True,
            training_set=trainset,
            extraction_batch=batch,
        )

        document.extractions.all.return_value = [ext3, ext2, ext1]
        document.created_at = doc_created_at

        result = _generate_manifest_row(document)

        # Assert the contents
        self.assertEqual(result["org_id"], "org_id")
        self.assertEqual(result["document_id"], "doc_id")
        self.assertEqual(
            result["duplicate_count"], 4
        )  # As original document shouldn't be counted
        self.assertEqual(result["page_count"], 42)
        self.assertEqual(result["token_count"], 1000)
        self.assertEqual(result["inbound_path"], "abc/def/123")
        self.assertEqual(result["filesize"], 1234)
        self.assertEqual(result["format_id"], "format")
        self.assertEqual(result["content_md5"], "md5")
        self.assertEqual(result["content_type"], "content_type")
        self.assertEqual(result["language"], "EN")
        self.assertEqual(
            result["created_at"], doc_created_at.strftime(MANIFEST_DATE_FORMAT)
        )
        self.assertEqual(result["extraction_count"], 3)
        self.assertEqual(result["verified_extraction_count"], 2)
        self.assertEqual(result["latest_extraction_id"], "ext_id3")
        self.assertEqual(
            result["latest_extraction_created_at"],
            ext3_created_at.strftime(MANIFEST_DATE_FORMAT),
        )
        self.assertEqual(result["latest_extraction_verified"], False)
        self.assertEqual(
            result["latest_extraction_verified_at"],
            ext3_verified_at.strftime(MANIFEST_DATE_FORMAT),
        )
        self.assertEqual(result["latest_extraction_verify_expired"], True)
        self.assertEqual(
            result["latest_extraction_training_set_id"], trainset.obfuscated_id
        )
        self.assertEqual(result["latest_extraction_training_set_label"], trainset.label)
        self.assertEqual(result["latest_extraction_batch_id"], batch.obfuscated_id)
        self.assertEqual(result["latest_extraction_batch_label"], batch.label)
        self.assertEqual(result["text_preview"], "Electric bill for 1234-56291")

    def test_generate_manifest_row_no_extractions(self):
        document = self._make_document()

        document.extractions.all.return_value = []

        result = _generate_manifest_row(document)

        # Assert the contents
        self.assertEqual(result["extraction_count"], 0)
        self.assertEqual(result["verified_extraction_count"], 0)
        self.assertEqual(result["latest_extraction_id"], None)
        self.assertEqual(result["latest_extraction_created_at"], None)
        self.assertEqual(result["latest_extraction_verified"], False)
        self.assertEqual(result["latest_extraction_verified_at"], None)
        self.assertEqual(result["latest_extraction_verify_expired"], False)
        self.assertEqual(result["latest_extraction_training_set_id"], None)
        self.assertEqual(result["latest_extraction_training_set_label"], None)
        self.assertEqual(result["latest_extraction_batch_id"], None)
        self.assertEqual(result["latest_extraction_batch_label"], None)

    def test_generate_manifest_row_no_text_content(self):
        """Test that manifest generation works when a document has no text_content."""
        # Create a real document, to ensure related text_content is handled correctly
        from documents.tests.util import create_document
        from organizations.tests.util import create_organization, create_data_pool
        org, org_path = create_organization()
        dp, dp_path = create_data_pool(org)
        doc, doc_path = create_document(data_pool=dp)

        # Mock out the extractions on the real document using patch
        mock_extractions = Mock(all=Mock(return_value=[]))
        with patch.object(doc.__class__, 'extractions',
                          new_callable=PropertyMock,
                          return_value=mock_extractions):
            doc.duplicate_count = 0
            result = _generate_manifest_row(doc)

            # Assert text_preview is None
            self.assertIsNone(result["text_preview"])
