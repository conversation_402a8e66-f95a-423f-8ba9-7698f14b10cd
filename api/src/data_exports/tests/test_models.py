from unittest.mock import MagicMock, ANY, patch

from django.test import TestCase

from glynt_api.tests.util import TestCaseMixin
from data_exports.tests.util import create_default_report


class ReportModelTestCase(TestCase, TestCaseMixin):

    def setUp(self):
        self.setUpPyfakefs()

    @patch("data_exports.signals.create_report")
    def test_file_remote_path(self, mock_create_report):
        report, _ = create_default_report()
        expected_path = f"{report.uuid}/report.csv"
        self.assertEqual(report.file_remote_path, expected_path)

    @patch("data_exports.signals.create_report")
    @patch("data_exports.models.storage_client")
    def test_store_file_content(self, mock_storage_client, mock_create_report):
        # Test that the file content is stored correctly
        report, _ = create_default_report()
        mock_storage = MagicMock()
        mock_storage_client.return_value = mock_storage
        content = b"some data"
        report.store_file_content(content)
        mock_storage.upload_fileobj.assert_called_once_with(
            bucket=report.bucket_name, remote_path=report.file_remote_path, fileobj=ANY
        )
