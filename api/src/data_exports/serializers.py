import logging

from rest_framework import serializers

from glynt_api.serializers import HyperlinkedModelSerializer
from glynt_api.util import build_absolute_uri
from cached_reverse.urls import reverse
from .models import Report, REPORT_TYPE_FUNCTIONS
from organizations.models import DataPool


logger = logging.getLogger(__name__)


class ReportFileSerializer(HyperlinkedModelSerializer):
    class Meta:
        model = Report
        fields = ("file_temp_url",)
        read_only_fields = ("file_temp_url",)


class ReportSerializer(HyperlinkedModelSerializer):
    requester = serializers.SerializerMethodField()
    id = serializers.CharField(source="obfuscated_id", read_only=True)
    filters = serializers.JSONField()

    status = serializers.SerializerMethodField()
    file_access_url = serializers.SerializerMethodField()
    data_pool_label = serializers.SerializerMethodField()

    class Meta:
        model = Report
        fields = [
            "id",
            "report_type",
            "status",
            "data_pool_label",
            "file_access_url",
            "filters",
            "requester",
            "created_at",
        ]
        read_only_fields = ["id", "status", "file_access_url", "requester", "created_at", "data_pool_label"]

    def get_status(self, obj):
        return obj.get_status_display

    def get_data_pool_label(self, obj):
        obfuscated_id = obj.filters.get("data_pool")
        if not obfuscated_id:
            return None

        try:
            data_pool = DataPool.get_object_by_obfuscated_id(obfuscated_id)
            return data_pool.label
        except DataPool.DoesNotExist:
            return None

    def get_filters(self, obj):
        return obj.filters

    def get_file_access_url(self, obj):
        """Return the url to retrieve a temp file access url."""
        # Get the status first, if success then return the file access url
        if obj.succeeded:
            url_name = "{}:report-file".format(self.context["request"].version)
            path = reverse(url_name, kwargs={"obfuscated_id": obj.obfuscated_id})
            return build_absolute_uri(self.context["request"], path)
        else:
            return None

    def get_requester(self, obj):
        # Assuming the `requester` method returns a serializable dictionary
        return obj.requester if hasattr(obj, "requester") else None

    def validate_report_type(self, value):
        """
        Check that the report_type is listed in the REPORT_TYPE_FUNCTIONS dict.
        """
        valid_types = list(REPORT_TYPE_FUNCTIONS.keys())
        if value not in valid_types:
            raise serializers.ValidationError(
                f"The report type {value} is not supported. Please use one of {valid_types}."
            )
        return value

    def validate_filters(self, value):
        """
        Ensure 'filters' contains a 'data_pool' key with a non-empty value.
        """
        if not isinstance(value, dict):
            raise serializers.ValidationError("Filters must be a valid JSON object.")

        # NOTE: the data_pool guard here is to prevent an export of all records,
        # which could be taxing on the system.  This may be modified per report type
        # or relaxed in the future.
        if 'data_pool' not in value:
            raise serializers.ValidationError("The 'data_pool' key is required in filters.")

        if not value['data_pool']:
            raise serializers.ValidationError("The 'data_pool' key must not be empty.")

        return value
