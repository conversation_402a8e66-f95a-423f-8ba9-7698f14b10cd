# Generated by Django 2.2.28 on 2024-05-03 20:47

from django.db import migrations, models
import jsonfield.fields
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Report",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("_persistent_id_map", jsonfield.fields.JSONField(default=dict)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("uuid", models.UUIDField(default=uuid.uuid4, editable=False)),
                ("report_type", models.<PERSON>r<PERSON><PERSON>(max_length=255)),
                ("filters", jsonfield.fields.J<PERSON><PERSON>ield(default=dict)),
                ("_requester", jsonfield.fields.JSONField(default=dict)),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
    ]
