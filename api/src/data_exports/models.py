import logging
from io import Bytes<PERSON>

from django.db import models
from jsonfield import <PERSON><PERSON><PERSON><PERSON>

from glynt_api.decorators import persistent_id_aware_property
from glynt_api.mixins import UUIDMixin
from glynt_api.models import Model
from jobs.mixins import JobMixin
from api_storage import storage_client
from . import actions


logger = logging.getLogger(__name__)

REPORT_TYPE_FUNCTIONS = {
    "document_manifest": actions.generate_document_manifest_report,
}


class Report(UUIDMixin, JobMixin, Model):

    report_type = models.CharField(max_length=255)
    filters = J<PERSON><PERSON>ield()

    # Track requester to allow filtering by user
    _requester = <PERSON><PERSON><PERSON>ield()

    @persistent_id_aware_property
    def requester(self):
        pass

    @property
    def file_remote_path(self):
        """Generates and returns the remote path where the file content will be
        stored in the storage service."""
        return f"{self.uuid}/report.csv"

    def store_file_content(self, content):
        """Store report body content using the file_storage_client(). Content
        should be a byte array.
        """
        storage = storage_client()
        storage.upload_fileobj(
            bucket=self.bucket_name,
            remote_path=self.file_remote_path,
            fileobj=BytesIO(content),
        )

    @property
    def file_temp_url(self):
        """Generates and returns a temporary presigned url to GET the Report
        file content. This is the url that is exposed to users to access their
        reports."""
        storage = storage_client()
        return storage.generate_presigned_get_url(
            bucket=self.bucket_name,
            remote_path=self.file_remote_path,
            valid_for=60 * 60,
        )

    @property
    def file_exists(self):
        storage = storage_client()
        return storage.check_object_exists(self.bucket_name, self.file_remote_path)

    @property
    def file_content(self):
        storage = storage_client()
        doc_obj = storage.download_fileobj(self.bucket_name, self.file_remote_path)
        return doc_obj.read()

    class Meta:
        ordering = ["-created_at"]
