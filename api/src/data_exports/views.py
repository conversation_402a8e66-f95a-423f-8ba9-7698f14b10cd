from logging import getLogger
from rest_framework.decorators import action
from django_filters import <PERSON><PERSON><PERSON><PERSON><PERSON>
from django.db.models import Subquery, Func, F, Value, CharField
from glynt_api.viewsets import ModelViewSet
from glynt_api.mixins import (
    CreatedAtUpdatedAtFilterMixin,
    StaffOnlyCreateMixin,
    StaffOnlyListMixin,
    StaffOnlyDestroyMixin,
    StaffOnlyRetrieveMixin
)
from .models import Report
from .serializers import ReportSerializer, ReportFileSerializer
from users.util import requester_to_dict
from jobs import display_to_states


logger = getLogger(__name__)


class JSONUnquote(Func):
    # Current version of django does not seem to support JSON field querying
    # directly. This is a workaround/temp solution enable querying JSON fields.
    function = "JSON_UNQUOTE"
    template = "%(function)s(%(expressions)s)"


class ReportFilterSet(CreatedAtUpdatedAtFilterMixin):

    status = CharFilter(method="status_filter", label="Status")
    user = CharFilter(method="user_filter", label="User ID")

    class Meta:
        model = Report
        fields = []

    def status_filter(self, queryset, field_name, value):
        """Filter for State choices based on their string rep"""
        filter_states = display_to_states(value)

        return queryset.annotate(
            _latest_state=Subquery(Report.get_states_subquery().values("state")[:1])
        ).filter(_latest_state__in=filter_states)

    def user_filter(self, queryset, field_name, value):
        # Annotate the queryset with the 'id' extracted and unquoted from the JSON
        queryset = queryset.annotate(
            user_id=JSONUnquote(
                Func(
                    F("_requester"),
                    Value("$.id"),
                    function="JSON_EXTRACT",
                    output_field=CharField(),
                )
            )
        )
        # Now perform a filter on the annotated field
        return queryset.filter(user_id__contains=value)


class ReportViewSet(
    StaffOnlyListMixin,
    StaffOnlyRetrieveMixin,
    StaffOnlyCreateMixin,
    StaffOnlyDestroyMixin,
    ModelViewSet,
):
    queryset = Report.objects.all()
    serializer_class = ReportSerializer
    basic_views = ["list", "retrieve", "create", "destroy"]
    filterset_class = ReportFilterSet

    @action(detail=True)
    def file(self, request, *args, **kwargs):
        """Return a temporary file access URL which can be used to retrieve the document file."""
        self.serializer_class = ReportFileSerializer
        return super().retrieve(request, *args, **kwargs)

    def perform_create(self, serializer):
        serializer.save(requester=requester_to_dict(self.request.user))

    def get_queryset(self):
        qs = super().get_queryset()
        return qs
