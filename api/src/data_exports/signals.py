import logging

from django.db.models.signals import post_save, post_delete
from django.db.transaction import on_commit
from django.dispatch import receiver

from api_storage.signals import delete_storage_artifacts
from .tasks import generate_report
from .models import Report

logger = logging.getLogger(__name__)


@receiver(post_save, sender=Report)
def create_report(sender, instance, created, **kwargs):
    """Starts a report job on creation if its status is not terminal.

    NOTE: Due to the use of on_commit, this signal will only fire in unit tests
    if TransactionTestCase is used instead of a standard TestCase.
    """
    if created and not instance.finished:
        _process_report_post_save(instance)


def _process_report_post_save(report):
    """Submethod used for test mocking"""
    report.set_state("PENDING")
    on_commit(lambda: generate_report.delay(report_id=report.id))


post_delete.connect(delete_storage_artifacts, sender=Report)
