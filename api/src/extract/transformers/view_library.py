from extract.transformers.views import (
    view_base_utility, view_fll2, view_fll3, view_fll3_expanded, view_umh
)

# Each published view is defined in this dictionary,
# so you must add an entry for each view.
VIEWS = {
    'UMH': view_umh.QUERY,
    'FLL2': view_fll2.QUERY,
    'FLL3': view_fll3.QUERY,
    'FLL3_EXPANDED': view_fll3_expanded.QUERY,
    'BASE_UTILITY': view_base_utility.BASE_UTILITY_QUERY,
}

DOC_ENRICHMENT_COLUMNS = {
    'UMH': view_umh.UMH_DOC_ENRICHMENT_COLUMNS
}
