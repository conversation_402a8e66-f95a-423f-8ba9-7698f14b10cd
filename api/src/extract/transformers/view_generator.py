import logging
from datetime import datetime
from typing import List

from django.core.exceptions import ObjectDoesNotExist

from documents.enrichment_joiner import DocEnrichment<PERSON>oiner
from rest_framework.exceptions import ValidationError

from document_data.db_client import SqlDBClient
from extract.transformers import view_library


ASSIGN_TAG = 'assign'
ADDITIONAL_FIELDS_QUERY = "SELECT * FROM document_data_additionalfield WHERE document_id = {doc_id}"

logger = logging.getLogger(__name__)


class TransformedViewGenerator:
    def __init__(self, extraction, view_name):
        self._extraction = extraction
        self._view_name = view_name.upper()
        self._db_client = SqlDBClient()

    def run(self):
        return self._generate_transformed_view()

    def _generate_transformed_view(self):
        # The view will be in the format of "view:<view_name>"
        view_def = view_library.VIEWS.get(self._view_name)
        if view_def:
            # Assert that the view def contains a doc_id so that we can filter by
            # our document.
            if "{doc_id}" not in view_def:
                raise ValidationError('View definition must contain "{doc_id}" for document filtering')

            fmt_kwargs = {
                'doc_id': self._extraction.document_id
            }

            if "{doc_enrichments_select}" in view_def:
                fmt_kwargs['doc_enrichments_select'] = self._get_doc_enrichments_select(
                    view_library.DOC_ENRICHMENT_COLUMNS.get(self._view_name, None)
                )

            query = view_def.format(**fmt_kwargs)

            logger.debug(f'Running view query "{query}"')
            results = self._db_client.execute(query)

            # Append any assigned fields
            self._inject_assigned_data(results)

            # Append standard columns
            self._inject_metadata_columns(results)

            return results or []

        return []

    def _get_doc_enrichments_select(self, doc_enrichment_columns: List[str]):
        doc_enrichment_data = self._get_related_enrichment_row(self._extraction.document) or {}

        enrichment_columns = doc_enrichment_columns or doc_enrichment_data.keys()
        select = ", ".join(f'"{doc_enrichment_data.get(c, "")}" {c}' for c in enrichment_columns)
        if select:
            return select + ", "
        return ""

    def _get_related_enrichment_row(self, doc):
        """Look for an explicit relationship, if not, run the join rules to find a matching enrichment."""
        try:
            return doc.enrichments.data
        except (AttributeError, ObjectDoesNotExist):
            try:
                join_config = doc.data_pool.config.doc_enrich_join_config
            except AttributeError:
                join_config = None
            joiner = DocEnrichmentJoiner(join_config)
            enrichment_row = joiner.get_enrichments_row(doc)
            if enrichment_row:
                return enrichment_row.data
            return None

    def _inject_metadata_columns(self, data):
        for row in data or []:
            row['DocumentID'] = self._extraction.document.obfuscated_id
            row['DocumentLabel'] = self._extraction.document.label
            row['Timestamp'] = TransformedViewGenerator.get_timestamp()

    def _inject_assigned_data(self, data):
        from document_data.models import AdditionalField  # Importing here to avoid circular import

        # Get the training revision for this extraction. It will define all assigned fields.
        revision = self._extraction.training_revision

        # Enumerate all fields with an assign tag
        assigned_fields = {}
        for f in revision.baked_fields.all():
            for tag in list(f.glynt_tags.names()):
                if ASSIGN_TAG in tag:
                    # The `assign` tag can be either "assign" or "assign:name"
                    # If "name" is specified, that is the intended output field name.
                    # Otherwise, the label of the input field is the intended output field name.
                    tag, *output_field_name = tag.split(':')
                    output_field_name = output_field_name[0] if output_field_name else f.label
                    assigned_fields[f.label] = output_field_name

                    # Initialize this column with blank data
                    for row in data:
                        row[output_field_name] = ''

        for field in AdditionalField.objects.filter(document_id=self._extraction.document.id):
            if field.label in assigned_fields:
                output_field_name = assigned_fields[field.label]
                for row in data:
                    row[output_field_name] = field.value

    @staticmethod
    def get_timestamp():
        """Returns timestamp of when transformation occurred"""
        now = datetime.now()
        dt_string = now.strftime("%Y-%m-%dT%H:%M:%SZ")

        return dt_string
