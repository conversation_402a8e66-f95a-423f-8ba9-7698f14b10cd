# -*- coding: utf-8 -*-
# Generated by Django 1.11.21 on 2019-07-11 17:11
from __future__ import unicode_literals

import django.db.models.deletion
import jsonfield.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('api_tests', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='CascadeDeleteTestModel',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('_persistent_id_map', jsonfield.fields.JSONField(default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('parent', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='api_tests.APITestModel')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
