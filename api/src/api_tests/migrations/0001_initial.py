# -*- coding: utf-8 -*-
# Generated by Django 1.11.21 on 2019-06-13 22:15
from __future__ import unicode_literals

import jsonfield.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='PersistentIDAwareModelMixinTestModel',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('_persistent_id_map', jsonfield.fields.J<PERSON><PERSON>ield(default=dict)),
                ('_persistent_id_aware_charfield', models.CharField(blank=True, max_length=63)),
                ('_persistent_id_aware_jsonfield', jsonfield.fields.J<PERSON>NField(default=dict)),
                ('regular_charfield', models.CharField(blank=True, max_length=63)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='APITestModel',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('_persistent_id_map', jsonfield.fields.JSONField(default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
