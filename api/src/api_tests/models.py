from django.db import models
from jsonfield import <PERSON><PERSON><PERSON><PERSON>

from glynt_api.decorators import persistent_id_aware_property
from glynt_api.models import Model, PersistentIDAwareModelMixin


class PersistentIDAwareModelMixinTestModel(PersistentIDAwareModelMixin):

    _persistent_id_aware_charfield = models.Char<PERSON>ield(max_length=63, blank=True)

    @persistent_id_aware_property
    def persistent_id_aware_charfield(self):
        pass

    _persistent_id_aware_jsonfield = JSO<PERSON>ield()

    @persistent_id_aware_property
    def persistent_id_aware_jsonfield(self):
        pass

    regular_charfield = models.CharField(max_length=63, blank=True)


class APITestModel(Model):
    pass


class CascadeDeleteTestModel(Model):
    parent = models.ForeignKey(
        APITestModel,
        on_delete=models.CASCADE
    )
