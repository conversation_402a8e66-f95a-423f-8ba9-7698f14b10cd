# App Usage

This app contains what can be thought of as fixtures or reference
implementations of the features added in the `glynt_api` app, and where we must
test that several apps are working together as expected.

## Adding Migrations

To create additional migrations for this app, first add `api_tests` to the
`glynt_api.settings.INSTALLED_APPS` settings. Then run `manage.py
makemigrations` inside the dev vm. Finally, remove `api_tests` from settings.
