import logging

from django import forms
from django.utils.translation import gettext_lazy as _

from .models import Status

logger = logging.getLogger(__name__)


class ActivateMaintenanceModeForm(forms.Form):
    MAX_DURATION = 2147483647

    error_messages = {
        'invalid_units': _("Invalid duration units."),
        'duration_too_long': _(
            "Expected duration too long. Keep it under"
            " {} seconds.".format(MAX_DURATION)
        ),
    }

    class Meta:
        model = Status
        fields = ()

    expected_duration = forms.IntegerField(
        required=False,
        min_value=0,
        max_value=MAX_DURATION,
        help_text=_(
            "Optional. If set, a Retry-after header will be "
            "added to 503 response in maintenance mode. Expects positive"
            " integer."
        )
    )

    HOUR = 3600
    MINUTE = 60
    SECOND = 1
    UNIT_CHOICES = (
        (HOUR, 'Hour'),
        (MINUTE, 'Minute'),
        (SECOND, 'Second'),
    )
    duration_unit = forms.ChoiceField(
        required=True,
        initial=HOUR,
        widget=forms.RadioSelect(
            attrs={'class': 'Radio'}
        ),
        choices=UNIT_CHOICES,
    )

    def clean_duration_unit(self):
        try:
            return int(self.cleaned_data['duration_unit'])
        except ValueError:
            raise forms.ValidationError(
                self.error_messages['invalid_units'],
                code='invalid_units',
            )

    def clean(self):
        super().clean()

        expected_duration = self.cleaned_data.get('expected_duration')
        duration_unit = self.cleaned_data.get('duration_unit')
        if expected_duration and duration_unit:
            seconds = expected_duration * duration_unit
            # don't exceed django models.PositiveIntegerField safe value
            if seconds > self.MAX_DURATION:
                raise forms.ValidationError(
                    self.error_messages['duration_too_long'],
                    code='duration_too_long',
                )
            self.cleaned_data['expected_duration_seconds'] = seconds

        return self.cleaned_data
