import logging
import unittest
from datetime import datetime, timedelta
from unittest.mock import patch

from django.test import Client, TransactionTestCase
from pytz import timezone

from business_reporting.actions import (
    build_usage_report_data, get_event_details, get_event_set,
    get_extraction_event_details, get_training_event_details,
    get_usage_report_header_row
)
from documents.tests.util import create_document
from event_log.tests.util import create_extraction_event, create_training_event
from extract.tests.util import create_extraction
from glynt_api.tests.util import TestCaseMixin
from organizations.tests.util import create_data_pool, create_organization
from training.tests.util import (
    create_training_ready_training_set, create_training_revision
)
from users.tests.util import create_staff_user, create_user

logger = logging.getLogger(__name__)


@unittest.skip('GL-2539 these tests are flakey')
class GenerateExtractionUsageReportTestCase(
    TransactionTestCase,
    TestCaseMixin
):
    def setUp(self):
        self.setUpPyfakefs()

        self.staff_user, self.staff_user_path = create_staff_user()

        self.staff_client = Client()
        self.staff_client.login(username=self.staff_user.username, password='12345')

        self.user, self.user_path = create_user()
        self.client = Client()
        self.client.login(username='normaluser', password='12345')

        self.org, self.org_path = create_organization()
        self.dp, self.dp_path = create_data_pool(organization=self.org)

        self.user.organizations.set([self.org])
        self.user.data_pools.set([self.dp])
        self.user.save()

        self.doc, self.doc_path = create_document(data_pool=self.dp)
        self.ts, self.ts_path = create_training_ready_training_set(data_pool=self.dp)
        self.tr, self.tr_path = create_training_revision(
            data_pool=self.dp, training_set=self.ts
        )
        self.ex, self.ex_path = create_extraction(
            data_pool=self.dp, training_revision=self.tr, document=self.doc
        )

        self.ex_ev = create_extraction_event(
            organization=self.org,
            data_pool=self.dp,
            requester=self.user,
            extraction=self.ex
        )

        self.tr_ev = create_training_event(
            organization=self.org,
            data_pool=self.dp,
            requester=self.user,
            training_revision=self.tr
        )

        self.expected_ex_rows = [
            'Organization ID', 'Organization Label',
            'Data Pool ID', 'Data Pool Label',
            'Extraction Batch ID', 'Extraction Batch Label', 'Extraction Batch Deleted',
            'Extraction ID', 'Extraction Status', 'Extraction Deleted',
            'Page Count', 'Document ID', 'Document Label',
            'OCR Engine', 'Timestamp'
        ]

        self.expected_tr_rows = [
            'Organization ID', 'Organization Label',
            'Data Pool ID', 'Data Pool Label',
            'Training Set ID', 'Training Set Label', 'Training Set Deleted',
            'Training Revision ID', 'Training Revision Label',
            'Training Revision Status', 'Training Revision Number',
            'Training Revision Deleted', 'Field Count', 'Field Names',
            'Document Count', 'Document Labels', 'OCR Engine', 'Timestamp'
        ]

    def test_build_usage_report_data_for_extraction(self):
        start_date = datetime.now() - timedelta(days=1)
        end_date = datetime.now() + timedelta(days=1)
        report_data = build_usage_report_data(
            'extraction',
            start_date,
            end_date
        )
        self.assertEqual(len(report_data), 1)
        self.assertSetEqual(
            set(self.expected_ex_rows),
            set(report_data[0].keys())
        )
        self.assertEqual(report_data[0]['Organization ID'], self.org.obfuscated_id)
        self.assertEqual(report_data[0]['Organization Label'], self.org.label)
        self.assertEqual(report_data[0]['Data Pool ID'], self.dp.obfuscated_id)
        self.assertEqual(report_data[0]['Data Pool Label'], self.dp.label)
        self.assertEqual(report_data[0]['Extraction Batch ID'], '')
        self.assertEqual(report_data[0]['Extraction Batch Label'], '')
        self.assertIsNone(report_data[0]['Extraction Batch Deleted'])
        self.assertEqual(report_data[0]['Extraction ID'], self.ex.obfuscated_id)
        self.assertEqual(report_data[0]['Extraction Status'], 'Success')
        self.assertFalse(report_data[0]['Extraction Deleted'])
        self.assertEqual(report_data[0]['Page Count'], self.ex.tokenized.n_pages)
        self.assertEqual(report_data[0]['Document ID'], self.doc.obfuscated_id)
        self.assertEqual(report_data[0]['Document Label'], self.doc.label)
        self.assertEqual(
            report_data[0]['Timestamp'],
            self.ex_ev.created_at.astimezone(timezone('US/Pacific'))
        )

    def test_build_usage_report_data_for_training(self):
        start_date = datetime.now() - timedelta(days=1)
        end_date = datetime.now() + timedelta(days=1)
        report_data = build_usage_report_data(
            'training_revision',
            start_date,
            end_date
        )
        self.assertEqual(len(report_data), 1)
        self.assertSetEqual(
            set(self.expected_tr_rows),
            set(report_data[0].keys())
        )
        logger.debug(f"Report data: {report_data}")
        self.assertEqual(report_data[0]['Organization ID'], self.org.obfuscated_id)
        self.assertEqual(report_data[0]['Organization Label'], self.org.label)
        self.assertEqual(report_data[0]['Data Pool ID'], self.dp.obfuscated_id)
        self.assertEqual(report_data[0]['Data Pool Label'], self.dp.label)
        self.assertEqual(report_data[0]['Training Set ID'], self.ts.obfuscated_id)
        self.assertEqual(report_data[0]['Training Set Label'], self.ts.label)
        self.assertFalse(report_data[0]['Training Set Deleted'])
        self.assertEqual(report_data[0]['Training Revision ID'], self.tr.obfuscated_id)
        self.assertEqual(report_data[0]['Training Revision Label'], self.tr.label)
        self.assertEqual(report_data[0]['Training Revision Status'], 'Success')
        self.assertEqual(report_data[0]['Training Revision Number'], 1)
        self.assertFalse(report_data[0]['Training Revision Deleted'])
        self.assertEqual(report_data[0]['Field Count'], self.tr.baked_fields.count())
        self.assertEqual(
            report_data[0]['Field Names'],
            list(self.tr.baked_fields.values_list('label', flat=True))
        )
        self.assertEqual(report_data[0]['Document Count'], self.tr.documents.count())
        self.assertEqual(
            report_data[0]['Document Labels'],
            list(self.tr.documents.values_list('label', flat=True))
        )
        self.assertEqual(
            report_data[0]['Timestamp'],
            self.tr_ev.created_at.astimezone(timezone('US/Pacific'))
        )

    @patch('business_reporting.actions.get_extraction_event_details')
    @patch('business_reporting.actions.get_training_event_details')
    def test_get_event_details_for_extraction_event(self, mock_gted, mock_geed):
        get_event_details('extraction', {})
        mock_geed.assert_called()
        mock_gted.assert_not_called()

    @patch('business_reporting.actions.get_extraction_event_details')
    @patch('business_reporting.actions.get_training_event_details')
    def test_get_event_details_for_training_event(self, mock_gted, mock_geed):
        get_event_details('training_revision', {})
        mock_geed.assert_not_called()
        mock_gted.assert_called()

    @patch('business_reporting.actions.get_extraction_event_details')
    @patch('business_reporting.actions.get_training_event_details')
    def test_get_event_details_for_unknown_event(self, mock_gted, mock_geed):
        details = get_event_details('unknown', {})
        mock_geed.assert_not_called()
        mock_gted.assert_not_called()
        self.assertDictEqual(details, {})

    def test_get_event_set_for_extraction_event(self):
        start_date = datetime.now() - timedelta(days=1)
        end_date = datetime.now() + timedelta(days=1)
        event_set = get_event_set('extraction', start_date, end_date)
        self.assertEqual(event_set.count(), 1)
        ev = event_set.first()
        self.assertEqual(ev.type, self.ex_ev.type)
        self.assertEqual(ev.organization_id, self.ex_ev.organization_id)
        self.assertEqual(ev.data_pool_id, self.ex_ev.data_pool_id)
        self.assertEqual(ev.user_id, self.ex_ev.user_id)
        self.assertIsNone(ev.integration_id)
        self.assertDictEqual(ev.details, self.ex_ev.details)

    def test_get_event_set_for_training_event(self):
        start_date = datetime.now() - timedelta(days=1)
        end_date = datetime.now() + timedelta(days=1)
        event_set = get_event_set('training_revision', start_date, end_date)
        self.assertEqual(event_set.count(), 1)
        ev = event_set.first()
        self.assertEqual(ev.type, self.tr_ev.type)
        self.assertEqual(ev.organization_id, self.tr_ev.organization_id)
        self.assertEqual(ev.data_pool_id, self.tr_ev.data_pool_id)
        self.assertEqual(ev.user_id, self.tr_ev.user_id)
        self.assertIsNone(ev.integration_id)
        self.assertDictEqual(ev.details, self.tr_ev.details)

    def test_get_event_set_all_day(self):
        pt = timezone('US/Pacific')
        utc = timezone('UTC')
        start_date = datetime.now(tz=pt) - timedelta(days=1)
        end_date = datetime.now(tz=pt) + timedelta(days=1)
        event_date = datetime.now(tz=pt).replace(hour=12) - timedelta(days=1)
        new_event = create_extraction_event(
            organization=self.org,
            data_pool=self.dp,
            requester=self.user,
            extraction=self.ex
        )
        new_event.created_at = event_date.astimezone(utc)
        new_event.save()
        event_set = get_event_set('extraction', start_date, end_date)
        self.assertNotIn(new_event, event_set)
        event_set = get_event_set('extraction', start_date, end_date, all_day=True)
        self.assertIn(new_event, event_set)

    def test_get_event_set_filter_organizations(self):
        new_org, _ = create_organization(label="New Test Org")
        new_event = create_extraction_event(
            organization=new_org,
            data_pool=self.dp,
            requester=self.user,
            extraction=self.ex
        )
        start_date = datetime.now() - timedelta(days=1)
        end_date = datetime.now() + timedelta(days=1)
        event_set = get_event_set(
            'extraction',
            start_date,
            end_date,
            orgs=[new_org.obfuscated_id]
        )
        self.assertIn(new_event, event_set)
        self.assertNotIn(self.ex_ev, event_set)

    def test_get_event_set_filter_data_pools(self):
        new_dp, _ = create_data_pool(
            label="New Test Data Pool",
            organization=self.org
        )
        new_event = create_extraction_event(
            organization=self.org,
            data_pool=new_dp,
            requester=self.user,
            extraction=self.ex
        )
        start_date = datetime.now() - timedelta(days=1)
        end_date = datetime.now() + timedelta(days=1)
        event_set = get_event_set(
            'extraction',
            start_date,
            end_date,
            pools=[new_dp.obfuscated_id]
        )
        self.assertIn(new_event, event_set)
        self.assertNotIn(self.ex_ev, event_set)

    def test_get_extraction_event_details_action(self):
        details = get_extraction_event_details(self.ex_ev.details)
        self.assertEqual(details['Extraction Batch ID'], '')
        self.assertEqual(details['Extraction Batch Label'], '')
        self.assertIsNone(details['Extraction Batch Deleted'])
        self.assertEqual(details['Extraction ID'], self.ex.obfuscated_id)
        self.assertEqual(details['Extraction Status'], 'Success')
        self.assertFalse(details['Extraction Deleted'])
        self.assertEqual(details['Page Count'], self.ex.tokenized.n_pages)
        self.assertEqual(details['Document ID'], self.doc.obfuscated_id)
        self.assertEqual(details['Document Label'], self.doc.label)

    def test_get_training_event_details_action(self):
        details = get_training_event_details(self.tr_ev.details)
        self.assertEqual(details['Training Set ID'], self.ts.obfuscated_id)
        self.assertEqual(details['Training Set Label'], self.ts.label)
        self.assertFalse(details['Training Set Deleted'])
        self.assertEqual(details['Training Revision ID'], self.tr.obfuscated_id)
        self.assertEqual(details['Training Revision Label'], self.tr.label)
        self.assertEqual(details['Training Revision Status'], 'Success')
        self.assertFalse(details['Training Revision Deleted'])
        self.assertEqual(details['Field Count'], self.tr.baked_fields.count())
        self.assertEqual(
            details['Field Names'],
            list(self.tr.baked_fields.values_list('label', flat=True))
        )
        self.assertEqual(details['Document Count'], self.tr.documents.count())
        self.assertEqual(
            details['Document Labels'],
            list(self.tr.documents.values_list('label', flat=True))
        )

    def test_get_usage_report_header_ro_action(self):
        ex_header = get_usage_report_header_row('extraction')
        self.assertListEqual(self.expected_ex_rows, ex_header)
        tr_header = get_usage_report_header_row('training_revision')
        self.assertListEqual(self.expected_tr_rows, tr_header)
