import logging
from datetime import datetime, timedelta
from unittest.mock import patch

from django.test import TransactionTestCase
from pytz import timezone

from business_reporting.tasks import generate_usage_report
from glynt_api.tests.util import TestCaseMixin

logger = logging.getLogger(__name__)


class GenerateUsageReportTaskTestCase(TransactionTestCase, TestCaseMixin):
    def setUp(self):
        self.setUpPyfakefs()

    @patch('business_reporting.tasks.datetime')
    @patch('business_reporting.tasks.build_usage_report_data')
    @patch('business_reporting.tasks.upload_file_to_slack_action')
    def test_generate_daily_extraction_usage_report_task(
        self,
        mock_upload_file,
        mock_build_report,
        mock_datetime
    ):
        now = datetime.now(tz=timezone('US/Pacific'))
        start_date = now - timedelta(days=1)
        end_date = now
        mock_datetime.now.return_value = now
        generate_usage_report('extraction', 'Daily')
        mock_build_report.assert_called_with(
            'extraction',
            start_date,
            end_date,
            all_day=False
        )
        mock_upload_file.assert_called()

    @patch('business_reporting.tasks.datetime')
    @patch('business_reporting.tasks.build_usage_report_data')
    @patch('business_reporting.tasks.upload_file_to_slack_action')
    def test_generate_daily_training_usage_report_task(
        self,
        mock_upload_file,
        mock_build_report,
        mock_datetime
    ):
        now = datetime.now(tz=timezone('US/Pacific'))
        start_date = now - timedelta(days=1)
        end_date = now
        mock_datetime.now.return_value = now
        generate_usage_report('training_revision', 'Daily')
        mock_build_report.assert_called_with(
            'training_revision',
            start_date,
            end_date,
            all_day=False
        )
        mock_upload_file.assert_called()

    @patch('business_reporting.tasks.datetime')
    @patch('business_reporting.tasks.build_usage_report_data')
    @patch('business_reporting.tasks.upload_file_to_slack_action')
    def test_generate_weekly_extraction_usage_report_task(
        self,
        mock_upload_file,
        mock_build_report,
        mock_datetime
    ):
        now = datetime.now(tz=timezone('US/Pacific'))
        start_date = now - timedelta(days=7)
        end_date = now
        mock_datetime.now.return_value = now
        generate_usage_report('extraction', 'Weekly')
        mock_build_report.assert_called_with(
            'extraction',
            start_date,
            end_date,
            all_day=False
        )
        mock_upload_file.assert_called()

    @patch('business_reporting.tasks.datetime')
    @patch('business_reporting.tasks.build_usage_report_data')
    @patch('business_reporting.tasks.upload_file_to_slack_action')
    def test_generate_weekly_training_usage_report_task(
        self,
        mock_upload_file,
        mock_build_report,
        mock_datetime
    ):
        now = datetime.now(tz=timezone('US/Pacific'))
        start_date = now - timedelta(days=7)
        end_date = now
        mock_datetime.now.return_value = now
        generate_usage_report('training_revision', 'Weekly')
        mock_build_report.assert_called_with(
            'training_revision',
            start_date,
            end_date,
            all_day=False
        )
        mock_upload_file.assert_called()

    @patch('business_reporting.tasks.datetime')
    @patch('business_reporting.tasks.build_usage_report_data')
    @patch('business_reporting.tasks.upload_file_to_slack_action')
    def test_generate_monthly_extraction_usage_report_task(
        self,
        mock_upload_file,
        mock_build_report,
        mock_datetime
    ):
        now = datetime.now(tz=timezone('US/Pacific'))
        start_date = now.replace(day=1) - timedelta(days=2)
        start_date = start_date.replace(day=1)
        end_date = now.replace(day=1)
        mock_datetime.now.return_value = now
        generate_usage_report('extraction', 'Monthly')
        mock_build_report.assert_called_with(
            'extraction',
            start_date,
            end_date,
            all_day=True
        )
        mock_upload_file.assert_called()

    @patch('business_reporting.tasks.datetime')
    @patch('business_reporting.tasks.build_usage_report_data')
    @patch('business_reporting.tasks.upload_file_to_slack_action')
    def test_generate_monthly_training_usage_report_task(
        self,
        mock_upload_file,
        mock_build_report,
        mock_datetime
    ):
        now = datetime.now(tz=timezone('US/Pacific'))
        start_date = now.replace(day=1) - timedelta(days=2)
        start_date = start_date.replace(day=1)
        end_date = now.replace(day=1)
        mock_datetime.now.return_value = now
        generate_usage_report('training_revision', 'Monthly')
        mock_build_report.assert_called_with(
            'training_revision',
            start_date,
            end_date,
            all_day=True
        )
        mock_upload_file.assert_called()

    @patch('business_reporting.tasks.build_usage_report_data')
    @patch('business_reporting.tasks.upload_file_to_slack_action')
    def test_generate_unknown_usage_report_task(
        self,
        mock_upload_file,
        mock_build_report
    ):
        generate_usage_report('foo', 'unknown')
        mock_build_report.assert_not_called()
        mock_upload_file.assert_not_called()
