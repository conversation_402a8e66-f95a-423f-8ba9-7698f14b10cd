import csv
import logging
from datetime import datetime, timedelta
from io import String<PERSON>

from celery import shared_task
from celery.app.task import Task
from django.conf import settings
from pytz import timezone
from slack_sdk.errors import SlackApiError

from business_reporting.actions import (
    build_usage_report_data, get_usage_report_header_row
)
from slack_client.actions import (
    send_slack_message_action, upload_file_to_slack_action
)

logger = logging.getLogger(__name__)


class UsageReportTask(Task):
    def on_failure(self, exc, task_id, args, kwargs, einfo):
        event_type = kwargs['event_type']
        report_type = kwargs['report_type']
        logger.warning(f"Failed to generate {report_type} {event_type} report.")
        slack_message = {
            'channel': settings.SLACK_CUSTOMER_METRICS_CHANNEL,
            'text': (
                f'<!channel> An exception was raised while trying to '
                f'create {event_type} {report_type} report: {exc}'
            )
        }
        send_slack_message_action(settings.SLACK_TOKEN, **slack_message)


@shared_task(
    base=UsageReportTask,
    bind=True,
    acks_late=True,
    autoretry_for=(SlackApiError,),
    retry_backoff=True
)
def generate_usage_report(self, event_type, report_type):
    pt = timezone('US/Pacific')
    end_date = datetime.now(tz=pt)
    all_day = False
    if report_type == "Daily":
        start_date = end_date - timedelta(days=1)
    elif report_type == "Weekly":
        start_date = end_date - timedelta(days=7)
    elif report_type == "Monthly":
        end_date = end_date.replace(day=1)
        start_date = end_date - timedelta(days=2)
        start_date = start_date.replace(day=1)
        all_day = True
    else:
        logger.error(f"Unknown report type: {report_type}.")
        return

    logger.info(f"Generating {event_type} usage report from {start_date} to {end_date}.")
    report_data = build_usage_report_data(
        event_type,
        start_date,
        end_date,
        all_day=all_day
    )
    start = start_date.strftime("%m_%d_%Y")
    end = end_date.strftime("%m_%d_%Y")
    filename = f"{report_type}_{event_type}_usage_report_{settings.ENVIRONMENT}_{start}_to_{end}.csv"
    with StringIO() as tmpf:
        header = get_usage_report_header_row(event_type)
        csv_writer = csv.DictWriter(tmpf, fieldnames=header)
        csv_writer.writeheader()
        csv_writer.writerows(report_data)
        slack_message = {
            'channels': settings.SLACK_CUSTOMER_METRICS_CHANNEL,
            'content': tmpf.getvalue(),
            'filename': filename,
            'filetype': 'csv',
            'initial_comment': f'{report_type} {event_type} usage report for {start} to {end}.',
        }
        upload_file_to_slack_action(settings.SLACK_TOKEN, **slack_message)
    logger.info("Generate usage report task complete.")
