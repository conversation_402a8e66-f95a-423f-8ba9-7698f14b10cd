import logging

from django.core.exceptions import ObjectDoesNotExist
from pytz import timezone

from event_log.models import Event
from extract.models import Extraction, ExtractionBatch
from organizations.models import DataPool, Organization
from training.models import TrainingRevision

logger = logging.getLogger(__name__)


def build_usage_report_data(
    type,
    start_date,
    end_date,
    all_day=False,
    orgs=None,
    pools=None
):
    """Build report data as a list of dictionaries with the usage information
    took from Event objects for a given event type and between a given time range.
    Optionaly the event objects included in the report data can be filtered by
    Organizations or Data Pools.
        `type`: A string with the event type for the desired report data.

        `start_date`: A datetime object that represents the start of the date range
                      of the event objects filtered by created_at attribute.

        `end_date`: A datetime object that represents the end of the date range of
                    the event objects filtered by created_at attribute.

        `all_day`: A boolean flag. If True then the date range will be from start_date
                   at 0 hours to before end_date at 0 hours PT. If False (default)
                   then the date range will be from start_date at 17 hours to before
                   end_date at 17 hours.

        `orgs`: A list of Organization obfuscated_id's to filter the Event objects.
                This argument is optional, by default is None to include all Organizations
                in the report.

        `pools`: A list of Data Pool obfuscated_id's to filter the Event objects.
                 This argument is optional, by default is None to include all Data
                 Pools in the report.
    """
    events = get_event_set(
        type,
        start_date,
        end_date,
        all_day=all_day,
        orgs=orgs,
        pools=pools
    )
    n_envents = events.count()

    report_data = []
    for i, e in enumerate(events, 1):
        logger.debug(f"Extracting event {i}/{n_envents}")
        data = {
            'Organization ID': '',
            'Organization Label': '',
            'Data Pool ID': '',
            'Data Pool Label': '',
            'Timestamp': e.created_at.astimezone(timezone('US/Pacific'))
        }

        data['Organization ID'] = e.organization_id
        try:
            data['Organization Label'] = Organization.get_object_by_obfuscated_id(
                e.organization_id
            ).label
        except ObjectDoesNotExist:
            logger.warning(
                f"Organization {e.organization_id} appears to have been deleted."
            )

        data['Data Pool ID'] = e.data_pool_id
        try:
            data['Data Pool Label'] = DataPool.get_object_by_obfuscated_id(
                e.data_pool_id
            ).label
        except ObjectDoesNotExist:
            logger.warning(f'Data Pool {e.data_pool_id} appears to have been deleted.')

        data.update(get_event_details(type, e.details))
        report_data.append(data)

    return report_data


def get_event_set(
    type,
    start_date,
    end_date,
    all_day=False,
    orgs=None,
    pools=None
):
    """
    Returns an iterable object with a list of Event objects of a given type and
    with a created_at attribute included in a given date range. Optionally can
    be filtered by Organizations and/or Data Pools.
        `type`: A string with the event type for the desired report data.

        `start_date`: A datetime object that represents the start of the date range
                      of the event objects filtered by created_at attribute.

        `end_date`: A datetime object that represents the end of the date range of
                    the event objects filtered by created_at attribute.

        `all_day`: A boolean flag. If True then the date range will be from start_date
                   at 0 hours to before end_date at 0 hours PT. If False (default)
                   then the date range will be from start_date at 17 hours to before
                   end_date at 17 hours.

        `orgs`: A list of Organization obfuscated_id's to filter the Event objects.
                This argument is optional, by default is None to include all Organizations
                in the report.

        `pools`: A list of Data Pool obfuscated_id's to filter the Event objects.
                 This argument is optional, by default is None to include all Data
                 Pools in the report.
    """
    logger.debug(f"Query time range: {start_date} - {end_date}")
    pt = timezone('US/Pacific')
    if not start_date.tzinfo:
        start_date = pt.localize(start_date)

    if not end_date.tzinfo:
        end_date = pt.localize(end_date)

    if all_day:
        start_date = start_date.replace(
            hour=0,
            minute=0,
            second=0,
            microsecond=0
        )
        end_date = end_date.replace(
            hour=0,
            minute=0,
            second=0,
            microsecond=0
        )
    else:
        start_date = start_date.replace(
            hour=17,
            minute=0,
            second=0,
            microsecond=0
        )
        end_date = end_date.replace(
            hour=17,
            minute=0,
            second=0,
            microsecond=0
        )

    start_date = start_date.astimezone(timezone('UTC'))
    end_date = end_date.astimezone(timezone('UTC'))
    logger.debug(f"Transformed time range: {start_date} - {end_date}")

    events = (
        Event.objects
        .filter(type=type)
        .filter(created_at__gte=start_date)
        .filter(created_at__lt=end_date)
    )

    if orgs:
        events = (ev for ev in events if ev.organization_id in orgs)
    if pools:
        events = (ev for ev in events if ev.data_pool_id in pools)

    return events


def get_event_details(type, details):
    """Returns a dictionary with details information from Event objects."""
    if type == 'extraction':
        return get_extraction_event_details(details)
    elif type == 'training_revision':
        return get_training_event_details(details)
    else:
        logger.warning(f"Unknown event type: {type}")
        return {}


def get_extraction_event_details(details):
    data = {
        'Extraction Batch ID': '',
        'Extraction Batch Label': '',
        'Extraction Batch Deleted': False,
        'Extraction ID': '',
        'Extraction Status': '',
        'Extraction Deleted': False,
        'Page Count': '',
        'Document ID': '',
        'Document Label': '',
        'OCR Engine': '',
    }
    ex_id = details.get('extraction_id')
    data['Extraction ID'] = ex_id
    data['Extraction Status'] = details.get('status')
    data['OCR Engine'] = details.get('tokenizer_version')
    try:
        ex = Extraction.get_object_by_obfuscated_id(ex_id)
    except ObjectDoesNotExist:
        logger.warning(f'Extraction {ex_id} appears to have been deleted.')
        data['Extraction Deleted'] = True
    else:
        if ex.extraction_batch:
            data['Extraction Batch Label'] = ex.extraction_batch.label
            data['Extraction Batch ID'] = ex.extraction_batch.obfuscated_id

        if ex.document:
            data['Document ID'] = ex.document.obfuscated_id
            data['Document Label'] = ex.document.label
        else:
            logger.warning(
                f'Document for Extraction {ex_id} '
                'appears to have been deleted.'
            )

    # it is possible for the batch to have been deleted before the
    # extraction completed and so the extraction_batch_id and
    # extraction_batch_label could not have been recorded.
    eb_id = details.get('extraction_batch_id')
    if eb_id:
        if not data.get('Extraction Batch Label'):
            data['Extraction Batch ID'] = eb_id
            try:
                data['Extraction Batch Label'] = ExtractionBatch.get_object_by_obfuscated_id(eb_id).label
            except ObjectDoesNotExist:
                logger.warning(
                    f"Extraction Batch {eb_id} appears to have been deleted."
                )
                data['Extraction Batch Label'] = details.get('extraction_batch_label')
                data['Extraction Batch Deleted'] = True
    else:
        data['Extraction Batch Deleted'] = None

    data['Page Count'] = details.get('n_pages')
    return data


def get_training_event_details(details):
    data = {
        'Training Set ID': '',
        'Training Set Label': '',
        'Training Set Deleted': False,
        'Training Revision ID': '',
        'Training Revision Label': '',
        'Training Revision Status': '',
        'Training Revision Number': '',
        'Training Revision Deleted': False,
        'Field Count': '',
        'Field Names': '',
        'Document Count': '',
        'Document Labels': '',
        'OCR Engine': '',
    }
    tr_id = details.get('training_revision_id')
    data['Training Revision ID'] = tr_id
    data['Training Revision Status'] = details.get('status')
    data['Training Revision Number'] = details.get('training_revision_number', 0)
    data['OCR Engine'] = details.get('tokenizer_version')

    try:
        tr = TrainingRevision.get_object_by_obfuscated_id(tr_id)
    except ObjectDoesNotExist:
        logger.warning(
            f"Training Revision {tr_id} appears to have been deleted."
        )
        data['Training Revision Label'] = details.get('training_revision_label', '')
        data['Training Revision Deleted'] = True
    else:
        data['Training Revision Label'] = tr.label

        # Training Set
        if tr.training_set:
            data['Training Set ID'] = tr.training_set.obfuscated_id
            data['Training Set Label'] = tr.training_set.label
            if not data['Training Revision Number']:
                data['Training Revision Number'] = tr.training_set.training_revisions.count()
        else:
            data['Training Set ID'] = details.get('training_set_id', '')
            data['Training Set Label'] = details.get('training_set_label', '')
            data['Training Set Deleted'] = True

        # Fields
        if tr.baked_fields:
            data['Field Count'] = tr.baked_fields.count()
            data['Field Names'] = list(tr.baked_fields.values_list('label', flat=True))
        else:
            logger.warning(f'No fields found for Training Revision {tr_id}.')

        # Documents
        if tr.documents:
            data['Document Count'] = tr.documents.count()
            data['Document Labels'] = list(tr.documents.values_list('label', flat=True))
        else:
            logger.warning(f'No documents found for Training Revision {tr_id}.')

    return data


def get_usage_report_header_row(type):
    if type == 'extraction':
        return [
            'Organization ID', 'Organization Label',
            'Data Pool ID', 'Data Pool Label',
            'Extraction Batch ID', 'Extraction Batch Label', 'Extraction Batch Deleted',
            'Extraction ID', 'Extraction Status', 'Extraction Deleted',
            'Page Count', 'Document ID', 'Document Label',
            'OCR Engine', 'Timestamp'
        ]
    elif type == 'training_revision':
        return [
            'Organization ID', 'Organization Label',
            'Data Pool ID', 'Data Pool Label',
            'Training Set ID', 'Training Set Label', 'Training Set Deleted',
            'Training Revision ID', 'Training Revision Label',
            'Training Revision Status', 'Training Revision Number',
            'Training Revision Deleted', 'Field Count', 'Field Names',
            'Document Count', 'Document Labels', 'OCR Engine', 'Timestamp'
        ]
    else:
        logger.warning(f"Unknown event type: {type}")
        return []
