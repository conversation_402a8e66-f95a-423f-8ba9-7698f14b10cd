from django.contrib.auth import get_user_model
from rest_framework.authentication import (
    BaseAuthentication, get_authorization_header
)


class DevTokenAuthentication(BaseAuthentication):
    """A mocked authentication scheme used for dev environment and auth testing"""

    keyword = 'Dev'

    def authenticate(self, request):

        User = get_user_model()

        try:
            any_user = User.objects.filter()[:1].get()
        except User.DoesNotExist:
            return None

        auth = get_authorization_header(request).split()

        if not auth or auth[0].lower() != self.keyword.lower().encode() \
           or len(auth) == 1:
            return None

        return any_user, auth[1].decode()


class ApiKeyAuthentication(BaseAuthentication):
    """
    Authentication against API key.
    """
    keyword = "Token"

    def get_model(self):
        from users.models import ServiceAccount
        return ServiceAccount

    def authenticate(self, request):
        auth = get_authorization_header(request).split()

        if not auth or auth[0].lower() != self.keyword.lower().encode():
            return None

        if len(auth) == 1:
            return None

        if len(auth) > 2:
            return None

        try:
            token = auth[1].decode()
        except UnicodeError:
            return None

        return self.authenticate_credentials(token)

    def authenticate_credentials(self, key):
        model = self.get_model()

        try:
            service_account = model.get_by_key(key)
        except model.DoesNotExist:
            return None

        if not service_account.is_active:
            return None

        return (service_account, service_account.hashed_key)
