import logging

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response

from auth0.exceptions import AuthorizationError
from auth0.util import get_token as auth0_get_token

logger = logging.getLogger(__name__)


@api_view(['POST'])
@permission_classes((AllowAny,))
def get_token(request):
    try:
        username = request.data['username']
    except KeyError:
        return Response({"detail": "'username' must be provided in the POST body."}, status=400)

    try:
        password = request.data['password']
    except KeyError:
        return Response({"detail": "'password' must be provided in the POST body."}, status=400)

    try:
        token = auth0_get_token(username, password)
    except AuthorizationError as ex:
        logger.error(repr(ex))
        return Response({"detail": "Invalid authorization credentials."}, status=403)

    return Response({
        "access_token": str(token),
        "token_type": "Bearer"
    })
