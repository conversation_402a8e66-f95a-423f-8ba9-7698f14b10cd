from django.contrib.auth import get_user_model
from django.http import HttpRequest
from django.test import Client
from hypothesis.extra.django import TestCase

from api_auth import backends


class BackendsTestCases(TestCase):

    fixtures = ['api_auth_test_fixtures.json']

    def setUp(self):
        self.client = Client()
        self.auth_class = backends.DevTokenAuthentication()
        self.request = HttpRequest()

    def test_dev_token_auth_valid(self):
        self.request.META['HTTP_AUTHORIZATION'] = "Dev whatso"
        result = self.auth_class.authenticate(self.request)
        self.assertIsInstance(result[0], get_user_model())
        self.assertEqual(result[1], "whatso")

    def test_dev_token_auth_invalid(self):
        self.request.META['HTTP_AUTHORIZATION'] = "whatso"
        result = self.auth_class.authenticate(self.request)
        self.assertIsNone(result)

    def test_dev_token_auth_none(self):
        result = self.auth_class.authenticate(self.request)
        self.assertIsNone(result)


class ApiKeyAuthenticationTestCase(TestCase):

    def setUp(self):
        from users.models import ServiceAccount
        self.authentication = backends.ApiKeyAuthentication()
        self.request = HttpRequest()
        self.raw_key = ServiceAccount.generate_key()
        hashed_key = ServiceAccount.hash_key(self.raw_key)
        self.service_account = ServiceAccount.objects.create(
            label='Test Service Account',
            is_active=True,
            hashed_key=hashed_key,
        )
        self.service_account.save()

    def tearDown(self):
        self.service_account.delete()

    def test_missing_authorization_header(self):
        result = self.authentication.authenticate(self.request)
        self.assertIsNone(result)

    def test_incorrect_auth_keyword(self):
        self.request.META['HTTP_AUTHORIZATION'] = "WrongKey testkey123"
        result = self.authentication.authenticate(self.request)
        self.assertIsNone(result)

    def test_single_token_in_auth_header(self):
        self.request.META['HTTP_AUTHORIZATION'] = "Token"
        result = self.authentication.authenticate(self.request)
        self.assertIsNone(result)

    def test_more_than_two_tokens_in_auth_header(self):
        self.request.META['HTTP_AUTHORIZATION'] = "Token testkey123 extra"
        result = self.authentication.authenticate(self.request)
        self.assertIsNone(result)

    def test_invalid_token(self):
        self.request.META['HTTP_AUTHORIZATION'] = "Token invalidkey123"
        result = self.authentication.authenticate(self.request)
        self.assertIsNone(result)

    def test_inactive_service_account(self):
        self.service_account.is_active = False
        self.service_account.save()
        self.request.META['HTTP_AUTHORIZATION'] = f"Token {self.raw_key}"
        result = self.authentication.authenticate(self.request)
        self.assertIsNone(result)

    def test_valid_token(self):
        self.request.META['HTTP_AUTHORIZATION'] = f"Token {self.raw_key}"
        result = self.authentication.authenticate(self.request)
        self.assertIsNotNone(result)
        self.assertEqual(result[0], self.service_account)
        self.assertEqual(result[1], self.service_account.hashed_key)

    def test_unicode_error_in_token(self):
        self.request.META['HTTP_AUTHORIZATION'] = "Token \x80"
        result = self.authentication.authenticate(self.request)
        self.assertIsNone(result)
