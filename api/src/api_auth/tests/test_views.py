import json
from unittest.mock import patch

from django.conf import settings
from django.test import Client, override_settings
from django.urls import reverse
from hypothesis.extra.django import TestCase

from auth0.exceptions import AuthorizationError

TESTING_THROTTLE_RATES = {
    'anon': '1000/second',
    'user': '1000/second',
    'heavy': '1000/second'
}
DETHROTTLED_REST_FRAMEWORK = settings.REST_FRAMEWORK.copy()
DETHROTTLED_REST_FRAMEWORK['DEFAULT_THROTTLE_RATES'] = TESTING_THROTTLE_RATES


@override_settings(REST_FRAMEWORK=DETHROTTLED_REST_FRAMEWORK)
class ViewsTestCase(TestCase):

    def setUp(self):
        self.client = Client()

    @patch('api_auth.views.auth0_get_token', return_value='some_token')
    def test_get_token_happy_case(self, mock_get_token):
        data = {'username': 'valid_un', 'password': 'valid_pw'}
        response = self.client.post(reverse('v6:auth:get_token'), json.dumps(data), content_type='application/json')
        self.assertEqual(response.status_code, 200)
        resp = response.json()
        self.assertEqual(resp['access_token'], 'some_token')
        self.assertEqual(resp['token_type'], 'Bearer')

    @patch('api_auth.views.auth0_get_token')
    def test_get_token_invalid_username(self, mock_get_token):
        mock_get_token.side_effect = AuthorizationError()
        data = {'username': 'invalid', 'password': 'valid_pw'}
        response = self.client.post(reverse('v6:auth:get_token'), json.dumps(data), content_type='application/json')
        self.assertEqual(response.status_code, 403)
        detail = response.json()['detail']
        self.assertEqual(detail, 'Invalid authorization credentials.')

    @patch('api_auth.views.auth0_get_token')
    def test_get_token_invalid_password(self, mock_get_token):
        mock_get_token.side_effect = AuthorizationError()
        data = {'username': 'valid_un', 'password': 'invalid'}
        response = self.client.post(reverse('v6:auth:get_token'), json.dumps(data), content_type='application/json')
        self.assertEqual(response.status_code, 403)
        detail = response.json()['detail']
        self.assertEqual(detail, 'Invalid authorization credentials.')

    @patch('api_auth.views.auth0_get_token')
    def test_get_token_invalid_username_password(self, mock_get_token):
        mock_get_token.side_effect = AuthorizationError()
        data = {'username': 'invalid', 'password': 'invalid'}
        response = self.client.post(reverse('v6:auth:get_token'), json.dumps(data), content_type='application/json')
        self.assertEqual(response.status_code, 403)
        detail = response.json()['detail']
        self.assertEqual(detail, 'Invalid authorization credentials.')

    def test_get_token_missing_username(self):
        data = {'password': 'some_password'}
        response = self.client.post(reverse('v6:auth:get_token'), json.dumps(data), content_type='application/json')
        self.assertEqual(response.status_code, 400)
        detail = response.json()['detail']
        self.assertEqual(detail, "'username' must be provided in the POST body.")

    def test_get_token_missing_password(self):
        data = {'username': 'some_username'}
        response = self.client.post(reverse('v6:auth:get_token'), json.dumps(data), content_type='application/json')
        self.assertEqual(response.status_code, 400)
        detail = response.json()['detail']
        self.assertEqual(detail, "'password' must be provided in the POST body.")

    def test_get_token_arbitrary_data(self):
        data = {'anykey': 'anyvalue'}
        response = self.client.post(reverse('v6:auth:get_token'), json.dumps(data), content_type='application/json')
        self.assertEqual(response.status_code, 400)
        detail = response.json()['detail']
        self.assertEqual(detail, "'username' must be provided in the POST body.")
