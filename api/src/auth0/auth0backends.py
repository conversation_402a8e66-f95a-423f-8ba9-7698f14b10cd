import json
import logging
from datetime import datetime, timedelta
from hashlib import md5

import jwt
import requests
from django.conf import settings
from social_core.backends.oauth import BaseOAuth2
from social_core.utils import module_member
from social_django.models import UserSocialAuth

from auth0.models import VerifiedAccessToken

from .exceptions import AuthorizationError
from .util import get_integration_model, send_verification_email

logger = logging.getLogger(__name__)


class Auth0UserBackend(BaseOAuth2):
    """Python Social Core powered Auth0 OAuth authentication backend"""
    name = 'auth0'

    ACCESS_TOKEN_METHOD = 'POST'

    # This set of extra data serves as cache of user profile
    EXTRA_DATA = [
        ('picture', 'picture'),
        ('sub', 'sub'),
        ('email', 'email'),
        ('email_verified', 'email_verified'),
        ('iat', 'profile_iat'),
    ]

    SCOPE_SEPARATOR = ' '

    def authorization_url(self):
        return "https://" + self.setting('AUTH0_DOMAIN') + "/authorize"

    def access_token_url(self):
        return "https://" + self.setting('AUTH0_DOMAIN') + "/oauth/token"

    def userinfo_url(self):
        return "https://" + self.setting('AUTH0_DOMAIN') + "/userinfo"

    def get_user_profile(self, verified):
        """
        Obtains user's profile from cache or Auth0 given a VerifiedAccessToken
        instance.
        """
        if not isinstance(verified, VerifiedAccessToken):
            return None

        # Check cache
        try:
            social_auth_user = UserSocialAuth.objects.get(
                uid=verified.subject,
                provider=self.name,
            )
        except:  # noqa
            pass
        else:
            user_data = social_auth_user.extra_data
            if user_data and user_data.get('email_verified', False) and (
                # check cache expiration, default to id token default (ie. 36000)
                0 < datetime.utcnow().timestamp() - user_data.get('profile_iat', 0)
                < self.setting('AUTH0_USER_PROFILE_EXPIRATION', 36000)
            ):
                profile = dict(
                    [(tup[0], user_data.get(tup[1])) for tup in self.EXTRA_DATA]
                )
                if profile.get('sub') and profile.get('email'):
                    return profile

        headers = {
            'Authorization': "Bearer {}".format(verified.access_token),
            'Content-Type': 'application/json',
        }
        try:
            response = requests.get(self.userinfo_url(), headers=headers)
        except Exception as ex:
            logger.error('Error getting user profile with access token.'
                         'Exception: {}'.format(repr(ex)))
            return None

        if response.status_code != 200:
            logger.error(
                "Error obtaining user info from Auth0. "
                "Response status: {} "
                "Response body: {}".format(
                    response.status_code,
                    response.json()
                )
            )
            return None
        else:
            response_content = response.content.decode()
            profile = json.loads(response_content)

            return profile

    def get_user_details(self, response, **kwargs):
        """
        Get user details in one of two ways:
        a. Decoding JWT-encoded ID token in response if response is not None
           (from social:complete).
        b. Retrieve user profile from Auth0's /userinfo endpoint with Access
           token if provided in kwargs.

        This method also caches the access token in response (if provided) if the
        id_token in response is verified.
        """
        if response and response.get('id_token'):
            id_token = response.get('id_token')
            try:
                payload = jwt.decode(
                    id_token,
                    self.setting('AUTH0_PUBLIC_KEY'),
                    algorithms=['RS256'],
                    audience=self.setting('AUTH0_CLIENT_ID'),
                    issuer=self.setting('AUTH0_JWT_ISSUER')
                )
            except Exception as ex:
                logger.error("Error decoding JWT ID token. Exception:"
                             "{}".format(repr(ex)))
                raise AuthorizationError("Invalid authentication credential")
            else:
                # Cache access token
                access_token = response.get('access_token')
                expires_in = response.get('expires_in')
                if access_token and expires_in and isinstance(expires_in, int):
                    expires_at = datetime.utcnow() + timedelta(seconds=expires_in)
                    try:
                        hashed = md5(access_token.encode('utf-8')).hexdigest()
                        VerifiedAccessToken(
                            access_token=access_token,
                            md5=hashed,
                            subject=payload['sub'],
                            expires_at=expires_at
                        ).save()
                    except Exception as ex:
                        logger.error("Error caching access token: {}".format(ex))
        elif kwargs.get('verified'):
            payload = self.get_user_profile(kwargs['verified'])
            if not payload:
                raise AuthorizationError("Permission to access user information"
                                         " is not granted.")
        else:
            return {}

        if not payload.get('email'):
            raise AuthorizationError("Permission to access email information is"
                                     " not granted.")

        # Must not authorize if the email is not verified
        # Note: should we enable social connections in the future, we must
        # ensure that the identity provider verifies the user's email and set
        # email_verified field in the ID token accordingly.
        if not payload.get('email_verified', False):
            # attempt to submit a 'send verify email address email` job
            try:
                sent = send_verification_email(payload.get('sub'))
            except Exception as ex:
                sent = False
                logger.error(repr(ex))

            msg = "Email address verification pending.{}".format(
                ' Verification email re-sent.' if sent else ''
            )

            raise AuthorizationError(msg)

        return {
            'username': payload.get('email'),
            'picture': payload.get('picture'),
            'email': payload.get('email'),
            'user_id': payload.get('sub'),
            'sub': payload.get('sub'),
            'email_verified': payload.get('email_verified', False),
            'iat': payload.get('iat', datetime.utcnow().timestamp())
        }

    def get_user_id(self, details, response):
        """Return current user id."""
        try:
            return details.get('user_id')
        except Exception:
            return None

    def authenticate(self, *args, **kwargs):
        """Authenticate user using social credentials
        Prepare kwargs in order to pass kwargs inspection in parent's
        authenticate() method such that it will enter social auth pipeline even
        if response is not in kwargs.
        """
        kwargs.setdefault('backend', self)
        kwargs.setdefault('response', {})
        kwargs.setdefault('strategy', self.strategy)
        return super().authenticate(*args, **kwargs)

    def run_pipeline(self, pipeline, pipeline_index=0, *args, **kwargs):
        """
        Mostly copied from parent except one tweak:
        Exits pipeline and returns None as soon as social_uid returns with None uid.
        """
        out = kwargs.copy()
        out.setdefault('strategy', self.strategy)
        out.setdefault('backend', out.pop(self.name, None) or self)
        out.setdefault('request', self.strategy.request_data())
        out.setdefault('details', {})

        if (
            not isinstance(pipeline_index, int)
            or pipeline_index < 0
            or pipeline_index >= len(pipeline)
        ):
            pipeline_index = 0

        for idx, name in enumerate(pipeline[pipeline_index:]):
            out['pipeline_index'] = pipeline_index + idx
            func = module_member(name)
            result = func(*args, **out) or {}
            # Exits pipeline and returns None as soon as social_uid returns with None uid.
            if name.split('.')[-1] == 'social_uid' and not result.get('uid'):
                return None
            if not isinstance(result, dict):
                return result
            out.update(result)
        return out


class Auth0M2MBackend:

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.integration_model = get_integration_model()

    def get_user(self, client_id):
        try:
            integration = self.integration_model.objects.get(
                client_id=client_id
            )
        except self.integration_model.DoesNotExist:
            return None
        return integration

    def authenticate(self, *args, **kwargs):
        payload = kwargs.get('auth0_m2m_payload')

        if not payload:
            return None

        if not isinstance(payload, dict):
            return None

        if not payload.get('azp'):
            logger.error("'azp' not found in M2M payload")
            msg = "Invalid authentication credential"
            raise AuthorizationError(msg)

        if payload.get('aud') != settings.AUTH0_API_IDENTIFIER:
            msg = "Provided token is not authorized for requested resource"
            raise AuthorizationError(msg)

        client_id = payload['azp']

        integration = self.get_user(client_id)

        if not integration:
            msg = "Account not found."
            raise AuthorizationError(msg)

        return integration
