from django.db import models


class Auth0M2MClientMixin(models.Model):
    """Mixin for Models to add client_id and is_active fields for
    Auth0M2MBackend to authenticate against.
    """
    class Meta:
        abstract = True
        permissions = (
            ('get_m2m_client_info', 'Can get Auth0 client information'),
        )

    client_id = models.CharField(
        max_length=32, unique=True, blank=True, null=True,
        help_text=("This Client ID should correspond to Auth0 Application "
                   "Client ID"),
    )

    is_active = models.BooleanField(
        default=True,
        help_text=("Designates whether the integration is active. Inactive"
                   " integration will be denied access to the API."),
    )


class VerifiedAccessToken(models.Model):
    access_token = models.TextField()
    md5 = models.Char<PERSON>ield(max_length=32, db_index=True)
    subject = models.CharField(max_length=255)
    expires_at = models.DateTimeField()

    def __str__(self):
        return str(self.md5)

    class Meta:
        db_table = 'auth0_verifiedaccesstoken'
