import datetime
import logging
from hashlib import md5

import jwt
from django.contrib.auth import authenticate as _authenticate
from django.contrib.messages import get_messages
from django.utils.translation import ugettext as _
from rest_framework.authentication import get_authorization_header
from rest_framework.exceptions import AuthenticationFailed
from rest_framework_jwt.authentication import JSONWebTokenAuthentication
from rest_framework_jwt.settings import api_settings

from auth0.models import VerifiedAccessToken

from .exceptions import AuthorizationError

jwt_decode_handler = api_settings.JWT_DECODE_HANDLER

logger = logging.getLogger(__name__)


class Auth0AccessTokenAuthentication(JSONWebTokenAuthentication):

    def authenticate(self, request):
        """
        Returns a two-tuple of authorized object and token if a valid access token has been
        supplied.  Otherwise returns `None`.
        """
        token = self.get_jwt_value(request)
        if token is None:
            # No token present in request

            # See if we got an error message from the social auth flow which was
            # handled by the SocialAuthExceptionMiddleware.
            message_storage = get_messages(request)
            auth_errors = []
            for message in message_storage:
                if 'social-auth' in message.tags and message.level >= 40:
                    auth_errors.append(message.message)
            if auth_errors:
                raise AuthenticationFailed(auth_errors[0])

            return None

        kwargs = {'access_token': token.decode('utf-8')}

        # Token type is specified in order to be loaded to social_auth extra data
        kwargs['token_type'] = get_authorization_header(request).split()[0].decode('utf-8')

        # Pass the token for authentication
        try:
            authorized = self.authenticate_credentials(**kwargs)
        except AuthorizationError as auth_err:
            msg = str(auth_err)
            raise AuthenticationFailed(msg)

        if not authorized:
            return None

        return (authorized, token)

    def authenticate_credentials(self, **kwargs):
        """
        Returns an active user that matches the jwt access token identity.
        """
        authorized = self.get_authorized_from_access_token(**kwargs)

        if not authorized:
            return None

        if not authorized.is_active:
            msg = _('Account is disabled.')
            raise AuthenticationFailed(msg)

        return authorized

    def get_authorized_from_access_token(self, **kwargs):
        """A handler for Auth0AccessTokenAuthentication to authenticate an Auth0
        user or application with an access token and returns the corresponding
        Django `user` or `integration` object.
        Tries to decode the token if it is JWT-encoded to check token expiration
        and validity.

        Cache the access token to VerifiedAccessToken database if it can be
        successfully verified.

        Fail authentication if the access token is not in VerifiedAccessToken.
        """
        access_token = kwargs.get('access_token')
        if not access_token:
            return None

        payload = {}
        try:
            payload = jwt_decode_handler(access_token)
        except jwt.DecodeError:
            # Proceed if the token is not JWT-encoded
            # Must catch this error first to allow non-JWT-encoded access token
            # Otherwise jwt.InvalidTokenError will be caught
            pass
        except jwt.ExpiredSignature:
            msg = _('Signature has expired.')
            raise AuthenticationFailed(msg)
        except jwt.InvalidTokenError:
            msg = _('Invalid signature.')
            raise AuthenticationFailed(msg)

        # Subject with @client indicates M2M client instead of user
        # It should be authenticated by Auth0M2MBackend
        if '@client' in payload.get('sub', ''):
            kwargs['auth0_m2m_payload'] = payload
            # This prevents the client from being authenticated by Auth0UserBackend
            kwargs.pop('access_token')

        # Check cache
        hashed = md5(access_token.encode('utf-8')).hexdigest()
        cached = VerifiedAccessToken.objects.filter(
            md5=hashed,
            access_token=access_token
        )
        num_cached = cached.count()
        if num_cached > 1:
            logger.warning(
                "More than one ({}) access tokens with md5 {}"
                " cached.".format(num_cached, hashed)
            )

        if payload:
            # Verified (trusted) access token
            # Add to VerifiedAccessToken
            if num_cached != 1:
                if num_cached > 1:
                    # There should not be more than one cached, clear them all
                    # and cache again.
                    cached.delete()
                # cache token
                expires_at = datetime.datetime.utcfromtimestamp(payload['exp'])
                expires_at = expires_at.replace(tzinfo=datetime.timezone.utc)
                verified = VerifiedAccessToken(
                    access_token=access_token,
                    md5=hashed,
                    subject=payload['sub'],
                    expires_at=expires_at,
                )
                verified.save()
                kwargs['verified'] = verified
            else:
                kwargs['verified'] = cached.first()
        else:
            # Non-JWT access token, check cached VerifiedAccessToken
            if not num_cached:
                msg = _('Invalid authentication credential')
                raise AuthenticationFailed(msg)

            # Only take the first even if more than one (this really shouldn't happen)
            first_cached = cached.first()

            # Check token expiration
            now = datetime.datetime.now(datetime.timezone.utc)
            if now >= first_cached.expires_at:
                # Delete old tokens
                cached.delete()
                msg = _('Signature has expired.')
                raise AuthenticationFailed(msg)

            kwargs['verified'] = first_cached

        authorized = _authenticate(**kwargs)

        return authorized or None
