import logging
import urllib

from django.conf import settings
from django.contrib.auth import logout as django_logout
from django.contrib.auth.decorators import permission_required
from django.http import JsonResponse
from django.shortcuts import redirect
from django.urls import NoReverseMatch, reverse

from .util import get_auth0_client

logger = logging.getLogger(__name__)


def landing(request):
    if request.GET.get('next'):
        next = request.GET.get('next')
        redirect_url = _sanitize_redirect(next)
    else:
        # default redirect url if not specified
        try:
            redirect_url = reverse(settings.LOGOUT_REDIRECT_URL)
        except NoReverseMatch:
            redirect_url = settings.LOGOUT_REDIRECT_URL

    redirect_url = request.build_absolute_uri(redirect_url)

    return redirect(redirect_url)


def logout(request):
    """Logs out the django user and then redirects the user's browser to the
    Auth0 logout page so that the Auth0 session is also logged out. Finally
    redirects the user to the landing page which will further redirect user to
    either the location specified in `?next=` query string of the request if
    present, or the default redirect URL implemented in the landing view.

    If this logout pattern is not followed, it can make it difficult for a
    browser to login to a different Auth0 user without clearing cookies, which
    is not ideal.

    Note: this functionality has nothing to do with the python-social-auth
    disconnect pipeline.
    https://python-social-auth-docs.readthedocs.io/en/latest/logging_out.html
    """
    django_logout(request)

    params = {}
    if request.GET.get('next'):
        next = request.GET.get('next')
        next = _sanitize_redirect(next)
        params['next'] = request.build_absolute_uri(next)
    next_param = urllib.parse.urlencode(params)

    redirect_url = reverse('auth0:landing')
    redirect_url = request.build_absolute_uri(redirect_url)
    redirect_url += '?{}'.format(next_param) if params else ''

    query_params = urllib.parse.urlencode({
        'returnTo': redirect_url
    })

    auth0_logout_url = 'https://' + settings.AUTH0_DOMAIN + '/v2/logout?' + query_params

    return redirect(auth0_logout_url)


def login(request):
    """Logs out the django user and the Auth0 session then redirects the user's
    browser to social:begin to start the python-social-auth authentication, with
    the `?next=` parameter if presented in query string.

    If this login pattern is not followed, it can make it difficult for a
    browser to login again without clearing cookies if the required scope is
    not granted for the access token (eg. email), which is not ideal.
    """
    django_logout(request)

    params = {}
    if request.GET.get('next'):
        next = request.GET.get('next')
        next = _sanitize_redirect(next)
        params['next'] = request.build_absolute_uri(next)
    next_param = urllib.parse.urlencode(params)

    landing_next = reverse('social:begin', args=['auth0'])
    landing_next = request.build_absolute_uri(landing_next)
    landing_next += '?{}'.format(next_param) if params else ''

    landing_params = urllib.parse.urlencode({
        'next': landing_next,
    })

    redirect_url = reverse('auth0:landing')
    redirect_url = request.build_absolute_uri(redirect_url)

    query_params = urllib.parse.urlencode({
        'returnTo': redirect_url + '?' + landing_params
    })

    auth0_logout_url = 'https://' + settings.AUTH0_DOMAIN + '/v2/logout?' + query_params

    return redirect(auth0_logout_url)


@permission_required(
    '{}.get_m2m_client_info'.format(
        getattr(settings, 'AUTH_INTEGRATION_MODEL', '')
        .split('.', maxsplit=1)[0]
    ),
    raise_exception=True
)
def client_details(request, client_id):
    """
    Returns json of Auth0 client application information if retrieved
    sucessfully, empty dict otherwise.

    Requires `get_m2m_client_info` of the client model app to access this view,
    otherwise HTTP 403 response is returned.
    """
    try:
        details = get_auth0_client(client_id)
    except Exception as ex:
        logger.error("Error retrieving Auth0 client details for {}: {!r}".format(
            client_id, ex))
        details = {}
    return JsonResponse(details)


def _sanitize_redirect(next):
    """Sanitize a redirect parameter to protect against XSS
    """
    url = urllib.parse.urlparse(next)
    if not url.netloc:
        return next

    for host in settings.ALLOWED_HOSTS:
        if url.netloc == host:
            return next

    # We need a netloc to replace the old one, so pick the one on the
    # end of the allowed list. N.B., For testing, Django arranges for
    # this to be 'testserver'
    return f'{ url.scheme }://{ settings.ALLOWED_HOSTS[-1] }/'
