import logging
import random
import string
import urllib
from uuid import uuid4

import requests
from django.apps import apps as django_apps
from django.conf import settings
from django.contrib.auth.models import AbstractUser
from django.core.exceptions import ImproperlyConfigured

from .exceptions import (
    Auth0Exception, AuthorizationError, NotFound, RequestException,
    UnexpectedResponse
)
from .models import Auth0M2MClientMixin

logger = logging.getLogger(__name__)


def generate_password():
    """
    Generate password that fulfills all of the following requirement:
    - 12+ characters in length
    - contains at least one upper case letter (A-Z)
    - contains at least one lower case letter (a-z)
    - contains at least one number (0-9)
    - contains at least one special character (!@#$%^&*)
    """
    special_char = '!@#$%^&*'
    password = str(uuid4())
    password = ''.join([
        random.choice((char.lower, char.upper))() for char in password
    ]) + \
        random.choice(string.ascii_uppercase) + \
        random.choice(string.ascii_lowercase) + \
        random.choice(string.digits) + \
        random.choice(special_char)
    return password


def auth0_domain():
    """
    Auth0 domain as configured in project's settings.
    Raises ImproperlyConfigured if not configured.
    """
    try:
        return settings.AUTH0_DOMAIN
    except AttributeError:
        raise ImproperlyConfigured(
            "settings.AUTH0_DOMAIN not configured"
        )


def access_token_url():
    """Auth0 access token url for configured tenant.

    Could raise ImproperlyConfigured if required settings are not configured.
    """
    return "https://" + auth0_domain() + "/oauth/token"


def authorization_url():
    """Auth0 authorization url for configured tenant.

    Could raise ImproperlyConfigured if required settings are not configured.
    """
    return "https://" + auth0_domain() + "/authorize"


def management_api_url():
    """Auth0 Management API base url for configured tenant.
    Also the identifier of the management API.

    Could raise ImproperlyConfigured if required settings are not configured.
    """
    return "https://" + auth0_domain() + "/api/v2/"


def get_integration_model():
    """
    Return the Integration model that is active in this project as specified in
    settings.AUTH_INTEGRATION_MODEL.

    Could raise ImproperlyConfigured.
    """
    try:
        return django_apps.get_model(
            settings.AUTH_INTEGRATION_MODEL,
            require_ready=False)
    except AttributeError:
        raise ImproperlyConfigured(
            "AUTH_INTEGRATION_MODEL not configured in settings"
        )
    except ValueError:
        raise ImproperlyConfigured(
            "AUTH_INTEGRATION_MODEL must be of the form 'app_label.model_name'"
        )
    except LookupError:
        raise ImproperlyConfigured(
            "AUTH_INTEGRATION_MODEL refers to model '{}' that has not been"
            " installed".format(settings.AUTH_INTEGRATION_MODEL)
        )


def get_auth_header(request):
    """Returns a tuple of auth_type and credential from the Authorization
    Header of a request.
    """
    auth = request.META.get("HTTP_AUTHORIZATION", None)
    if auth:
        parts = auth.split(' ', maxsplit=1)
        try:
            auth_type = parts[0]
            credential = parts[1]
        except IndexError:
            raise AuthorizationError(
                'Unable to parse Authorization header. Value should be '
                '"<auth_type> <credential>".'
            )
        if ' ' in auth_type or ' ' in credential:
            raise AuthorizationError(
                'Unable to parse Authorization header. Value should be '
                '"<auth_type> <credential>". White space is not allowed in '
                '<auth_type> or <credential>.'
            )
        return (auth_type, credential)
    return None


def get_token(username, password):
    """Obtains an Access Token from Auth0 given a user's Auth0 username and password.

    This function uses the Password Grant Type, and relies on the Grant Type
    being allowed by the Auth0 Application, as well as the tenant being
    correctly configured for Password Grant Type requests. See the README of
    this package for more details.

    Could raise ImproperlyConfigured if required settings are not configured.
    """
    logger.debug('Get access token for user with username {}'.format(username))

    try:
        data = {
            'grant_type': 'password',
            'username': username,
            'password': password,
            'client_id': settings.AUTH0_CLIENT_ID,
            'client_secret': settings.AUTH0_CLIENT_SECRET,
            'scope': ' '.join(settings.SOCIAL_AUTH_AUTH0_SCOPE)
        }
    except Exception as ex:
        raise ImproperlyConfigured(ex)

    url = access_token_url()

    try:
        response = requests.post(url, json=data)
    except Exception as ex:
        raise RequestException(
            "Error requesting access token url {}:"
            " {!r}".format(url, ex))

    try:
        resp_json = response.json()
    except ValueError:
        raise UnexpectedResponse(
            "Response content from {} is not valid json".format(url))

    if response.status_code != 200:
        raise AuthorizationError(
            'Unable to authorize user with username {}. Error message: {}'.format(
                username, resp_json))

    try:
        token = resp_json['access_token']
    except KeyError:
        raise UnexpectedResponse("`access_token` not in response json.")

    logger.debug('Retrieved token for user with username {}'.format(username))

    return token


def get_management_api_token():
    """Obtains a Management API v2 Access Token from Auth0

    This function works only when the calling app is registered as one of the
    Machine to Machine Applications for the Auth0 Management API, and is
    authorized.
    The calling app must also have permission for 'client_credentials' grant
    type. (`Advance Settings` -> `Grant Types` via Dashboard)
    See https://auth0.com/docs/api/management/v2/get-access-tokens-for-production

    Raise ImproperlyConfigured if required settings are not configured.

    Raises AuthorizationError if not authorized to access Auth0 management API.

    Raises Auth0Exception for Auth0 API related errors.
    """
    try:
        data = {
            'grant_type': 'client_credentials',
            'client_id': settings.AUTH0_CLIENT_ID,
            'client_secret': settings.AUTH0_CLIENT_SECRET,
            'audience': management_api_url(),
        }
    except AttributeError as ex:
        raise ImproperlyConfigured(ex)

    url = access_token_url()

    try:
        response = requests.post(url, json=data)
    except Exception as ex:
        raise RequestException(
            "Error acquiring management API token from {}:"
            " {!r}".format(url, ex))

    try:
        resp_json = response.json()
    except ValueError:
        raise UnexpectedResponse(
            "Response content from {} is not valid json".format(url))

    if response.status_code != 200:
        raise AuthorizationError(
            'Unable to acquire Auth0 Management API access token. '
            'Error message: {}'.format(resp_json)
        )

    try:
        token = resp_json['access_token']
    except KeyError:
        raise UnexpectedResponse("`access_token` not in response json")

    scope = resp_json.get('scope')

    logger.debug('Retrieved Auth0 Management API access token')
    logger.debug('Granted scope: {}'.format(scope))

    return token


def create_auth0_user(raw_password=None, verification_email=True,
                      extra_user_data=None, **kwargs):
    """Creates an Auth0 (`Username-Password-Authentication` database connection) user.

    This function works only when the calling app is granted `create:users` scope
    for the management API.
    See https://auth0.com/docs/api/management/v2/create-m2m-app

    Also, an `Username-Password-Authentication` database connection must be
    properly set up for the tenant. (See README of the package)

    If `user` is provided in kwargs, it will be used to provide the info to
    create the Auth0 user. `user` must be an instance of
    django.contrib.auth.models.AbstractUser.
    Otherwise `email` argument is required and must be of str type. The Auth0
    user will be created with email only which is minimum requirement.

    Extra user data (besides the user properties) can be provided with
    `extra_user_data` keyword argument which should be a dictionary.
    Value provided in extra_user_data will override user property of same
    name.

    Management API access token can also be provided as `management_api_token`
    in kwargs to be used to access the Auth0 Management API, otherwise it will
    utilize get_management_api_token() to obtain the necessary access token.

    If `verification_email` is set to `True`, an email with a link to verify email
    address will be sent to the email address used for user creation upon user
    creation. If set to `False`, such email will not be sent automatically.
    Calling app is responsible to make sure email address being used gets
    verified. It is set to `True` by default.

    Raises TypeError if:
    - neither `user` or `email` is provided, or
    - value of `user` is not an instance of
      django.contrib.auth.models.AbstractUser, or
    - no email is provided for Auth0 user creation

    Raises ImproperlyConfigured if required settings are not configured.

    Raises AuthorizationError if not authorized to access Auth0 management API.

    Raises Auth0Exception for Auth0 API related errors.

    Returns:
        - `False` if new Auth0 user is not created (user already exists).
        - Auth0 user ID if new Auth0 user is created.
        - `None` if new user is created but unable to retrieve user ID from
          response. In such case a warning is logged.
    """

    body_field_to_user_attr = {
        'email': 'email',
        'given_name': 'first_name',
        'family_name': 'last_name',
        'name': 'name',
        'nickname': 'nickname',
        'picture': 'picture',
    }

    if kwargs.get('user'):
        user = kwargs['user']

        if not isinstance(user, AbstractUser):
            raise TypeError(
                "Create Auth0 user error: `user` must be instance of "
                "django.contrib.auth.models.AbstractUser if provided. "
                "{} given.".format(type(user)))

        user_info = {}
        for field in body_field_to_user_attr:
            user_attr = body_field_to_user_attr[field]
            user_attr_value = getattr(user, user_attr, None)
            if user_attr_value:
                user_info[field] = str(user_attr_value)

    elif kwargs.get('email'):
        user_info = {
            'email': str(kwargs['email']),
        }
    else:
        raise TypeError("Create Auth0 user error: No user or email is provided.")

    if isinstance(extra_user_data, dict):
        for field in body_field_to_user_attr:
            extra_data = extra_user_data.pop(field, None)
            if extra_data:
                user_info[field] = str(extra_data)
        if extra_user_data:
            user_info['user_metadata'] = extra_user_data

    if not user_info.get('email'):
        raise TypeError("Create Auth0 user error: No email is provided.")

    user_info['email'] = user_info['email'].lower()
    logger.debug('Create Auth0 user with email `{}`'.format(
        user_info['email']
    ))

    management_api_token = (
        kwargs.get('management_api_token')
        or get_management_api_token()
    )

    password = raw_password or generate_password()
    password = str(password)

    data = {
        'connection': "Username-Password-Authentication",
        'password': password,
        'verify_email': bool(verification_email),
    }
    data.update(user_info)

    headers = {
        'Authorization': 'Bearer {}'.format(management_api_token),
    }

    create_user_url = management_api_url() + 'users'
    try:
        response = requests.post(create_user_url, json=data, headers=headers)
    except Exception as ex:
        raise RequestException(
            'Error creating Auth0 user `{}` from {}: {!r}'.format(
                user_info['email'], create_user_url, ex))

    if response.status_code == 409:
        logger.info(
            "Auth0 user `{}` already exists, no new user is"
            " created".format(user_info['email'])
        )
        return False

    if response.status_code != 201:
        raise Auth0Exception(
            'Error creating Auth0 user `{}` from {}: {}'.format(
                user_info['email'], create_user_url, response.text))

    try:
        user_id = response.json()['user_id']
    except Exception:
        user_id = None

    if not user_id:
        logger.warning(
            "Unable to retrieve user_id for `{}` from create Auth0 user"
            " response.".format(user_info['email'])
        )

    logger.info('Auth0 user for `{}` created. user_id: {}'.format(
        user_info['email'], user_id
    ))

    return user_id


def get_user_by_email(email, **kwargs):
    management_api_token = (
        kwargs.get('management_api_token')
        or get_management_api_token()
    )

    headers = {'Authorization': f'Bearer {management_api_token}'}

    list_users_url = management_api_url() + 'users-by-email'
    filter_param = {'email': email}
    try:
        response = requests.get(list_users_url, headers=headers, params=filter_param)
    except Exception as ex:
        raise RequestException(
            f'Error retrieving Auth0 user `{email}`: {ex}'
        )

    if response.status_code != 200:
        raise RequestException(
            f"Failed to get list of users: {response.status_code} - {response.content}"
        )

    assert len(response.json()) == 1, f"More than one user found with {email} email address."

    user = response.json()[0]

    assert user['email'] == email, f"User returned from auth0 API did not match the requested email: {email}"

    return user


def delete_auth0_user(user_id=None, user=None, email=None, **kwargs):
    management_api_token = (
        kwargs.get('management_api_token')
        or get_management_api_token()
    )

    headers = {'Authorization': f'Bearer {management_api_token}'}

    if not user_id:
        if user:
            if not isinstance(user, AbstractUser):
                raise TypeError(
                    "Delete Auth0 user error: `user` must be instance of "
                    "django.contrib.auth.models.AbstractUser if provided. "
                    "{} given.".format(type(user)))
            email = user.email
        elif not email:
            raise Exception(
                "No user id, user object, or email address provided. "
                "Must provide at least one of these"
            )

        auth0_user = get_user_by_email(
            email, management_api_token=management_api_token
        )
        user_id = auth0_user['user_id']

    delete_user_url = management_api_url() + 'users/' + user_id
    logger.info(f"Deleting auth0 user: email - {email}, id - {user_id}.")
    try:
        resp = requests.delete(delete_user_url, headers=headers)
    except Exception as ex:
        raise RequestException(
            f"Error deleting Auth0 user `{user_id}`: {ex}"
        )

    if resp.status_code != 204:
        raise RequestException(
            f"Delete user request failed: {resp.status_code} - {resp.content}"
        )
    logger.info(f"User {user_id} deleted successfully")


def delete_auth0_application(integration=None, client_id=None, **kwargs):
    management_api_token = (
        kwargs.get('management_api_token')
        or get_management_api_token()
    )

    headers = {'Authorization': f'Bearer {management_api_token}'}

    if not client_id:
        if integration:
            if not isinstance(integration, Auth0M2MClientMixin):
                raise TypeError(
                    "Delete Auth0 application error: `integration` must be instance of "
                    f"Auth0M2MClientMixin if provided. {type(integration)} given."
                )
            client_id = integration.client_id
        else:
            raise Exception(
                "No client id or integration provided. "
                "Must provide at least one of these"
            )

    delete_user_url = management_api_url() + 'clients/' + client_id
    try:
        resp = requests.delete(delete_user_url, headers=headers)
    except Exception as ex:
        raise RequestException(
            f'Error deleting Auth0 client `{client_id}`: {ex}'
        )

    if resp.status_code != 204:
        raise RequestException(
            f"Delete client request failed: {resp.status_code} - {resp.content}"
        )
    logger.info(f"Application {client_id} deleted successfully")


def create_auth0_application(**kwargs):
    """Creates an Auth0 application.

    This function works only when the calling app is granted `create:clients` scope
    for the management API.
    See https://auth0.com/docs/api/management/v2#!/Clients/post_clients

    Either `name` or `integration` is required in kwargs to provide name for
    client application creation. `integration` should be an instance of
    Auth0M2MClientMixin provided with this package.

    Management API access token can also be provided as `management_api_token`
    in kwargs to be used to access the Auth0 Management API, otherwise it will
    utilize get_management_api_token() to obtain the necessary access token.

    Raises TypeError for missing/invalid required arguments.

    Raises ImproperlyConfigured if required settings are not configured.

    Raises AuthorizationError if not authorized to access Auth0 management API.

    Raises Auth0Exception for Auth0 API related errors.

    Returns:
        - Auth0 client ID if new Auth0 application is created.
        - `None` if new application is created but unable to retrieve client ID from
          response. In such case a warning is logged.
    """

    if kwargs.get('integration'):
        integration = kwargs['integration']
        if not isinstance(integration, Auth0M2MClientMixin):
            raise TypeError(
                "Create Auth0 application error: `integration` must be instance of "
                "Auth0M2MClientMixin if provided. {} given.".format(
                    type(integration)))
        name = integration.__str__() if hasattr(integration, '__str__') else ''
    else:
        name = kwargs.get('name', '')

    if not name:
        raise TypeError("Name not provided for Auth0 client application creation")

    logger.debug("Creating Auth0 application `{}`".format(name))

    data = {
        'name': name,
        'app_type': 'non_interactive',
        'is_first_party': False,
        'oidc_conformant': True,
        'grant_types': ['client_credentials'],
    }

    management_api_token = (
        kwargs.get('management_api_token')
        or get_management_api_token()
    )

    headers = {
        'Authorization': 'Bearer {}'.format(management_api_token),
    }

    create_client_url = management_api_url() + 'clients'
    try:
        response = requests.post(create_client_url, json=data, headers=headers)
    except Exception as ex:
        raise RequestException(
            'Error creating Auth0 client application `{}` from {}: {!r}'.format(
                name, create_client_url, ex))

    if response.status_code != 201:
        raise Auth0Exception(
            'Error creating Auth0 client application `{}` from {}: {}'.format(
                name, create_client_url, response.text))

    try:
        client_id = response.json()['client_id']
    except Exception:
        client_id = None

    if not client_id:
        logger.warning(
            "Unable to retrieve client_id for `{}` from create Auth0 "
            "application response.".format(name)
        )

    logger.info('Auth0 client application `{}` created. client_id: {}'.format(
        name, client_id
    ))

    return client_id


def create_auth0_client_grant(client_id, **kwargs):
    """Creates an Auth0 client grant for the client to access the calling app.

    This function works only when the calling app is granted
    `create:client_grants` scope for the management API.
    See https://auth0.com/docs/api/management/v2#!/Client_Grants/post_client_grants

    Management API access token can also be provided as `management_api_token`
    in kwargs to be used to access the Auth0 Management API, otherwise it will
    utilize get_management_api_token() to obtain the necessary access token.

    Raises ImproperlyConfigured if required settings are not configured.

    Raises AuthorizationError if not authorized to access Auth0 management API.

    Raises Auth0Exception for Auth0 API related errors.

    Returns True if client grant is created successfully.
    """
    logger.debug("Creating Auth0 client grant for {}".format(client_id))

    try:
        data = {
            'client_id': client_id,
            'audience': settings.AUTH0_API_IDENTIFIER,
            'scope': ['read', 'write'],
        }
    except AttributeError as ex:
        raise ImproperlyConfigured(ex)

    management_api_token = (
        kwargs.get('management_api_token')
        or get_management_api_token()
    )

    headers = {
        'Authorization': 'Bearer {}'.format(management_api_token),
    }

    create_client_grant_url = management_api_url() + 'client-grants'
    try:
        response = requests.post(create_client_grant_url, json=data, headers=headers)
    except Exception as ex:
        raise RequestException(
            'Error creating Auth0 client grant for client {} from {}: {!r}'.format(
                client_id, create_client_grant_url, ex))

    if response.status_code != 201:
        raise Auth0Exception(
            'Error creating Auth0 client grant for client {} from {}: {}'.format(
                client_id, create_client_grant_url, response.text))

    logger.debug('Auth0 client grant successfully created for {}.'.format(client_id))

    return True


def get_auth0_client(client_id, **kwargs):
    """Retrieve an Auth0 client information.

    This function works only when the calling app is granted `read:clients`
    scope for the management API. `client_secret` is available only with
    `read:client_keys` scope.
    See https://auth0.com/docs/api/management/v2#!/Clients/get_clients_by_id

    Management API access token can also be provided as `management_api_token`
    in kwargs to be used to access the Auth0 Management API, otherwise it will
    utilize get_management_api_token() to obtain the necessary access token.

    By default only selected fields are returned. Set `all_fields` kwargs to
    `True` to retrieve all fields available.

    Raises ImproperlyConfigured if required settings are not configured.

    Raises AuthorizationError if not authorized to access Auth0 management API.

    Raises Auth0Exception for Auth0 API related errors.

    Returns dictionary of client application information.
    """
    logger.debug("Retrieving Auth0 client information for {}".format(client_id))

    querystr = ''
    if not kwargs.get('all_fields'):
        selected_fields = [
            'name', 'description', 'client_id',
            'client_secret', 'tenant',
        ]
        params = {
            'fields': ','.join(selected_fields),
        }
        querystr = urllib.parse.urlencode(params)

    management_api_token = (
        kwargs.get('management_api_token')
        or get_management_api_token()
    )

    headers = {
        'Authorization': 'Bearer {}'.format(management_api_token),
    }

    get_client_url = '{}{}{}{}'.format(
        management_api_url(), 'clients/', client_id,
        ('?' + querystr if querystr else '')
    )
    try:
        response = requests.get(get_client_url, headers=headers)
    except Exception as ex:
        raise RequestException(
            'Error getting information of Auth0 client {} from {}: {!r}'.format(
                client_id, get_client_url, ex))

    try:
        resp_json = response.json()
    except ValueError:
        raise UnexpectedResponse(
            "Response content from {} is not valid json".format(get_client_url)
        )

    if response.status_code != 200:
        raise Auth0Exception(
            'Error getting information of Auth0 client {} from {}: {}'.format(
                client_id, get_client_url, resp_json))

    try:
        assert isinstance(resp_json, dict)
    except AssertionError:
        raise UnexpectedResponse(
            "Expects dict response from {}. Got {}.".format(
                get_client_url, type(resp_json)))

    logger.info('Auth0 client information successfully retrieved for {}.'.format(
        client_id
    ))

    return resp_json


def send_verification_email(user_id, **kwargs):
    """Send an email to the specified user that asks them to click a link to
    verify their email address.

    Requires `update:users` scope for the Management API.
    The user's connection must be a database connection.

    Management API access token can be provided as `management_api_token`
    in kwargs to be used to access the Auth0 Management API, otherwise it will
    utilize get_management_api_token() to obtain the necessary access token.

    Raises ImproperlyConfigured if required settings are not configured.

    Raises AuthorizationError if not authorized to access Auth0 management API.

    Raises Auth0Exception for Auth0 API related errors.

    Returns `True` if send email job is created successfully.
    """
    logger.debug("Sending email verification email to user {}".format(user_id))

    try:
        data = {
            'user_id': user_id,
            'client_id': settings.AUTH0_CLIENT_ID,
        }
    except AttributeError as ex:
        raise ImproperlyConfigured(ex)

    management_api_token = (
        kwargs.get('management_api_token')
        or get_management_api_token()
    )

    headers = {
        'Authorization': 'Bearer {}'.format(management_api_token),
    }

    url = management_api_url() + 'jobs/verification-email'
    try:
        response = requests.post(url, json=data, headers=headers)
    except Exception as ex:
        raise RequestException(
            'Error sending email verification email to Auth0 user {}'
            ' from {}: {!r}'.format(
                user_id, url, ex))

    if response.status_code != 201:
        raise Auth0Exception(
            'Error sending email verification email to Auth0 user {}'
            ' from {}: {}'.format(
                user_id, url, response.text))

    logger.info(
        "Sumitted job to send email verification email to Auth0 user {}".format(
            user_id))

    return True


def get_connections(name, **kwargs):
    """Get Auth0 connections by connection name.

    Requires `read:connections` scope for the Management API.

    Optional kwargs:

    `management_api_token`: Management API access token to be used to access the
        Auth0 Management API, otherwise it will utilize
        get_management_api_token() to obtain the necessary access token.

    Raises ImproperlyConfigured if required settings are not configured.

    Raises AuthorizationError if not authorized to access Auth0 management API.

    Raises Auth0Exception for Auth0 API related errors.

    Returns list of Auth0 connections with matching name.
    """
    logger.debug("Getting Auth0 connections for {}".format(name))

    data = {
        'name': name,
    }

    management_api_token = (
        kwargs.get('management_api_token')
        or get_management_api_token()
    )

    headers = {
        'Authorization': 'Bearer {}'.format(management_api_token),
    }

    url = management_api_url() + 'connections'
    try:
        response = requests.get(url, params=data, headers=headers)
    except Exception as ex:
        raise RequestException(
            'Error getting connection information for {} from {}: {!r}'.format(
                name, url, ex))

    try:
        resp_json = response.json()
    except ValueError:
        raise UnexpectedResponse(
            "Response content from {} is not valid json".format(url)
        )

    if response.status_code != 200:
        raise Auth0Exception(
            'Error getting connection information for {} from {}: {}'.format(
                name, url, resp_json))

    connections = resp_json
    try:
        assert isinstance(connections, list)
    except AssertionError:
        raise UnexpectedResponse(
            "Expects connections list from {}. Got {}.".format(
                url, type(connections)))

    logger.info("Retrieved connections with name {}: {}".format(
        name, connections
    ))
    return connections


def generate_password_reset_ticket(email_address, **kwargs):
    """Generates a password reset ticket represented as an URL. Following the
    URL will land on the password reset page for the user.

    Only the latest password reset ticket generated for the specified user is
    valid. Subsequent generation of password reset ticket invalidates previous
    tickets. (TODO: test)

    Requires `create:user_tickets` scope for the Management API.

    `email_adderss` must be str type.

    Optional kwargs:

    `management_api_token`: Management API access token to be used to access the
        Auth0 Management API, otherwise it will utilize
        get_management_api_token() to obtain the necessary access token.

    `result_url`: The user will be redirected to this endpoint once the ticket
        is used. No redirection is made if not specified.

    `ticket_lifetime`: The ticket's lifetime in seconds starting from the moment
        of creation. After expiration, the ticket cannot be used to change the
        user's password. If not specified or if you send 0, the Auth0 default
        lifetime of 5 days will be applied.
        Expects value convertible to int with `int()` if provided, invalid value
        is ignored and a warning is logged in such case.

    `mark_email_as_verified`: Set to `True` if email_verified attribute must be
        set to true once password is changed, `False` if email_verified
        attribute should not be updated. Default to `False`.
        *NOTE*: If set to `True`, this ticket must be sent to the email address
        of the user *ONLY*. Consuming of this ticket implies email verification.

    `connection_id`: The connection that provides the identity for which the
        password is to be changed. If not provided, it will call
        get_connections() method to retrieve the connection ID of
        `Username-Password-Authentication` from Auth0 management API.

    Raises TypeError if email address is not a string.

    Raises ImproperlyConfigured if required settings are not configured.

    Raises AuthorizationError if not authorized to access Auth0 management API.

    Raises Auth0Exception for Auth0 API related errors.

    Returns ticket represented as URL string if success.
    """
    logger.debug("Generating password reset ticket for {}".format(email_address))

    if not isinstance(email_address, str):
        raise TypeError("`email_address` must be a string, {} given.".format(
            type(email_address)
        ))

    # Convert to lower case since Auth0 stores all email addersses in lower case
    email_address = email_address.lower()

    management_api_token = (
        kwargs.get('management_api_token')
        or get_management_api_token()
    )

    result_url = kwargs.pop('result_url', None)
    ticket_lifetime = kwargs.pop('ticket_lifetime', 0)
    mark_email_as_verified = kwargs.pop('mark_email_as_verified', False)
    connection_id = kwargs.pop('connection_id', None)

    # get connection ID from management API
    if not connection_id:
        connection_name = "Username-Password-Authentication"
        connections = get_connections(
            connection_name,
            management_api_token=management_api_token
        )
        try:
            connection_id = connections[0]['id']
        except Exception:
            raise UnexpectedResponse(
                "Unable to retrieve connection ID of `{}`, "
                " connections retrieved from Auth0: {}".format(
                    connection_name, connections
                )
            )

    data = {
        'email': email_address,
        'includeEmailInRedirect': True,
        'connection_id': connection_id,
        # specify because the documentation does not indicate default value if
        # not provided.
        'mark_email_as_verified': bool(mark_email_as_verified),
    }
    if result_url:
        data['result_url'] = result_url

    try:
        ticket_lifetime = int(ticket_lifetime)
    except Exception:
        logger.warning(
            "generate_password_reset_ticket: "
            "Unable to convert `ticket_lifetime` `{!r}` to int, ignoring.".format(
                ticket_lifetime
            ))
    else:
        data['ttl_sec'] = ticket_lifetime

    headers = {
        'Authorization': 'Bearer {}'.format(management_api_token),
    }

    url = management_api_url() + 'tickets/password-change'
    try:
        response = requests.post(url, json=data, headers=headers)
    except Exception as ex:
        raise RequestException(
            'Error generating password reset ticket for {} from {}: {!r}'.format(
                email_address, url, ex))

    try:
        resp_json = response.json()
    except ValueError:
        raise UnexpectedResponse(
            "Response content from {} is not valid json".format(url)
        )

    if response.status_code != 201:
        err_msg = 'Error generating password reset ticket for {} from {}: {}'.format(
            email_address, url, resp_json)
        if resp_json.get('statusCode') == 404:
            raise NotFound(err_msg)
        raise Auth0Exception(err_msg)

    try:
        ticket = resp_json['ticket']
        assert isinstance(ticket, str)
    except Exception:
        raise UnexpectedResponse(
            "Unable to retrieve ticket from response json from {}".format(url))

    logger.info("Generated password reset ticket for {}".format(email_address))

    return ticket
