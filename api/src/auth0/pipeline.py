from social_core.pipeline import social_auth


def auth0_social_details(backend, details, response, *args, **kwargs):
    """Custom social auth pipeline that replaces stock social_details pipeline
    which passes verified access token to auth0 backend's get_user_details() to
    retrieve user details.
    """
    if (
        backend and backend.name == 'auth0'
        and kwargs.get('verified') and kwargs.get('access_token')
    ):
        # Get user details with verified access token
        result = {'details': dict(backend.get_user_details(
            response, verified=kwargs['verified']), **details)}
    else:
        result = social_auth.social_details(backend, details, response, *args, **kwargs)
    return result
