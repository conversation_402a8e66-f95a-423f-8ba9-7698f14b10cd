# -*- coding: utf-8 -*-
# Generated by Django 1.11.20 on 2019-03-15 01:33
from __future__ import unicode_literals

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='VerifiedAccessToken',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('access_token', models.TextField()),
                ('md5', models.Char<PERSON>ield(db_index=True, max_length=32)),
                ('subject', models.Char<PERSON>ield(max_length=255)),
                ('expires_at', models.DateTimeField()),
                ('userinfo', models.TextField()),
            ],
            options={
                'db_table': 'auth0_verifiedaccesstoken',
            },
        ),
    ]
