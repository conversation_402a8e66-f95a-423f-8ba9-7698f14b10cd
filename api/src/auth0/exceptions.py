from social_core.exceptions import SocialAuthBaseException


class Auth0Exception(Exception):
    """Base class for auth0 exceptions."""
    pass


class AuthorizationError(SocialAuthBaseException, Auth0Exception):
    """Exception for errors relating to authorizing a User."""
    pass


class NotFound(Auth0Exception):
    """
    Exception for account not found when calling Auth0 API regarding specific
    user/application.
    """
    pass


class RequestException(Auth0Exception):
    """Exception for errors relating to sending request to Auth0 API."""
    pass


class UnexpectedResponse(Auth0Exception):
    """Exception for unexpected response type/content from Auth0 API."""
    pass
