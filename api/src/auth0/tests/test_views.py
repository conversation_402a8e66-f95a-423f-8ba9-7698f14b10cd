from unittest.mock import patch
from urllib.parse import quote_plus

from django.contrib.auth.models import Permission
from django.contrib.contenttypes.models import ContentType
from django.http import HttpResponse
from django.test import Client, TestCase
from django.urls import reverse

from users.models import User, Integration
from auth0.models import Auth0M2MClientMixin


class ViewsTestCase(TestCase):

    def setUp(self):
        self.client = Client()

    @patch('auth0.views.django_logout')
    @patch('auth0.views.redirect')
    def test_login(self, mock_redirect, mock_logout):
        mock_redirect.return_value = HttpResponse('Test Response')

        redirect_cases = [
            ('simple', 'https://test.com/v2/logout?returnTo=' + quote_plus(
                'http://testserver/auth/landing?next=' + quote_plus(
                    'http://testserver/auth/social/login/auth0?next=' + quote_plus(
                        'http://testserver/auth/simple')))),
            ('/some-page', 'https://test.com/v2/logout?returnTo=' + quote_plus(
                'http://testserver/auth/landing?next=' + quote_plus(
                    'http://testserver/auth/social/login/auth0?next=' + quote_plus(
                        'http://testserver/some-page')))),
            # Verify that we protect against XSS attacks
            ('http://example.com/some-page', 'https://test.com/v2/logout?returnTo=' + quote_plus(
                'http://testserver/auth/landing?next=' + quote_plus(
                    'http://testserver/auth/social/login/auth0?next=' + quote_plus(
                        'http://testserver/')))),
        ]

        for logout_next_url, expected in redirect_cases:
            with self.settings(AUTH0_DOMAIN='test.com'):
                url = '{}?next={}'.format(
                    reverse('auth0:login'),
                    logout_next_url
                )
                response = self.client.get(url)
                self.assertEqual(response.status_code, 200)

                # Be sure the django standard logout is called
                assert mock_logout.call_count == 1
                # Be sure the redirect is called with the correct url
                mock_redirect.assert_called_with(expected)

            mock_logout.reset_mock()
            mock_redirect.reset_mock()

        # without next
        expected = 'https://test.com/v2/logout?returnTo=' + quote_plus(
            'http://testserver/auth/landing?next=' + quote_plus(
                'http://testserver/auth/social/login/auth0'))
        with self.settings(AUTH0_DOMAIN='test.com'):
            url = reverse('auth0:login')
            response = self.client.get(url)
            self.assertEqual(response.status_code, 200)

            # Be sure the django standard logout is called
            assert mock_logout.call_count == 1
            # Be sure the redirect is called with the correct url
            mock_redirect.assert_called_with(expected)

    @patch('auth0.views.django_logout')
    @patch('auth0.views.redirect')
    def test_logout(self, mock_redirect, mock_logout):
        mock_redirect.return_value = HttpResponse('Test Response')

        redirect_cases = [
            ('simple', 'https://test.com/v2/logout?returnTo=' + quote_plus(
                'http://testserver/auth/landing?next=' + quote_plus(
                    'http://testserver/auth/simple'))),
            ('/some-page', 'https://test.com/v2/logout?returnTo=' + quote_plus(
                'http://testserver/auth/landing?next=' + quote_plus(
                    'http://testserver/some-page'))),
            # Verify that we protect against XSS attacks
            ('http://example.com/some-page', 'https://test.com/v2/logout?returnTo=' + quote_plus(
                'http://testserver/auth/landing?next=' + quote_plus(
                    'http://testserver/'))),
        ]

        for logout_next_url, expected in redirect_cases:
            with self.settings(AUTH0_DOMAIN='test.com'):
                url = '{}?next={}'.format(
                    reverse('auth0:logout'),
                    logout_next_url
                )
                response = self.client.get(url)
                self.assertEqual(response.status_code, 200)

                # Be sure the django standard logout is called
                assert mock_logout.call_count == 1
                # Be sure the redirect is called with the correct url
                mock_redirect.assert_called_with(expected)

            mock_logout.reset_mock()
            mock_redirect.reset_mock()

        # without next
        expected = 'https://test.com/v2/logout?returnTo=' + \
            quote_plus('http://testserver/auth/landing')
        with self.settings(AUTH0_DOMAIN='test.com'):
            url = reverse('auth0:logout')
            response = self.client.get(url)
            self.assertEqual(response.status_code, 200)

            # Be sure the django standard logout is called
            assert mock_logout.call_count == 1
            # Be sure the redirect is called with the correct url
            mock_redirect.assert_called_with(expected)

    @patch('auth0.views.redirect')
    def test_landing(self, mock_redirect):
        mock_redirect.return_value = HttpResponse('Test Response')
        self.settings(LOGOUT_REDIRECT_URL='')
        redirect_cases = [
            # ('more_urls:deep', 'http://testserver/more-urls/deep/'),
            ('simple', 'http://testserver/auth/simple'),
            ('/some-page', 'http://testserver/some-page'),
            # Note, this tests settings, and not XSS protection
            ('http://example.com/something', 'http://example.com/something'),
        ]

        for logout_redirect_url, expected in redirect_cases:
            with self.settings(LOGOUT_REDIRECT_URL=logout_redirect_url):
                response = self.client.get(reverse('auth0:landing'))
                self.assertEqual(response.status_code, 200)

                # Be sure the redirect is called with the correct url
                mock_redirect.assert_called_with(expected)

            mock_redirect.reset_mock()

        next_cases = [
            ('simple', 'http://testserver/auth/simple'),
            ('/some-page', 'http://testserver/some-page'),
            # Verify that we protect against XSS attacks
            ('http://example.com/some-page', 'http://testserver/'),
        ]

        for next_url, expected in next_cases:
            url = '{}?next={}'.format(reverse('auth0:landing'), next_url)
            response = self.client.get(url)
            self.assertEqual(response.status_code, 200)

            # Be sure the redirect is called with the correct url
            mock_redirect.assert_called_with(expected)

    def test_client_details(self):
        username = 'user'
        password = '12345'
        user = User.objects.create_user(
            username=username, password=password
        )
        self.client.login(username=username, password=password)

        class IntegrationModel(Auth0M2MClientMixin):
            class Meta:
                app_label = 'test'

        client_id = 'client_12345'
        client_details = {'key': 'value'}

        # no permission
        with patch('auth0.views.get_auth0_client'):
            response = self.client.get(
                reverse('auth0:client_details', args=(client_id,))
            )
            self.assertEqual(response.status_code, 403)
        content_type = ContentType.objects.get_for_model(Integration)
        permission = Permission.objects.get(
            codename='get_m2m_client_info',
            content_type=content_type,
        )
        user.user_permissions.add(permission)

        assert user.has_perms(('users.get_m2m_client_info',))

        # retrieval succeeded
        with patch('auth0.views.get_auth0_client') as mock_get_auth0_client:
            mock_get_auth0_client.return_value = client_details
            response = self.client.get(
                reverse('auth0:client_details', args=(client_id,))
            )
            self.assertEqual(response.status_code, 200)
            content = response.json()
            self.assertEqual(content, client_details)

        # retrieval failed
        with patch('auth0.views.get_auth0_client') as mock_get_auth0_client:
            mock_get_auth0_client.side_effect = Exception
            response = self.client.get(
                reverse('auth0:client_details', args=(client_id,))
            )
            self.assertEqual(response.status_code, 200)
            content = response.json()
            self.assertEqual(content, {})
