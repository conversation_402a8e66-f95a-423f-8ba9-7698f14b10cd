import json
import re
from unittest.mock import patch
from urllib.parse import parse_qs, urlencode, urlparse

import pytest
import responses
from users.models import User
from django.core.exceptions import ImproperlyConfigured
from django.http import HttpRequest
from hypothesis import example, given
from hypothesis import strategies as st

from auth0 import util
from auth0.exceptions import (
    Auth0Exception, AuthorizationError, NotFound, UnexpectedResponse
)
from auth0.models import Auth0M2MClientMixin

from .util import patch_access_management_api


def test_generate_password():
    password = util.generate_password()
    assert isinstance(password, str)
    assert len(password) >= 12
    assert re.search(r'[A-Z]', password)
    assert re.search(r'[a-z]', password)
    assert re.search(r'[0-9]', password)
    assert re.search(r'[\!\@\#\$\%\^\&\*]', password)


@patch(
    'auth0.util.settings',
    AUTH0_DOMAIN='example.com',
)
def test_auth0_domain_happy(settings):
    expected = 'example.com'
    assert util.auth0_domain() == expected


@patch('auth0.util.settings')
def test_auth0_domain_not_configured(settings):
    del settings.AUTH0_DOMAIN
    with pytest.raises(ImproperlyConfigured):
        util.auth0_domain()


@patch(
    'auth0.util.settings',
    AUTH0_DOMAIN='example.com',
)
def test_access_token_url(settings):
    url = util.access_token_url()
    assert url == 'https://example.com/oauth/token'


@patch(
    'auth0.util.settings',
    AUTH0_DOMAIN='example.com',
)
def test_authorization_url(settings):
    url = util.authorization_url()
    assert url == 'https://example.com/authorize'


@patch(
    'auth0.util.settings',
    AUTH0_DOMAIN='example.com',
)
def test_management_api_url(settings):
    url = util.management_api_url()
    assert url == 'https://example.com/api/v2/'


@patch(
    'auth0.util.settings',
    AUTH_INTEGRATION_MODEL='users.Integration'
)
@patch('django.apps.apps.get_model', return_value='fake_integration_model')
def test_get_integration_model_happy(settings, mock_get_model):
    integration_model = util.get_integration_model()
    assert integration_model == 'fake_integration_model'


@patch(
    'auth0.util.settings',
    AUTH_INTEGRATION_MODEL=None
)
def test_get_integration_model_not_configured(mock_settings):
    with pytest.raises(ImproperlyConfigured):
        util.get_integration_model()


@patch(
    'auth0.util.settings',
    AUTH_INTEGRATION_MODEL='users'
)
def test_get_integration_model_invalid_setting(settings):
    with pytest.raises(ImproperlyConfigured):
        util.get_integration_model()


@patch(
    'auth0.util.settings',
    AUTH_INTEGRATION_MODEL='users.DoesNotExist'
)
def test_get_integration_model_not_installed(settings):
    with pytest.raises(ImproperlyConfigured):
        util.get_integration_model()


@responses.activate
@patch(
    'auth0.util.settings',
    AUTH0_DOMAIN='example.com',
    AUTH0_CLIENT_ID='some_client_id',
    AUTH0_CLIENT_SECRET='some_client_secret'
)
def test_get_token_happy_case(settings):
    responses.add(
        responses.POST,
        'https://example.com/oauth/token',
        json={'access_token': 'some_token'},
        status=200
    )
    token = util.get_token('valid_username', 'valid_password')
    assert token == 'some_token'


@patch('auth0.util.settings')
def test_get_token_no_auth0_client_id(settings):
    del settings.AUTH0_CLIENT_ID
    with pytest.raises(ImproperlyConfigured):
        util.get_token('valid_username', 'valid_password')


@patch('auth0.util.settings')
def test_get_token_no_auth0_client_secret(settings):
    del settings.AUTH0_CLIENT_SECRET
    with pytest.raises(ImproperlyConfigured):
        util.get_token('valid_username', 'valid_password')


@patch('auth0.util.settings')
def test_get_token_no_scope(settings):
    del settings.SOCIAL_AUTH_AUTH0_SCOPE
    with pytest.raises(ImproperlyConfigured):
        util.get_token('valid_username', 'valid_password')


@patch(
    'auth0.util.settings',
    AUTH0_DOMAIN=''
)
def test_get_token_invalid_url(settings):
    with pytest.raises(Auth0Exception):
        util.get_token('valid_username', 'valid_password')


@responses.activate
@patch(
    'auth0.util.settings',
    AUTH0_DOMAIN='example.com',
    AUTH0_CLIENT_ID='some_client_id',
    AUTH0_CLIENT_SECRET='some_client_secret'
)
def test_get_token_invalid_response(settings):
    responses.add(
        responses.POST,
        'https://example.com/oauth/token',
        body="Not json",
        status=200
    )
    with pytest.raises(Auth0Exception):
        util.get_token('valid_username', 'valid_password')


@responses.activate
@patch(
    'auth0.util.settings',
    AUTH0_DOMAIN='example.com',
    AUTH0_CLIENT_ID='some_client_id',
    AUTH0_CLIENT_SECRET='some_client_secret'
)
def test_get_token_invalid(settings):
    responses.add(
        responses.POST,
        'https://example.com/oauth/token',
        json={'msg': 'access denied'},
        status=401
    )
    with pytest.raises(AuthorizationError):
        util.get_token('valid_username', 'valid_password')


@responses.activate
@patch(
    'auth0.util.settings',
    AUTH0_DOMAIN='example.com',
    AUTH0_CLIENT_ID='some_client_id',
    AUTH0_CLIENT_SECRET='some_client_secret'
)
def test_get_token_no_access_token_in_response(settings):
    responses.add(
        responses.POST,
        'https://example.com/oauth/token',
        json={'msg': 'no access token'},
        status=200
    )
    with pytest.raises(Auth0Exception):
        util.get_token('valid_username', 'valid_password')


@given(st.text(), st.text())
@example('Bearer', 'some_token')
@example('Bearer', '')  # invalid
@example('', 'some_token')  # invalid
def test_get_auth_header(auth_type, credential):
    request = HttpRequest()
    auth_header = '%s %s' % (auth_type, credential)
    request.META = {'HTTP_AUTHORIZATION': auth_header}
    try:
        a, c = util.get_auth_header(request)
    # If an AuthorizationError is raised, only these issues could've caused it
    except AuthorizationError:
        assert (
            auth_type == ''
            or credential == ''
            or ' ' in auth_type
            or ' ' in credential
        )
        return
    # Otherwise, the auth_type and credential are returned correctly
    assert a == auth_type
    assert c == credential


def test_get_auth_header_blank_value():
    request = HttpRequest()
    request.META = {'HTTP_AUTHORIZATION': ''}
    assert util.get_auth_header(request) is None


def test_get_auth_header_insufficient_parts():
    request = HttpRequest()
    request.META = {'HTTP_AUTHORIZATION': 'no_space'}
    with pytest.raises(AuthorizationError):
        util.get_auth_header(request)


def test_get_auth_header_missing_header():
    request = HttpRequest()
    assert util.get_auth_header(request) is None


@patch(
    'auth0.util.settings',
    AUTH0_DOMAIN='example.com',
    AUTH0_CLIENT_ID='client_id',
    AUTH0_CLIENT_SECRET='client_secret',
)
@responses.activate
def test_get_management_api_token_happy(settings):
    responses.add(
        responses.POST,
        'https://example.com/oauth/token',
        json={'access_token': 'some_token'},
        status=200
    )
    token = util.get_management_api_token()
    assert token == 'some_token'


@patch(
    'auth0.util.settings',
    AUTH0_DOMAIN='example.com',
)
def test_get_management_api_token_no_client_id(settings):
    del settings.AUTH0_CLIENT_ID
    with pytest.raises(ImproperlyConfigured):
        util.get_management_api_token()


@patch(
    'auth0.util.settings',
    AUTH0_DOMAIN='example.com',
)
def test_get_management_api_token_no_client_secret(settings):
    del settings.AUTH0_CLIENT_SECRET
    with pytest.raises(ImproperlyConfigured):
        util.get_management_api_token()


@patch(
    'auth0.util.settings',
    AUTH0_DOMAIN='example.com',
    AUTH0_CLIENT_ID='client_id',
    AUTH0_CLIENT_SECRET='client_secret',
)
@responses.activate
def test_get_management_api_token_non_200(settings):
    responses.add(
        responses.POST,
        'https://example.com/oauth/token',
        json={'error': 'authorization error'},
        status=403
    )
    with pytest.raises(AuthorizationError):
        util.get_management_api_token()


@patch(
    'auth0.util.settings',
    AUTH0_DOMAIN='example.com',
    AUTH0_CLIENT_ID='client_id',
    AUTH0_CLIENT_SECRET='client_secret',
)
@responses.activate
def test_get_management_api_token_unexpected_response(settings):
    responses.add(
        responses.POST,
        'https://example.com/oauth/token',
        status=200
    )
    with pytest.raises(UnexpectedResponse):
        util.get_management_api_token()


@responses.activate
@patch_access_management_api
def test_get_user_by_email(management_api_mocks=None):
    test_email = '<EMAIL>'
    responses.add(
        responses.GET,
        'https://example.com/api/users-by-email?' + urlencode({'email': test_email}),
        status=200,
        json=[{'user_id': 'blah', 'email': test_email}]
    )
    user = util.get_user_by_email(test_email)
    assert len(responses.calls) == 1
    assert isinstance(user, dict)


@pytest.mark.django_db
@responses.activate
@patch_access_management_api
def test_delete_user_happy(management_api_mocks=None):
    user_id = 'blah'
    responses.add(
        responses.DELETE,
        f'https://example.com/api/users/{user_id}',
        status=204
    )

    util.delete_auth0_user(user_id=user_id)
    assert len(responses.calls) == 1

    responses.calls.reset()

    test_email = '<EMAIL>'
    responses.add(
        responses.GET,
        'https://example.com/api/users-by-email?' + urlencode({'email': test_email}),
        status=200,
        json=[{'user_id': user_id, 'email': test_email}]
    )
    user = User.objects.create_user(
        email=test_email,
        username='fooy'
    )
    util.delete_auth0_user(user=user)
    assert len(responses.calls) == 2

    responses.calls.reset()
    util.delete_auth0_user(email=test_email)
    assert len(responses.calls) == 2


@pytest.mark.django_db
@responses.activate
@patch_access_management_api
def test_delete_application_happy(management_api_mocks=None):
    client_id = 'blah'
    responses.add(
        responses.DELETE,
        f'https://example.com/api/clients/{client_id}',
        status=204
    )

    util.delete_auth0_application(client_id=client_id)
    assert len(responses.calls) == 1

    responses.calls.reset()

    class IntegrationModel(Auth0M2MClientMixin):
        client_id = 'blah'

        class Meta:
            app_label = 'users'

    integration = IntegrationModel()
    util.delete_auth0_application(integration=integration)
    assert len(responses.calls) == 1


@pytest.mark.django_db
@responses.activate
@patch_access_management_api
def test_create_auth0_user_happy(management_api_mocks=None):
    password = 'password'
    email = '<EMAIL>'
    username = 'test_user'
    first_name = 'John'
    last_name = 'Doe'
    user_id = 'user_12345'

    def request_callback(request):
        try:
            resp_body = json.loads(request.body.decode())
            assert isinstance(resp_body, dict)
        except Exception:
            resp_body = {}
        resp_body['user_id'] = user_id
        headers = {
            'Content-type': 'application/json'
        }
        return (201, headers, json.dumps(resp_body).encode())

    responses.add_callback(
        responses.POST,
        'https://example.com/api/users',
        callback=request_callback
    )

    ret = util.create_auth0_user(email='<EMAIL>')
    assert len(responses.calls) == 1
    assert ret == user_id
    assert responses.calls[0].response.json()['email'] == email
    responses.calls.reset()
    assert len(responses.calls) == 0

    user = User.objects.create_user(
        email=email,
        username=username,
        first_name=first_name,
        last_name=last_name
    )
    ret = util.create_auth0_user(user=user)
    assert len(responses.calls) == 1
    assert ret == user_id
    resp_json = responses.calls[0].response.json()
    assert resp_json['email'] == email
    assert resp_json['given_name'] == first_name
    assert resp_json['family_name'] == last_name
    assert 'username' not in resp_json
    user.delete()
    responses.calls.reset()
    assert len(responses.calls) == 0

    user_no_email = User.objects.create_user(
        username=username
    )
    ret = util.create_auth0_user(
        user=user_no_email,
        extra_user_data={
            'email': email,
            'username': username,
            'given_name': first_name,
            'last_name': last_name
        },
        raw_password=password,
        verification_email=False
    )
    assert len(responses.calls) == 1
    assert ret == user_id
    resp_json = responses.calls[0].response.json()
    assert resp_json['email'] == email
    assert resp_json['given_name'] == first_name
    assert 'user_metadata' in resp_json
    assert 'last_name' in resp_json['user_metadata']
    assert resp_json['user_metadata']['last_name'] == last_name
    assert 'username' in resp_json['user_metadata']
    assert resp_json['user_metadata']['username'] == username
    assert resp_json['password'] == password
    assert resp_json['verify_email'] is False


@responses.activate
@patch_access_management_api
def test_create_auth0_user_already_exist(management_api_mocks=None):
    email = '<EMAIL>'
    responses.add(
        responses.POST,
        'https://example.com/api/users',
        json={'error': 'user already exist'},
        status=409
    )
    ret = util.create_auth0_user(email=email)
    assert ret is False


@responses.activate
@patch_access_management_api
def test_create_auth0_user_none_result(management_api_mocks=None):
    email = '<EMAIL>'
    responses.add(
        responses.POST,
        'https://example.com/api/users',
        json='no user_id in response',
        status=201
    )
    ret = util.create_auth0_user(email=email)
    assert ret is None


@pytest.mark.django_db
@responses.activate
@patch_access_management_api
def test_create_auth0_user_type_error(management_api_mocks=None):
    responses.add(
        responses.POST,
        'https://example.com/api/users'
    )

    # no user/email keyword argument
    with pytest.raises(TypeError):
        util.create_auth0_user()
        assert len(responses.calls) == 0

    # invalid user type
    with pytest.raises(TypeError):
        util.create_auth0_user(user=object())
        assert len(responses.calls) == 0

    # no email
    user_no_email = User.objects.create_user(
        username='username'
    )
    with pytest.raises(TypeError):
        util.create_auth0_user(
            user=user_no_email
        )
        assert len(responses.calls) == 0


@responses.activate
@patch_access_management_api
def test_create_auth0_user_exceptions(management_api_mocks=None):
    email = '<EMAIL>'
    responses.add(
        responses.POST,
        'https://example.com/api/users',
        json={'error': 'server under maintenance'},
        status=503
    )
    with pytest.raises(Auth0Exception):
        util.create_auth0_user(email=email)
        assert len(responses.calls) == 1


@responses.activate
@patch_access_management_api
def test_create_auth0_application_happy(management_api_mocks=None):
    name = 'app name'
    client_id = 'client_12345'

    def request_callback(request):
        try:
            resp_body = json.loads(request.body.decode())
            assert isinstance(resp_body, dict)
        except Exception:
            resp_body = {}
        resp_body['client_id'] = client_id
        headers = {
            'Content-type': 'application/json'
        }
        return (201, headers, json.dumps(resp_body).encode())

    responses.add_callback(
        responses.POST,
        'https://example.com/api/clients',
        callback=request_callback
    )

    ret = util.create_auth0_application(name=name)
    assert len(responses.calls) == 1
    assert ret == client_id
    assert responses.calls[0].response.json()['name'] == name
    responses.calls.reset()
    assert len(responses.calls) == 0

    class IntegrationModel(Auth0M2MClientMixin):
        class Meta:
            app_label = 'users'

    integration = IntegrationModel()

    ret = util.create_auth0_application(integration=integration)
    assert len(responses.calls) == 1
    assert ret == client_id
    resp_json = responses.calls[0].response.json()
    assert resp_json['name'] == str(integration)


@responses.activate
@patch_access_management_api
def test_create_auth0_application_none_result(management_api_mocks=None):
    name = 'app name'
    responses.add(
        responses.POST,
        'https://example.com/api/clients',
        json='no client_id in response',
        status=201
    )
    ret = util.create_auth0_application(name=name)
    assert ret is None


@responses.activate
@patch_access_management_api
def test_create_auth0_application_type_error(management_api_mocks=None):
    responses.add(
        responses.POST,
        'https://example.com/api/clients'
    )

    # no integration/name keyword argument
    with pytest.raises(TypeError):
        util.create_auth0_application()
        assert len(responses.calls) == 0

    # empty name
    with pytest.raises(TypeError):
        util.create_auth0_user(name='')
        assert len(responses.calls) == 0

    # invalid integration type
    with pytest.raises(TypeError):
        util.create_auth0_application(integration=object())
        assert len(responses.calls) == 0


@responses.activate
@patch_access_management_api
def test_create_auth0_application_exceptions(management_api_mocks=None):
    name = 'app name'
    responses.add(
        responses.POST,
        'https://example.com/api/clients',
        json={'error': 'server under maintenance'},
        status=503
    )
    with pytest.raises(Auth0Exception):
        util.create_auth0_application(name=name)
        assert len(responses.calls) == 1


@responses.activate
@patch('auth0.util.settings', AUTH0_API_IDENTIFIER='some_identifier')
@patch_access_management_api
def test_create_auth0_client_grant_happy(settings, management_api_mocks=None):
    client_id = 'client_12345'

    def request_callback(request):
        try:
            resp_body = json.loads(request.body.decode())
            assert isinstance(resp_body, dict)
        except Exception:
            resp_body = {}
        headers = {
            'Content-type': 'application/json'
        }
        return (201, headers, json.dumps(resp_body).encode())

    responses.add_callback(
        responses.POST,
        'https://example.com/api/client-grants',
        callback=request_callback
    )

    ret = util.create_auth0_client_grant(client_id=client_id)
    assert len(responses.calls) == 1
    assert ret is True
    resp_json = responses.calls[0].response.json()
    assert resp_json['client_id'] == client_id
    assert resp_json['audience'] == 'some_identifier'


@responses.activate
@patch('auth0.util.settings', AUTH0_API_IDENTIFIER='some_identifier')
@patch_access_management_api
def test_create_auth0_client_grant_improperly_configured(settings, management_api_mocks=None):
    client_id = 'client_12345'
    del settings.AUTH0_API_IDENTIFIER
    with pytest.raises(ImproperlyConfigured):
        util.create_auth0_client_grant(client_id=client_id)
        assert len(responses.calls) == 0


@responses.activate
@patch('auth0.util.settings', AUTH0_API_IDENTIFIER='some_identifier')
@patch_access_management_api
def test_create_auth0_client_grant_exceptions(settings, management_api_mocks=None):
    client_id = 'client_12345'
    responses.add(
        responses.POST,
        'https://example.com/api/client-grants',
        json={'error': 'server under maintenance'},
        status=503
    )
    with pytest.raises(Auth0Exception):
        util.create_auth0_client_grant(client_id=client_id)
        assert len(responses.calls) == 1


@responses.activate
@patch_access_management_api
def test_get_auth0_client_happy(management_api_mocks=None):
    client_id = 'client_12345'

    def request_callback(request):
        urlparsed = urlparse(request.url)
        path_url = urlparsed[2]
        cid = path_url.rsplit('/')[-2] \
            if path_url.endswith('/') \
            else path_url.rsplit('/')[-1]
        query = urlparsed[4]
        try:
            resp_body = parse_qs(query)
            assert isinstance(resp_body, dict)
        except Exception:
            resp_body = {}
        resp_body['client_id'] = cid
        headers = {
            'Content-type': 'application/json'
        }
        return (200, headers, json.dumps(resp_body).encode())

    responses.add_callback(
        responses.GET,
        re.compile(r'https://example.com/api/clients/.*'),
        callback=request_callback
    )

    ret = util.get_auth0_client(client_id=client_id)
    assert len(responses.calls) == 1
    resp_json = responses.calls[0].response.json()
    assert resp_json == ret
    assert resp_json['client_id'] == client_id
    assert 'fields' in resp_json
    fields = resp_json['fields'][0].split(',')
    assert 'name' in fields
    assert 'description' in fields
    assert 'client_id' in fields
    assert 'client_secret' in fields
    assert 'tenant' in fields

    responses.calls.reset()
    assert len(responses.calls) == 0

    ret = util.get_auth0_client(client_id=client_id, all_fields=True)
    assert len(responses.calls) == 1
    resp_json = responses.calls[0].response.json()
    assert resp_json == ret
    assert resp_json['client_id'] == client_id
    assert 'fields' not in resp_json


@responses.activate
@patch('auth0.util.settings', AUTH0_API_IDENTIFIER='some_identifier')
@patch_access_management_api
def test_get_auth0_client_unexpected_response(settings, management_api_mocks=None):
    client_id = 'client_12345'
    responses.add(
        responses.GET,
        re.compile(r'https://example.com/api/clients/.*'),
        json='string response',
        status=200
    )
    with pytest.raises(UnexpectedResponse):
        util.get_auth0_client(client_id=client_id)
        assert len(responses.calls) == 1

    responses.calls.reset()

    responses.add(
        responses.GET,
        re.compile(r'https://example.com/api/clients/.*'),
        status=200
    )
    with pytest.raises(UnexpectedResponse):
        util.get_auth0_client(client_id=client_id)
        assert len(responses.calls) == 1


@responses.activate
@patch('auth0.util.settings', AUTH0_API_IDENTIFIER='some_identifier')
@patch_access_management_api
def test_get_auth0_client_exceptions(settings, management_api_mocks=None):
    client_id = 'client_12345'
    responses.add(
        responses.GET,
        re.compile(r'https://example.com/api/clients/.*'),
        json={'error': 'server under maintenance'},
        status=503
    )
    with pytest.raises(Auth0Exception):
        util.get_auth0_client(client_id=client_id)
        assert len(responses.calls) == 1


@responses.activate
@patch('auth0.util.settings', AUTH0_CLIENT_ID='client_id')
@patch_access_management_api
def test_send_verification_email_happy(settings, management_api_mocks=None):
    user_id = 'user_12345'

    def request_callback(request):
        try:
            resp_body = json.loads(request.body.decode())
            assert isinstance(resp_body, dict)
        except Exception:
            resp_body = {}
        headers = {
            'Content-type': 'application/json'
        }
        return (201, headers, json.dumps(resp_body).encode())

    responses.add_callback(
        responses.POST,
        'https://example.com/api/jobs/verification-email',
        callback=request_callback
    )

    ret = util.send_verification_email(user_id=user_id)
    assert len(responses.calls) == 1
    assert ret is True
    resp_json = responses.calls[0].response.json()
    assert resp_json['user_id'] == user_id


@responses.activate
@patch('auth0.util.settings', AUTH0_CLIENT_ID='client_id')
@patch_access_management_api
def test_send_verification_email_exception(settings, management_api_mocks=None):
    user_id = 'user_12345'
    responses.add(
        responses.POST,
        'https://example.com/api/jobs/verification-email',
        json={'error': 'server under maintenance'},
        status=503
    )
    with pytest.raises(Auth0Exception):
        util.send_verification_email(user_id=user_id)
        assert len(responses.calls) == 1


@responses.activate
@patch_access_management_api
def test_get_connections_happy(management_api_mocks=None):
    name = 'connection name'

    def request_callback(request):
        urlparsed = urlparse(request.url)
        query = urlparsed[4]
        try:
            resp_body = [parse_qs(query)]
            assert isinstance(resp_body, list)
        except Exception:
            resp_body = []
        headers = {
            'Content-type': 'application/json'
        }
        return (200, headers, json.dumps(resp_body).encode())

    responses.add_callback(
        responses.GET,
        'https://example.com/api/connections',
        callback=request_callback
    )

    ret = util.get_connections(name)
    assert isinstance(ret, list)
    assert len(responses.calls) == 1
    resp_json = responses.calls[0].response.json()
    assert resp_json == ret
    assert resp_json[0]['name'][0] == name


@responses.activate
@patch_access_management_api
def test_get_connections_exception(management_api_mocks=None):
    name = 'connection name'
    responses.add(
        responses.GET,
        'https://example.com/api/connections',
        json={'error': 'server under maintenance'},
        status=503
    )
    with pytest.raises(Auth0Exception):
        util.get_connections(name)
        assert len(responses.calls) == 1


@responses.activate
@patch_access_management_api
def test_get_connections_unexpected_response(management_api_mocks=None):
    name = 'connection name'
    responses.add(
        responses.GET,
        'https://example.com/api/connections',
        status=200
    )
    with pytest.raises(UnexpectedResponse):
        util.get_connections(name)
        assert len(responses.calls) == 1

    responses.calls.reset()
    responses.add(
        responses.GET,
        'https://example.com/api/connections',
        json='string response body',
        status=200
    )
    with pytest.raises(UnexpectedResponse):
        util.get_connections(name)
        assert len(responses.calls) == 1


@responses.activate
@patch('auth0.util.get_connections', return_value=[{'id': 'conn1'}])
@patch_access_management_api
def test_generate_password_reset_ticket_happy(mock_get_connections, management_api_mocks=None):
    email = '<EMAIL>'
    ticket = 'ticket'
    result_url = 'http://example.com'
    ticket_lifetime = 3600
    mark_email_as_verified = True
    connection_id = 'conn2'

    def request_callback(request):
        try:
            resp_body = json.loads(request.body.decode())
            assert isinstance(resp_body, dict)
        except Exception:
            resp_body = {}
        resp_body['ticket'] = ticket
        headers = {
            'Content-type': 'application/json'
        }
        return (201, headers, json.dumps(resp_body).encode())

    responses.add_callback(
        responses.POST,
        'https://example.com/api/tickets/password-change',
        callback=request_callback
    )

    ret = util.generate_password_reset_ticket(email)
    assert len(responses.calls) == 1
    assert ret == ticket
    resp_json = responses.calls[0].response.json()
    assert resp_json['email'] == email.lower()
    assert resp_json['connection_id'] == 'conn1'
    assert resp_json['mark_email_as_verified'] is False
    assert resp_json['ttl_sec'] == 0
    assert 'result_url' not in resp_json

    responses.calls.reset()

    ret = util.generate_password_reset_ticket(
        email, result_url=result_url, ticket_lifetime=ticket_lifetime,
        mark_email_as_verified=mark_email_as_verified,
        connection_id=connection_id
    )
    assert len(responses.calls) == 1
    assert ret == ticket
    resp_json = responses.calls[0].response.json()
    assert resp_json['email'] == email.lower()
    assert resp_json['connection_id'] == connection_id
    assert resp_json['mark_email_as_verified'] == mark_email_as_verified
    assert resp_json['result_url'] == result_url
    assert resp_json['ttl_sec'] == ticket_lifetime


@responses.activate
@patch_access_management_api
def test_generate_password_reset_ticket_type_error(management_api_mocks=None):
    # invalid email type
    email = ['<EMAIL>', '<EMAIL>']
    ticket = 'ticket'
    responses.add(
        responses.POST,
        'https://example.com/api/tickets/password-change',
        json={'ticket': ticket},
        status=201
    )
    with pytest.raises(TypeError):
        util.generate_password_reset_ticket(email)
        assert len(responses.calls) == 0


@responses.activate
@patch('auth0.util.get_connections', return_value=['conn1'])
@patch_access_management_api
def test_generate_password_reset_ticket_unexpected_connections(
    mock_get_connections, management_api_mocks=None
):
    email = '<EMAIL>'
    with pytest.raises(UnexpectedResponse):
        util.generate_password_reset_ticket(email)
        assert len(responses.calls) == 0


@responses.activate
@patch('auth0.util.get_connections', return_value=[{'id': 'conn1'}])
@patch_access_management_api
def test_generate_password_reset_ticket_unexpected_response(
    mock_get_connections, management_api_mocks=None
):
    email = '<EMAIL>'
    ticket = 'ticket'

    # not json
    responses.add(
        responses.POST,
        'https://example.com/api/tickets/password-change',
        status=201
    )
    with pytest.raises(UnexpectedResponse):
        util.generate_password_reset_ticket(email)
        assert len(responses.calls) == 1

    responses.calls.reset()

    # no ticket
    responses.add(
        responses.POST,
        'https://example.com/api/tickets/password-change',
        json={'key': 'value'},
        status=201
    )
    with pytest.raises(UnexpectedResponse):
        util.generate_password_reset_ticket(email)
        assert len(responses.calls) == 1

    responses.calls.reset()

    # ticket not string
    responses.add(
        responses.POST,
        'https://example.com/api/tickets/password-change',
        json={'ticket': [ticket]},
        status=201
    )
    with pytest.raises(UnexpectedResponse):
        util.generate_password_reset_ticket(email)
        assert len(responses.calls) == 1


@responses.activate
@patch('auth0.util.get_connections', return_value=[{'id': 'conn1'}])
@patch_access_management_api
def test_generate_password_reset_ticket_not_found(
    mock_get_connections, management_api_mocks=None
):
    email = '<EMAIL>'
    responses.add(
        responses.POST,
        'https://example.com/api/tickets/password-change',
        json={'statusCode': 404},
        status=404
    )
    with pytest.raises(NotFound):
        util.generate_password_reset_ticket(email)
        assert len(responses.calls) == 1


@responses.activate
@patch('auth0.util.get_connections', return_value=[{'id': 'conn1'}])
@patch_access_management_api
def test_generate_password_reset_ticket_exception(
    mock_get_connections, management_api_mocks=None
):
    email = '<EMAIL>'
    responses.add(
        responses.POST,
        'https://example.com/api/tickets/password-change',
        json={'error': 'under maintenance'},
        status=503
    )
    with pytest.raises(Auth0Exception):
        util.generate_password_reset_ticket(email)
        assert len(responses.calls) == 1
