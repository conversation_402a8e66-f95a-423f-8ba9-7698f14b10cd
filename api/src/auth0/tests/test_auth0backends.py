import json
import re
from datetime import datetime, timedelta
from hashlib import md5
from unittest import TestCase

import pytest
import responses
from users.models import User
from django.test import override_settings
from hypothesis import example, given
from hypothesis import settings as hypothesis_settings
from hypothesis import strategies as st
from social_core.tests.models import TestStorage as _TestStorage
from social_core.tests.strategy import TestStrategy as _TestStrategy
from social_django.models import UserSocialAuth

from auth0.auth0backends import Auth0UserBackend
from auth0.exceptions import AuthorizationError
from auth0.models import VerifiedAccessToken


hypothesis_settings.register_profile('testing', deadline=500)
hypothesis_settings.load_profile('testing')

TEST_AUTH0_PUBLIC_KEY = """-----B<PERSON>IN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA8xZ5zuLqf1ANfOloBITk
B3MNeHEBZzFHshq+SwF9uAMS0i/1m0hS3XKJtZJRUQsbDqHjygJ6NTMP4EcxKy5L
cqyM/Pqq9FacUG/pX6Tn0+cYVrZNXWvac2FYMAJ4Tyq/uGfTnO/SP0gshuKqaB08
9sAh9rym/Bg+55XakMziYdTbSvvPc3z1wptSgnjdMqKQVqLQRq1blPgp/bR4viQx
6CImTdpvp6NI3HSG9tf2LxcGRnqpBrP4cSk/Vm/c5kHuI92PHvJxilV7Vc+OJL8O
k3ig6/LegOppuwZMldP8AsSUAA0LE463HLVRwXJQW0JWr9LqZ2hzmKjLNbdOZLhp
AQIDAQAB
-----END PUBLIC KEY-----"""

TEST_AUTH0_PRIVATE_KEY = """***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"""

# Test JWT generated on https://jwt.io/ with test key pair
TEST_JWT_ID_TOKEN = (
    "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6ImVtYWlsQGV4YW1wbGUuY29tIiwiaWF0IjoxNTE2M"
    "jM5MDIyLCJhdWQiOiJhdXRoMF9jbGllbnRfaWQiLCJpc3MiOiJhdXRoMF9qd3RfaXNzdWVyIiwiZW1haWwiOiJlbWFpbEBleGFtcGxlLmNvbSIsImV"
    "tYWlsX3ZlcmlmaWVkIjp0cnVlLCJwaWN0dXJlIjoicGljdHVyZV91cmwifQ.oAK9rfMiUTDPGrhICTeDF_T5ZYITkQN773OCwwpw4gWc8kVJQZaIs0"
    "jhQ8ieUbxOEKo0Y6F-COxDyHlZkSL9lC9jJrqP2_f3W420UcSzjdO8DoZEd3RyZgvEibKXCftuWPxL_nKJYM3Jv7QXGKICaS7galZWccUhLNwjnxtn"
    "7nOh2-ANsRlw0bQxBw-jhNkraq0h1GO4eAegQxi3-6ov7YhwFfEXo5JmCSScDDoBdxYMXcOYyEFTOh0VGy01fyn14n9jYQQVCCz3mboUoXydV-6mTW"
    "_dnuX65YaOAq0rpELLnxpW2vGEifwKDztUb46VdLWYpiH1GIWdjC4ZlsI6gg"
)
TEST_PAYLOAD = {
    "sub": "**********",
    "name": "<EMAIL>",
    "iat": **********,
    "aud": "auth0_client_id",
    "iss": "auth0_jwt_issuer",
    "email": "<EMAIL>",
    "email_verified": True,
    "picture": "picture_url"
}

INVALID_ID_TOKENS = {
    'invalid_aud': {
        'jwt': "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************"
        "Bfand0X2lzc3VlciIsInN1YiI6IjEyMzQ1Njc4OTAiLCJlbWFpbCI6ImVtYWlsQGV4YW1wbGUuY29tIiwiZW1haWxfdmVyaWZpZWQiOnRydWV9"
        ".8q4ts-cCvLmZ6TRlvUxkny_T1qp3RdBCij2KlsuTrFnPasJ9hrt5onkLnOQAE3F4LviVU6HVsT1HH7Cn38ASpD3RROEM6PNd_WzswSLWWYq_6"
        "LQfbEuQphFCxj01F5b2685bGmUhl1-Npi1wDTqU9YFmymfphgoz-i-QWPBm6o9MggjoAgWG3rVolBO2AtLQuEdFwzo18fmWPsvqxGhVAGSSAl_"
        "vmcJcWAJTpJu4it_SVm1EJL2lyTYvVIndF7ZsKfdU7u_Vu8MyxgMx3v3uRz3jdXAkzemihsbI09Evaat6S4t1lHAvQ614gbIfGFsl3CFU_HsYD"
        "FEj0wJ-PhaHPg",
        'payload': {
            "iat": **********,
            "aud": "invalid_aud",
            "iss": "auth0_jwt_issuer",
            "sub": "**********",
            "email": "<EMAIL>",
            "email_verified": True
        }
    },
    'invalid_iss': {
        'jwt': "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************"
        "ludmFsaWRfaXNzIiwic3ViIjoiMTIzNDU2Nzg5MCIsImVtYWlsIjoiZW1haWxAZXhhbXBsZS5jb20iLCJlbWFpbF92ZXJpZmllZCI6dHJ1ZX0."
        "UCN0Ri_EjqE1-oX3a9W6iSjPoXDBaO93ToRW45iQi6V-Mc_fj7ZnNQ0hS2kMcDWm4SY8_ehx7AMDibTgRocYrfZtW56keNvqvtTqp-avwRPnGf"
        "CZVu44hiXXbdBXxFvb7YYcYWLw-Dg_jI55dEq5uff8yeejJnOFDFoUCPcT9SP9sCQVuStk7-mWozvTzy40iUouku_BjFfk5UdDJAK1EriS79S_"
        "97wLGhPr7Zp5pq1wIpe0uTNyByUok6FO8YjnwMg1VQx38guywQCGTtkocVl6Mz4nyBzLrT3zg7nbsx4Oqex-bNtZK1VvdTF1n8Ugi5U_yJLRb_"
        "NELIcy12tZow",
        'payload': {
            "iat": **********,
            "aud": "auth0_client_id",
            "iss": "invalid_iss",
            "sub": "**********",
            "email": "<EMAIL>",
            "email_verified": True
        }
    },
    'no_email': {
        'jwt': "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************"
        "F1dGgwX2p3dF9pc3N1ZXIiLCJzdWIiOiIxMjM0NTY3ODkwIiwiZW1haWxfdmVyaWZpZWQiOnRydWV9.rpSt1qS-kxI8XwTryy1S228kdZ9GEDJ"
        "8GrYqaYSBMfTs_O72hyC-FWLnlIS-tEH4p0fXAgDnSnyajoYi-OxEc3pq31cFHtadlldvDjuRBCsLhd5drkgUveBhMnpVn2oKutP0EbNUIeAAI"
        "lfx4CzhY2iDsRYmYweWJ2WG4HpmQrl06QoH9Yc5RN1tp9EfaLXrwcC_ZEz9BDD9uD1PqGsymyOvgKfCnE9ZV6lFfQmNtQ6oP2X1hvGjQcOuyo9"
        "ApPD34LCKCtT7vPgvJw1_oEYhyrhbqr7lMRUXMMU1OHvUVViIwP1lVFEpveMWnolU-LN3B4FYvkuWPepk3BcqScJ8Uw",
        'payload': {
            "iat": **********,
            "aud": "auth0_client_id",
            "iss": "auth0_jwt_issuer",
            "sub": "**********",
            "email_verified": True
        }
    },
    'email_unverified': {
        'jwt': "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************"
        "F1dGgwX2p3dF9pc3N1ZXIiLCJzdWIiOiIxMjM0NTY3ODkwIiwiZW1haWwiOiJlbWFpbEBleGFtcGxlLmNvbSIsImVtYWlsX3ZlcmlmaWVkIjpm"
        "YWxzZX0.IIxXJsauxX5n7WIGitRMaeg1nGxoJeYCOv38-AnGNVXRD39qIuaKwHKEyK6d2BP4vMwEscrvKsnq4PEnzMrSG8BLmLyJtNd6amsYqH"
        "gWk9v8kDHiLomF5ibw-QnNtdvFSXF9ny2aHVhwDpfRZsZ1FYxDfEpcmrWHe-SGBZRxqBfhRB7KJNndtFWKvg0AFp9xN9fuiuhlr4hQyv9iDv7E"
        "ZYF5lttf-pQ3t0jPqnz77xmIPsf5jX0s2lSdnFDT9iezVoUnMskD80MuLtTqA4qUKrbDyI-Z_DxvN1IS8KAKJOICVPUJMlFzaNkGjSiWUcOSkP"
        "o76Te0czKzTj3yAUL1Fg",
        'payload': {
            "iat": **********,
            "aud": "auth0_client_id",
            "iss": "auth0_jwt_issuer",
            "sub": "**********",
            "email": "<EMAIL>",
            "email_verified": False
        }
    },
}

VALID_ACCESS_TOKEN = 'valid_access_token'
VERIFIED_ACCESS_TOKEN = VerifiedAccessToken(
    access_token=VALID_ACCESS_TOKEN,
    md5=md5(VALID_ACCESS_TOKEN.encode('utf-8')).hexdigest(),
    subject=TEST_PAYLOAD['sub'],
    expires_at=datetime.utcnow() + timedelta(seconds=600)
)


SOCIAL_AUTH_AUTH0_PIPELINE = (
    'auth0.pipeline.auth0_social_details',
    'social_core.pipeline.social_auth.social_uid',
    'social_core.pipeline.social_auth.social_user',
    'social_core.pipeline.user.get_username',
    'social_core.pipeline.social_auth.associate_by_email',
    'social_core.pipeline.user.create_user',
    'social_core.pipeline.social_auth.associate_user',
    'social_core.pipeline.social_auth.load_extra_data',
    'social_core.pipeline.user.user_details'
)


class Auth0UserBackendTestCase(TestCase):

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def _create_test_strategy(self):
        strategy = _TestStrategy(self.storage, tpl=self.tpl)
        strategy.set_settings({'AUTH0_DOMAIN': self.auth0_domain})
        return strategy

    def _create_backend(self):
        return Auth0UserBackend(self.strategy, redirect_uri=self.complete_url)

    def setUp(self):
        self.name = 'auth0'
        self.auth0_domain = 'some_url.com'
        self.complete_url = 'https://some_complete_url.com/complete'
        self.tpl = None
        self.storage = _TestStorage

        self.strategy = self._create_test_strategy()
        self.backend = self._create_backend()

    def test_authorization_url(self):
        url = self.backend.authorization_url()
        self.assertEqual(url, 'https://' + self.auth0_domain + '/authorize')

    def test_access_token_url(self):
        url = self.backend.access_token_url()
        self.assertEqual(url, 'https://' + self.auth0_domain + '/oauth/token')

    def test_userinfo_url(self):
        url = self.backend.userinfo_url()
        self.assertEqual(url, 'https://' + self.auth0_domain + '/userinfo')

    @pytest.mark.django_db
    @responses.activate
    @given(st.data())
    @example(VERIFIED_ACCESS_TOKEN)
    @example(VERIFIED_ACCESS_TOKEN.access_token)  # invalid
    @example('')  # invalid
    @example(None)  # invalid
    def test_get_user_profile(self, verified):
        valid_token = VERIFIED_ACCESS_TOKEN.access_token
        valid_auth_header = 'Bearer {}'.format(valid_token)
        userinfo = {
            'sub': VERIFIED_ACCESS_TOKEN.subject,
            'email': TEST_PAYLOAD['email'],
            'email_verified': True,
            'picture': TEST_PAYLOAD['picture']
        }

        def request_callback(request):
            auth_header = request.headers.get('Authorization')
            if auth_header == valid_auth_header:
                resp_body = userinfo.copy()
                headers = {
                    'Content-type': 'application/json'
                }
                return (200, headers, json.dumps(resp_body).encode())
            return (401, {}, "Unauthorized")

        responses.add_callback(
            responses.GET,
            'https://' + self.auth0_domain + '/userinfo',
            callback=request_callback
        )

        resp = self.backend.get_user_profile(verified)

        if isinstance(verified, VerifiedAccessToken):
            # first time should get from /userinfo endpoint
            self.assertEqual(len(responses.calls), 1)
            self.assertEqual(
                responses.calls[0].request.url,
                'https://' + self.auth0_domain + '/userinfo'
            )
            if verified.access_token == VERIFIED_ACCESS_TOKEN.access_token:
                self.assertEqual(
                    responses.calls[0].request.headers.get('Authorization'),
                    valid_auth_header
                )
                self.assertTrue(isinstance(resp, dict))
                self.assertEqual(resp.get('sub'), userinfo['sub'])
                self.assertEqual(resp.get('picture'), userinfo['picture'])
                self.assertEqual(resp.get('email'), userinfo['email'])
                self.assertTrue(resp.get('email_verified'))

            # if the info is cached, data should be obtained from cache
            user = User.objects.create_user(
                userinfo['email'],
                email=userinfo['email']
            )
            social_auth_user = UserSocialAuth(
                user_id=user.id,
                uid=userinfo['sub'],
                provider=self.backend.name
            )
            social_auth_user.extra_data = userinfo.copy()
            social_auth_user.extra_data['profile_iat'] = \
                datetime.utcnow().timestamp()
            social_auth_user.save()

            # assert no further access to /userinfo endpoint
            resp = self.backend.get_user_profile(verified)
            self.assertEqual(len(responses.calls), 1)
            if verified.access_token == VERIFIED_ACCESS_TOKEN.access_token:
                self.assertTrue(isinstance(resp, dict))
                self.assertEqual(resp.get('sub'), userinfo['sub'])
                self.assertEqual(resp.get('picture'), userinfo['picture'])
                self.assertEqual(resp.get('email'), userinfo['email'])
                self.assertTrue(resp.get('email_verified'))

            # assert revisit /userinfo endpoint if cache expired
            social_auth_user.extra_data['profile_iat'] = 0
            social_auth_user.save()
            resp = self.backend.get_user_profile(verified)
            self.assertEqual(len(responses.calls), 2)
        else:
            assert resp is None

    @override_settings(AUTH0_DOMAIN='some_url.com')
    @override_settings(AUTH0_CLIENT_ID=TEST_PAYLOAD['aud'])
    @override_settings(AUTH0_CLIENT_SECRET='any secret')
    @responses.activate
    @given(
        st.dictionaries(st.text(), st.data(), max_size=3),
        st.dictionaries(st.text(), st.data(), max_size=3)
    )
    @example({'id_token': TEST_JWT_ID_TOKEN}, {})
    @example({}, {'verified': VERIFIED_ACCESS_TOKEN})
    @example({}, {'access_token': VALID_ACCESS_TOKEN})  # invalid
    @example({}, {'access_token': 'invalid_access_token'})  # invalid
    @example({'id_token': 'invalid_id_token'}, {})  # invalid
    @example({'id_token': INVALID_ID_TOKENS['invalid_aud']['jwt']}, {})  # invalid
    @example({'id_token': INVALID_ID_TOKENS['invalid_iss']['jwt']}, {})  # invalid
    @example({'id_token': INVALID_ID_TOKENS['no_email']['jwt']}, {})  # invalid
    @example({'id_token': INVALID_ID_TOKENS['email_unverified']['jwt']}, {})  # invalid
    @example({}, {})  # invalid
    def test_get_user_details(self, response, kwargs):
        valid_access_token = VALID_ACCESS_TOKEN
        valid_id_token = TEST_JWT_ID_TOKEN
        valid_auth_header = 'Bearer {}'.format(valid_access_token)
        userinfo = {
            'sub': VERIFIED_ACCESS_TOKEN.subject,
            'email': TEST_PAYLOAD['email'],
            'email_verified': True,
            'picture': TEST_PAYLOAD['picture']
        }

        def request_callback(request):
            endpoint = request.path_url[1:]
            if 'userinfo' in endpoint:
                auth_header = request.headers.get('Authorization')
                if auth_header == valid_auth_header:
                    resp_body = userinfo.copy()
                    headers = {
                        'Content-type': 'application/json'
                    }
                    return (200, headers, json.dumps(resp_body).encode())
                return (401, {}, "Unauthorized")
            elif 'verification-email' in endpoint:
                resp_body = {}
                return (200, {}, json.dumps(resp_body).encode())
            elif 'oauth/token' in endpoint:
                resp_body = {
                    'access_token': 'management_access_token',
                    'scope': 'read write',
                }
                return (200, {}, json.dumps(resp_body).encode())

        responses.add_callback(
            responses.GET,
            re.compile(f'https://{self.auth0_domain}/(userinfo|.*jobs/verification-email|oauth/token)'),
            callback=request_callback
        )

        responses.add_callback(
            responses.POST,
            re.compile(f'https://{self.auth0_domain}/(userinfo|.*jobs/verification-email|oauth/token)'),
            callback=request_callback
        )

        self.backend.strategy.set_settings({
            'AUTH0_PUBLIC_KEY': TEST_AUTH0_PUBLIC_KEY,
            'AUTH0_CLIENT_ID': TEST_PAYLOAD['aud'],
            'AUTH0_JWT_ISSUER': TEST_PAYLOAD['iss'],
        })

        try:
            resp = self.backend.get_user_details(response, **kwargs)
        except AuthorizationError:
            self.assertTrue(
                kwargs.get('verified')
                or (response and response.get('id_token'))
            )
            if kwargs.get('verified'):
                user_profile = \
                    self.backend.get_user_profile(kwargs.get('verified'))
                self.assertFalse(
                    user_profile
                    and user_profile.get('email')
                    and user_profile.get('email_verified', False)
                )
            else:
                self.assertNotEqual(response.get('id_token'), TEST_JWT_ID_TOKEN)
        else:
            try:
                id_token = response.get('id_token')
                if not id_token:
                    raise Exception()
            except Exception:
                if kwargs.get('verified'):
                    self.assertEqual(kwargs['verified'], VERIFIED_ACCESS_TOKEN)
                    user_profile = \
                        self.backend.get_user_profile(kwargs.get('verified'))
                    self.assertIsNotNone(user_profile)
                    self.assertEqual(
                        resp.get('username'),
                        user_profile.get('email')
                    )
                    self.assertEqual(
                        resp.get('email'),
                        user_profile.get('email')
                    )
                    self.assertEqual(
                        resp.get('picture'),
                        user_profile.get('picture')
                    )
                    self.assertEqual(
                        resp.get('user_id'),
                        user_profile.get('sub')
                    )
                    self.assertEqual(
                        resp.get('sub'),
                        user_profile.get('sub')
                    )
                    self.assertEqual(
                        resp.get('email_verified'),
                        user_profile.get('email_verified')
                    )
                else:
                    # all other cases should all return {}
                    self.assertEqual(resp, {})
            else:
                self.assertEqual(id_token, valid_id_token)
                self.assertEqual(resp.get('username'), TEST_PAYLOAD['email'])
                self.assertEqual(resp.get('email'), TEST_PAYLOAD['email'])
                self.assertEqual(resp.get('picture'), TEST_PAYLOAD['picture'])
                self.assertEqual(resp.get('user_id'), TEST_PAYLOAD['sub'])
                self.assertEqual(resp.get('sub'), TEST_PAYLOAD['sub'])
                self.assertEqual(
                    resp.get('email_verified'),
                    TEST_PAYLOAD['email_verified']
                )

    @given(st.dictionaries(st.text(), st.text(), max_size=5), st.data())
    @example({'user_id': 'some_user_id'}, {})
    @example(None, None)  # invalid
    def test_get_user_id(self, details, response):
        resp = self.backend.get_user_id(details, response)
        try:
            expected = details['user_id']
        except Exception:
            self.assertIsNone(resp)
        else:
            self.assertEqual(resp, expected)

    @override_settings(AUTH0_DOMAIN='some_url.com')
    @override_settings(AUTH0_CLIENT_ID=TEST_PAYLOAD['aud'])
    @override_settings(AUTH0_CLIENT_SECRET='any secret')
    @responses.activate
    @given(kwargs=st.dictionaries(st.text(), st.data()))
    @example({'access_token': 'valid_access_token',
              'verified': VERIFIED_ACCESS_TOKEN})
    @example({'access_token': 'invalid_access_token'})  # invalid
    @example({'response': {'id_token': TEST_JWT_ID_TOKEN}})
    @example({'response': {
        'id_token': INVALID_ID_TOKENS['invalid_aud']['jwt']
    }})  # invalid
    @example({'response': {
        'id_token': INVALID_ID_TOKENS['invalid_iss']['jwt']
    }})  # invalid
    @example({'response': {
        'id_token': INVALID_ID_TOKENS['no_email']['jwt']
    }})  # invalid
    @example({'response': {
        'id_token': INVALID_ID_TOKENS['email_unverified']['jwt']
    }})  # invalid
    @example({'response': {'id_token': 'invalid_id_token'}})  # invalid
    @example({'response': {}})  # invalid
    def test_authenticate(self, kwargs, *args):
        valid_access_token = VERIFIED_ACCESS_TOKEN.access_token
        valid_auth_header = 'Bearer {}'.format(valid_access_token)
        sub = VERIFIED_ACCESS_TOKEN.subject
        userinfo = {
            'sub': VERIFIED_ACCESS_TOKEN.subject,
            'email': TEST_PAYLOAD['email'],
            'email_verified': True,
            'picture': TEST_PAYLOAD['picture']
        }

        def request_callback(request):
            endpoint = request.path_url[1:]
            if endpoint == 'userinfo':
                auth_header = request.headers.get('Authorization')
                if auth_header == valid_auth_header:
                    resp_body = userinfo.copy()
                    headers = {
                        'Content-type': 'application/json'
                    }
                    return (200, headers, json.dumps(resp_body).encode())
                else:
                    return (401, {}, "Unauthorized")
            elif 'verification-email' in endpoint:
                resp_body = {}
                return (200, {}, json.dumps(resp_body).encode())
            elif 'oauth/token' in endpoint:
                resp_body = {
                    'access_token': 'management_access_token',
                    'scope': 'read write',
                }
                return (200, {}, json.dumps(resp_body).encode())
            else:
                return (200, {}, TEST_JWT_ID_TOKEN)

        responses.add_callback(
            responses.GET,
            re.compile(f'https://{self.auth0_domain}/(userinfo|authorize|.*jobs/verification-email|oauth/token)'),
            callback=request_callback
        )
        responses.add_callback(
            responses.POST,
            re.compile(f'https://{self.auth0_domain}/(userinfo|authorize|.*jobs/verification-email|oauth/token)'),
            callback=request_callback
        )

        self.backend.strategy.set_settings({
            'AUTH0_PUBLIC_KEY': TEST_AUTH0_PUBLIC_KEY,
            'AUTH0_CLIENT_ID': 'auth0_client_id',
            'AUTH0_JWT_ISSUER': 'auth0_jwt_issuer',
            'SOCIAL_AUTH_AUTH0_PIPELINE': SOCIAL_AUTH_AUTH0_PIPELINE,
        })

        try:
            resp = self.backend.authenticate(*args, **kwargs)
        except AuthorizationError:
            try:
                id_token = kwargs['response']['id_token']
                if not id_token:
                    raise Exception()
            except Exception:
                if kwargs.get('verified'):
                    self.assertNotEqual(
                        kwargs['verified'],
                        VERIFIED_ACCESS_TOKEN
                    )
                    user_profile = \
                        self.backend.get_user_profile(kwargs.get('verified'))
                    self.assertFalse(
                        user_profile
                        and user_profile.get('email')
                        and user_profile.get('email_verified', False)
                    )
            else:
                self.assertNotEqual(id_token, TEST_JWT_ID_TOKEN)
        else:
            try:
                id_token = kwargs['response']['id_token']
                if not id_token:
                    raise Exception()
            except Exception:
                if kwargs.get('verified'):
                    self.assertEqual(kwargs['verified'], VERIFIED_ACCESS_TOKEN)
                    self.assertIsNotNone(resp)
                    self.assertEqual(resp.social_user.uid, sub)
                else:
                    self.assertIsNone(resp)
            else:
                self.assertEqual(id_token, TEST_JWT_ID_TOKEN)
                self.assertIsNotNone(resp)
                self.assertEqual(resp.social_user.uid, TEST_PAYLOAD['sub'])

    @override_settings(AUTH0_DOMAIN='some_url.com')
    @override_settings(AUTH0_CLIENT_ID=TEST_PAYLOAD['aud'])
    @override_settings(AUTH0_CLIENT_SECRET='any secret')
    @responses.activate
    @given(st.dictionaries(st.text(), st.data()))
    @example({'response': {'id_token': TEST_JWT_ID_TOKEN}})
    @example({'access_token': 'valid_access_token',
              'verified': VERIFIED_ACCESS_TOKEN})
    @example({'access_token': 'invalid_access_token'})  # invalid
    @example({'response': {
        'id_token': INVALID_ID_TOKENS['invalid_aud']['jwt']
    }})  # invalid
    @example({'response': {
        'id_token': INVALID_ID_TOKENS['invalid_iss']['jwt']
    }})  # invalid
    @example({'response': {
        'id_token': INVALID_ID_TOKENS['no_email']['jwt']
    }})  # invalid
    @example({'response': {
        'id_token': INVALID_ID_TOKENS['email_unverified']['jwt']
    }})  # invalid
    @example({'response': {'id_token': 'invalid_id_token'}})  # invalid
    @example({'response': {}})  # invalid
    def test_run_pipeline(self, data):
        valid_access_token = VERIFIED_ACCESS_TOKEN.access_token
        valid_auth_header = 'Bearer {}'.format(valid_access_token)
        userinfo = {
            # 'sub': VERIFIED_ACCESS_TOKEN.subject,
            'email': TEST_PAYLOAD['email'],
            'email_verified': True,
            'picture': TEST_PAYLOAD['picture']
        }

        def request_callback(request):
            endpoint = request.path_url[1:]
            if endpoint == 'userinfo':
                auth_header = request.headers.get('Authorization')
                if auth_header == valid_auth_header:
                    resp_body = userinfo.copy()
                    headers = {
                        'Content-type': 'application/json'
                    }
                    return (200, headers, json.dumps(resp_body).encode())
                else:
                    return (401, {}, "Unauthorized")
            elif 'verification-email' in endpoint:
                resp_body = {}
                return (200, {}, json.dumps(resp_body).encode())
            elif 'oauth/token' in endpoint:
                resp_body = {
                    'access_token': 'management_access_token',
                    'scope': 'read write',
                }
                return (200, {}, json.dumps(resp_body).encode())
            else:
                return (200, {}, TEST_JWT_ID_TOKEN)

        responses.add_callback(
            responses.GET,
            re.compile(f'https://{self.auth0_domain}/(userinfo|authorize|.*jobs/verification-email|oauth/token)'),
            callback=request_callback
        )
        responses.add_callback(
            responses.POST,
            re.compile(f'https://{self.auth0_domain}/(userinfo|authorize|.*jobs/verification-email|oauth/token)'),
            callback=request_callback
        )

        self.backend.strategy.set_settings({
            'AUTH0_PUBLIC_KEY': TEST_AUTH0_PUBLIC_KEY,
            'AUTH0_CLIENT_ID': 'auth0_client_id',
            'AUTH0_JWT_ISSUER': 'auth0_jwt_issuer',
            'SOCIAL_AUTH_AUTH0_PIPELINE': SOCIAL_AUTH_AUTH0_PIPELINE,
        })

        kwargs = {
            'response': data.pop('response', {}),
        }
        if 'access_token' in data:
            kwargs['access_token'] = data['access_token']
        if 'verified' in data:
            kwargs['verified'] = data['verified']

        try:
            out = self.backend.run_pipeline(
                SOCIAL_AUTH_AUTH0_PIPELINE, 0, **kwargs
            )
        except AuthorizationError:
            try:
                id_token = kwargs['response']['id_token']
                if not id_token:
                    raise Exception()
            except Exception:
                if kwargs.get('verified'):
                    self.assertNotEqual(
                        kwargs['verified'],
                        VERIFIED_ACCESS_TOKEN
                    )
                    user_profile = \
                        self.backend.get_user_profile(kwargs.get('verified'))
                    self.assertFalse(
                        user_profile
                        and user_profile.get('email')
                        and user_profile.get('email_verified', False)
                    )
            else:
                self.assertNotEqual(id_token, TEST_JWT_ID_TOKEN)
        else:
            if kwargs.get('verified'):
                user_profile = self.backend.get_user_details({}, verified=kwargs.get('verified'))
            elif kwargs.get('response') and kwargs.get('response').get('id_token'):
                user_profile = self.backend.get_user_details(kwargs['response'])
            else:
                user_profile = None
            # assert exits pipeline and returns None if no uid (from 'sub' of
            # userinfo)
            if not user_profile or not user_profile.get('sub'):
                self.assertIsNone(out)
            else:
                self.assertIsNotNone(out)
