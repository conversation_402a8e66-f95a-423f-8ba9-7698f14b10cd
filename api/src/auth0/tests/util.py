from contextlib import contextmanager
from functools import wraps
from unittest.mock import patch


@contextmanager
def patch_multiple_targets(mock_patches):
    """
    `mock_patches` is a list (or iterable) of mock.patch objects
    Example usage:
        with patch_multiple_targets([
            patch('os.path.exists', side_effect=mock_path_exists),
            patch('subprocess.Popen'),
            patch('os.mkdir'),
            patch('os.symlink')
        ]):
            do_stuff()
    Or if you need to do stuff with the mock objects:
        with patch_multiple_targets([
            patch('os.path.exists', side_effect=mock_path_exists),
            patch('subprocess.Popen'),
            patch('os.mkdir'),
            patch('os.symlink')
        ]) as (mpe, msp, mom, mos):
            do_stuff_with_mocks(mpe, msp, mom, mos)
    """
    mocks = []

    for mock_patch in mock_patches:
        _mock = mock_patch.start()
        mocks.append(_mock)

    yield mocks

    for mock_patch in mock_patches:
        mock_patch.stop()


def patch_access_management_api(func, *args, **kwargs):
    """Patches several parts of accessing the Auth0 management API"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        with patch_multiple_targets([
            patch('auth0.util.get_management_api_token', return_value='some_token'),
            patch(
                'auth0.util.management_api_url',
                return_value='https://example.com/api/'
            ),
        ]) as (mock_gmat, mock_mau):
            mocks = {
                'get_management_api_token': mock_gmat,
                'management_api_url': mock_mau
            }
            kwargs['management_api_mocks'] = mocks

            return func(*args, **kwargs)

    return wrapper
