# App Usage

A drop-in replacement for Django and DRF's reverse() methods. Uses the django
cache, which must be pre-populated by way of the management command as a cache
to speed up generation of reverse urls in specified url namespaces. Originally
built to accelerate the reversal of urls for the DRF Hyperlink Relation Fields.

Optionally, consider using redis as the django cache for even greater speed gains.

## Populating the cache.

This command flushes the current entries in the cache and then populates it:
```bash
./manage.py populate_url_cache
```
This process looks for the following variables in the `settings.py` file:

`BASE_URL` should be the url from which the root urls.py is served. For
example, "https://mysite.com". This of course limits the site to only be served
from a single domain, but it allows for much speedier reversing of cached urls.

`CACHEABLE_URL_NAMESPACES` the namespaces to be cached. Note that ONLY
namespaces present in the top level of the `ROOT_URLCONF` may be specified
here.  At present, this app does not yet support specifying nested namespaces,
nor does it support named urls outside of a namespace (such as named urls
defined directly in the `ROOT_URLCONF`). These features can be added when
needed. Instead, top-level namespaces may be specified, and all urls within
will be cached (including urls in nested namespaces).

## Reversing cached urls.

Since this reverse is a drop-in replacement, simply replace the import of
`reverse` from django with the following:

```python
from cached_reverse.urls import reverse

path = reverse(...)
```

A key difference is that the `viewname` must be a url pattern name. This
`reverse` does NOT support passing view callables at this time. That feature
can be added if needed.
