import logging
from functools import lru_cache
from importlib import import_module

from django.conf import settings
from django.core.cache import cache as django_cache
from django.urls.resolvers import URLPattern, URLResolver

logger = logging.getLogger(__name__)


def _get_root_urlpatterns():
    """Get project's root urlpatterns from settings.ROOT_URLCONF."""
    root_url_module = import_module(settings.ROOT_URLCONF)
    return root_url_module.urlpatterns


@lru_cache(maxsize=32)
def _get_cached_url(urlpattern_name):
    """Retrieve value from Django cache using the urlpattern name. Adds an LRU
    cache to not hit the Django cache during some calls.
    """
    return django_cache.get(urlpattern_name)


def _cache_urlpatterns(urlpatterns, resolver=None, namespace=None, namespace_path=None):
    """Recursively loop through all urlpatterns in the provided list, caching
    any urls which are children of settings.CACHEABLE_URL_NAMESPACES.

    resolver and namespace_prefix should be left at their default values (None)
    when invoking this function for the base ROOT_URLCONF urlpatterns list.

    `urlpatterns`: The list of urlpatterns to search for cacheable patterns.
                   Usually this will be invoked with the urlpatterns list from
                   the ROOT_URLCONF, and recursion will handle traversing all
                   child urlpatterns.

    `resolver`: (default: None) The Django URLResolver for the current namespace
                which is used to get the urltemplates and param lists for the
                matched urlpatterns. Remember: leave this at the default.
                Recursion will manage it.

    `namespace`: (default: None) The complete namespace of the provided
                 urlpatterns. In other words, if the current list of
                 urlpatterns is in a nested namespace, all of the namespaces
                 should be included. For example, `polls` or `polls:choices`.
                 Remember: leave this at the default. Recursion will manage it.

    `namespace_path`: (default: None) The complete path of the provided
                      namespaces. In other words, it represents the portion of
                      the URL that corresponds to namespaces, nested namespaces
                      are supported. For example, `polls/` or `polls/choices/`.
                      Remember: leave this at the default. Recursion will
                      manage it.
    """
    for pattern in urlpatterns:
        logger.debug(f'Considering pattern {pattern.pattern}')
        # pattern might be a resolver that we need to recurse into, so we check
        if isinstance(pattern, URLResolver):
            logger.debug(f'{pattern.pattern} is a URLResolver')
            # alias the name to better reflect what it is
            resolver = pattern

            # "if namespace" covers the case if we're already in a cacheable
            # namespace (meaning we've recursed at least once). If we're within
            # a "parent" cacheable namespace, we always further recurse into
            # "child" resolvers.
            if namespace:
                next_namespace = f'{namespace}:{resolver.namespace}'
            elif resolver.namespace in settings.CACHEABLE_URL_NAMESPACES:
                next_namespace = resolver.namespace
            else:
                # if neither of the above are true, we just skip caching this resolver
                continue

            # get the portion of the url for the currentnamespace
            next_namespace_path = str(resolver.pattern)
            if next_namespace_path.startswith('^'):
                next_namespace_path = next_namespace_path[1:]
            if namespace_path:
                next_namespace_path = f'{namespace_path}{next_namespace_path}'

            logger.debug(
                f'{resolver.pattern} has a cacheable namespace ({resolver.namespace}) or is '
                f'a child of a cacheable namespace. Recursing into next_namespace: '
                f'{next_namespace} with next_namespace_path: {next_namespace_path}'
            )
            _cache_urlpatterns(
                resolver.urlconf_name,
                resolver=resolver,
                namespace=next_namespace,
                namespace_path=next_namespace_path
            )
            continue

        elif isinstance(pattern, URLPattern):
            # We can early-out if namespace is None, because we don't support
            # patterns outside of namespaces at this time.
            logger.debug(f'Attempting to get top-level namespace from namespace {namespace}')
            try:
                top_level_namespace = namespace.split(':')[0]
            except AttributeError:
                continue

            # if we are in a cacheable namespace, then cache the pattern
            logger.debug(f'Checking if top level namespace {top_level_namespace} is cacheable.')
            if top_level_namespace in settings.CACHEABLE_URL_NAMESPACES:
                logger.debug(f'{top_level_namespace} is a cacheable namespace. Caching patterns.')
                urlpattern_name = f'{namespace}:{pattern.name}'
                logger.info(f'urlpattern_name: {urlpattern_name}')
                url_data_list = resolver.reverse_dict.getlist(pattern.name)
                for url_data, _, _, _ in url_data_list:
                    for url_template, _ in url_data:
                        url_template = f'{settings.BASE_URL}/{namespace_path}{url_template}'

                        logger.debug(
                            "Clearing cached value for url pattern "
                            f"{urlpattern_name}"
                        )
                        django_cache.delete(urlpattern_name)

                        logger.debug(
                            f"Caching urlpattern {urlpattern_name} "
                            f"with template {url_template}"
                        )
                        django_cache.set(urlpattern_name, url_template, timeout=None)


def populate_cache():
    """
    Populate the django cache with all the patterns in
    settings.CACHEABLE_URL_NAMESPACES from settings.ROOT_URLCONF.

    Creates entries in the format:
    <urlpattern_name> : {"url": <url_template>, "params": <list_of_required_params>}

    i.e.
    'polls:choices' : {
        "url": "polls/choices/%(choice_id)s/",
        "params": ["choice_id"]
    }

    Those entries will be utilized by the provided cached_reverse.urls.reverse
    function to provide a quicker reverse than the stock reverse provider with
    django or django-rest-framework.

    The process has two steps:
    1- Flush all existing entries
    2- Recurse through the URLResolvers and cache the urlpatterns under the
       namespaces defined in settings.CACHEABLE_URL_NAMESPACES.
    """
    if not settings.CACHEABLE_URL_NAMESPACES:
        raise Exception('CACHEABLE_URL_NAMESPACES is not configured.')

    # Clear the lrucache
    _get_cached_url.cache_clear()

    _cache_urlpatterns(_get_root_urlpatterns())
