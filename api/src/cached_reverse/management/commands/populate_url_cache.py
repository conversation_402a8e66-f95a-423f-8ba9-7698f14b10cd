from django.core.management.base import BaseCommand

from cached_reverse.util import populate_cache


class Command(BaseCommand):
    help = 'Populates the cache which is used to speed up reversing for API urls.'

    def handle(self, *args, **kwargs):
        """Clean and populate the cache."""
        populate_cache()
        self.stdout.write(self.style.SUCCESS('URL reverse cache generation completed.'))
