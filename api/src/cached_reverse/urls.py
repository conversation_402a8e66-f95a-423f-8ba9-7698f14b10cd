import logging

from rest_framework.reverse import preserve_builtin_query_params
from rest_framework.reverse import reverse as drf_reverse

from cached_reverse.util import _get_cached_url

logger = logging.getLogger(__name__)


def reverse(viewname, args=None, kwargs=None, request=None, format=None, **extra):
    """
    Limited drop-in replacement for DRF reverse method. It is limited because
    it does NOT currently support viewnames which are callable views. It will
    only reverse urlpattern name strings.

    Relies on the django cache which is expected to have been pre-populated by
    a management command with the cached urls. See the management directory of
    this app for more information.
    """
    _viewname = viewname
    if request and request.version is not None and not viewname.startswith(request.version):
        _viewname = f'{request.version}:{viewname}'

    url = _get_cached_url(_viewname)
    if url:
        # cache hit!
        url = url % kwargs
        return preserve_builtin_query_params(url, request)

    # cache missed, use drf reverse to return url
    return drf_reverse(viewname, args=args, kwargs=kwargs, request=request, format=format, **extra)
