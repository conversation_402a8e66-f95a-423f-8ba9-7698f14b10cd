from django.conf import settings
from django.core.cache import cache
from django.test import TestCase, override_settings
from django.urls import include, path

from cached_reverse.util import _cache_urlpatterns


def dummy_view():
    return 'I am a dummy view callable.'


@override_settings(
    BASE_URL='http://testserver', CACHEABLE_URL_NAMESPACES=['cacheable_ns_1', 'cacheable_ns_2']
)
class CacheURLPatternsTestCase(TestCase):

    @classmethod
    def setUpTestData(cls):
        cls.child_ns_urlpatterns = [
            path(r'sub_child', dummy_view, name='sub_child')
        ]
        cls.cacheable_ns_1_urlpatterns = [
            path(r'ns1_child', dummy_view, name='ns1_child'),
            path(
                r'child_ns/',
                include((cls.child_ns_urlpatterns, 'child_ns'), namespace='child_ns')
            )
        ]
        cls.cacheable_ns_2_urlpatterns = [
            path(r'ns2_child', dummy_view, name='ns2_child')
        ]
        cls.urlpatterns = [
            path(r'^pir$', dummy_view, name='pattern_in_root'),
            path(
                r'^cacheable_ns_1/',
                include(
                    (cls.cacheable_ns_1_urlpatterns, 'cahceable_ns_2'),
                    namespace='cacheable_ns_1'
                )
            ),
            path(
                r'^cacheable_ns_2/',
                include(
                    (cls.cacheable_ns_2_urlpatterns, 'cacheable_ns_2'),
                    namespace='cacheable_ns_2'
                )
            )
        ]
        _cache_urlpatterns(cls.urlpatterns)

    def test_can_cache_non_nested_pattern(self):
        assert cache.get('cacheable_ns_1:ns1_child') == f'{settings.BASE_URL}/cacheable_ns_1/ns1_child'

    def test_can_cache_nested_pattern(self):
        assert cache.get('cacheable_ns_1:child_ns:sub_child') == \
            f'{settings.BASE_URL}/cacheable_ns_1/child_ns/sub_child'

    def test_can_cache_multiple_namespaces(self):
        """Just double check that there isn't some bug where only the first
        cacheable_url_namespace is cached for some reason.
        """
        assert cache.get('cacheable_ns_2:ns2_child') == f'{settings.BASE_URL}/cacheable_ns_2/ns2_child'

    def test_does_not_cache_patterns_in_root_urlpatterns(self):
        assert cache.get('pattern_in_root') is None
