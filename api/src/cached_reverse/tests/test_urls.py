from unittest.mock import patch

from django.conf import settings
from django.test import TestCase, override_settings
from django.urls import include, path

from cached_reverse.urls import reverse
from cached_reverse.util import _cache_urlpatterns


def dummy_view(kwargs):
    return 'I am a dummy view callable.'


@override_settings(BASE_URL='http://testserver', CACHEABLE_URL_NAMESPACES=['cacheable_ns'])
class CachedReverseTestCase(TestCase):

    @classmethod
    def setUpTestData(cls):
        cls.child_ns_urlpatterns = [
            path('sub_child/<int:sub_child_param>', dummy_view, name='sub_child')
        ]
        cls.cacheable_ns_1_urlpatterns = [
            path('child/<int:child_param>', dummy_view, name='child'),
            path(
                'child_ns/',
                include((cls.child_ns_urlpatterns, 'child_ns'), namespace='child_ns')
            )
        ]
        cls.urlpatterns = [
            path(
                'cacheable_ns/',
                include((cls.cacheable_ns_1_urlpatterns, 'cacheable_ns'), namespace='cacheable_ns')
            )
        ]
        _cache_urlpatterns(cls.urlpatterns)

    @patch('cached_reverse.urls.drf_reverse')
    def test_cache_hit(self, mock_drf_reverse):
        url = reverse('cacheable_ns:child', kwargs={'child_param': 1234})
        assert url == f'{settings.BASE_URL}/cacheable_ns/child/1234'

        url = reverse('cacheable_ns:child_ns:sub_child', kwargs={'sub_child_param': 1234})
        assert url == f'{settings.BASE_URL}/cacheable_ns/child_ns/sub_child/1234'

        # The very fact that we're able to reverse urls which don't exist in
        # the urls.py configured in settings.py or raise some other error is
        # proof that the cache is being hit. But just to double check, we can
        # assert that DRF reverse was not called.
        mock_drf_reverse.assert_not_called()

    @patch('cached_reverse.urls.drf_reverse')
    def test_cache_miss(self, mock_drf_reverse):
        mock_drf_reverse.return_value = 'mocked_url'
        url = reverse('i_am_uncacheable', kwargs={'meaningless_param': 1234})
        assert mock_drf_reverse.return_value == url
        mock_drf_reverse.assert_called_once()
