from unittest.mock import MagicMock, patch

from api_metrics.metrics import put_metric_data


def test_put_metric_data_trigger():
    """Test that settings.METRICS_CLOUDWATCH_NAMESPACE causes put_metric_data
    to invoke boto3.
    """
    sample_data = {'some_metric': 'data'}
    mock_cloudwatch_client = MagicMock()

    # This mocks the actual outbound call to cloudwatch
    mock_put_metric_data = MagicMock()
    mock_cloudwatch_client.put_metric_data = mock_put_metric_data

    with patch('api_metrics.metrics._get_cloudwatch_client', return_value=mock_cloudwatch_client):
        # No metric put when settings.METRICS_CLOUDWATCH_NAMESPACE is not defined
        put_metric_data(sample_data)
        assert not mock_put_metric_data.called

        sample_namespace = 'sample_namespace'
        with patch(
            'api_metrics.metrics.settings.METRICS_CLOUDWATCH_NAMESPACE',
            return_value=sample_namespace
        ):
            # Metric is put when setting is defined.
            put_metric_data(sample_data)
            assert mock_put_metric_data.called_with(
                MetricData=sample_data,
                Namespace=sample_namespace
            )
