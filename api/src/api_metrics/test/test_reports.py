import logging

from django.conf import settings
from django.test import Client, TestCase
from django.urls import reverse
from request_profiler.models import ProfilingRecord, RuleSet

from api_metrics.reports import build_response_time_report
from users.tests.util import create_staff_user

logger = logging.getLogger(__name__)


class ResponseTimeReportTestCase(TestCase):

    def setUp(self):
        self.staff_user, self.staff_user_url = create_staff_user()
        self.staff_client = Client()
        self.staff_client.login(username=self.staff_user.username, password='12345')

        # save a default ruleset so that profiles start being stored
        RuleSet().save()
        self.staff_client.get(reverse('v6:datapool-list'))

    def test_response_time_report_no_breaching(self):
        ProfilingRecord.objects.all().delete()
        under_threshold = settings.RESPONSE_TIME_ALARM_THRESHOLD - 0.5
        for rec in ProfilingRecord.objects.all():
            rec.duration = under_threshold
            rec.save()
        csv_data = build_response_time_report()
        self.assertIsNone(csv_data)

    def test_response_time_report_breaching(self):
        over_threshold = settings.RESPONSE_TIME_ALARM_THRESHOLD + 0.5
        for rec in ProfilingRecord.objects.all():
            rec.duration = over_threshold
            rec.save()
        csv_data = build_response_time_report()
        self.assertIsNotNone(csv_data)
        csv_data.pop(0)  # remove header row
        for rec in ProfilingRecord.objects.all():
            for row in csv_data:
                self.assertEqual(row[0], str(rec.start_ts))
                self.assertEqual(row[1], str(over_threshold))
                self.assertEqual(row[2], str(rec.http_method))
                self.assertEqual(row[3], str(rec.request_uri))
                self.assertEqual(row[4], str(rec.query_string))
