import csv
import datetime
import logging
from io import <PERSON><PERSON>

from celery import shared_task
from django.conf import settings

from api_metrics import put_metric_data as put_metric_data_action
from api_metrics.reports import build_response_time_report
from slack_client.actions import upload_file_to_slack_action

logger = logging.getLogger(__name__)


@shared_task
def put_metric_data(metric_data):
    put_metric_data_action(metric_data)


@shared_task
def response_time_report():
    """A periodic task that analyzes the last 24 hours of requests and
    publishes a csv file collating all requests that breached a duration
    threshold."""
    logger.info("Starting response time report task.")
    csv_rows = build_response_time_report()

    if not csv_rows:
        logger.info("No breaches found in last period. Task complete.")
        return

    logger.info("Creating csv file and publishing to slack.")

    with StringIO() as tmpf:
        csv_writer = csv.writer(tmpf)
        csv_writer.writerows(csv_rows)
        slack_message = {
            'channels': settings.SLACK_MONITORING_CHANNEL,
            'content': tmpf.getvalue(),
            'filename': f'response_time_breach_report_{datetime.date.today()}.csv',
            'filetype': 'csv',
            'initial_comment': (
                f'{len(csv_rows) - 1} request response time(s) breaching the threshold of '
                f'{settings.RESPONSE_TIME_ALARM_THRESHOLD} seconds were detected '
                'in the last 24 hours.'
            )
        }
        upload_file_to_slack_action(settings.SLACK_TOKEN, **slack_message)
    logger.info("Response time reporting task complete.")
