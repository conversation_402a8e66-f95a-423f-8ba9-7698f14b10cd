from django.db import connection
from django.db.utils import OperationalError

from api_metrics import put_metric_data

"""Tests django's ability to connect to its database and publish a binary
metric based on the results.  If any other exceptions are raised by the test it
will stop the metric from being published and it will appear as missing data in
the cloudwatch alarm. Alarms can treat missing data in different ways so this
allows us to make decisions about missing data in cloudwatch rather than here."""

try:
    connection.ensure_connection()
except OperationalError:
    value = 0
else:
    value = 1

put_metric_data([{
    'MetricName': 'database-available',
    'Unit': 'Count',
    'Value': value
}])
