from django.conf import settings

from api_metrics import put_metric_data
from glynt_api.util import is_boxcars_available

"""Pings the boxcars app server and publishes a heartbeat metric. Will
be expanded to include other health metrics once a boxcars health endpoint
is exposed."""

if is_boxcars_available():
    value = 1
else:
    value = 0

put_metric_data([{
    'MetricName': f'boxcars-{settings.ENVIRONMENT}-heartbeat',
    'Unit': 'Count',
    'Value': value
}])
