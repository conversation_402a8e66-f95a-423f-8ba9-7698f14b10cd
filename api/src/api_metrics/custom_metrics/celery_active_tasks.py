from api_metrics import put_metric_data
from glynt_api.celery import app

"""Publishes the number of active celery tasks for each available worker."""


task_totals = {}

i = app.control.inspect()

# there can be several workers across different hosts for a single task
for worker_name, active_tasks in i.active().items():
    # worker names are currently in 'queue@host' format
    if '@' in worker_name:
        task = worker_name.split('@')[0]
        task_totals[task] = task_totals.get(task, 0) + len(active_tasks)

for worker_name in task_totals:
    put_metric_data([{
        "MetricName": f"{worker_name}-ActiveTasks",
        "Unit": "Count",
        "Value": task_totals[worker_name]
    }])
