import requests
from django.conf import settings

from api_metrics import put_metric_data

"""Publishes the number of messages in each available queue waiting to be
processed."""

# TODO: This is similar to code that in other Glynt components. We might
# decide to consolidate them.

mgt_base_url = settings.RABBITMQ_MANAGEMENT_URL
mgt_user = settings.RABBITMQ_MANAGEMENT_USER_NAME
mgt_password = settings.RABBITMQ_MANAGEMENT_USER_PASSWORD
url = f"{mgt_base_url}/api/queues"

session = requests.Session()
session.auth = (mgt_user, mgt_password)
resp = session.get(url)

queues = resp.json()
for queue in queues:
    queue_name = queue['name']
    is_durable = queue.get("durable", False)  # would survive a broker restart
    if is_durable and queue_name.startswith("glynt_api_"):
        put_metric_data([{
            'MetricName': f'{queue_name}-QueueCount',
            'Unit': 'Count',
            'Value': queue.get('messages', 0)
        }])
