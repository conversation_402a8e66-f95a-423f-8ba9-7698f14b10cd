import logging

import boto3
from django.conf import settings

logger = logging.getLogger(__name__)


def _get_cloudwatch_client():
    return boto3.client('cloudwatch')


def put_metric_data(metric_data):
    """Given a list of metric_data points, publish them to Cloudwatch.
    metric_data should be a list of the following form, with one "metric dict"
    per metric to put.

        [
            {
                'MetricName': '<str: metric name>',
                'Unit': '<str: unit of metric>',
                'Value': '<float or int: metric value>,
                'Dimensions': [
                    {
                        'Name': '<str: name of dimension>'
                        'Value': '<str: value of dimension>'
                    }
                ]
            }
        ]

    MetricName, Unit, and Value are all required. Dimensions is optional.

    NOTE: boto3 does not allow lists of over 20 metric points to be published
    at the same time.
    """
    if settings.METRICS_CLOUDWATCH_NAMESPACE:

        cloudwatch = _get_cloudwatch_client()

        logger.debug('Publishing metric data to cloudwatch: {}"'.format(metric_data))

        cloudwatch.put_metric_data(
            MetricData=metric_data,
            Namespace=settings.METRICS_CLOUDWATCH_NAMESPACE
        )
