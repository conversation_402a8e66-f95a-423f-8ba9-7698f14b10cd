import logging
from datetime import timedelta

from django.conf import settings
from django.utils import timezone
from request_profiler.models import ProfilingRecord

logger = logging.getLogger(__name__)


def build_response_time_report():
    logger.info("Querying request profiler for breaching durations.")
    a_day_ago = timezone.now() - timedelta(days=1)
    breaching_requests = ProfilingRecord.objects.filter(
        duration__gte=settings.RESPONSE_TIME_ALARM_THRESHOLD,
        start_ts__gt=a_day_ago
    )
    if breaching_requests:
        logger.info(f"Found {len(breaching_requests)} breaching requests.")
        fieldnames = [
            'start_ts', 'duration', 'http_method', 'request_uri', 'query_string',
            'view_func_name', 'response_status_code', 'remote_addr'
        ]
        rows = [fieldnames]
        for profiler in breaching_requests:
            row = []
            for f in fieldnames:
                row.append(str(getattr(profiler, f)))
            rows.append(row)
        return rows
