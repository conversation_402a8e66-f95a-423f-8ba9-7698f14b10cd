"""
Django settings for glynt_api project.
"""
import json
import logging
import os
import sys
from pathlib import Path
from urllib.parse import quote_plus, urlparse

import boto3
from dotenv import load_dotenv

from .celery_beat_schedule import BEAT_SCHEDULE

# auth0_jwt and celeryconfig below require the environment to be set up
load_dotenv()


from glynt_api import auth0_jwt  # noqa: E402
from glynt_api import celeryconfig  # noqa: E402


ENVIRONMENT = os.getenv('APP_ENV')
ENVIRONMENT = 'testing' if 'test' in sys.argv else ENVIRONMENT
assert ENVIRONMENT in ('development', 'testing', 'sandbox', 'stage', 'production')
if ENVIRONMENT in ['testing', 'development']:
    IS_LIVE_ENV = False
else:
    IS_LIVE_ENV = True
# When customer environments exist, this list can be used to mark those
# environments as production environments
PRODUCTION_ENVS = ['production']

if ENVIRONMENT == 'development':
    # This is a unique identifier of the machine running the development
    # environment. It is used to generate unique tenant_id's for each instance
    # of the dev env. (See organizations.models.DataPool.tenant_id).
    with open('/etc/machine-id', 'r') as f:
        DEV_ENV_UNIQUE_ID = f.read()

# What our Virtual Private Cloud address is
VPC_HOST = os.getenv('VPC_HOST', None)

# Super gross hack to just keep moving. Anywhere this is actually used should
# be refactored to be less awful, but we need to get the MVP out the door.
DEVELOPMENT_HOST = os.getenv('DEVELOPMENT_HOST', 'https://localhost:10443')

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = os.getenv('APP_SECRET_KEY')
if SECRET_KEY is None and ENVIRONMENT in ('testing', 'development'):
    SECRET_KEY = 'kfinb1dt3cCVzOhA6zeuvQ1Q5eXi5K5A6pAZWRoPibMZTXIwlhmjNCDIvW2SH'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = not IS_LIVE_ENV

ALLOWED_HOSTS = os.getenv('APP_ALLOWED_HOSTS', '').split(',')

# Build paths inside the project like this: os.path.join(BASE_DIR, ...)
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# Defines the base url upon which relative paths are based. For example:
# https://api.glynt.ai<glynt-api/settings.py> modded to defeat
BASE_URL = os.getenv('APP_BASE_URL')

# list of API versions. Used throughout settings.
VERSIONS = ['v6']

# Path to the common/fixtures directory, containing test files.
COMMON_FIXTURES_PATH = Path(BASE_DIR, '../../common/fixtures')

# Path to the common/Dockerized_GS directory, resourecs for running Ghostscript inside a secure container.
COMMON_DOCKERIZED_GS_PATH = Path(BASE_DIR, '../../common/dockerized_GS')

# Some tests may only be run within the local vagrant VM dev environment, and will fail in CI (either CI in the cloud,
# or locally using the CI Ubuntu image via <runtests.sh>). This toggles whether or not to run them. Set to `True`
# temporarily in your local environment to run these tests.
DEV_ENVIRONMENT_INSTEAD_OF_CI = False

# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'admin_tools.apps.AdminToolsConfig',  # Our app, from ./admin_tools
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django_extensions',
    'django_celery_beat',
    'django_filters',
    'api_docs.apps.APIDocsConfig',  # Our app, from ./api_docs
    'api_auth.apps.APIAuthConfig',  # Our app, from ./api_auth
    'users.apps.UsersConfig',  # Our app, from ./users
    'social_django',
    'auth0.apps.Auth0Config',  # Our app, from ./auth0
    'rest_framework',
    'api_storage.apps.APIStorageConfig',  # Our app, from ./api_storage
    'organizations.apps.OrganizationsConfig',  # Our app, from ./organizations
    'tags.apps.TagsConfig',  # Our app, from ./tags
    'documents.apps.DocumentsConfig',  # Our app, from ./documents
    'training.apps.TrainingConfig',  # Our app, from ./training
    'extract.apps.ExtractConfig',  # Our app, from ./extract
    'api_mailer.apps.APIMailerConfig',  # Our app, from ./api_mailer
    'event_log.apps.EventLogConfig',  # Our app, from ./event_log
    'maintenance_mode.apps.MaintenanceModeConfig',  # Our app, from ./ maintenance-mode
    'slack_client.apps.SlackClientConfig',  # Our app, from glynt/common/python/django-slack-client
    'request_profiler',
    'jobs.apps.JobsConfig',  # Our app, from ./jobs
    'cached_reverse.apps.CachedReverseConfig',  # Our app, from ./cached_reverse
    'business_reporting.apps.BusinessReportingConfig',  # Our app, from ./business_reporting
    'data_exports.apps.DataExportConfig',  # Our app, from ./data_exports
    'static_enrichment_tables.apps.StaticEnrichmentTablesConfig',
    'document_data.apps.DocumentDataConfig',
    'packager.apps.PackagerConfig',  # Our app, from ./packager
    'mat_data.apps.MatDataConfig',
]
if ENVIRONMENT == 'testing':
    INSTALLED_APPS += [
        'api_tests.apps.APITestsConfig',  # Our app, from ./api_tests
    ]
if ENVIRONMENT in ['development', 'stage', 'sandbox']:
    INSTALLED_APPS += ['silk']

LOGIN_URL = 'auth0:login'
LOGIN_ERROR_URL = '/'
LOGIN_REDIRECT_URL = '/'
LOGOUT_REDIRECT_URL = '/'


# django-silk (https://github.com/jazzband/django-silk)

# This is not a django-silky setting, but rather one we have invented to
# control whether or not silky should be installed and enabled. Toggle this to
# True to begin profiling requests, and to enable the silky web UI. Remember to
# restart the app server if you enable this.
SILKY_ENABLED = False
# IMPORTANT: Due to security concerns with silk storing sensitive data in plain
# text in the database, as well as possible performance considerations, silk
# should never be enabled on production. Notice above in INSTALLED_APPS that we
# don't even install Silky in production. This conditional is an extra
# protection to triple check that we are not accidentally enabling this in
# production.
if ENVIRONMENT in PRODUCTION_ENVS + ['testing']:
    SILKY_ENABLED = False

# Turn on the SILKY_PYTHON_PROFILER setting to use Python's built-in cProfile
# profiler. Each request will be separately profiled and the profiler's output
# will be available on the request's Profiling page in the Silk UI.
SILKY_PYTHON_PROFILER = True

# If you would like to also generate a binary .prof file set the following. Can
# be downloaded from the Silky UI and inspected with tools like snakeviz. It is
# good practice to empty this folder out when you finish profiling work and are
# disabling Silk.
SILKY_PYTHON_PROFILER_BINARY = False

# Path to directory that will hold python profiler results
SILKY_PYTHON_PROFILER_RESULT_PATH = os.getenv('APP_SILKY_PYTHON_PROFILER_RESULT_PATH')

# Whether to separately profile how long Silk itself is taking per request
SILKY_META = True

# The following settings control access to the silk ui
SILKY_AUTHENTICATION = True  # User must login
SILKY_AUTHORISATION = True  # User must have permissions (by default: is_staff)

# Can be used to dynamically apply in-depth profiling to functions. See
# documentation for details. During profiling, it is often easier to inject
# some dynamic profiling here, rather than inserting decorators all through the
# code base.
SILKY_DYNAMIC_PROFILING = []


MIDDLEWARE = [
    'glynt_api.middlewares.GlyntApiRequestProfilingMiddleWare',
    'django.middleware.security.SecurityMiddleware',
    'maintenance_mode.middleware.MaintenanceModeMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'social_django.middleware.SocialAuthExceptionMiddleware',
    'documents.middlewares.BoxcarsUnavailableResponseMiddleware',
]

if SILKY_ENABLED:
    # Inserted as early as possible
    MIDDLEWARE.insert(0, 'silk.middleware.SilkyMiddleware')

# HTTP Strict Transport Security
# https://docs.djangoproject.com/en/2.2/ref/middleware/#http-strict-transport-security
# https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Strict-Transport-Security
if IS_LIVE_ENV:
    # This is a recommended standard and should be changed only with careful consideration
    SECURE_HSTS_SECONDS = 60 * 60 * 24 * 365
    SECURE_HSTS_INCLUDE_SUBDOMAINS = True
    SECURE_HSTS_PRELOAD = True
    SECURE_BROWSER_XSS_FILTER = True
    # This are also a standard but is recommended to be handled on the front-end Web server if we have one
    SECURE_CONTENT_TYPE_NOSNIFF = True
    SECURE_SSL_REDIRECT = True


ROOT_URLCONF = 'glynt_api.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [os.path.join(BASE_DIR, 'templates')],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'glynt_api.wsgi.application'

CSRF_COOKIE_HTTPONLY = True
CSRF_COOKIE_SECURE = True
if IS_LIVE_ENV:
    SESSION_COOKIE_SECURE = True

X_FRAME_OPTIONS = 'DENY'


# Databases

if ENVIRONMENT == 'testing':
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.mysql',
            'NAME': 'glynt_api_test_db',
            'USER': 'root',
            'PASSWORD': 'password',
            'HOST': '127.0.0.1',
            'PORT': '3306'
        }
    }
else:
    database_uri = urlparse(os.getenv('APP_DB_URI'))

    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.%s' % database_uri.scheme,
            'NAME': database_uri.path[1:],
            'USER': database_uri.username,
            'PASSWORD': database_uri.password,
            'HOST': database_uri.hostname,
            'PORT': database_uri.port
        }
    }


# Silence warnings in management commands

SILENCED_SYSTEM_CHECKS = [
    "models.W027",  # MySQL does not support check constraints.
]


# Max Data Size
# https://docs.djangoproject.com/en/2.2/ref/settings/#data-upload-max-memory-size
# Remember: Nginx must also accept a request body size this large to even make it this far.

DATA_UPLOAD_MAX_MEMORY_SIZE = 512 * 1024 * 1024  # Bytes


# Password validation
# https://docs.djangoproject.com/en/2.2/ref/settings/#auth-password-validators

if IS_LIVE_ENV:
    AUTH_PASSWORD_VALIDATORS = [
        {
            'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
        },
        {
            'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
        },
        {
            'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
        },
        {
            'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
        },
    ]
else:
    AUTH_PASSWORD_VALIDATORS = []


# Custom User Model
# https://docs.djangoproject.com/en/2.2/topics/auth/customizing/#substituting-a-custom-user-model

AUTH_USER_MODEL = 'users.User'
AUTH_INTEGRATION_MODEL = 'users.Integration'


# Internationalization
# https://docs.djangoproject.com/en/2.2/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'UTC'

USE_I18N = False

USE_L10N = False

USE_TZ = True


# Dates and Time
# https://docs.djangoproject.com/en/2.2/ref/settings/#std:setting-DATETIME_INPUT_FORMATS

DATETIME_INPUT_FORMATS = [
    '%Y-%m-%dT%H:%M:%S',     # '2006-10-25 14:30:59' - This is ISO format, and
                             # the format we advocate for in the api documentation
    '%Y-%m-%d %H:%M:%S',     # '2006-10-25 14:30:59'
    '%Y-%m-%d %H:%M:%S.%f',  # '2006-10-25 14:30:59.000200'
    '%Y-%m-%d %H:%M',        # '2006-10-25 14:30'
    '%Y-%m-%d',              # '2006-10-25'
    '%m/%d/%Y %H:%M:%S',     # '10/25/2006 14:30:59'
    '%m/%d/%Y %H:%M:%S.%f',  # '10/25/2006 14:30:59.000200'
    '%m/%d/%Y %H:%M',        # '10/25/2006 14:30'
    '%m/%d/%Y',              # '10/25/2006'
    '%m/%d/%y %H:%M:%S',     # '10/25/06 14:30:59'
    '%m/%d/%y %H:%M:%S.%f',  # '10/25/06 14:30:59.000200'
    '%m/%d/%y %H:%M',        # '10/25/06 14:30'
    '%m/%d/%y',              # '10/25/06'
]


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/2.2/howto/static-files/

STATIC_ROOT = 'static/'
STATIC_URL = '/static/'

# Caching
# https://github.com/jazzband/django-redis
if ENVIRONMENT == 'testing':
    CACHES = {
        "default": {
            "BACKEND": "django.core.cache.backends.locmem.LocMemCache",
            "LOCATION": "memcache"
        }
    }
else:
    parsed = urlparse(f"{os.environ['REDIS_BASE_URL']}/1")
    auth_str = f":{quote_plus(os.environ['REDIS_PASSWORD'])}"
    url = parsed._replace(netloc=f'{auth_str}@{parsed.netloc}').geturl()

    CACHES = {
        "default": {
            "BACKEND": "django_redis.cache.RedisCache",
            # NOTE: don't set the ssl_cert_reqs=CERT_REQUIRED for rediss
            # servers here; the plugin does this for you, and this will
            # yield an error
            "LOCATION": url,
            # "LOCATION": f"{os.getenv('REDIS_URI')}/1",
            "TIMEOUT": None,
            "OPTIONS": {
                "CLIENT_CLASS": "django_redis.client.DefaultClient",
            }
        }
    }

# cached_reverse - src/cached_reverse
# This is the list of NAMESPACES to be considered as cacheable when you run
# the `manage.py populate_url_cache`. See the cached_reverse app for more
# information on how reversal of these urls is optimized.
CACHEABLE_URL_NAMESPACES = VERSIONS

# Logging
# https://docs.djangoproject.com/en/2.2/topics/logging/

standard_handlers = {
    'file_log': {
        'class': 'logging.handlers.WatchedFileHandler',
        'formatter': 'normal',
        'filename': os.getenv('APP_LOG_FILE'),
        'level': 'ERROR'
    },
    'file_error': {
        'class': 'logging.handlers.WatchedFileHandler',
        'formatter': 'normal',
        'filename': os.getenv('APP_ERROR_LOG_FILE'),
        'level': 'ERROR'
    }
}

testing_handlers = {
    'console': {
        'class': 'logging.StreamHandler',
        'formatter': 'normal',
        'level': 'ERROR'
    }
}

LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'normal': {
            'format': '%(asctime)s.%(msecs)d [%(process)d] %(levelname)s [%(name)s] %(message)s',
            'datefmt': '%Y-%m-%d %H:%M:%S'
        }
    },
    'handlers': testing_handlers if ENVIRONMENT == 'testing' else standard_handlers,
    'root': {
        'handlers': ['console'] if ENVIRONMENT == 'testing' else ['file_log', 'file_error'],
        'level': 'ERROR'
    }
}


logging.getLogger('amqp').setLevel(logging.WARNING)
logging.getLogger('botocore').setLevel(logging.WARNING)
logging.getLogger('flower').setLevel(logging.WARNING)
logging.getLogger('kombu').propagate = False
logging.getLogger('MARKDOWN').setLevel(logging.WARNING)
logging.getLogger('urllib3').setLevel(logging.WARNING)


# REST Framework
# https://www.django-rest-framework.org

if not IS_LIVE_ENV:
    DEFAULT_AUTHENTICATION_CLASSES = (
        'api_auth.backends.DevTokenAuthentication',
        'auth0.authentication.Auth0AccessTokenAuthentication',
        'rest_framework.authentication.SessionAuthentication',
        'api_auth.backends.ApiKeyAuthentication'
    )
else:
    DEFAULT_AUTHENTICATION_CLASSES = (
        'auth0.authentication.Auth0AccessTokenAuthentication',
        'rest_framework.authentication.SessionAuthentication',
        'api_auth.backends.ApiKeyAuthentication'
    )

REST_FRAMEWORK = {
    'DEFAULT_VERSIONING_CLASS': 'rest_framework.versioning.NamespaceVersioning',
    # These are the only allowed versions. We do not provide a DEFAULT_VERSION,
    # because we should never allow an API request which does not explicitly
    # set the version.
    'ALLOWED_VERSIONS': VERSIONS,
    'DEFAULT_RENDERER_CLASSES': (
        'rest_framework.renderers.JSONRenderer',
        'glynt_api.renderers.BrowsableAPIRenderer',
    ),
    'DEFAULT_PARSER_CLASSES': (
        'rest_framework.parsers.JSONParser',
    ),
    'DEFAULT_PERMISSION_CLASSES': (
        'rest_framework.permissions.IsAuthenticated',
    ),
    'DEFAULT_AUTHENTICATION_CLASSES': DEFAULT_AUTHENTICATION_CLASSES,
    'DEFAULT_FILTER_BACKENDS': (
        'glynt_api.filters.DjangoFilterBackend',
        'rest_framework.filters.OrderingFilter'
    ),
    'DEFAULT_PAGINATION_CLASS': 'glynt_api.pagination.LimitOffsetPagination',
    'PAGE_SIZE': 10,
    'DEFAULT_THROTTLE_CLASSES': (
        'rest_framework.throttling.AnonRateThrottle',
        'rest_framework.throttling.UserRateThrottle',
        'rest_framework.throttling.ScopedRateThrottle',
    ),
    'DEFAULT_THROTTLE_RATES': {
        'anon': '200/hour',
        'user': '2000/minute',
        'heavy': '200/minute'  # restricted rate limit for computationally expensive endpoints
    },
    'EXCEPTION_HANDLER': 'glynt_api.exception_handlers.exception_handler',
}

AUTH0_API_IDENTIFIER = os.getenv('AUTH0_API_IDENTIFIER')
AUTH0_CLIENT_ID = os.getenv('AUTH0_CLIENT_ID')
AUTH0_CLIENT_SECRET = os.getenv('AUTH0_CLIENT_SECRET')
AUTH0_DOMAIN = os.getenv('AUTH0_DOMAIN')
AUTH0_JWT_ISSUER = auth0_jwt.AUTH0_JWT_ISSUER
AUTH0_PUBLIC_KEY = auth0_jwt.AUTH0_PUBLIC_KEY

# Python Social Auth
# https://python-social-auth.readthedocs.io/en/latest/
# Required by Django Auth0

SOCIAL_AUTH_ADMIN_USER_SEARCH_FIELDS = ['username', 'first_name', 'email']
SOCIAL_AUTH_REVOKE_TOKENS_ON_DISCONNECT = True
SOCIAL_AUTH_TRAILING_SLASH = False

SOCIAL_AUTH_AUTH0_KEY = os.getenv('AUTH0_CLIENT_ID')
SOCIAL_AUTH_AUTH0_SECRET = os.getenv('AUTH0_CLIENT_SECRET')
SOCIAL_AUTH_AUTH0_SCOPE = [
    'openid',
    'profile',
    'email',
]
SOCIAL_AUTH_AUTH0_DOMAIN = os.getenv('AUTH0_DOMAIN')
audience = os.getenv('AUTH0_AUDIENCE')
if not audience:
    if SOCIAL_AUTH_AUTH0_DOMAIN:
        audience = 'https://' + SOCIAL_AUTH_AUTH0_DOMAIN + '/userinfo'
if audience:
    SOCIAL_AUTH_AUTH0_AUTH_EXTRA_ARGUMENTS = {'audience': audience}

SOCIAL_AUTH_AUTH0_PIPELINE = (
    'auth0.pipeline.auth0_social_details',
    'social_core.pipeline.social_auth.social_uid',
    'social_core.pipeline.social_auth.social_user',
    'social_core.pipeline.user.get_username',
    'social_core.pipeline.social_auth.associate_by_email',
    'social_core.pipeline.user.create_user',
    'social_core.pipeline.social_auth.associate_user',
    'social_core.pipeline.social_auth.load_extra_data',
    'social_core.pipeline.user.user_details'
)

# Authentication backends
# https://docs.djangoproject.com/en/2.2/topics/auth/customizing/#specifying-authentication-backends

AUTHENTICATION_BACKENDS = [
    'auth0.auth0backends.Auth0M2MBackend',
    'auth0.auth0backends.Auth0UserBackend',
    'django.contrib.auth.backends.ModelBackend',  # needed for admin login
]


# Django Rest Framework JWT
# http://getblimp.github.io/django-rest-framework-jwt/

JWT_AUTH = {
    'JWT_PUBLIC_KEY': AUTH0_PUBLIC_KEY,
    'JWT_ALGORITHM': 'RS256',
    'JWT_AUDIENCE': AUTH0_API_IDENTIFIER,
    'JWT_ISSUER': AUTH0_JWT_ISSUER,
    'JWT_AUTH_HEADER_PREFIX': 'Bearer',
}


# Celery
CELERY_BROKER_URL = celeryconfig.broker_url

CELERY_ACCEPT_CONTENT = ['json']

if ENVIRONMENT == 'testing':
    CELERY_RESULT_BACKEND = "redis://:test-dummy@127.0.0.1:6379"
else:
    CELERY_RESULT_BACKEND = celeryconfig.result_backend

CELERY_TASK_ROUTES = celeryconfig.task_routes

# NOTE: We may wish to start paying more attention to results
CELERY_TASK_IGNORE_RESULT = True

CELERY_BEAT_SCHEDULE = BEAT_SCHEDULE

CELERY_BROKER_CONNECTION_TIMEOUT = 30.0

# workaround for bug with gevent/eventlet celery workers
# https://github.com/celery/celery/issues/4817
CELERY_BROKER_HEARTBEAT = 0

if ENVIRONMENT == 'testing':
    CELERY_TASK_ALWAYS_EAGER = True


# Celery broker management settings (used to get queues for metrics;
# not directly used by any part of Celery)
RABBITMQ_MANAGEMENT_URL = os.getenv('RABBITMQ_MANAGEMENT_URL')
RABBITMQ_MANAGEMENT_USER_NAME = os.getenv('RABBITMQ_MANAGEMENT_USER_NAME')
RABBITMQ_MANAGEMENT_USER_PASSWORD = os.getenv('RABBITMQ_MANAGEMENT_USER_PASSWORD')


# api_storage app

# FILE_STORAGE_MOCK_MISSING_ARTIFACTS is always false outside of dev. Set to true to allow
# the dev env to place dummy files in local storage that represent model
# artifacts. Helps when using data loaded from other environments (such as the
# live sandbox env) when the underlying artifacts (documents, model files, etc)
# can't be easily copied to dev.
FILE_STORAGE_MOCK_MISSING_ARTIFACTS = False if ENVIRONMENT != 'development' else False

if IS_LIVE_ENV:
    file_storage_bucket_prefix = os.getenv('APP_STORAGE_BUCKET_PREFIX') + '.'

    region_name = os.getenv('APP_FILE_STORAGE_REGION')
    secrets_manager = boto3.client('secretsmanager', region_name=region_name)
    # The SecretId matches the name of the secret setup in Terraform. Would be
    # DRYer to not have this hardcoded connection point.
    secret_response = secrets_manager.get_secret_value(SecretId=f'glynt_{ENVIRONMENT}_storage_creds')
    storage_creds = json.loads(secret_response['SecretString'])

    # FILE_STORAGE_BACKEND and FILE_STORAGE_KWARGS are declared here in settings
    # so that we can centralize which client will be used, and with what
    # parameters. All calls to api_storage.storage_client will then
    # use these settings.
    FILE_STORAGE_BACKEND = 'custom_s3'
    FILE_STORAGE_KWARGS = {
        'region_name': region_name,
        'bucket_prefix': file_storage_bucket_prefix,
        # Another hardcoded connection point - these key names come from them
        # being setup in terraform.
        'aws_access_key': storage_creds['access_key'],
        'aws_secret_key': storage_creds['secret_key']
    }
else:
    # TODO: it would be better if this wasn't buried inside src/glynt_api,
    # but pyfakefs for testing was struggling with it being elsewhere and we
    # need to keep moving for now.
    if ENVIRONMENT == 'testing':
        file_storage_base_path = '/tmp/local_storage_client/'
    else:
        file_storage_base_path = '/local_storage_client/'
    os.makedirs(file_storage_base_path, exist_ok=True)
    FILE_STORAGE_BACKEND = 'custom_local'
    FILE_STORAGE_KWARGS = {'base_path': file_storage_base_path}

# These three settings control the resource and bucket names which the api
# storage client will allow. Attempting to instantiate a storage client and
# then interact with a resource/bucket not in these lists will raise an error.
# This protects developers from typos which would result in
# similar-but-incorrect buckets being created.  This list is also how the
# module can search for orphaned artifacts and delete them when necessary.
FILE_STORAGE_ALLOWED_RESOURCES = [
    'documents.Document',
    'documents.Tokenized',
    'extract.Extraction',
    'training.TrainingRevision',
    'packager.Packager',
    'data-exports.Report',
]
# Note that the bucket names here must match the bucket_name property
# provided by glynt_api.models.Model.bucket_name. The logic is duplicated
# here to eliminate circular imports.
FILE_STORAGE_ALLOWED_BUCKET_NAMES = [r.lower() for r in FILE_STORAGE_ALLOWED_RESOURCES]
FILE_STORAGE_KWARGS['allowed_bucket_names'] = FILE_STORAGE_ALLOWED_BUCKET_NAMES

# Controls the maximum number of seconds that presigned get and put urls are valid for.
# 1 week is the aws allowed max for presigned urls with signature v4
FILE_STORAGE_KWARGS['default_get_valid_for'] = 60 * 60 * 24 * 7
FILE_STORAGE_KWARGS['default_put_valid_for'] = 60 * 60 * 24 * 7

# documents App

if ENVIRONMENT in ['testing']:
    BOXCARS_API_URL = 'http://boxcars.test'
else:
    BOXCARS_API_URL = os.getenv('BOXCARS_API_URL', None)

# the name of the dynamic parameter stored in AWS Parameter Store, access via boto3
BOXCARS_DEFAULT_VERSION_PARAM_NAME = f'/glynt/shared/{ENVIRONMENT}/default_boxcars_version'


# Core

if ENVIRONMENT in ['testing']:
    CORE_API_URL = 'http://core.test'
else:
    CORE_API_URL = os.getenv('CORE_API_URL', None)
CORE_SUPPORTED_MAJOR_VERSIONS = [0, 3, 4]


# UI

GLYNT_UI_URL = os.getenv('GLYNT_UI_URL', None)


# Tags

# The maximum number of tags which can be on an object. See the tags app.
MAX_TAGS = 50
# Beware: if you increase MAX_TAG_LENGTH, you'll need to increase the
# django-taggit default max_length, or else it will start silently truncating
# tags longer than 100 characters to 100 characters.
MAX_TAG_LENGTH = 100
TAGGIT_CASE_INSENSITIVE = False


# Email

EMAIL_FROM_ADDRESS = "<EMAIL>"

email_from_name_suffix = ''
if not IS_LIVE_ENV:
    email_from_name_suffix = '(dev)'
elif 'stage' in ENVIRONMENT:
    email_from_name_suffix = '(stage)'
elif 'sandbox' in ENVIRONMENT:
    email_from_name_suffix = '(sandbox)'
EMAIL_FROM_NAME = "GLYNT.ai{}".format(email_from_name_suffix)

WELCOME_EMAIL_REDIRECT = GLYNT_UI_URL or LOGIN_URL


# Slack

SLACK_TOKEN = os.getenv('SLACK_TOKEN')

# The whole channel name must be less than 22 characters (excluding `#`)
slack_training_requests_channel_suffix = ''
if ENVIRONMENT not in PRODUCTION_ENVS:
    slack_training_requests_channel_suffix = "_dev"
SLACK_TRAINING_REQUESTS_CHANNEL = "#training_requests{}".format(
    slack_training_requests_channel_suffix
)
SLACK_MONITORING_CHANNEL = os.getenv('SLACK_MONITORING_CHANNEL')
SLACK_CUSTOMER_METRICS_CHANNEL = os.getenv('SLACK_CUSTOMER_METRICS_CHANNEL')

UIPATH_BASE_URL = os.getenv('UIPATH_BASE_URL')
UIPATH_AUTH_URL = os.getenv('UIPATH_AUTH_URL')

# MapQuest API

MAPQUEST_API_CONSUMER_KEY = os.getenv('MAPQUEST_API_CONSUMER_KEY')

# Maintenance mode

MAINTENANCE_MODE_ALLOWED_URL_PATTERNS = [
    r'^/auth/social/',
    r'^/auth/login$',
    r'^/auth/logout$',
    r'^/auth/landing$',
    r'^/v6/docs/',
]


# api_metrics app

METRICS_CLOUDWATCH_NAMESPACE = None
if ENVIRONMENT in ['sandbox', 'stage', 'production']:
    METRICS_CLOUDWATCH_NAMESPACE = 'glynt-api-{}'.format(ENVIRONMENT)


# request profiler

# this method is used by the request profiler as a global exclusion filter
# for all requests that come in.  If the method returns False, then that
# request profile is cancelled and the profiler middleware is skipped.
# see https://github.com/yunojuno/django-request-profiler/blob/master/request_profiler/settings.py
def exclude_func(request):
    # if request.path.startswith('/admin'):
    #     return False
    return True


RESPONSE_TIME_ALARM_THRESHOLD = 5
REQUEST_PROFILER_GLOBAL_EXCLUDE_FUNC = exclude_func
if ENVIRONMENT == 'testing':
    # default is 10
    REQUEST_PROFILER_RULESET_CACHE_TIMEOUT = 0
