"""
Set Glynt up to process JWT tokens given AUTH0_DOMAIN

Cache `.well-known/jwks.json` to avoid triggered an Auth0 rate limit.

Given AUTH0_DOMAIN in the environment, derives
  - AUTH0_JWT_ISSUER
  - AUTH0_PUBLIC_KEY

See /glynt/common/django_auth0
"""

import json
import os
from tempfile import NamedTemporaryFile

import requests
from cryptography.hazmat.backends import default_backend
from cryptography.x509 import load_pem_x509_certificate


CACHE_FILENAME = '/tmp/jwks.json'


def _get_jwks(auth0_jwt_issuer):
    """Get jwks.json from the issuer

    Returns the corresponding dict

    Attempt to use a cached copy first. Otherwise retrieve and cache.
    """
    try:
        with open(CACHE_FILENAME, 'r') as f:
            jwks = json.load(f)
    except FileNotFoundError:
        jwks = requests.get(auth0_jwt_issuer + '.well-known/jwks.json').json()
        with NamedTemporaryFile(mode='w', delete=False) as tempf:
            json.dump(jwks, tempf)
            tempname = tempf.name
        # During deploy, this file will get written as root with a mode of 0600.
        # Fix that so that it's accesible to the glynt_api user.
        os.chmod(tempname, 0o666)
        os.rename(tempname, CACHE_FILENAME)
    return jwks


def _get_public_key(jwks):
    cert = (
        '-----BEGIN CERTIFICATE-----\n' +
        jwks['keys'][0]['x5c'][0] +
        '\n-----END CERTIFICATE-----')
    certificate = load_pem_x509_certificate(cert.encode('utf-8'), default_backend())
    public_key = certificate.public_key()
    return public_key


AUTH0_DOMAIN = os.getenv('AUTH0_DOMAIN')

if AUTH0_DOMAIN:
    AUTH0_JWT_ISSUER = 'https://' + AUTH0_DOMAIN + '/'
    jwks = _get_jwks(AUTH0_JWT_ISSUER)
    AUTH0_PUBLIC_KEY = _get_public_key(jwks)
else:
    AUTH0_JWT_ISSUER = None
    AUTH0_PUBLIC_KEY = None
