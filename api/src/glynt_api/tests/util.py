import json
import re
from contextlib import contextmanager

import responses
from django.conf import settings
from django.core.exceptions import ObjectDoesNotExist
from django.urls import NoReverseMatch, reverse
from pyfakefs.fake_filesystem_unittest import \
    TestCaseMixin as PyFakeFsTestCaseMixin

from glynt_api.util import get_core_versions, set_query_field

TEST_JWT_PUBLIC_KEY = """-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA8xZ5zuLqf1ANfOloBITk
B3MNeHEBZzFHshq+SwF9uAMS0i/1m0hS3XKJtZJRUQsbDqHjygJ6NTMP4EcxKy5L
cqyM/Pqq9FacUG/pX6Tn0+cYVrZNXWvac2FYMAJ4Tyq/uGfTnO/SP0gshuKqaB08
9sAh9rym/Bg+55XakMziYdTbSvvPc3z1wptSgnjdMqKQVqLQRq1blPgp/bR4viQx
6CImTdpvp6NI3HSG9tf2LxcGRnqpBrP4cSk/Vm/c5kHuI92PHvJxilV7Vc+OJL8O
k3ig6/LegOppuwZMldP8AsSUAA0LE463HLVRwXJQW0JWr9LqZ2hzmKjLNbdOZLhp
AQIDAQAB
-----END PUBLIC KEY-----"""

TEST_JWT_PRIVATE_KEY = """***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"""

TEST_JWT_AUTH = settings.JWT_AUTH
TEST_JWT_AUTH['JWT_PUBLIC_KEY'] = TEST_JWT_PUBLIC_KEY
TEST_JWT_AUTH['JWT_AUDIENCE'] = 'test_audience'
TEST_JWT_AUTH['JWT_ISSUER'] = 'test_issuer'

STANDARD_COPY_PROPERTIES_DIFFERENCE = {
    'pk', 'id', 'obfuscated_id',
    'updated_at', 'created_at',
}


class TestCaseMixin(PyFakeFsTestCaseMixin):
    def setUpPyfakefs(self, *args, **kwargs):
        super().setUpPyfakefs(*args, **kwargs)
        from django.views import templates
        self.fs.add_real_directory(templates.__path__._path[0])


class MockObject(object):
    def __init__(self, **kwargs):
        self._kwargs = kwargs
        for key, val in kwargs.items():
            setattr(self, key, val)

    def __str__(self):
        kwargs_str = ', '.join([
            '%s=%s' % (key, value)
            for key, value in sorted(self._kwargs.items())
        ])
        return '<MockObject %s>' % kwargs_str


class MockQueryset(object):
    def __init__(self, iterable):
        self.items = iterable

    def __getitem__(self, val):
        return self.items[val]

    def __len__(self):
        return len(self.items)

    def get(self, **lookup):
        for item in self.items:
            if all([
                getattr(item, key, None) == value
                for key, value in lookup.items()
            ]):
                return item
        raise ObjectDoesNotExist()


class BadType(object):
    """
    When used as a lookup with a `MockQueryset`, these objects
    will raise a `TypeError`, as occurs in Django when making
    queryset lookups with an incorrect type for the lookup value.
    """

    def __eq__(self):
        raise TypeError()


class MockSerializer(object):
    def __init__(self, *args, **kwargs):
        self.fields = kwargs.get('fields', [])
        self.exclude_fields = kwargs.get('exclude_fields', [])


def mock_get_serializer_class():
    return MockSerializer


def mock_get_serializer_context():
    return []


def get_api_path(url_name, data_pool_obfuscated_id=None, advanced=False, **kwargs):
    """Convenience utility to simplify reversing an api path. Returns a path
    without the protocol and server. Use get_api_url to get a url with the
    protocol and server included.
    """
    if data_pool_obfuscated_id:
        kwargs['data_pools_obfuscated_id'] = data_pool_obfuscated_id

    url = reverse(url_name, kwargs=kwargs)

    if advanced:
        url = set_query_field(url, 'advanced', 'true')

    return url


def get_api_url(*args, **kwargs):
    """Returns a fully qualified url in the testserver."""
    return get_url_from_path(get_api_path(*args, **kwargs))


def get_url_from_path(path, advanced=False):
    """Given a path and whether to add the advanced flag, create a fully
    qualified url to that path for the test server.
    """
    url = f'http://testserver{path}'
    if advanced:
        url = set_query_field(url, 'advanced', 'true', replace=True)

    return url


def mock_reverse(view_name, args=None, kwargs=None, request=None, format=None):
    args = args or []
    kwargs = kwargs or {}
    value = (args + list(kwargs.values()) + ['-'])[0]
    prefix = 'http://example.org' if request else ''
    suffix = ('.' + format) if (format is not None) else ''
    return '%s/%s/%s%s/' % (prefix, view_name, value, suffix)


def fail_reverse(view_name, args=None, kwargs=None, request=None, format=None):
    raise NoReverseMatch()


def mock_get_core_versions():
    with mocked_core_responses():
        return get_core_versions()


def mock_get_core_versions_from_core():
    return {
        'experimental_core_versions': ['5.0.0', '4.0.0-prereleaseInfo+buildInfo'],
        'core_versions': ['4.0.0', '3.0.0', '3.1.0'],
        'deprecated_core_versions': ['3.0.0'],
        'obsolete_core_versions': ['1.1'],
    }


@contextmanager
def mocked_core_responses(assert_all_requests_are_fired=True):
    def request_callback(request):
        path_url = request.path_url

        if re.search(r'/versions$', path_url):
            resp_body = mock_get_core_versions_from_core()
            return (200, {}, json.dumps(resp_body))

        match = re.search(r'status_code/(\d{3})/', path_url)
        if match:
            return (int(match.group(1)), {}, '')

        return (200, {}, '')

    with responses.RequestsMock(
            assert_all_requests_are_fired=assert_all_requests_are_fired
    ) as rsps:
        rsps._url_pattern = r'{}.*'.format(settings.CORE_API_URL)
        rsps.add_callback(
            responses.GET,
            re.compile(rsps._url_pattern),
            callback=request_callback,
            content_type='application/json'
        )
        yield rsps
