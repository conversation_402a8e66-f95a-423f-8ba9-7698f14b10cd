def persistent_id_aware_property(func):
    """
    This decorator turns a method into a property, but combined with
    PersistentIDAwareModelMixin, it also allows a field to be persistentID
    "aware".

    Apply this decorator to a method whose body is `pass`, and then create a
    field with the same name but prefixed with an underscore. The method is
    never run, it is only used for its name, and the underscore-prefixed field
    is where this decorator will store its data.

    A getter is provided which changes persistent IDs stored in the field to
    obfuscated IDs.

    A setter is provided which stores any value passed as-is with no
    modification.

    A deleter is not provided.

    Usage:
    ```
    class SomeModel(Model):
        _data = JSONField()

        @persistent_id_aware_property
        def data(self):
            pass
    ```
    """
    attr = '_{}'.format(func.__name__)

    def getter(obj):
        return obj.persistent_id_to_obfuscated_id_in(getattr(obj, attr))

    def setter(obj, value):
        setattr(obj, attr, value)

    p = property(getter, setter)
    return p
