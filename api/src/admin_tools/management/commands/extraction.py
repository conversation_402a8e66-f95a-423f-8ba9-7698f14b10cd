"""A management command to trace an Extraction

The intent is that this will move into an /admin URL
"""

from django.core.management.base import BaseCommand, CommandError

from documents.models import Document, Tokenized
from extract.models import Extraction, ExtractionBatch
from glynt_api import settings
from organizations.models import DataPool, Organization


class Command(BaseCommand):
    help = "Trace an Extraction"

    def add_arguments(self, parser):
        parser.add_argument(
            'extraction_id',
            default='',
            help='Obfuscated ID for an Extraction'
        )

    def handle(self, *args, **options):
        if options['extraction_id']:
            obfu_ex_id = options['extraction_id']
            self.handle_extraction(obfu_ex_id)
        else:
            raise CommandError("Must specify extraction_id")

    def handle_extraction(self, obfu_ex_id):
        extraction = Extraction.get_object_by_obfuscated_id(obfu_ex_id)
        print(f"Extraction {obfu_ex_id}")

        tokenized = Tokenized.objects.get(pk=extraction.tokenized_id)
        if tokenized:
            print(f"... tokenized with {tokenized.tokenizer_version}")
        else:
            print("*** No Tokenized")

        document = Document.objects.get(pk=extraction.document_id)
        print(f"Document {document.obfuscated_id} {document.label}{document.file_extension}")

        batch = ExtractionBatch.objects.get(pk=extraction.extraction_batch_id)
        print(f"ExtractionBatch {batch.obfuscated_id} {batch.label}")

        data_pool = DataPool.objects.get(pk=document.data_pool_id)
        print(f"DataPool {data_pool.obfuscated_id} {data_pool.label}")

        organization = Organization.objects.get(pk=data_pool.organization_id)
        print(f"Organization {organization.obfuscated_id} {organization.label}")

        url_base = self._url_base()
        print()
        print(f"{url_base}/data-pools/{data_pool.obfuscated_id}/extraction-batch-details/{batch.obfuscated_id}")
        print(f"{url_base}/organization/{organization.obfuscated_id}")

    def _url_base(self):
        if settings.ENVIRONMENT == 'production':
            return 'https://ui.glynt.ai'
        else:
            return f'https://ui-{settings.ENVIRONMENT}.glynt.ai'
