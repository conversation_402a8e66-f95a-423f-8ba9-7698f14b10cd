"""Remove TrainingRevions that use obsolete tokenizer versions
"""

from django.core.management.base import BaseCommand

from training.models import TrainingRevision


OBSOLETE_TOKENIZER_VERSIONS = [
    '0.0.2',
    # '4.1.0',  all gone
    '4.2.0',
    'ABBYY12',
]


class Command(BaseCommand):
    help = "Remove TrainingRevisions that use obsolete tokenizers"

    def handle(self, *args, **options):
        for tokenizer_version in OBSOLETE_TOKENIZER_VERSIONS:
            print(f"Looking for {tokenizer_version}...")
            deleted = 0
            for tr in TrainingRevision.objects.filter(tokenizer_version=tokenizer_version).all():
                tr.delete()
                deleted += 1
            print(f"... deleted {deleted} Training Revisions")
