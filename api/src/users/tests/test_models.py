from unittest.mock import patch
from urllib.parse import urljoin

from django.core.exceptions import ValidationError
from django.http.request import HttpRequest
from django.test import TestCase, override_settings

from users.models import User, ServiceAccount


class UserModelTestCase(TestCase):

    def setUp(self):
        self.user = User.objects.create(
            username='<EMAIL>',
            email='<EMAIL>'
        )

    def test_user_str_repr(self):
        self.assertEqual(str(self.user), '<EMAIL>')

    @patch('users.models.send_welcome_email.delay')
    @patch('users.models.auth0_util.create_auth0_user', return_value='12345')
    def test_welcome_email_redirect_url_full_happy(
        self, mock_create_auth0_user, mock_send_welcome_email
    ):
        full_redirect_url = 'http://www.example.com'
        with override_settings(WELCOME_EMAIL_REDIRECT=full_redirect_url):
            self.user.create_auth0_user()
            mock_send_welcome_email.assert_called_with(
                self.user.email, result_url=full_redirect_url
            )

    @patch('users.models.send_welcome_email.delay')
    @patch('users.models.auth0_util.create_auth0_user', return_value='12345')
    def test_welcome_email_redirect_url_relative_happy(
        self, mock_create_auth0_user, mock_send_welcome_email
    ):
        def build_absolute_uri(relative_uri):
            return urljoin('http://example.com', relative_uri)

        relative_redirect_url = '/index.html'

        with override_settings(WELCOME_EMAIL_REDIRECT=relative_redirect_url):
            with patch.object(
                HttpRequest, 'build_absolute_uri', wraps=build_absolute_uri
            ):
                self.user.create_auth0_user(HttpRequest())
                mock_send_welcome_email.assert_called_with(
                    self.user.email,
                    result_url='http://example.com' + relative_redirect_url
                )

    @patch('users.models.send_welcome_email.delay')
    @patch('users.models.auth0_util.create_auth0_user', return_value='12345')
    def test_welcome_email_redirect_url_name_happy(
        self, mock_create_auth0_user, mock_send_welcome_email
    ):
        def build_absolute_uri(relative_uri):
            return urljoin('http://example.com', relative_uri)

        url_name = 'admin:index'

        with override_settings(WELCOME_EMAIL_REDIRECT=url_name):
            with patch.object(
                HttpRequest, 'build_absolute_uri', wraps=build_absolute_uri
            ):
                self.user.create_auth0_user(HttpRequest())
                mock_send_welcome_email.assert_called_with(
                    self.user.email,
                    result_url='http://example.com/admin/'
                )

    @patch('users.models.send_welcome_email.delay')
    @patch('users.models.auth0_util.create_auth0_user', return_value='12345')
    def test_welcome_email_redirect_url_full_invalid_scheme(
        self, mock_create_auth0_user, mock_send_welcome_email
    ):
        full_redirect_url = 'ftp://www.example.com'
        with override_settings(WELCOME_EMAIL_REDIRECT=full_redirect_url):
            self.user.create_auth0_user()
            mock_send_welcome_email.assert_called_with(
                self.user.email, result_url=None
            )

    @patch('users.models.send_welcome_email.delay')
    @patch('users.models.auth0_util.create_auth0_user', return_value='12345')
    def test_welcome_email_redirect_url_relative_no_request(
        self, mock_create_auth0_user, mock_send_welcome_email
    ):
        relative_redirect_url = '/index.html'
        with override_settings(WELCOME_EMAIL_REDIRECT=relative_redirect_url):
            self.user.create_auth0_user()
            mock_send_welcome_email.assert_called_with(
                self.user.email, result_url=None
            )


class ServiceAccountModelTestCase(TestCase):

    def setUp(self):
        # Set up non-modified objects used by all test methods
        self.label = "Test Service Account"
        self.raw_key = ServiceAccount.generate_key()
        self.hashed_key = ServiceAccount.hash_key(self.raw_key)
        self.service_account = ServiceAccount.objects.create(
            label=self.label,
            is_active=True,
            hashed_key=self.hashed_key
        )
        self.service_account.save()

    def tearDown(self):
        self.service_account.delete()

    def test_label_cleaning(self):
        self.service_account.label = "<Tobias Fünke>"
        self.service_account.clean()
        self.assertNotIn('<', self.service_account.label)
        self.assertNotIn('>', self.service_account.label)

    def test_label_validation(self):
        self.service_account.label = "<>"
        with self.assertRaises(ValidationError):
            self.service_account.clean()

    def test_get_by_key(self):
        retrieved_account = ServiceAccount.get_by_key(self.raw_key)
        self.assertEqual(retrieved_account, self.service_account)

    def test_get_by_key_with_invalid_key(self):
        with self.assertRaises(ServiceAccount.DoesNotExist):
            ServiceAccount.get_by_key('invalid_key')

    def test_set_key(self):
        self.service_account.set_key(self.raw_key)
        self.assertEqual(self.service_account.hashed_key, self.hashed_key)

    def test_is_staff_property(self):
        self.assertTrue(self.service_account.is_staff)

    def test_is_anonymous_property(self):
        self.assertFalse(self.service_account.is_anonymous)

    def test_is_authenticated_property(self):
        self.assertTrue(self.service_account.is_authenticated)
