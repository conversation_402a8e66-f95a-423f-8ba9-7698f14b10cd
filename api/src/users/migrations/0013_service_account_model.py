# Generated by Django 2.2.28 on 2023-11-08 21:40

from django.db import migrations, models
import glynt_api.util
import jsonfield.fields


class Migration(migrations.Migration):

    dependencies = [
        ('auth', '0011_update_proxy_permissions'),
        ('users', '0012_enable_label_validators'),
    ]

    operations = [
        migrations.CreateModel(
            name='ServiceAccount',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('_persistent_id_map', jsonfield.fields.JSONField(default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('label', models.CharField(help_text='`<` and `>` will be removed.', max_length=63, validators=[glynt_api.util.validate_non_printable_characters, glynt_api.util.validate_whitespaces])),
                ('hashed_key', models.CharField(max_length=128, unique=True)),
                ('is_active', models.BooleanField(default=True)),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this service account belongs to. A service account will get all permissions granted to each of their groups.', related_name='service_accounts', related_query_name='service_account', to='auth.Group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this service account.', related_name='service_accounts', related_query_name='service_account', to='auth.Permission', verbose_name='service account permissions')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
