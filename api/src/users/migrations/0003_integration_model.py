# -*- coding: utf-8 -*-
# Generated by Django 1.11.20 on 2019-03-06 18:01
from __future__ import unicode_literals

import uuid

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('organizations', '0004_datapool_verify_extractions'),
        ('auth', '0008_alter_user_username_max_length'),
        ('users', '0002_unique_email'),
    ]

    operations = [
        migrations.CreateModel(
            name='Integration',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('client_id', models.CharField(blank=True, help_text='This Client ID should correspond to Auth0 Application Client ID', max_length=32, null=True, unique=True)),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether the integration is active. Inactive integration will be denied access to the API.')),
                ('label', models.CharField(help_text='`<` and `>` will be removed.', max_length=63)),
                ('data_pools', models.ManyToManyField(blank=True, related_name='integrations', related_query_name='integration', to='organizations.DataPool')),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this integration belongs to. An integration will get all permissions granted to each of their groups.', related_name='integrations', related_query_name='integration', to='auth.Group', verbose_name='groups')),
                ('organization', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='integrations', related_query_name='integration', to='organizations.Organization')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this integration.', related_name='integrations', related_query_name='integration', to='auth.Permission', verbose_name='integration permissions')),
            ],
            options={
                'ordering': ['created_at'],
            },
        ),
        migrations.AlterUniqueTogether(
            name='integration',
            unique_together=set([('label', 'organization')]),
        ),
    ]
