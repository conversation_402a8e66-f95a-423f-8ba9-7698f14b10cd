# API Storage

This app is a wrapper over the common/python/storage package. This app
provides the django-side, api-specific components.

## Setup

This package expects a variety of settings to be present. See the
`glynt_api.settings.py`, `api_storage app` section for an annotated explanation.

Additionally, this package provides additional tooling for the common API case
where a API models map 1-to-1 to storage buckets. For each such model, the
model must be given a `resource_name` and `bucket_name` property which is
unique to the class.  See `glynt_api.models.Model` for the implementations
being used.

If automatic storage artifact cleanup on model instance deletion is desired,
then the model should have the `api_storage.signals.delete_storage_artifacts`
signal connected to it, like so:

```python
post_delete.connect(delete_storage_artifacts, sender=<SomeModel>)
```
