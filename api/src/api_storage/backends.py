import io
from datetime import datetime, timedelta, timezone
from uuid import UUID

from django.conf import settings
from django.urls import reverse

from glynt_api.util import set_query_field
from glynt_s3_storage_client import LocalStorageClient, S3StorageClient


class ValidateRemotePathMixin:
    def _validate_path_starts_with_uuid(self, bucket, remote_path):
        """Validates that the first directory of the remote_path is a version4
        uuid. This is enforced to encourage the good practice of making the first
        portion of a path in a bucket unique in S3 wherever possible. Since most
        objects in the API have a uuid associated anyways, this is a natural unique
        prefix to use.
        """
        uuid = remote_path.split('/')[0]
        try:
            UUID(uuid, version=4)
        except ValueError:
            raise ValueError(
                'remote_path must start with a version 4 UUID if enforce_uuid is True'
            )

        return True

    def validate_remote_path(self, bucket, remote_path, enforce_uuid=True):
        """If enforce_uuid is True (which it is, by default), validates that the
        first directory of the remote_path is a version4 uuid. See that
        function for more information.
        """
        super().validate_remote_path(bucket, remote_path)
        if enforce_uuid:
            self._validate_path_starts_with_uuid(bucket, remote_path)
        return True


class CustomLocalStorageClient(ValidateRemotePathMixin, LocalStorageClient):

    def __init__(self, *args, **kwargs):
        if settings.ENVIRONMENT not in ['development', 'testing']:
            raise RuntimeError(
                'LocalStorageClient may only be used in development or testing environments.'
            )
        super().__init__(*args, **kwargs)

    def check_object_exists(self, bucket, remote_path):
        """Extends check_object_exists to allow the missing file to be
        automatically created if settings.FILE_STORAGE_MOCK_MISSING_ARTIFACTS
        is set to True. Useful during development when copy data from a live
        environment into the dev env, but all the artifacts cannot be copied
        over due to time or size.
        """
        exists = super().check_object_exists(bucket, remote_path)

        if not exists and settings.FILE_STORAGE_MOCK_MISSING_ARTIFACTS:
            self.upload_fileobj(bucket, remote_path, io.StringIO("dummy document content"))
            return self.check_object_exists(bucket, remote_path)

        return exists

    def generate_presigned_put_url(
        self,
        bucket,
        remote_path,
        content_type,
        content_md5=None,
        valid_for=None,
        use_vpc=False,
        **kwargs
    ):
        """Generate a presigned PUT url for a file at a given remote_path
        within the given bucket. content_type is required if the method is
        PUT. content_md5 is optional.  valid_for is the number of seconds the
        url will remain valid for. See the corresponding BaseStorageClient
        method for more details.
        """
        return self._get_presigned_url(
            bucket,
            remote_path,
            'PUT',
            content_type,
            content_md5,
            valid_for=valid_for,
            use_vpc=use_vpc
        )

    def generate_presigned_get_url(
        self,
        bucket,
        remote_path,
        valid_for=None,
        use_vpc=False,
        **kwargs
    ):
        """Generate a presigned GET url for a file at a given remote_path
        within the given bucket. valid_for is the number of seconds the url
        will remain valid for. See the corresponding BaseStorageClient method
        for more details.
        """
        return self._get_presigned_url(
            bucket, remote_path, 'GET', valid_for=valid_for, use_vpc=use_vpc
        )

    def _get_presigned_url(
        self,
        bucket,
        remote_path,
        method,
        content_type=None,
        content_md5=None,
        valid_for=None,
        use_vpc=False
    ):
        self.validate_bucket_name(bucket)
        self.validate_remote_path(bucket, remote_path)

        _valid_for = valid_for
        if _valid_for is None:
            if method == 'PUT':
                _valid_for = self.default_put_valid_for
            else:
                _valid_for = self.default_get_valid_for
        self._check_valid_for(_valid_for)

        path = reverse('local_files:presigned_url')

        path = set_query_field(path, 'allowed_method', method)

        expires = datetime.now(timezone.utc) + timedelta(seconds=_valid_for)
        expires = int(expires.timestamp())
        path = set_query_field(path, 'expires', expires)

        path = set_query_field(path, 'resource', bucket)
        path = set_query_field(path, 'remote_path', remote_path)

        if method == 'PUT':
            if content_type:
                path = set_query_field(path, 'content_type', content_type)

            if content_md5:
                path = set_query_field(path, 'content_md5', content_md5)

        if settings.ENVIRONMENT == 'development':
            # Have to use DEVELOPMENT_HOST because I don't want to make this
            # implementation require `request` (where no other implementation
            # would). There is probably a better way, but gotta go fast.
            if use_vpc and settings.VPC_HOST:
                return f'https://{settings.VPC_HOST}{path}'

            return f'{settings.DEVELOPMENT_HOST}{path}'

        elif settings.ENVIRONMENT == 'testing':
            return f'http://testserver{path}'


class CustomS3StorageClient(ValidateRemotePathMixin, S3StorageClient):
    pass
