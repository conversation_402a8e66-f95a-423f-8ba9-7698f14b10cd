from django.conf import settings

from glynt_s3_storage_client import storage_client as raw_get_storage_client

from .backends import CustomLocalStorageClient, CustomS3StorageClient


def storage_client():
    """Return instantiated StorageClient based on settings."""
    if settings.FILE_STORAGE_BACKEND == 'custom_local':
        return CustomLocalStorageClient(**settings.FILE_STORAGE_KWARGS)
    elif settings.FILE_STORAGE_BACKEND == 'custom_s3':
        return CustomS3StorageClient(**settings.FILE_STORAGE_KWARGS)
    return raw_get_storage_client(settings.FILE_STORAGE_BACKEND, **settings.FILE_STORAGE_KWARGS)
