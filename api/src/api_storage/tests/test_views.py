import logging

from django.test import Client, TestCase
from django.urls import reverse
from pyfakefs.fake_filesystem_unittest import TestCase as FFSTestCase

from glynt_api.util import set_query_field

logger = logging.getLogger(__name__)


class StorageViewsTestCase(FFSTestCase, TestCase):

    def setUp(self):
        self.setUpPyfakefs()
        self.client = Client()
        self.base_url = reverse('local_files:presigned_url')
        self.future_datetime = 10190242490  # In the year 2292...
        self.content_md5 = 'ZajifYh5KDgxtmS9i38K1A=='
        self.content_type = 'application/text'
        self.remote_path = '7f97e44b-7717-497e-a406-bc7ff0b4c623/path/to/file.txt'
        self.sample_file_content = b'Hello, World!'
        self.resource = 'documents.tokenized'

    @property
    def url(self):
        url = self.base_url
        url = set_query_field(url, 'expires', self.future_datetime)
        url = set_query_field(url, 'resource', self.resource)
        url = set_query_field(url, 'remote_path', self.remote_path)
        return url

    @property
    def put_url(self):
        url = self.url
        url = set_query_field(url, 'allowed_method', 'PUT')
        return url

    @property
    def get_url(self):
        url = self.url
        url = set_query_field(url, 'allowed_method', 'GET')
        return url

    def test_local_storage_presigned_url_requires_params(self):
        url = self.base_url
        response = self.client.get(url)
        self.assertContains(
            response, '"allowed_method" query parameter is required', status_code=400
        )

        url = set_query_field(url, 'allowed_method', 'GET')
        response = self.client.get(url)
        self.assertContains(
            response, '"expires" query parameter is required', status_code=400
        )

        url = set_query_field(url, 'expires', self.future_datetime)
        response = self.client.get(url)
        self.assertContains(
            response, '"resource" query parameter is required', status_code=400
        )

        url = set_query_field(url, 'resource', self.resource)
        response = self.client.get(url)
        self.assertContains(
            response, '"remote_path" query parameter is required', status_code=400
        )

        url = set_query_field(url, 'remote_path', self.remote_path)
        response = self.client.get(url)
        self.assertContains(response, 'not found', status_code=404)

    def test_local_storage_presigned_url_expired(self):
        url = self.get_url
        url = set_query_field(url, 'expires', 0)  # a long, long time ago...
        response = self.client.get(url)
        self.assertContains(
            response, 'Presigned URL has expired.', status_code=400
        )

        future_datetime = 10190242490  # In the year 2292...
        url = set_query_field(url, 'expires', future_datetime)
        response = self.client.get(url)
        self.assertContains(response, 'not found', status_code=404)

    def test_local_storage_presigned_url_remote_path(self):
        invalid_remote_path = 'some/path/to/file.txt'
        url = self.get_url
        url = set_query_field(url, 'remote_path', invalid_remote_path)
        response = self.client.get(url)
        self.assertContains(response, 'not a valid remote_path', status_code=400)

        remote_path = '7f97e44b-7717-497e-a406-bc7ff0b4c623/path/to/file.txt'
        url = set_query_field(url, 'remote_path', remote_path)
        response = self.client.get(url)
        self.assertContains(response, 'not found', status_code=404)

    def test_local_storage_presigned_url_PUT_and_GET_success(self):
        url = self.put_url

        response = self.client.put(url, data=self.sample_file_content,)
        self.assertContains(response, '', status_code=204)

        # PUT again is fine - overwrite happens silently
        response = self.client.put(
            url, data=self.sample_file_content,
            # HTTP_CONTENT_MD5=self.content_md5,
            # CONTENT_TYPE=self.content_type
        )
        self.assertContains(response, '', status_code=204)

        # Now we can GET the previously PUT file
        url = set_query_field(url, 'allowed_method', 'GET', replace=True)
        response = self.client.get(url)
        self.assertContains(response, self.sample_file_content, status_code=200)

    def test_PUT_presigned_url_content_md5_header_required_if_signed(self):
        # signed but no content-md5 header
        url = self.put_url
        url = set_query_field(url, 'content_md5', self.content_md5)
        response = self.client.put(url, data=self.sample_file_content)
        self.assertContains(response, '', status_code=403)

    def test_PUT_presigned_url_content_md5_success(self):
        # unsigned
        url = self.put_url
        response = self.client.put(
            url, data=self.sample_file_content, HTTP_CONTENT_MD5=self.content_md5
        )
        self.assertContains(response, '', status_code=204)

        # signed
        url = set_query_field(url, 'content_md5', self.content_md5)
        response = self.client.put(
            url, data=self.sample_file_content, HTTP_CONTENT_MD5=self.content_md5
        )
        self.assertContains(response, '', status_code=204)

    def test_PUT_presigned_url_content_md5_header_query_mismatch(self):
        # header does not match query string (signed content-md5)
        url = self.put_url
        url = set_query_field(url, 'content_md5', self.content_md5)
        response = self.client.put(
            url, data=self.sample_file_content, HTTP_CONTENT_MD5='mismatch_md5=='
        )
        self.assertContains(response, '', status_code=403)

    def test_PUT_presigned_url_content_md5_header_body_mismatch(self):
        # header does not match actual request body
        url = self.put_url
        response = self.client.put(
            url, data=b'some other content', HTTP_CONTENT_MD5=self.content_md5
        )
        self.assertContains(response, '', status_code=400)

    def test_PUT_presigned_url_content_type_header_required_if_signed(self):
        # signed but no content-type header
        url = self.put_url
        url = set_query_field(url, 'content_type', self.content_type)
        response = self.client.put(url, data=self.sample_file_content)
        self.assertContains(response, '', status_code=403)

    def test_PUT_presigned_url_content_type_success(self):
        # unsigned
        url = self.put_url
        response = self.client.put(
            url, data=self.sample_file_content, CONTENT_TYPE=self.content_type
        )
        self.assertContains(response, '', status_code=204)

        # signed
        url = set_query_field(url, 'content_type', self.content_type)
        response = self.client.put(
            url, data=self.sample_file_content,
            CONTENT_TYPE=self.content_type
        )
        self.assertContains(response, '', status_code=204)

    def test_PUT_presigned_url_content_type_header_query_mismatch(self):
        # header does not match query string (signed content-type)
        url = self.put_url
        url = set_query_field(url, 'content_type', self.content_type)
        response = self.client.put(
            url, data=self.sample_file_content, CONTENT_TYPE='text/plain'
        )
        self.assertContains(response, '', status_code=403)
