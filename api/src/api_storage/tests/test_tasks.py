from unittest.mock import patch

from django.test import TestCase

from api_storage.tasks import delete_resource_folder
from documents.tests.util import create_document
from organizations.tests.util import create_data_pool, create_organization


class StorageDeleteResourceFolderTaskTestCase(TestCase):

    def setUp(self):
        self.org, self.org_path = create_organization()
        self.dp, self.dp_path = create_data_pool(organization=self.org)
        self.doc, _ = create_document(data_pool=self.dp, upload_doc=False)

    @patch('api_storage.actions.delete_resource_folder')
    def test_called_action(self, mock_drf_action):
        delete_resource_folder(self.doc.bucket_name, self.doc.uuid)
        mock_drf_action.assert_called_with(self.doc.bucket_name, self.doc.uuid)
