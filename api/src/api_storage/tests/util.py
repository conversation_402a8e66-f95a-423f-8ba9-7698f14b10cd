from functools import wraps
from unittest.mock import patch

from api_storage.actions import \
    delete_resource_folder as delete_resource_folder_action
from api_storage.tasks import copy_resource_folder


def mock_copy_resource_folder_task(resource, source_uuid, target_uuid):
    """Used to mock out the `copy_resource_folder_task` task in tests."""
    copy_resource_folder.apply((resource, source_uuid, target_uuid))


def patch_enqueue_delete_resource_folder(func, *args, **kwargs):
    """Patches enqueueing of delete resource folder task triggered after
    deleting of several resources so that we bypass the celery queue.

    The created mock is injected into the kwargs under the name
    `mock_enqueue_delete_resource_folder`.

    Usage example:

    @patch('some.other.patch')
    @patch_enqueue_delete_resource_folder
    def some_test(self, some_other_mock, mock_enqueue_delete_resource_folder):
        ...
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        with patch(
            'api_storage.actions.delete_resource_folder_task.delay',
            wraps=delete_resource_folder_action
        ) as mock_drf:
            kwargs['mock_enqueue_delete_resource_folder'] = mock_drf

            return func(*args, **kwargs)

    return wrapper
