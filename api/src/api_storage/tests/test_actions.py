import logging
from unittest.mock import MagicMock, patch

from django.test import TestCase, TransactionTestCase
from pyfakefs.fake_filesystem_unittest import TestCaseMixin

from api_storage.actions import (
    delete_resource_folder, enqueue_delete_resource_folder,
    remove_orphaned_artifacts
)
from documents.tests.util import create_document
from extract.tests.util import create_extraction
from organizations.tests.util import create_data_pool, create_organization
from training.tests.util import (
    create_training_ready_training_set, create_training_revision
)

logger = logging.getLogger(__name__)


class StorageActionsTestCase(TestCase):

    def setUp(self):
        self.org, self.org_path = create_organization()
        self.dp, self.dp_path = create_data_pool(organization=self.org)
        self.doc, _ = create_document(data_pool=self.dp, upload_doc=False)

    @patch('api_storage.actions.delete_resource_folder_task.delay')
    def test_enqueue_delete_resource_folder(self, mock_drf_task):
        enqueue_delete_resource_folder(self.doc.bucket_name, self.doc.uuid)
        mock_drf_task.assert_called_with(self.doc.bucket_name, self.doc.uuid)

    @patch('api_storage.actions.storage_client')
    def test_delete_resource_folder(self, mock_gfsc):
        mock_storage = MagicMock()
        mock_gfsc.return_value = mock_storage

        with self.assertLogs(logger='api_storage.actions', level='INFO') as cm:
            assert len(cm.output) == 0
            delete_resource_folder(self.doc.bucket_name, self.doc.uuid)

        expected_log = (
            f'INFO:api_storage.actions:Deleted resource folder for {self.doc.uuid} '
            f'from bucket {self.doc.bucket_name}.'
        )
        self.assertIn(expected_log, cm.output)

        self.assertTrue(mock_gfsc.called)
        mock_storage.delete.assert_called_with(
            self.doc.bucket_name, str(self.doc.uuid) + '/'
        )

    @patch('api_storage.actions.storage_client')
    def test_delete_resource_folder_no_exception_for_file_not_found_error(self, mock_gfsc):
        mock_storage = MagicMock()
        mock_storage.delete.side_effect = FileNotFoundError
        mock_gfsc.return_value = mock_storage

        with self.assertLogs(logger='api_storage.actions', level='INFO') as cm:
            assert len(cm.output) == 0
            delete_resource_folder(self.doc.bucket_name, self.doc.uuid)

        expected_log = (
            f'INFO:api_storage.actions:No resource folder for {self.doc.uuid} '
            f'to delete from bucket {self.doc.bucket_name}.'
        )
        self.assertIn(expected_log, cm.output)

        self.assertTrue(mock_gfsc.called)
        mock_storage.delete.assert_called_with(self.doc.bucket_name, str(self.doc.uuid) + '/')

    @patch('api_storage.actions.storage_client')
    def test_delete_resource_folder_no_exception_for_other_exceptions(self, mock_gfsc):
        mock_storage = MagicMock()
        mock_storage.delete.side_effect = Exception
        mock_gfsc.return_value = mock_storage

        with self.assertLogs(logger='api_storage.actions', level='ERROR') as cm:
            assert len(cm.output) == 0
            delete_resource_folder(self.doc.bucket_name, self.doc.uuid)

        expected_log = (
            'ERROR:api_storage.actions:Error deleting resource folder for '
            f'{self.doc.uuid} from bucket {self.doc.bucket_name}'
        )
        expected_in_log = False
        for error_log in cm.output:
            if error_log.startswith(expected_log):
                expected_in_log = True
                break
        self.assertTrue(expected_in_log)

        self.assertTrue(mock_gfsc.called)

        mock_storage.delete.assert_called_with(
            self.doc.bucket_name, str(self.doc.uuid) + '/'
        )


class RemoveOrphanedArtifactsTestCase(TransactionTestCase, TestCaseMixin):

    def setUp(self):
        self.setUpPyfakefs()
        self.org, _ = create_organization()
        self.dp, _ = create_data_pool(organization=self.org)

        self.doc, _ = create_document(data_pool=self.dp)

        self.ts, _ = create_training_ready_training_set(
            data_pool=self.dp, documents=[self.doc]
        )
        self.tr, _ = create_training_revision(
            data_pool=self.dp, training_set=self.ts
        )
        self.ex, _ = create_extraction(
            data_pool=self.dp,
            training_revision=self.tr,
            document=self.doc
        )

    @patch('api_storage.tasks.delete_resource_folder.delay')
    @patch('api_storage.actions._last_modified_over_30_days')
    def test_remove_orphaned_artifacts(self, patch_age_check, patch_task):
        tokenized = self.doc.tokenizeds.first()

        tokenized.delete()
        self.doc.delete()
        self.tr.delete()
        self.ex.delete()

        self.assertNotEqual(tokenized.tokenized, {})
        self.assertTrue(self.doc.file_exists)
        self.assertTrue(self.tr.model_file_exists)
        self.assertTrue(self.ex.results_file_exists)

        patch_age_check.return_value = False
        remove_orphaned_artifacts()

        self.assertNotEqual(tokenized.tokenized, {})
        self.assertTrue(self.doc.file_exists)
        self.assertTrue(self.tr.model_file_exists)
        self.assertTrue(self.ex.results_file_exists)

        patch_age_check.return_value = True
        remove_orphaned_artifacts()

        self.assertEqual(tokenized.tokenized, {})
        self.assertFalse(self.doc.file_exists)
        self.assertFalse(self.tr.model_file_exists)
        self.assertFalse(self.ex.results_file_exists)

    @patch('api_storage.actions._last_modified_over_30_days')
    def test_does_not_remove_existing_object_artifacts(self, patch_age_check):
        patch_age_check.return_value = True
        tokenized = self.doc.tokenizeds.first()

        self.assertNotEqual(tokenized.tokenized, {})
        self.assertTrue(self.doc.file_exists)
        self.assertTrue(self.tr.model_file_exists)
        self.assertTrue(self.ex.results_file_exists)

        remove_orphaned_artifacts()

        self.assertNotEqual(tokenized.tokenized, {})
        self.assertTrue(self.doc.file_exists)
        self.assertTrue(self.tr.model_file_exists)
        self.assertTrue(self.ex.results_file_exists)
