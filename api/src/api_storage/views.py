import io
from datetime import datetime, timezone

from django.http import (
    HttpResponse, HttpResponseBadRequest, HttpResponseForbidden,
    HttpResponseNotFound
)
from django.views.decorators.csrf import csrf_exempt

from api_storage import storage_client
from glynt_api.util import calculate_content_md5


@csrf_exempt
def local_storage_presigned_url(request):
    """This view is provided so that we can generate semi-functional presigned
    urls in development and testing enviornments. Presigned urls generated by
    our customers or our other StorageClients will behave differently, and the
    code which handles presigned URLs must handle that.

    This view does not actually read "signed" urls. It simply reads the query
    parameters and responds accordingly, without checking for any sort of
    signature. As such, this is completely insecure and should only be used for
    development purposes.

    The query parameter `allowed_method` must be present, and must be one of
    GET or PUT. Only the indicated method will be allowed, all other methods
    will return a 400 response. If not present, a 400 response is returned.

    The query parameter `expires` must be present. It should be an epoch
    integer, and this view will return a 400 response if the provided epoch
    integer is greater than the current epoch time. If not present, a 400
    response is returned.

    Otherwise, if a GET request, returns the raw bytes of the file at path
    defined by the query parameters `resource` and `remote_path`, which should
    be the strings as needed for use by LocalStorageClient.download_fileobj().
    Returns a 404 if the object does not exist.

    If a PUT request and the query parameter `content_md5` is provided, the
    `Content-MD5` header must be set and its value must match the content_md5
    query parameter. Otherwise HTTP 403 is returned.

    If a PUT request and Content-MD5 header is provided, the Content-MD5 header
    value must match the actual content MD5 of the request, otherwise HTTP 400
    is returned.

    If a PUT request and the query parameter `content_type` is provided, the
    `Content-Type` header must be set and its value must match the content_type
    query parameter. Otherwise HTTP 403 is returned.

    PUT request returns a 204 if the file was successfully put to local storage.
    """
    required_params = ['allowed_method', 'expires', 'resource', 'remote_path']
    for param in required_params:
        if request.GET.get(param, None) is None:
            return HttpResponseBadRequest('"%s" query parameter is required.' % param)

    supported_methods = ('GET', 'PUT')
    allowed_method = request.GET.get('allowed_method')
    if allowed_method not in supported_methods:
        return HttpResponseBadRequest(
            'Unsupported allowed_method parameter. Expected one of %s' % str(supported_methods)
        )
    if str(request.method) != str(allowed_method):
        return HttpResponseBadRequest(
            'Unsupported method. Only %s requests allowed.' % allowed_method
        )

    expires = request.GET.get('expires')
    if int(expires) <= datetime.now(timezone.utc).timestamp():
        return HttpResponseBadRequest('Presigned URL has expired.')

    resource = request.GET.get('resource')
    remote_path = request.GET.get('remote_path')

    storage = storage_client()
    if request.method == 'GET':
        try:
            file_bytes = storage.download_fileobj(resource, remote_path)
        except FileNotFoundError:
            return HttpResponseNotFound(f'{remote_path} not found.')
        except ValueError:
            return HttpResponseBadRequest(f'{remote_path} is not a valid remote_path.')
        return HttpResponse(file_bytes)
    elif request.method == 'PUT':
        header_content_md5 = request.META.get('HTTP_CONTENT_MD5', None)
        required_md5 = request.GET.get('content_md5', None)
        if required_md5 is not None:
            # requires Content-MD5 header matching the content_md5 in query
            # string, which indicates the signed content-md5
            if header_content_md5 != required_md5:
                # signature error, returns 403
                return HttpResponseForbidden(
                    'Provided Content-MD5 header (%s) does not match presigned URL content MD5 (%s)' % (
                        header_content_md5, required_md5
                    )
                )
        required_content_type = request.GET.get('content_type', None)
        if required_content_type is not None:
            # requires Content-Type header matching the content_type in query
            # string, which indicates the signed content-type
            header_content_type = request.META.get('CONTENT_TYPE', None)
            if header_content_type != required_content_type:
                # signature error, returns 403
                return HttpResponseForbidden(
                    'Provided Content-Type header (%s) does not match presigned URL content type (%s)' % (
                        header_content_type, required_content_type
                    )
                )
        # now check that the actual md5 of the request body matches the one
        # specified in the header if provided
        if header_content_md5 is not None:
            content_md5 = calculate_content_md5(request.body)
            if content_md5 != header_content_md5:
                # returns 400
                return HttpResponseBadRequest(
                    'Provided Content-MD5 header (%s) does not match actual content MD5 (%s)' % (
                        header_content_md5, content_md5
                    )
                )
        try:
            storage.upload_fileobj(resource, remote_path, io.BytesIO(request.body))
            return HttpResponse(status=204)
        except ValueError:
            return HttpResponseBadRequest('%s is not a valid remote_path.' % remote_path)
