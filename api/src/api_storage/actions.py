import logging
from datetime import datetime, timedelta

from django.apps import apps as glynt_apps
from django.conf import settings
from django.core.exceptions import ValidationError
from django.utils import timezone

from . import storage_client
from .tasks import delete_resource_folder as delete_resource_folder_task

logger = logging.getLogger(__name__)


def enqueue_delete_resource_folder(bucket, resource_uuid):
    delete_resource_folder_task.delay(bucket, resource_uuid)


def delete_resource_folder(bucket, resource_uuid):
    logger.info(f"Deleting resource folder for {resource_uuid} from bucket {bucket}.")
    storage = storage_client()
    try:
        storage.delete(bucket, str(resource_uuid) + '/')
    except FileNotFoundError:
        logger.info(f"No resource folder for {resource_uuid} to delete from bucket {bucket}.")
    except Exception:
        logger.exception(f"Error deleting resource folder for {resource_uuid} from bucket {bucket}.")
    else:
        logger.info(f"Deleted resource folder for {resource_uuid} from bucket {bucket}.")


def remove_orphaned_artifacts():
    """Deletes stored object artifacts for objects that no longer exist.

    Script relies on FILE_STORAGE_ALLOWED_RESOURCES variable in
    settings.py to import each model to be processed.
    """
    resources = settings.FILE_STORAGE_ALLOWED_RESOURCES

    storage = storage_client()

    logger.info(f"Resources to evaluate: {resources}.")

    for resource in resources:
        log_prefix = f"resource {resource}:"
        logger.info(f"{log_prefix} Evaluating storage artifacts.")

        app, model_name = resource.split('.')
        app = app.replace("-", "_")
        model = glynt_apps.get_model(app, model_name)
        bucket = model().bucket_name

        total_objects_in_storage = 0
        total_objects_in_database = model.objects.count()
        total_deleted = 0

        uuids_of_existing_objects = set(str(uuid) for uuid in model.objects.values_list('uuid', flat=True))

        resource_objs = storage.list_bucket_objs(bucket)

        for obj in resource_objs:
            total_objects_in_storage += 1
            uuid = obj['Key'].split('/')[0]
            if uuid not in uuids_of_existing_objects and \
               _last_modified_over_30_days(obj['LastModified']):
                try:
                    # look again for extra safely
                    model.objects.get(uuid=uuid)
                except model.DoesNotExist:
                    logger.info(
                        f"{log_prefix} UUID {uuid} not found in database. "
                        f"Deleting {obj['Key']} object from storage."
                    )
                    storage.delete(bucket, obj['Key'])
                    total_deleted += 1
                except (ValidationError, ValueError) as e:
                    logger.warning(
                        f"{log_prefix} Invalid UUID for object: {uuid}; exception: {e}"
                    )
                except Exception as e:
                    logger.warning(
                        f"{log_prefix} Unhandled error for UUID {uuid}: {e}"
                    )

        logger.info(
            f"{log_prefix} Finished cleaning orphaned object artifacts."
        )
        logger.info(
            f"{log_prefix} Total objects in storage: {total_objects_in_storage}"
        )
        logger.info(
            f"{log_prefix} Total objects in database: {total_objects_in_database}"
        )
        logger.info(
            f"{log_prefix} Total objects deleted from storage: {total_deleted}"
        )


def _last_modified_over_30_days(last_modified):
    thirty_days_ago = datetime.now(timezone.utc) + timedelta(days=-30)
    if last_modified < thirty_days_ago:
        return True
    return False
