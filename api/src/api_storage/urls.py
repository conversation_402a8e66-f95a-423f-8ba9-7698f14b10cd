from django.conf.urls import include, url

from . import CustomLocalStorageClient, storage_client, views

urlpatterns = [
    url(r'^presigned_url/$', views.local_storage_presigned_url, name='presigned_url'),
]

# storage_root_url_patterns is provided so that the logic for determining
# storage client type can be isolated to this app. To include in a root url patterns:
#     urlpatterns = urlpatterns + storage_root_url_patterns
if type(storage_client()) is CustomLocalStorageClient:
    storage_root_urlpatterns = [
        url(r'^local-files/', include(('api_storage.urls', 'storage'), namespace='local_files'))
    ]
else:
    storage_root_urlpatterns = []
