import logging

from celery import shared_task

from api_storage import storage_client

logger = logging.getLogger(__name__)


@shared_task
def copy_resource_folder(bucket_name, source_uuid, target_uuid):
    logger.info(f"Copying resource folder {source_uuid} to {target_uuid} in bucket {bucket_name}.")
    storage = storage_client()
    storage.copy_folder(bucket_name, source_uuid, target_uuid)


@shared_task
def delete_resource_folder(resource, resource_uuid):
    from .actions import delete_resource_folder as \
        delete_resource_folder_action
    delete_resource_folder_action(resource, resource_uuid)


@shared_task
def remove_orphaned_artifacts():
    """Deletes artifacts in storage that have no corresponding object in the
    database. Intended to be run on a schedule through celerybeat."""
    from .actions import remove_orphaned_artifacts as remove_orphans
    logger.info("Starting remove_orphaned_artifacts action.")
    remove_orphans()
    logger.info("remove_orphaned_artifacts action complete.")
