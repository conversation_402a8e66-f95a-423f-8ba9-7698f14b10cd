import enum


@enum.unique
class States(enum.Enum):
    """A list of supported job states.
    States between 100-199 are considered STARTED_STATUSES.
    States of 200 and above are considered TERMINAL_STATUSES.
    Do not set a job's state to a lower one than the current state"""

    PENDING = 0  # pending jobs are eligible for conversion to in_progress by the task gate
    TOKENIZING = 1  # a pre-processing state where a job cannot yet be converted to in_progress

    IN_PROGRESS = 100  # a sub-service job is currently doing work
    BENCHMARKING = 110  # training revisions that are waiting on extractions
    FINISHING = 125  # the sub-service has completed its work and post-processing is happening

    # Terminal states must be 180 and above
    PARTIAL_SUCCESS = 190  # extraction batch with mixed results
    SUCCEEDED = 200  # job completed successfully

    # TODO (GL-4203): delete when old data expires (see also STATE_CHOICES below)
    PARTIAL_SUCCESS_OLD = 250  # previous code for PARTIAL_SUCCESS

    # failures and error conditions below
    FAILED = 300  # job failed at any point in its life


# Serialized state mappings
STATE_CHOICES = (
    (States.PENDING.value, 'Pending'),
    (States.TOKENIZING.value, 'Pending'),
    (States.IN_PROGRESS.value, 'In Progress'),
    (States.BENCHMARKING.value, 'In Progress'),
    (States.FINISHING.value, 'In Progress'),
    (States.PARTIAL_SUCCESS_OLD.value, 'Partial Success'),  # TODO (GL-4203): delete
    (States.PARTIAL_SUCCESS.value, 'Partial Success'),
    (States.SUCCEEDED.value, 'Success'),
    (States.FAILED.value, 'Failed')
)


STARTED_STATUSES = [state.value for state in States if 100 <= state.value < 180]
TERMINAL_STATUSES = [state.value for state in States if state.value >= 180]
TERMINAL_STATUS_NAMES = [state.name for state in States if state.value >= 180]


STATUS_DISPLAY_LOOKUP_MAP = {k: v for k, v in STATE_CHOICES}


def display_to_states(state_name):
    """Return state codes (ints) associated with an API-facing name"""
    return [value for value, display in STATE_CHOICES if display == state_name]
