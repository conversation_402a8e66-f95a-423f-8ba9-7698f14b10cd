import base64
import logging

import sendgrid
from sendgrid.helpers.mail import (
    Attachment, Category, Content, ContentId, Disposition, Email, FileContent,
    FileName, FileType, Ganalytics, Mail, Personalization,
    SubscriptionTracking, TrackingSettings
)

logger = logging.getLogger(__name__)


class SendgridMailer:
    """
    A mailer utilizing Sendgrid API.

    TODO: The Sendgrig API has a lot more to offer, we'll expand this mailer's
    functionalities as needed.
    """

    def __init__(self, mail=None, personalization=None, api=None,
                 prepare_mail=True, **kwargs):
        """
        Initializes API and helpers. Optionally, accepts a number of basic
        arguments to allow for initialization in a single line.
        Provide `sendgrid_api_key` in kwargs to override `SENDGRID_API_KEY`
        environment variable.
        """
        api_client_kwargs = {}
        api_key = kwargs.pop('sendgrid_api_key', None)
        if api_key:
            api_client_kwargs['api_key'] = api_key

        self.mail = mail if mail else Mail()
        self.personalization = personalization if personalization else Personalization()
        self.api = api if api else \
            sendgrid.SendGridAPIClient(**api_client_kwargs)
        self.tracking_settings = TrackingSettings()

        if prepare_mail:
            self.prepare_mail(**kwargs)

    def prepare_mail(self, **kwargs):
        """
        Prepares a mail message. Intended as a shortcut method. Simply
        wraps many of the required and commonly used methods. The three
        'addresses' arguments accept a list of address, or a string of a
        single address.
        """

        from_address = kwargs.pop('from_address', None)
        from_name = kwargs.pop('from_name', None)
        to_addresses = kwargs.pop('to_addresses', None)
        subject = kwargs.pop('subject', None)
        content_type = kwargs.pop('content_type', None)
        content = kwargs.pop('content', None)
        cc_addresses = kwargs.pop('cc_addresses', None)
        bcc_addresses = kwargs.pop('bcc_addresses', None)
        attachment = kwargs.pop('attachment', None)
        attachment_content_type = kwargs.pop('attachment_content_type', None)
        attachment_filename = kwargs.pop('attachment_filename', None)

        if kwargs:
            raise TypeError(
                'Unexpected keyword arguments: {}'.format(kwargs.keys())
            )

        self.set_from(from_address, from_name)
        self.set_subject(subject)
        self.add_to(to_addresses)
        self.add_content(content_type, content)
        self.add_cc(cc_addresses)
        self.add_bcc(bcc_addresses)

        if attachment:
            self.add_attachment(attachment_content_type, attachment,
                                attachment_filename)

    def add_to(self, addresses):
        """Required. Add recipient email address."""
        if addresses:
            if not isinstance(addresses, str):
                for address in addresses:
                    self.personalization.add_to(Email(address))
            else:
                self.personalization.add_to(Email(addresses))
        else:
            raise ValueError("Must provide 'Recipient email address'")

    def set_from(self, address, name=None):
        """Required. Set the sending email address."""
        if address:
            self.mail.from_email = Email(address, name)
        else:
            raise ValueError("Must provide 'from email address'")

    def set_subject(self, subject):
        """Required. Set the subject line."""
        if subject:
            self.mail.subject = subject
        else:
            raise ValueError("Must provide 'Subject'")

    def add_content(self, content_type, content):
        """Required. Set the Content and Content Type of the message."""
        if content_type and content:
            self.mail.add_content(Content(content_type, content))
        elif any([content_type, content]):
            raise ValueError("Must provide both 'content_type' and 'content'")

    def add_cc(self, cc_addresses):
        """Add CC recipient email address."""
        if cc_addresses is not None:
            if not isinstance(cc_addresses, str):
                for address in cc_addresses:
                    self.personalization.add_cc(Email(address))
            else:
                self.personalization.add_cc(Email(cc_addresses))

    def add_bcc(self, bcc_addresses):
        """Add BCC recipient email address."""
        if bcc_addresses is not None:
            if not isinstance(bcc_addresses, str):
                for address in bcc_addresses:
                    self.personalization.add_bcc(Email(address))
            else:
                self.personalization.add_bcc(Email(bcc_addresses))

    def add_attachment(self, content_type, content, filename,
                       disposition='attachment', content_id=None):
        """Add an attachment to the message."""
        filename = filename or 'attachment'
        if content and content_type:
            attachment = Attachment()
            attachment.file_content = FileContent(
                base64.b64encode(content).decode()
            )
            attachment.file_type = FileType(content_type)
            attachment.file_name = FileName(filename)
            attachment.disposition = Disposition(disposition)
            if content_id is not None:
                attachment.content_id = ContentId(content_id)
            self.mail.add_attachment(attachment)
        else:
            raise ValueError("Must provide both 'content_type' and 'content'")

    def add_category(self, category):
        self.mail.add_category(Category(category))

    def set_ganalytics(self, enabled=False, source="", medium="", term="",
                       content="", campaign=""):
        """Set Google Analytics tracking to email"""
        self.tracking_settings.ganalytics = Ganalytics(
            enabled,
            source,
            medium,
            term,
            content,
            campaign
        )
        self.mail.tracking_settings = self.tracking_settings

    def set_subscription_tracking(self, enabled=False, text=None, html=None,
                                  substitution_tag=None):
        """Set Subscription tracking to email.
        Note that subscription tracking is automatically enabled unless this
        method is called to disable such tracking. Which means an unsubscribe
        link is added to the bottom of the email automatically if not disabled.
        """
        self.tracking_settings.subscription_tracking = SubscriptionTracking(
            enabled,
            text,
            html,
            substitution_tag
        )
        self.mail.tracking_settings = self.tracking_settings

    def send(self):
        """Create request and send to API. Returns an HTTP response object."""
        self.mail.add_personalization(self.personalization)
        data = self.mail.get()
        try:
            response = self.api.client.mail.send.post(request_body=data)
        except Exception as ex:
            logger.error('sendgrid mailer error: {}'.format(repr(ex)))
            logger.error('data: {}'.format(data))
            raise
        return response
