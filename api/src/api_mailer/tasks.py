import logging

from celery import shared_task
from django.conf import settings
from django.template.loader import get_template

from auth0 import util as auth0_util
from auth0.util import (
    Auth0Exception, AuthorizationError, ImproperlyConfigured, NotFound
)
from .sendgrid_mailer import SendgridMailer

logger = logging.getLogger(__name__)


class SetPasswordTicketException(Exception):
    pass


class SendEmailException(Exception):
    pass


def _send_email(
    email_label, recipients, subject, content,
    from_address=None,
    from_name=None,
    attachment=None, attachment_filename=None,
    attachment_content_type=None
):
    """
    Send email with Sendgrid mailer.
    Returns True if sucess, False otherwise.
    """
    from_address = from_address or getattr(settings, 'EMAIL_FROM_ADDRESS', None)
    from_name = from_name or getattr(settings, 'EMAIL_FROM_NAME', None)

    if not (
        from_address and from_name and email_label and recipients and subject and content
    ):
        logger.error(
            'send_email error: Missing one or more required arguments: {}'.format(
                'email_label, receipients, subject, content, from_address,'
                ' from_name'
            )
        )
        return False

    try:
        message = SendgridMailer(
            from_address=from_address,
            from_name=from_name,
            to_addresses=recipients,
            subject=subject,
            content=content,
            content_type='text/html',
            attachment=attachment,
            attachment_content_type=attachment_content_type,
            attachment_filename=attachment_filename,
        )

        # Note that subscription tracking is automatically enabled unless turned
        # off explicitly, which means an unsubscribe link is added to the bottom
        # of the email automatically.

        # TODO: the unsubscribe link unsubscribes the email address globally, so
        # the email address won't get any emails from the Sendgrid account. So if
        # we are using a single Sendgrid account for all the Wattzon emails, the
        # link would unsubscribe from all. There are some subscription group
        # settings that can let the user control which group to unsubscribe from,
        # but we don't have it set up in the Sendgrid account we are currently
        # using and such such functionality is to be explored.
        #
        # In the meantime, we can disable the subscription tracking and no
        # unsubscribe link will be added to the email.
        # message.set_subscription_tracking(enabled=False)
    except Exception as ex:
        logger.error('Error preparing mail for {}: {}.'.format(
            email_label, ex
        ))
        return False

    try:
        message.send()
    except Exception as ex:
        logger.error('There was a problem sending {} email. Error: {}'.format(
            email_label, ex
        ))
        raise SendEmailException()

    if isinstance(recipients, str):
        logger.info('{} email sent to {}'.format(
            email_label, recipients
        ))
    elif len(recipients) > 5:
        logger.info('{} email sent to [{},...,{}]'.format(
            email_label,
            ','.join(recipients[:2]),
            ','.join(recipients[-2:])
        ))
    else:
        logger.info('{} email sent to [{}]'.format(
            email_label, ','.join(recipients)
        ))

    return True


@shared_task(
    bind=True, acks_late=True,
    autoretry_for=(SetPasswordTicketException, SendEmailException),
    retry_backoff=True
)
def send_welcome_email(self, email_address, result_url=None):
    """
    Sends a welcome email with set password link to the email address registered
    with Auth0.
    """
    if not email_address:
        raise Exception("Email address not provided")

    try:
        ticket = auth0_util.generate_password_reset_ticket(
            email_address,
            result_url=result_url,
            ticket_lifetime=getattr(
                settings,
                'WELCOME_EMAIL_PASSWORD_RESET_TICKET_LIFETIME',
                0
            ),
            mark_email_as_verified=True
        )
    except (ImproperlyConfigured, AuthorizationError, TypeError, NotFound):
        # these exceptions are not retried
        raise
    except Auth0Exception as ex:
        raise SetPasswordTicketException(ex)

    # prepare the email
    email_label = 'welcome'
    subject = "Welcome to {}".format(
        getattr(settings, 'EMAIL_FROM_NAME', 'GLYNT.ai')
    )
    template = get_template('emails/welcome.html')
    context = {
        'application_name': 'GLYNT API',
        'ticket': ticket,
    }
    content = template.render(context)

    _send_email(
        email_label, [email_address], subject, content
    )


@shared_task(
    bind=True, acks_late=True,
    autoretry_for=(SendEmailException,),
    retry_backoff=True
)
def send_copy_notification(self, email_address, resource_name, report_json=None):
    """
    Sends a notification after copy action is finished.
    """
    logger.info("sending copy notification")
    if not email_address:
        raise Exception("Email address not provided")

    # prepare the email
    email_label = 'copy finish notification'
    subject = "Copy {} Report".format(resource_name)
    content = (
        """<p>Copied resources reference attached.</p>"""
    )
    attachment = None
    attachment_content_type = None
    attachment_filename = None
    if report_json:
        attachment = report_json.encode('utf-8', errors='ignore')
        attachment_content_type = "text/json"
        attachment_filename = "copied_resources.json"

    logger.info(content)

    _send_email(
        email_label, email_address, subject, content,
        attachment=attachment,
        attachment_content_type=attachment_content_type,
        attachment_filename=attachment_filename
    )
