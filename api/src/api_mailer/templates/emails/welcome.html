<html>
  <head>
  <style type="text/css">
  p {
      font-size:90%;
      line-height:120%;
  }
  </style>
  </head>
  <body>
    <center>
      <table style="width: 600px;-webkit-text-size-adjust: 100%;-ms-text-size-adjust: 100%;mso-table-lspace: 0pt;mso-table-rspace: 0pt;margin: 0;padding: 0;font-family: &quot;ProximaNova&quot;, sans-serif;border-collapse: collapse !important;height: 100% !important;" align="center" border="0" cellpadding="0" cellspacing="0" height="100%" width="100%" id="bodyTable">
        <tr>
          <td align="center" valign="top" id="bodyCell"
style="-webkit-text-size-adjust: 100%;-ms-text-size-adjust: 100%;mso-table-lspace: 0pt;mso-table-rspace: 0pt;margin: 0;padding: 20px;font-family: &quot;ProximaNova&quot;, sans-serif;height: 100% !important;">
          <div class="main">
            <p style="text-align: center;-webkit-text-size-adjust: 100%;-ms-text-size-adjust: 100%; margin-bottom: 30px;">
              <img
src="https://glynt.ai/wp-content/uploads/2019/01/Logo_Glynt_1441-1.png" width="150"
alt="Glynt.ai" style="-ms-interpolation-mode: bicubic;border:
0;height: auto;line-height: 100%;outline: none;text-decoration: none;">
            </p>

            <h1>Welcome to {{ application_name }}!</h1>

            <p>You've successfully signed up for GLYNT API. Once you click the
            SET UP PASSWORD button or follow the link below to set up your
            password, you can start using the API to easily upload documents and
            extract clean, labelled data. Let's get started!</p>

            <p>
                <a href="{{ ticket }}"
                    style="display:inline-block;width:200px;background-color:#f4d03f;border-radius:3px;color:#666666;font-size:15px;line-height:45px;text-align:center;text-decoration:none"
                    target="_blank" >
                    SET UP PASSWORD
                </a>
            </p>
            <p>
            Alternately, you can copy and paste this link to your web browser to
            set up your password:
            <br/>{{ ticket }}
            </p>

            <p>If you are having any issues with your account, please don't
            hesitate to contact your GLYNT customer representative.</p>

            <p>
            Thanks!
            </p>

            <strong>The GLYNT.ai Team</strong>

          </div>
          </td>
        </tr>
      </table>
    </center>
  </body>
</html>
