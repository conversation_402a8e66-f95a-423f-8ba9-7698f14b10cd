import base64
import json
from unittest import TestCase

import sendgrid

from api_mailer.sendgrid_mailer import SendgridMailer


class SendgridMailerTestCase(TestCase):

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def setUp(self):
        self.from_address = '<EMAIL>'
        self.from_name = 'Sender'
        self.to_addresses = ['<EMAIL>', '<EMAIL>']
        self.subject = "Subject"
        self.content = "<html><body>Email content</body></html>"
        self.content_type = "text/html"

    def test_prepare_mail(self):
        message = SendgridMailer(prepare_mail=False)
        self.assertIsNone(message.mail.from_email)
        message.prepare_mail(
            from_address=self.from_address,
            from_name=self.from_name,
            to_addresses=self.to_addresses,
            subject=self.subject,
            content=self.content,
            content_type=self.content_type,
        )
        self.assertEqual(message.mail.from_email.email, self.from_address)
        self.assertEqual(message.mail.from_email.name, self.from_name)
        self.assertEqual(message.mail.subject.subject, self.subject)
        self.assertEqual(len(message.mail.contents), 1)
        self.assertEqual(
            message.mail.contents[0].get(),
            {'type': self.content_type, 'value': self.content}
        )
        self.assertEqual(len(message.personalization.tos), 2)
        self.assertIn(message.personalization.tos[0].get('email'), self.to_addresses)
        self.assertIn(message.personalization.tos[1].get('email'), self.to_addresses)
        self.assertNotEqual(
            message.personalization.tos[1].get('email'),
            message.personalization.tos[0].get('email')
        )

        message = SendgridMailer(prepare_mail=False)
        with self.assertRaises(TypeError):
            message.prepare_mail(
                unexpected_kwarg='unexpected_kwarg'
            )

        with self.assertRaises(ValueError):
            message = SendgridMailer(prepare_mail=False)
            message.prepare_mail(
                from_name=self.from_name,
                to_addresses=self.to_addresses,
                subject=self.subject,
                content=self.content,
                content_type=self.content_type,
            )

        with self.assertRaises(ValueError):
            message = SendgridMailer(prepare_mail=False)
            message.prepare_mail(
                from_address=self.from_address,
                from_name=self.from_name,
                subject=self.subject,
                content=self.content,
                content_type=self.content_type,
            )

        with self.assertRaises(ValueError):
            message = SendgridMailer(prepare_mail=False)
            message.prepare_mail(
                from_address=self.from_address,
                from_name=self.from_name,
                to_addresses=self.to_addresses,
                content=self.content,
                content_type=self.content_type,
            )

        with self.assertRaises(ValueError):
            message = SendgridMailer(prepare_mail=False)
            message.prepare_mail(
                from_address=self.from_address,
                from_name=self.from_name,
                to_addresses=self.to_addresses,
                subject=self.subject,
                content_type=self.content_type,
            )

        with self.assertRaises(ValueError):
            message = SendgridMailer(prepare_mail=False)
            message.prepare_mail(
                from_address=self.from_address,
                from_name=self.from_name,
                to_addresses=self.to_addresses,
                subject=self.subject,
                content=self.content,
            )

        message = SendgridMailer(prepare_mail=False)
        message.prepare_mail(
            from_address=self.from_address,
            from_name=self.from_name,
            to_addresses=self.to_addresses,
            subject=self.subject,
        )
        contents = message.mail.contents
        contents = contents if contents is not None else []
        self.assertEqual(len(contents), 0)

    def test_init_prepare_mail(self):
        message = SendgridMailer(
            from_address=self.from_address,
            from_name=self.from_name,
            to_addresses=self.to_addresses,
            subject=self.subject,
            content=self.content,
            content_type=self.content_type,
        )
        self.assertIsNotNone(message.mail.from_email)

    def test_send_mail(self):
        api_client_kwargs = {
            'api_key': 'apikey',
            'host': 'http://www.example.com',
        }
        sendgrid_api = sendgrid.SendGridAPIClient(**api_client_kwargs)
        message = SendgridMailer(
            api=sendgrid_api,
            from_address=self.from_address,
            from_name=self.from_name,
            to_addresses=self.to_addresses,
            subject=self.subject,
            content=self.content,
            content_type=self.content_type,
        )
        try:
            message.send()
        except Exception as ex:  # noqa
            # We expect the exception, because example.com isn't a real API. We
            # check below that the request is in a format we expect.
            pass

        data = message.mail.get()
        self.assertIn('from', data)
        self.assertEqual(data.get('from', {}).get('email', None), self.from_address)
        self.assertEqual(data.get('from', {}).get('name', None), self.from_name)
        self.assertEqual(data.get('subject', None), self.subject)
        self.assertIn('personalizations', data)
        self.assertEqual(len(data['personalizations']), 1)
        p0 = data['personalizations'][0]
        self.assertIn('to', p0)
        self.assertEqual(len(p0['to']), len(self.to_addresses))
        self.assertEqual(
            sorted([e.get('email', None) for e in p0['to']]),
            sorted(self.to_addresses)
        )
        self.assertIn('content', data)
        self.assertIsNotNone(data['content'])
        self.assertEqual(len(data['content']), 1)
        content0 = data['content'][0]
        self.assertEqual(content0.get('type', None), self.content_type)
        self.assertEqual(content0.get('value', None), self.content)

    def test_send_mail_with_json_attachment(self):
        data = {
            'key': 'value'
        }
        attachment = json.dumps(data).encode('utf-8', errors='ignore')

        api_client_kwargs = {
            'api_key': 'apikey',
            'host': 'http://www.example.com',
        }
        sendgrid_api = sendgrid.SendGridAPIClient(**api_client_kwargs)
        message = SendgridMailer(
            api=sendgrid_api,
            from_address=self.from_address,
            from_name=self.from_name,
            to_addresses=self.to_addresses,
            subject=self.subject,
            content=self.content,
            content_type=self.content_type,
            attachment=attachment,
            attachment_content_type='text/json',
            attachment_filename='attachment.json'
        )
        try:
            message.send()
        except Exception as ex:  # noqa
            # We expect the exception, because example isn't a real API. We
            # check below that the request is in a format we expect.
            pass

        data = message.mail.get()
        self.assertIn('attachments', data)
        self.assertEqual(len(data['attachments']), 1)
        attachment0 = data['attachments'][0]
        self.assertIn('content', attachment0)
        self.assertEqual(
            attachment0['content'],
            base64.b64encode(attachment).decode()
        )
        self.assertIn('filename', attachment0)
        self.assertEqual(attachment0['filename'], 'attachment.json')
        self.assertIn('type', attachment0)
        self.assertEqual(attachment0['type'], 'text/json')
