---
title: GLYNT API

language_tabs: # must be one of https://git.io/vQNgJ
  - shell

toc_footers:
  - <a href='https://github.com/lord/slate'>Documentation Powered by Slate</a>

includes:
  - errors
  - data_pools
  - documents
  - training_sets
  - extractions
  - extraction_batches
  - mat

search: true
---

# Definitions

* **Access Token**: A string token used for API auth. Note that these are in no
  way related to the "tokens" which are "words" on a Document (see below).
* **Client**: The software interacting with the GLYNT API.
* **Data Pool**: An isolated environment where data is managed. Used to achieve
  Data Segmentation within an Organization. An Organization may have one or
  more Data Pools, as they see fit.
* **Organization**:  A group which is utilizing GLYNT's product offerings.
* **Token**: A white-space delimited collection of characters in a Document.
  Intuitively, a "word" on a Document. Note that these have nothing to do with
  access tokens which are used for API auth (see above).
* **Training Set**: A Training Set is a collection of documents which are
  brought together to form a workspace in order to work on that collection of
  documents as a group.

# Introduction

The GLYNT API is a RESTful API that makes it easy to upload documents and
extract clean, labelled data. If you login at <https://api.glynt.ai> with your
user credentials (provided by your GLYNT customer representative), then you
will be able to browse the API interactively to view and edit your data.  The
base URL of the api is <https://api.glynt.ai/v6/>. This is referred to as the
`api_base_url`.

All data uploaded to or created by the API is segmented by Data Pool. Your
organization may have one or more Data Pools. A production Data Pool and a
stage Data Pool will be created for your organization by your GLYNT
representative, but additional Data Pools can be created on request. These Data
Pools are completely separate environments. The ID of the Data Pool to be
interacted with is passed in the URL of every request to the API like so:
`<api_base_url>/data-pools/<data_pool_id>/`. This is
referred to as the `datapool_url`, and serves as the base URL for all
endpoints outside of authorization.

To get started, you must first provide training data to your GLYNT customer
representative (outside of the API) so that the machine learning models can be
prepared for your unique document types. Your customer representative can tell
you what they need, give you an estimate of how long training will take, and
will let you know when it is available for extractions. You will also be able
to see the prepared Training Sets using the `/training-sets/` endpoints.

Once your Training Sets have been created and made available on your Data Pool(s),
you're ready to begin interacting with the GLYNT API, using standard REST
endpoints to interact with various resources. To begin a session, authenticate
with the API to obtain an access token as per the Auth section of the docs,
below.

With your token in hand, you're ready to begin submitting data for extractions.
The most common workflow will be:

  1. Upload Documents with one or several `POST`s to the `/documents/`
     endpoint, and subsequent `PUT`s to the temporary `file_upload_url`s.
  2. Initiate an Extraction Batch against the uploaded Documents with a `POST`
     to the `/extraction-batches/` endpoint, passing the IDs of the recently
     uploaded Documents and the ID of the Training Set to extract against. This
     `POST` initiates the Extraction Batch job.
  3. Poll the `/extraction-batches/` endpoint with `GET` requests using the
     Extraction Batch ID returned in step 2 until all results are available.
     Polling about once per minute is a reasonable default.
  4. Download the results for each Extraction of the finished Extraction Batch
     using the `/extractions/` endpoint.

You can use your user credentials directly to interact with your data and
experiment with the system. Machine-to-Machine integrations are also supported.
See the [Machine to Machine Flow](#machine-to-machine-flow) section below for
more information.


# Maintenance

Occasionally, the GLYNT API will go down for scheduled maintenance. In such
case, all API endpoints will return a 503 response.


# Auth

There are two methods of authenticating and authorizing with the API, one for
users and one for machine-to-machine integrations. In both the user and M2M
flows, the result will be an access token which is issued to the requesting
party. 

Access tokens are valid for 12 hours. Refresh tokens are not supported at this
time.

The access token is passed with all further requests to the API using
the `Authorization` header, like so:

`Authorization: <token_type> <access_token>`

<aside class="notice">
You must replace `&lt;token_type&gt;` and `&lt;access_token&gt;` with the values returned
by the Auth flow of your choice.
</aside>


## User Flow

> To retrieve an access token using the User Flow:

```shell
curl --request POST \
     --url 'https://api.glynt.ai/v6/auth/get-token/' \
     --header 'content-type: application/json' \
     --data '{"username":"<YOUR_USERNAME>","password":"<YOUR_PASSWORD>"}'
```

> Make sure to replace `<YOUR_USERNAME>` and `<YOUR_PASSWORD>` with the values
> provided to you by your GLYNT representative. This command will return JSON
> structured like this:

```json
{
  "access_token": "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
  "token_type": "Bearer"
}
```

Users of your organization may use their credentials to request an API access
token using a POST to the `/get-token/` endpoint (see the detailed
specification for more details). To have accounts created for your users,
please contact your GLYNT representative. These are the same credentials used
to access the browsable api at <https://api.glynt.ai>.

This flow is intended for developers to have easy access to the API using
simple credentials, in order to become familiar with the API or to execute ad
hoc requests outside the scope of a more complete integration (which should use
the Machine-to-Machine Flow.)

User passwords must be 12+ characters, and there are no special character
requirements.

## Machine-to-Machine Flow

> To retrieve an access token using the M2M Flow:

```shell
curl --request POST \
     --url 'https://glynt.auth0.com/oauth/token' \
     --header 'content-type: application/json' \
     --data '{"grant_type":"client_credentials","client_id":"<YOUR_CLIENT_ID>","client_secret": "<YOUR_CLIENT_SECRET>","audience":"glynt-public-api"}'
```

> Make sure to replace `<YOUR_CLIENT_ID>` and `<YOUR_CLIENT_SECRET>` with the
> values provided to you by your GLYNT representative. This command will return
> JSON structured like this:

```json
{
  "access_token": "***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
  "token_type": "Bearer"
}
```

The primary method for communicating with the GLYNT API is through Machine to
Machine integrations. M2M integrations allow an application written by your
Organization to have a secure set of credentials to interact with the GLYNT API
outside of the context of a user. This allows you to automate interactions with
the API, for example if you wanted to create tooling or building your own user
interface.

To get started, contact your GLYNT representative. They will provide you with a
Client ID and Client Secret. Keep these credentials safe and secure. If you
ever suspect they have been compromised, contact your GLYNT Representative to
have access revoked immediately.

Using the Client ID and Client Secret, your applications can execute an Oauth
2.0 Client Credentials Flow in order to obtain an access token for the GLYNT
API.


# Rate Limit

The API has a general rate limit of 200 requests per minute. M2M tokens are
limited to 6 per 12 hours, with bursts of 2 per 5 minutes.


# Labels, Tags & GLYNT Tags

Several resources have a `label` property. This property is an arbitrary string
of at most 255 characters for documents and 63 for all other resources. It is a
meaningful, case sensitive, unique label for the resource. Leading or trailing
whitespaces are not allowed. Non-printable characters are not allowed. Uniqueness
is enforced depending on the resource type. See the relevant resource section for
more information.

<aside class="notice">
Non-printable characters are parts of a character set that do not represent a
written symbol or part of the text within a document or code, but rather are
there in the context of signal and control in character encoding. Non-printable
characters are also known as non-printing characters or control characters.
</aside>

Several resources have `tags` and/or `glynt_tags` properties. These are both
lists of strings, and each string is limited to 100 characters. Leading or
trailing whitespaces are not allowed. Non-printable characters are not allowed.

`tags` are for your use only, allowing you to attach more verbose information
to a resource to facilitate managing your data. GLYNT will never modify this
data, and you may always make changes to them as long as the resource exists.
Each `tags` property may contain up to 10 tags. All tags are case sensitive.

You have read-only access to `glynt_tags`. This data is assigned by the GLYNT
system or administrators. It is most often used for internal tagging, feature
previews, or to facilitate communication about objects in the API. Unless noted
otherwise, this data is volatile and can change without notice.

<aside class="success">
Note that <code>tags</code> and <code>glynt_tags</code> are inherently
unordered. There are no guarantees about ordering being preserved between calls
to the API.
</aside>

<aside class="warning">
No integration should be made against <code>glynt_tags</code> unless explictly
mentioned in this documentation or instructed by your GLYNT representative. The
content of that field can change at any time without notice, or it can go away
entirely, in which case you will not be able to reference it any more.
</aside>


# API List View Pagination

> This query will show the 49th and 50th Documents.

```shell
curl "https://api.glynt.ai/v6/data-pools/pRvt5/documents/?limit=2&offset=48"
```

> This command will return JSON structured like this (many properties have been
> excluded from each document in this example to simplify the example.):

```json
{
  "count": 50,
  "next": null,
  "previous": "http://api.glynt.ai/v6/data-pools/pRvt5/documents/?limit=2&offset=46",
  "results": [
    {
      "url": "http://api.glynt.ai/v6/data-pools/pRvt5/documents/a841b7ba/",
      "id": "a841b7ba",
      "label": "one_cool_doc.pdf"
    },
    {
      "url": "http://api.glynt.ai/v6/data-pools/pRvt5/documents/aef4b54e/",
      "id": "aef4b54e",
      "label": "a_lame_doc.jpg"
    }
  ]
}
```

Endpoints which return multiple resource instances are paginated. In addition
to the `results` property which contains the resource instances themselves,
such paginated endpoints also return `count`, `next`, and `previous`
properties. These communicate the total number of resource instances in the
list, a link to the next page in the list, and a link to the previous page in
the list respectively. If there is no next or previous page, that property will
be `null`.

The API uses a limit-offset pagination scheme. The limit is the number of items
to retrieve, and defaults to 10 if not provided, and the maximum allowed value
is 100. The offset indicates how many items in the list to skip, and defaults
to 0. Thus, if limit and offset are both ignored the view would show the 10
oldest instances of the resource.

<aside class="notice">
Note that this is completely unrelated to the concept of Documents containing
multiple pages. See the Document resource for more information.
</aside>


## Ordering List Views

> Take as an example this request:

```shell
curl "https://api.glynt.ai/v6/data-pools/pRvt5/documents/"
```

>  That will return all documents in the data pool, sorted by creation date,
>  oldest first. That is equivalent to this:

```shell
curl "https://api.glynt.ai/v6/data-pools/pRvt5/documents/?ordering=created_at"
```

> We could instead invert the ordering, and have newest first:

```shell
curl "https://api.glynt.ai/v6/data-pools/pRvt5/documents/?ordering=-created_at"
```

> Or we could sort alphabetically by label:

```shell
curl "https://api.glynt.ai/v6/data-pools/pRvt5/documents/?ordering=label"
```

> Or we could combine two ordering values to first order by update time, and
> when two documents have matching `updated_at` properties, further sort by
> created_at, newest first:

```shell
curl "https://api.glynt.ai/v6/data-pools/pRvt5/documents/?ordering=updated_at,-created_at"
```

By default, items in all list views are ordered by creation date, starting with
the oldest.

The `ordering` query parameter may be passed when requesting any list view, and
will be used to control how to order the items in the list. The below table
summarizes options which are available on all list views, so long as the
property itself exists on the resource type being listed. Some resources have
special ordering values. The detailed documentation of those resources will
explain their use.

Ordering Value | Effect
-------------- | ------
created_at | By creation date, oldest first. This is the filter which is used if no ordering filter is passed.
updated_at | By last updated date, oldest first.
label | Alphabetical by label.

Every ordering filter value may have a `-` prepended to it, and this will
reverse the usual ordering.

Multiple ordering values may be passed comma separated. In this case, objects
will be ordered by the first ordering value, then by the second, and so on.


# Filtering

> This is an example of filtering Documents for those which are tagged both
> `invoice` and `customer 7`

```shell
curl "https://api.glynt.ai/v6/data-pools/pRvt5/documents/?tag=invoice&tag=customer+7"
```

> This command will return JSON structured like this (many properties have been
> excluded from each document in this example to simplify the example.):

```json
{
  "count": 15,
  "next": "http://api.glynt.ai/v6/data-pools/pRvt5/documents/?limit=10&offset=10",
  "previous": null,
  "results": [
    {
      "url": "http://api.glynt.ai/v6/data-pools/pRvt5/documents/a841b7ba/",
      "created_at": "2020-02-06T21:06:33.110518Z",
      "updated_at": "2020-02-06T21:10:02.149824Z",
      "id": "a841b7ba",
      "label": "one_cool_doc.pdf",
      "tags": ["invoice", "customer 7", "group 3"]
    },
    {
      "url": "http://api.glynt.ai/v6/data-pools/pRvt5/documents/aef4b54e/",
      "created_at": "2020-02-08T01:00:58.012030Z",
      "updated_at": "2020-02-08T01:03:22.288199Z",
      "id": "aef4b54e",
      "label": "a_lame_doc.jpg",
      "tags": ["invoice", "customer 7"]
    }
  ]
}
```

> Given a Training Set of ID 'ts12345', query Documents related to that
> Training Set (output not shown):

```shell
curl "https://api.glynt.ai/v6/data-pools/pRvt5/documents/?training_set=ts12345"
```

> Query Training Sets which are related to Document of ID 'do12345' (output not shown):

```shell
curl "https://api.glynt.ai/v6/data-pools/pRvt5/training-sets/?document=do12345"
```

> Query Training Sets created before February 2nd:

```shell
curl "https://api.glynt.ai/v6/data-pools/pRvt5/training-sets/?created_before=2020-02-08T00:00"
```

> Query Training Sets created after the training set of ID a841b7ba was
> created. Note that since datetime filters are inclusive, we increase the
> microseconds by one to exclude it:

``` shell
curl "https://api.glynt.ai/v6/data-pools/pRvt5/training-sets/?created_after=2020-02-06T21:06:33.110519Z
```

Endpoints which return multiple resource instances may be filtered to return
only a subset of the complete list. Multiple filters may be passed, and they
will all be applied to find only resources which fulfill the requirements of all
the filters.

Filter values must be (URL
encoded)[https://www.w3schools.com/tags/ref_urlencode.ASP].

## Filtering by properties

When passing a datetime value as a filter value, it should be [ISO
8601](https://www.w3.org/TR/NOTE-datetime) formatted, with a minimum
specificity of YYYY-MM-DDThh:mm. If further granularity is desired, seconds,
fractional seconds (up to 6 decimal places), and timezone offsets are all
supported as per the ISO 8601 specification. An example of a completely
specific datetime would be: `2020-02-06T11:45:13.000000-08:00`, meaning
February 6th 2020 at 11:45:13 Pacific Standard Time.

When a resource has a `labels`, `created_at`, `updated_at`, `tags`, or
`glynt_tags` property, the following filters are available:

### Case Sensitive Filters

Query Parameter | Filters For
--------------- | -----------
label | Resources whose label exactly matches the given label.
label__contains | Resources whose label contains the given string.
created_before | Resources which have a `created_at` at or before the given datetime.
created_after | Resources which have a `created_at` at or after the given [ISO 8601](https://www.w3.org/TR/NOTE-datetime) datetime.
updated_before | Resources which have an `updated_at` including or before the given [ISO 8601](https://www.w3.org/TR/NOTE-datetime) datetime.
updated_after | Resources which have an `updated_at` including or before the given [ISO 8601](https://www.w3.org/TR/NOTE-datetime) datetime.
tag | Resources with a Tag exactly matching the given Tag label.
tag__contains | Resources with at least one Tag which contains the given string.
exclude_tag | Resources that do not have a Tag with the exact matching string.
glynt_tag | Resources exactly matching the given GLYNT Tag label.
glynt_tag__contains | Resources with at least one GLYNT Tag which contains the given string.
exclude_glynt_tag | Resources that do not have a GLYNT Tag with the exact matching string.

### Case Insensitive Filters

Query Parameter | Filters For
--------------- | -----------
ilabel | Resources whose label matches case insensitively the given label.
ilabel__contains | Resources whose label contains case insensitively the given string.
itag | Resources with a Tag case insensitive matching the given Tag label.
itag__contains | Resources with at least one Tag which contains case insensitively the given string.
iglynt_tag | Resources case insensitively matching the given GLYNT Tag label.
iglynt_tag__contains | Resources with at least one GLYNT Tag which contains case insensitively the given string.

## Filtering by relationships

Whenever a resource has a relationship to other resource(s), that relationship
can be queried using a filter with the name
`<related_resource_name>=<id_to_filter_on>`. Such filters always use
underscores between words in the filter name, and always use the singular form
of the resource, never the plural (for example, `?training_set=<some_id>`).
These filters only search for exactly matching IDs, not partial matches. As
with all filters, multiple filters may be passed to further restrict the query.

Some resources also provide specialized filters. See the relevant Resource
section for more details.

<aside class="warning">
Unrecognized query parameters are silently ignored, so be careful to validate
that filters are spelled correctly and working as expected.
</aside>


