# Errors

> When errors occur, they are returned with an HTTP status code, and a JSON
> body. For most errors, a `detail` property is included in the JSON body providing more information.
> For example, if you requested a resource which does not exist, you would receive a 404 status code with a JSON body like the following:

```json
{
  "detail": "The requested resource could not be found."
}
```
> For most 400 Bad Request errors, the returned JSON body includes properties
> indicating the input parameters causing the error, and/or a `non_field_errors`
> property indicating the errors not caused by specific input parameter,
> providing lists of information.
> For example, if you requested to create a resource with missing required
> input, you would receive a 400 status code with a JSON body like the following:

```json
{
  "label": ["Label is required"]
}
```

The GLYNT API uses the following error codes(<b>\*</b>):

Error Code | Meaning
---------- | -------
400 | Bad Request -- Your request is invalid.
401 | Unauthorized -- Your access token was not provided or is invalid.
403 | Forbidden -- You do not have access to the requested resource.
404 | Not Found -- You requested a resource that does not exist.
405 | Method Not Allowed -- You tried to access a resource with an invalid method.
415 | Unsupported Media Type -- Your request payload is in a format not supported by this resource.
429 | Too Many Requests -- You've exceeded the rate limit.
500 | Internal Server Error -- We had a problem with our server. Try again later. If it persists, contact your GLYNT representative.
503 | Service Unavailable -- We're temporarily offline for maintenance. Please try again later.

<b>\*</b> This error documentation applies to all endpoints hosted at api.glynt.ai.
URLs outside of this domain have their own error handling procedures. Contact
your GLYNT representative if there are any issues interacting with the 3rd
party URLs.
