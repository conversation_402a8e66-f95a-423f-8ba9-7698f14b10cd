# Extraction Batches

## Extraction Batches Overview

Extraction Batches are provided as a convenient way to create and group
together a collection of Extractions. See the [Create an Extraction Batch
section](#create-an-extraction-batch) for the maximum number of documents which
can be created as part of a single batch. As in the typical workflow discussed
above, to post an Extraction Batch, you send a POST request with a list of
Document IDs to create an Extraction for. You may also pass the ID of which
Training Set to extract against. A series of Extractions is then automatically
created.

An Extraction Batch will inform you of the status of the batch, as well as the
boolean finished status. It also links to each of the Extractions it creates,
and you can retrieve the status values and results of those individual Extractions
as they become available if you do not want to wait for the entire Extraction
Batch to have completed.

All possible `status`es are listed in the table below.

Status | Meaning
------ | -------
Pending | No Extraction of the Batch has yet started processing.
In Progress | At least one Extraction of the Batch is in progress.
Success | All child extractions were successful.
Failed | All child extractions failed.
Partial Success | All child extractions are terminal, but there is a mix of success and failure.

An Extraction Batch will also inform you of the aggregate status of data post-
processing. This is found under `data_processing_status`.  A batch's transformed
data cannot be retrieved until this status is in a Success state.
Note: This feature is only applicable to environments where transformation V2
has been enabled. Status will show null otherwise since transformations are done
on demand.


## Retrieve all Extraction Batches

```shell
curl --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/extraction-batches/"
```
> This command will return a 200 response with a JSON structured like this:

```json
{
  "count": 2,
  "next": null,
  "previous": null,
  "results": [
    {
      "created_at": "2019-01-16T20:24:21.467694Z",
      "updated_at": "2019-01-16T20:34:00.100103Z",
      "url": "https://api.glynt.ai/v6/data-pools/pRvt5/extraction-batches/23d69fec/",
      "id": "23d69fec",
      "label": "December 2018 Invoices",
      "training_set": "https://api.glynt.ai/v6/data-pools/pRvt5/training-sets/33010eda/",
      "documents": [
        "https://api.glynt.ai/v6/data-pools/pRvt5/documents/4c543d94/",
        "https://api.glynt.ai/v6/data-pools/pRvt5/documents/56d03d54/",
        "https://api.glynt.ai/v6/data-pools/pRvt5/documents/5d2fb5da/",
      ],
      "extractions": [
        "https://api.glynt.ai/v6/data-pools/pRvt5/extractions/60a8332c/",
        "https://api.glynt.ai/v6/data-pools/pRvt5/extractions/6dd93e88/",
        "https://api.glynt.ai/v6/data-pools/pRvt5/extractions/73847cc6/",
      ],
      "status": "Success",
      "data_processing_status": "Success",
      "finished": true,
      "classify": false,
      "tags": [],
      "glynt_tags": []
    },
    {
      "created_at": "2019-01-16T20:26:11.467752Z",
      "updated_at": "2019-01-16T20:28:17.666631Z",
      "url": "https://api.glynt.ai/v6/data-pools/pRvt5/extraction-batches/40f584bc/",
      "id": "40f584bc",
      "label": "Special order forms",
      "training_set": "https://api.glynt.ai/v6/data-pools/pRvt5/training-sets/33010eda/",
      "documents": [
        "https://api.glynt.ai/v6/data-pools/pRvt5/documents/4c543d94/",
        "https://api.glynt.ai/v6/data-pools/pRvt5/documents/56d03d54/",
        "https://api.glynt.ai/v6/data-pools/pRvt5/documents/5d2fb5da/",
      ],
      "extractions": [
        "https://api.glynt.ai/v6/data-pools/pRvt5/extractions/6dd93e88/",
        "https://api.glynt.ai/v6/data-pools/pRvt5/extractions/73847cc6/",
      ],
      "status": "In Progress",
      "data_processing_status": "Pending",
      "finished": false,
      "classify": false,
      "tags": [],
      "glynt_tags": []
    }
  ]
}
```

Lists all Extraction Batches in the DataPool.

### HTTP Request
`GET <datapool_url>/extraction-batches/`

### Query Parameters
Parameter  | Default | Description
---------- | ------- | -----------
quick-view | false   | Limit the results to basic data (see below).

### Quick View Mode
Setting the `quick-view` parameter to `true` greatly increases the response
time when listing extraction batches, especially when there are increased
numbers of large extraction batches. It replaces the `documents` and
`extraction` fields with counts, and adds two additional fields:
`first_extraction` (the first extraction in a batch), `training_set_id`,
and `training_set_label` (the training set ID and label associated with a
batch).

```shell
curl --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/extraction-batches/?quick-view=true"
```

> This command returns a 200 response with a JSON structured like this (only changed parameters are shown):

```json
{
  "count": 2,
  "next": null,
  "previous": null,
  "results": [
    {
      "documents": 3,
      "extractions": 3,
      "first_extraction": "https://api.glynt.ai/v6/data-pools/pRvt5/extractions/60a8332c/?advanced=true",
      "training_set_id": "fR5vf",
      "training_set_label": "Training Set Label Example 1"
    },
    {
      "documents": 3,
      "extractions": 2,
      "first_extraction": "https://api.glynt.ai/v6/data-pools/pRvt5/extractions/6dd93e88/?advanced=true",
      "training_set_id": "ptRv5",
      "training_set_label": "Training Set Label Example 2"
    }
  ]
}
```

## Create an Extraction Batch

```shell
curl --request POST \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/extraction-batches/" \
     --header "Authorization: Bearer abc.123.def" \
     --header "content-type: application/json" \
     --data '{"label":"December 2018 Invoices","training_set":"89660ffa","documents":["4c543d94","56d03d54","5d2fb5da"]}'
```
> On success, this command will return a 201 response with a JSON body
> structured like this:

```json
{
  "created_at": "2018-02-17T14:54:30.699864Z",
  "updated_at": "2018-02-17T14:54:30.699864Z",
  "url": "https://api.glynt.ai/v6/data-pools/pRvt5/extraction-batches/b5a42d68/",
  "id": "b5a42d68",
  "label": "December 2018 Invoices",
  "training_set": "https://api.glynt.ai/v6/data-pools/pRvt5/training-sets/89660ffa/",
  "documents": [
    "https://api.glynt.ai/v6/data-pools/pRvt5/documents/4c543d94/",
    "https://api.glynt.ai/v6/data-pools/pRvt5/documents/56d03d54/",
    "https://api.glynt.ai/v6/data-pools/pRvt5/documents/5d2fb5da/",
  ],
  "extractions": [],
  "status": "Pending",
  "data_processing_status": "Pending",
  "finished": false,
  "classify": false,
  "tags": [],
  "glynt_tags": []
}
```

To create an Extraction Batch, you may select a Training Set to power the
extraction of data. Then, upload the Documents whose data you wish to extract.
Finally, submit the Extraction Batch with a POST request, passing the Training
Set ID and the IDs of the Documents to extract data from.

The `classify` field exists for legacy reasons.
The only legal value for `classify` is `false`.

The `status` property will change as the batch is processed. Extractions are
created automatically shortly after the Extraction Batch itself is created.
The `finished` status will be updated when processing has completed.

### HTTP Request
`POST <datapool_url>/extraction-batches/`

### Request Body Parameters
Parameter | Default | Description
--------- | ------- | -----------
documents | None | Required. List of Document IDs to extract data from. Minimum of 1, maximum of 1500 Document IDs.
label | A UUID | A string label for the Extraction Batch. See the Labels, Tags & GLYNT Tags section. Must be unique within a Data Pool.
tags | [] | A tags list. See the Labels, Tags & GLYNT Tags section.
training_set | None | Required  ID of the Training Set that will power the extractions.
classify | False | Present for legacy reasons. The only legal value is `false`.


## Retrieve an Extraction Batch
```shell
curl --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/extraction-batches/23d69fec/"
```
> If the Extraction Batch exists, this command will return a 200 response with
> a JSON body structured like this:

```json
{
  "created_at": "2019-01-16T20:24:21.467694Z",
  "updated_at": "2019-01-16T20:34:00.100103Z",
  "url": "https://api.glynt.ai/v6/data-pools/pRvt5/extraction-batches/23d69fec/",
  "id": "23d69fec",
  "label": "December 2018 Invoices",
  "training_set": "https://api.glynt.ai/v6/data-pools/pRvt5/training-sets/33010eda/",
  "documents": [
    "https://api.glynt.ai/v6/data-pools/pRvt5/documents/4c543d94/",
    "https://api.glynt.ai/v6/data-pools/pRvt5/documents/56d03d54/",
    "https://api.glynt.ai/v6/data-pools/pRvt5/documents/5d2fb5da/",
  ],
  "extractions": [
    "https://api.glynt.ai/v6/data-pools/pRvt5/extractions/60a8332c/",
    "https://api.glynt.ai/v6/data-pools/pRvt5/extractions/6dd93e88/",
    "https://api.glynt.ai/v6/data-pools/pRvt5/extractions/73847cc6/",
  ],
  "status": "Success",
  "data_processing_status": "Success",
  "finished": true,
  "classify": false,
  "tags": [],
  "glynt_tags": []
}
```
Returns detailed information about a given Extraction Batch.

### HTTP Request
`GET <datapool_url>/extraction-batches/<extraction_batch_id>/`


## Change Extraction Batch Properties
```shell
curl --request PATCH \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/extraction-batches/4427b0/" \
     --header "Authorization: Bearer abc.123.def" \
     --header "content-type: application/json" \
     --data '{"label": "New Label"}'
```
> On success, this command will return a 201 response with a JSON body structured like this:

```json
{
  "created_at": "2019-01-16T20:24:21.467694Z",
  "updated_at": "2019-01-16T20:34:00.100103Z",
  "url": "https://api.glynt.ai/v6/data-pools/pRvt5/extraction-batches/4427b0/",
  "id": "23d69fec",
  "label": "New Label",
  "training_set": "https://api.glynt.ai/v6/data-pools/pRvt5/training-sets/33010eda/",
  "documents": [
    "https://api.glynt.ai/v6/data-pools/pRvt5/documents/4c543d94/",
    "https://api.glynt.ai/v6/data-pools/pRvt5/documents/56d03d54/",
    "https://api.glynt.ai/v6/data-pools/pRvt5/documents/5d2fb5da/",
  ],
  "extractions": [
    "https://api.glynt.ai/v6/data-pools/pRvt5/extractions/60a8332c/",
    "https://api.glynt.ai/v6/data-pools/pRvt5/extractions/6dd93e88/",
    "https://api.glynt.ai/v6/data-pools/pRvt5/extractions/73847cc6/",
  ],
  "status": "Success",
  "data_processing_status": "Success",
  "finished": true,
  "classify": false,
  "tags": [],
  "glynt_tags": []
}
```
Change the mutable properties of an Extraction Batch. The mutable properties
are listed in the Request Body Parameters below.

### HTTP Request
`PATCH <datapool_url>/extraction-batches/<extraction_batch_id>/`

### Request Body Parameters
Parameter | Description
--------- | -----------
label | See Create a Extraction Batch request body parameters.
tags | See Create a Extraction Batch request body parameters.


## Delete an Extraction Batch
```shell
curl --request DELETE \
     --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/extraction-batches/27b0-20e5-11e9-ab14-d663bd873d93"
```

> On success, this command will return a 204 response with no JSON body.

Removes an Extraction Batch.

<aside class="success">
Deleting an Extraction Batch does not delete all associated Extractions. If you
wish to delete all child Extractions most easily, then first delete all child
Extractions, then delete the parent Extraction Batch.
</aside>

### HTTP Request
`DELETE <datapool_url>/extraction-batches/<extraction_batch_id>/`


## Retrieve Extraction Batch Field Distribution Information

```shell
curl --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/extraction-batches/639c2be0/field-distribution" \
```
> On success, this command will return a 200 response with a JSON body
> structured like this:

```json
{
  "document_count": 2,
  "fields": {
    "Account Number": {
      "responses": 2,
      "response_rate": 100
    },
    "Line Item One": {
      "reponses": 1,
      "response_rate": 50
    }
  },
  "documents": {
    "hg8rr1": {
      "label": "Sample Document 1",
      "training_set_id": "8fnadk",
      "training_set_label": "Sample Training Set",
      "review_url": "http://ui.glynt.ai/data-pools/Dnais5/review/639c2be0/extraction/defgG1",
      "fields": {
        "Account Number": {
          "content": "abc123",
          "tags": ["Sample Tag 1", "Sample Tag 2"],
          "glynt_tags": []
        }
      }
    },
    "nQkaL1": {
      "label": "Sample Document 2",
      "training_set_id": null,
      "training_set_label": null,
      "review_url": "http://ui.glynt.ai/data-pools/Dnais5/review/639c2be0/extraction/hg8rr1",
      "fields": {
        "Account Number": {
          "content": "def456",
          "tags": ["Sample Tag 1", "Sample Tag 2"],
          "glynt_tags": []
        },
        "Line Item One": {
          "content": "$100",
          "tags": [],
          "glynt_tags": ["Sample Glynt Tag"]
        }
      }
    }
  }
}
```

> Retrieve stats csv report with the following request:

```shell
curl --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/extraction-batches/639c2be0/field-distribution?format=csv" \
```

> On success, this command will return a 200 response with a byte string
> body representing a csv file containing field distribution stats:

```shell
Field Label, Responses, Response Rate, Total Documents
Account Number, 2, 100, 2
Line Item One, 1, 50, 2
```

> Retrieve "wide" csv report with the following request:

```shell
curl --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/extraction-batches/639c2be0/field-distribution?format=csv&report_type=wide" \
```

> On success, this command will return a 200 response with a byte string body
> representing a csv file containing field distribution data organized by
> document:

```shell
Training Set Id, Training Set Label, Document Id, Document Label, Account umber, Line Item One, Document Review URL
dsank2, Sample Training Set, hg8rr1, Sample Document 1, abc123, , http://ui.glynt.ai/data-pools/Dnais5/review/639c2be0/extraction/defgG1
, , nQkaL1, Sample Document 2, def456, $100, http://ui.glynt.ai/data-pools/Dnais5/review/639c2be0/extraction/hg8rr1
```

> Retrieve "long" csv report with the following request:

```shell
curl --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/extraction-batches/639c2be0/field-distribution?format=csv&report_type=long" \
```

> On success, this command will return a 200 response with a byte string body
> representing a csv file containing field distribution data organized by
> document and field:

```shell
Training Set Id, Training Set Label, Document Id, Document Label, Field Label, Field Tags, Field Glynt Tags, Returned Value, Document Review URL
dsank1, Sample Training Set, hg8rr1, Sample Document 1, Account Number, ["Sample Tag 1", "Sample Tag 2"], [], abc123, http://ui.glynt.ai/data-pools/Dnais5/review/639c2be0/extraction/defgG1
dsank1, Sample Training Set, hg8rr1, Sample Document 1, Line Item One, [], ["Sample Glynt Tag"], , http://ui.glynt.ai/data-pools/Dnais5/review/639c2be0/extraction/defgG1
, , nQkaL1, Sample Document 2, Account Number, ["Sample Tag 1", "Sample Tag 2"], [], def456, http://ui.glynt.ai/data-pools/Dnais5/review/639c2be0/extraction/hg8rr1
, , nQkaL1, Sample Document 2, Line Item One, [], ["Sample Glynt Tag"], $100, http://ui.glynt.ai/data-pools/Dnais5/review/639c2be0/extraction/hg8rr1
```

Returns information about the distribution of fields in an Extraction Batch.

The `training_sets` query parameter can be used to limit the report to only the
data about Extractions related to one or a handful of passed Training Sets.
This is useful when retrieving reports for a large Extraction Batch when you
wish to generate smaller reports that focus on certain groups of Documents.

### HTTP Request
`GET <datapool_url>/extraction-batches/<extraction_batch_id>/field-distribution`

### Query Parameters
Parameter | Default | Description
--------- | ------- | -----------
format | json | Possible values: csv, json. If csv is requested, the response content-type will be `text/csv` and the raw CSV data will be returned.
report_type | stats | Possible values: long, wide, stats. This query parameter is ignored unless the format is `csv`. `stats` returns a summary view of the data. `long` returns a field-centric view of the data and is only accessible to Glynt staff users. `wide` returns a document-centric view of the data.
training_sets | all sets and "None" set | Comma separated list of Training Set IDs. One of the elements of the list may be the special value `None` to include the Extractions with no Training Set. Only Extractions related to the given Training Sets will be included in the report. Similarly, only Fields from the included Extractions will be shown. If not passed, all Extractions in the batch are included in the report.

<aside class="notice">
NOTE: JSON reports may be integrated against and will remain backwards compatible. Keys may be added
to the JSON report in the future. CSV reports are intended for human use and may change without notice.
</aside>


## Retrieve Transformed Batch Results
```shell
curl --request GET \
     --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/extraction-batches/e923b6/transformed-results?format=json"
```

> On success, this command will return a 200 response with a raw JSON body.
>
> Retrieve transformed results in csv format with the following request: 

```shell
curl --request GET \
     --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/extraction-batches/e923b6/transformed-results?format=csv"
```

Returns a transformed version of extracted data to conform to stylistic,
formatting, or business requirements. Contact your Glynt representative for
more information about how to set up custom transformers in your data pools.

### HTTP Request
`GET <datapool_url>/extraction-batches/<extraction_batch_id>/transformed-results`

### Query Parameters
Parameter | Default | Description
--------- | ------- | -----------
format | None | Required. Possible values: csv, json. If csv is requested, the response content-type will be `text/csv` and the raw CSV data will be returned.
training_sets | all sets and "None" set | Comma separated list of Training Set IDs. One of the elements of the list may be the special value `None` to include the Extractions with no Training Set. Only Extractions related to the given Training Sets will be included in the report. Similarly, only Fields from the included Extractions will be shown. If not passed, all Extractions in the batch are included in the report.
view | None | An optional view name to use in place of the transformer or view value specified on the data pool.


## Refresh Transformed Extraction Batch Results
```shell
curl --request POST \
     --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/extraction-batches/e923b6/refresh-transformed"
```

> On success, this command will return a 201 response with an empty body.
>
Triggers a re-transformation of the Extraction's results data including its most current Corrections and Alterations.

### HTTP Request
`GET <datapool_url>/extraction-batches/<extraction_batch_id>/refresh-transformed`
