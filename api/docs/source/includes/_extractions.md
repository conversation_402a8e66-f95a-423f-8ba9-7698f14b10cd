# Extractions

## Extractions Overview

An Extraction is an extraction job for a single document. They may be created
directly or automatically by an Extraction Batch. Each Extraction has it's own
`status` and `finished` property, independent of any parent Extraction Batch,
if applicable.

All possible status values are listed in the table below.

Status | Meaning
------ | -------
Pending | Extraction has not yet started processing.
In Progress | Extraction is in progress.
Verifying | Extraction result is being verified.
Success | Extraction finished processing.
Failed | Extraction finished with an error. No data was extracted.

An Extraction will also inform you of the aggregate status of data post-
processing. This is found under `data_processing_status`.  A batch's transformed
data cannot be retrieved until this status is in a Success state.
Note: This feature is only applicable to environments where transformation V2
has been enabled. Status will show null otherwise since transformations are done
on demand.

> Sample result for a single Field, called `Billing_Month`. Notice it is
> comprised of two tokens: `November` and `2018`.

```json
"Billing_Month": {
  "content": "November 2018",
  "data_type": "date",
  "data_type_config": {"order": "MDY"},
  "tags": [],
  "glynt_tags": [],
  "is_additional": false,
  "field_order": 1
  "tokens": [
    {
      "content": "November",
      "page": 1,
      "bbox": [
        {"x": 1410, "y": 55},
        {"x": 1644, "y": 55},
        {"x": 1644, "y": 92},
        {"x": 1410, "y": 92}
      ],
    },
    {
      "content": "2018",
      "page": 1,
      "bbox": [
        {"x": 1650, "y": 55},
        {"x": 1720, "y": 55},
        {"x": 1720, "y": 92},
        {"x": 1650, "y": 92}
      ],
    }
  ]
}
```

Successful extractions have a `results` property, which links to the endpoint
to view the created extraction results. This property is omitted when
retrieving all extractions, so use the [Retrieve an
Extraction](#retrieve-an-extraction) endpoint to view the results.

Results are in JSON format, where the properties are the Fields as defined by the
Training Set, and the values are the extracted content of the field, as well as
useful metadata about the extracted content. The results properties are
explained in the following table:

Parameter | Description
--------- | -----------
content | The extracted string content for the field. If GLYNT did not extract data for this field, this parameter's value will be `null`. If the system can confidently assert that the Field is not present in the Document, then the value will be an empty string.
tokens | The tokens (see [Definitions](#definitions) of the document which were used to construct the content. The value of this property is an object, and it's sub-properties are listed below. Note that tokens are not always available, and this key will instead have a value of an empty array when that is the case.
tokens--content | String content of the token as it was captured from the document.
tokens--page | On which page the token appears.
tokens--bbox | Bounding box coordinates of the token as it appears on the page.

## Retrieve all Extractions

```shell
curl --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/extractions/"
```
> This command will return a 200 response with a JSON structured like this:

```json
{
  "count": 2,
  "next": null,
  "previous": null,
  "results": [
    {
      "created_at": "2019-01-16T20:24:25.120938Z",
      "updated_at": "2019-01-16T20:25:59.999881Z",
      "url": "https://api.glynt.ai/v6/data-pools/pRvt5/extractions/e923b69a/",
      "id": "e923b69a",
      "training_set": "https://api.glynt.ai/v6/data-pools/pRvt5/training-sets/774554c8/",
      "extraction_batch": "https://api.glynt.ai/v6/data-pools/pRvt5/extraction-batches/f108e010/",
      "document": "https://api.glynt.ai/v6/data-pools/pRvt5/documents/f8796b4e/",
      "status": "Success",
      "data_processing_status": "Success",
      "finished": true,
      "classify": false,
      "tags": [],
      "glynt_tags": []
    },
    {
      "created_at": "2019-01-16T20:24:26.823493Z",
      "updated_at": "2019-01-16T20:24:26.823493Z",
      "url": "https://api.glynt.ai/v6/data-pools/pRvt5/extractions/03c89b3c/",
      "id": "03c89b3c",
      "training_set": "https://api.glynt.ai/v6/data-pools/pRvt5/training-sets/774554c8/",
      "extraction_batch": "https://api.glynt.ai/v6/data-pools/pRvt5/extraction-batches/f108e010/",
      "document": "https://api.glynt.ai/v6/data-pools/pRvt5/documents/ff64f6bc/",
      "status": "In Progress",
      "data_processing_status": null,
      "finished": false,
      "classify": false,
      "tags": [],
      "glynt_tags": []
    }
  ]
}
```

Lists all Extractions in the DataPool. Omits `results` property from each
Extraction. To see the results, retrieve the individual Extraction.

### HTTP Request
`GET <datapool_url>/extractions/`


## Create an Extraction

```shell
curl --request POST \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/extractions/" \
     --header "Authorization: Bearer abc.123.def" \
     --header "content-type: application/json" \
     --data '{"training_set":"89660ffa","document":"4c543d94"}'
```
> On success, this command will return a 201 response with a JSON body
> structured like this:

```json
{
  "created_at": "2018-02-17T14:54:30.699864Z",
  "updated_at": "2018-02-17T14:54:30.699864Z",
  "url": "https://api.glynt.ai/v6/data-pools/pRvt5/extractions/e923b69a/",
  "id": "e923b69a",
  "training_set": "https://api.glynt.ai/v6/data-pools/pRvt5/training-sets/774554c8/",
  "extraction_batch": null,
  "document": "https://api.glynt.ai/v6/data-pools/pRvt5/documents/4c543d94/",
  "status": "Pending",
  "data_processing_status": null,
  "finished": false,
  "classify": false,
  "tags": [],
  "glynt_tags": []
}
```

To create an Extraction, you may select a Training Set to power the extraction
of data. Then, upload the Document whose data you wish to extract.  Finally,
submit the Extraction with a POST request, passing the Training Set ID and the
ID of the Documents to extract data from.

The `status` property will change as the Extraction is processed.  The
`finished` status will be updated when processing has completed.

### HTTP Request
`POST <datapool_url>/extractions/`

### Request Body Parameters
Parameter | Default | Description
--------- | ------- | -----------
training_set | None | Required ID of the Training Set that will power the extraction.
document | None | Required. ID of the Document to extract data from.
classify | False | For legacy reasons, the only legal value is `false`.
tags | [] | A tags list. See the Labels, Tags & GLYNT Tags section.


## Retrieve an Extraction
```shell
curl --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/extractions/e923b69a/"
```
> If the Extraction exists, this command will return a 200 response with a JSON
> body structured like this:

```json
{
  "created_at": "2019-01-16T20:24:25.120938Z",
  "updated_at": "2019-01-16T20:25:59.999881Z",
  "url": "https://api.glynt.ai/v6/data-pools/pRvt5/extractions/e923b69a/",
  "id": "e923b69a",
  "training_set": "https://api.glynt.ai/v6/data-pools/pRvt5/training-sets/774554c8/",
  "extraction_batch": "https://api.glynt.ai/v6/data-pools/pRvt5/extraction-batches/f108e010/",
  "document": "https://api.glynt.ai/v6/data-pools/pRvt5/documents/f8796b4e/",
  "status": "Success",
  "data_processing_status": "Success",
  "finished": true,
  "classify": false,
  "results": {
    "Billing_Month": {
      "content": "November 2018",
      "tokens": [
        {
          "content": "November",
          "page": 1,
          "bbox": [
            {"x": 1410, "y": 55},
            {"x": 1644, "y": 55},
            {"x": 1644, "y": 92},
            {"x": 1410, "y": 92}
          ],
        },
        {
          "content": "2018",
          "page": 1,
          "bbox": [
            {"x": 1650, "y": 55},
            {"x": 1720, "y": 55},
            {"x": 1720, "y": 92},
            {"x": 1650, "y": 92}
          ],
        }
      ],
      "data_type": "date",
      "data_type_config": {"order": "MDY"},
      "is_additional": false,
      "field_order": 1,
      "tags": ["field_tags"],
      "glynt_tags": ["field_glynt_tags"]
    }
  }
}
```
Returns detailed information about a given Extraction. Results dictionary
contains the results for each field including the content, token data, and the
tags and glynt tags that were present on the field at the time it was
trained.

### HTTP Request
`GET <datapool_url>/extractions/<extraction_id>/`


## Change Extraction Properties
```shell
curl --request PATCH \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/extractions/4427b0/" \
     --header "Authorization: Bearer abc.123.def" \
     --header "content-type: application/json" \
     --data '{"tags":["orig_tag","new_tag"]}'
```
> On success, this command will return a 201 response with a JSON body structured like this:

```json
{
  "created_at": "2018-02-17T14:54:30.699864Z",
  "updated_at": "2018-02-17T14:54:30.699864Z",
  "url": "https://api.glynt.ai/v6/data-pools/pRvt5/extractions/4427b0/",
  "id": "e923b69a",
  "training_set": "https://api.glynt.ai/v6/data-pools/pRvt5/training-sets/774554c8/",
  "extraction_batch": null,
  "document": "https://api.glynt.ai/v6/data-pools/pRvt5/documents/4c543d94/",
  "status": "Pending",
  "data_processing_status": null,
  "finished": false,
  "classify": false,
  "tags": ["orig_tag", "new_tag"],
  "glynt_tags": []
}
```
Change the mutable properties of an Extraction. The mutable properties are listed
in the Request Body Parameters below.

### HTTP Request
`PATCH <datapool_url>/extractions/<extraction_id>/`

### Request Body Parameters
Parameter | Description
--------- | -----------
tags | See Create a Extraction request body parameters.


## Delete an Extraction
```shell
curl --request DELETE \
     --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/extractions/e923b69a"
```

> On success, this command will return a 204 response with no JSON body.

Removes an Extraction and associated data.

### HTTP Request
`DELETE <datapool_url>/extractions/<extraction_id>/`


## Retrieve Transformed Extraction Results
```shell
curl --request GET \
     --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/extractions/e923b6/transformed-results?format=json"
```

> On success, this command will return a 200 response with a raw JSON body.
>
> Retrieve transformed results in csv format with the following request: 

```shell
curl --request GET \
     --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/extractions/e923b6/transformed-results?format=csv"
```


Returns a transformed version of extracted data to conform to stylistic,
formatting, or business requirements. Contact your Glynt representative for
more information about how to set up custom transformers in your data pools.

### HTTP Request
`GET <datapool_url>/extractions/<extraction_id>/transformed-results`

### Query Parameters
Parameter | Default | Description
--------- | ------- | -----------
format | None | Required. Possible values: csv, json. If csv is requested, the response content-type will be `text/csv` and the raw CSV data will be returned.
view | None | An optional view name to use in place of the transformer or view value specified on the data pool.


## Refresh Transformed Extraction Results
```shell
curl --request POST \
     --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/extractions/e923b6/refresh-transformed"
```

> On success, this command will return a 201 response with an empty body.
>
Triggers a re-transformation of the Extraction's result data including its most current Corrections and Alterations.

### HTTP Request
`GET <datapool_url>/extractions/<extraction_id>/refresh-transformed`


## Validate an Extraction's results
```shell
curl --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/extractions/e923b69a/validate/"
```
> If the Extraction exists, this command will return a 200 response with a JSON
> body structured like this:

```json
{
    "id": "yxAXZ1",
    "results": [
        {
            "dataset": "normalized",
            "label": "CurrentCharges",
            "result": "invalid",
            "error": "Invalid value for field type",
            "error_detail": "amount"
        }
    ]
}
```
Returns the extraction ID and a results list containing fields that failed validation and
the reason why.

### HTTP Request
`GET <datapool_url>/extractions/<extraction_id>/validate/`
