# Data Pools

## Data Pools Overview

A Data Pool can be thought of as an "environment." Each Data Pool is a
completely separate "silo" of documents, training sets, extractions, etc. Most
often, you will need only two Data Pools: Sandbox (for testing integrations),
and Production.

To manage your Data Pools, contact your GLYNT representative.


## Retrieve all Data Pools

```shell
curl --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/data-pools/"
```
> This command will return a 200 response with a JSON structured like this:

```json
{
  "count": 2,
  "next": null,
  "previous": null,
  "results": [
    {
      "created_at": "2019-01-16T20:24:21.467694Z",
      "updated_at": "2019-01-16T20:24:21.467694Z",
      "url": "https://api.glynt.ai/v6/data-pools/pRvt5/",
      "id": "pRvt5",
      "label": "Sandbox",
      "documents": "https://api.glynt.ai/v6/data-pools/pRvt5/documents/",
      "extraction_batches": "https://api.glynt.ai/v6/data-pools/pRvt5/extraction_batches/",
      "extractions": "https://api.glynt.ai/v6/data-pools/pRvt5/extractions/",
      "training_sets": "https://api.glynt.ai/v6/data-pools/pRvt5/training_sets/",
      "glynt_tags": []
    },
    {
      "created_at": "2019-01-16T21:21:30.467694Z",
      "updated_at": "2019-01-16T21:24:31.645855Z",
      "url": "https://api.glynt.ai/v6/data-pools/dD314/",
      "id": "dD314",
      "label": "Production",
      "documents": "https://api.glynt.ai/v6/data-pools/dD314/documents/",
      "extraction_batches": "https://api.glynt.ai/v6/data-pools/dD314/extraction_batches/",
      "extractions": "https://api.glynt.ai/v6/data-pools/dD314/extractions/",
      "training_sets": "https://api.glynt.ai/v6/data-pools/dD314/training_sets/",
      "glynt_tags": []
    }
  ]
}
```
Lists all Data Pools. Notice that there are a collection of properties which
link to the list views of the resources associated with the Data Pool.

### HTTP Request
`GET <api_base_url>/data-pools/`


## Retrieve a Data Pool
```shell
curl --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/"
```
> If the Data Pool exists, this command will return a 200 response with a JSON
> body structured like this:

```json
{
  "created_at": "2019-01-16T20:24:21.467694Z",
  "updated_at": "2019-01-16T20:24:21.467694Z",
  "url": "https://api.glynt.ai/v6/data-pools/pRvt5/",
  "id": "pRvt5",
  "label": "Sandbox",
  "documents": "https://api.glynt.ai/v6/data-pools/pRvt5/documents/",
  "extraction_batches": "https://api.glynt.ai/v6/data-pools/pRvt5/extraction_batches/",
  "extractions": "https://api.glynt.ai/v6/data-pools/pRvt5/extractions/",
  "training_sets": "https://api.glynt.ai/v6/data-pools/pRvt5/training_sets/",
  "glynt_tags": []
}
```
Returns detailed information about a given Data Pool.

### HTTP Request
`GET <api_base_url>/data-pools/<data_pool_id>/`

## Upsert route configuration

```shell
curl --request PUT \
    --header "content-type: application/json" \
    --header "Authorization: Bearer abc.123.def" \
    --url "https://api.glynt.ai/v6/data-pools/pRvt5/route-config" \
    --data '{"route_prefix": {"prefix": "something-prefix"}, "document_naming": {"format": "GLYNT_ID:AccountNumber:DocumentLabel"}}'
```

> On success, this command will return a 204.

This endpoint somewhat follows the PATCH rfc5789 standards. In short this endpoint can persists only partial changed.
To remove the configuration refer to the DELETE operator.

### HTTP Request

`PUT <api_base_url>/data-pools/<data_pool_id>/route-config`

## Delete route configuration

```shell
curl --request DELETE \
    --header "Authorization: Bearer abc.123.def" \
    --url "https://api.glynt.ai/v6/data-pools/pRvt5/route-config"
```

> On success, this command will return a 204.
> Due to the partial updates mechanism used for this endpoint to cleanup the configurations

Erase the current route configuration

### HTTP Request

`DELETE <api_base_url>/data-pools/<data_pool_id>/route-config`

## Get route configuration

```shell
curl --header "Authorization: Bearer abc.123.def" \
    --url "https://api.glynt.ai/v6/data-pools/pRvt5/route-config"
```

> If the Data Pool exists, this command will return a 200 response with a JSON
> If not previous configuration was set for the endpoint will set a default value
> body structured like this:

```json
{
  "route_prefix": {
    "prefix": "something-prefix"
  },
  "document_naming": {
    "format": "GLYNT_ID:AccountNumber:DocumentLabel"
  },
  "package_trigger": {
    "type": "verification"
  },
  "data_packaging_config": {
    "package_format":"one_data_file_per_document",
    "naming_formula": [
      "GLYNT",
      {"lookup_value": "document", "source": "AccountNumber"},
      {"lookup_value": "metadata", "source": "verified_at"}
    ]
  }
}
```

Returns detailed information about a given route configuration.

### HTTP Request

`GET <api_base_url>/data-pools/<data_pool_id>/route-config`

## Set default route configuration

```shell
curl --request PUT \
    --header "content-type: application/json" \
    --header "Authorization: Bearer abc.123.def" \
    --url "https://api.glynt.ai/v6/data-pools/pRvt5/route-config/set-default" \
    --data '{"prefix": "something-prefix"}'
```

> On success, this command will return a 204.

This endpoint somewhat follows the PATCH rfc5789 standards. In short this endpoint can persists only partial changed.
To remove the configuration refer to the DELETE operator.

### HTTP Request

`PUT <api_base_url>/data-pools/<data_pool_id>/route-config/set-default`

## Transformer configuration summary

The transformer configurations are managed by 2 concepts:
- The configuration definition: which is identified by its name and contains a specific
configuration structure
- Configuration mappings: this associates either an organization or a datapool to a specific
transformer configuration.

The configuration names are unique across the system and define a specific transformer configuration.

> If 2 configuration mappings are defined for the same organization, i.e. one at the organization
> level and one at the datapool level for the same organization, the datapool level configuration
> will take precedence for transformations done in the corresponding datapool.

## Upsert transformer configuration definition

This endpoint allow to create or update a transformer configuration definition. The body of the
request has the following format:

```text
{
  "transformer_config": {
    "config_name": "configuration name",
    "operators_config": [
      {
        "op_type": "operator type"
        // operator specific config
      },
      ...
    ]
  },
}
```

The `operators_config` attribute is a list of all
the operators to run with the configuration for each
of them. Refer to the transformer documentation for the
list of available operators and their specific configuration
options. The operators will run in the same order as the
`operators_config` list.

### HTTP Request

`PUT <api_base_url>/data-pools/<data_pool_id>/transformer/config-definiton`

## Get a transformer config definition

### HTTP Request

`GET <api_base_url>/data-pools/<data_pool_id>/transformer/config-definiton?config_name=<config_name>`

## Delete a transformer config definition

### HTTP Request

`DELETE <api_base_url>/data-pools/<data_pool_id>/transformer/config-definiton?config_name=<config_name>`

## Upsert a transformer config mapping

A config mapping associates either an organization or a datapool to a specific configuration.

The request to upsert a configuration mapping must have the following body:

```json
{
  "org_id": "ABC123",
  "data_pool_id": "DEF456",
  "config_name": "<name_of_config>"
}
```

### HTTP Request

`PUT <api_base_url>/data-pools/<data_pool_id>/transformer/config-mapping`

## Get a transformer config mapping

### HTTP Request

`GET <api_base_url>/data-pools/<data_pool_id>/transformer/config-mapping?org_id=<organization_id>&data_pool_id=<data_pool_id>`

## Delete a transformer config mapping

### HTTP Request

`GET <api_base_url>/data-pools/<data_pool_id>/transformer/config-mapping?org_id=<organization_id>&data_pool_id=<data_pool_id>`




## Field match enrichment configuration summary

The concept behind field match enrichment is that you can configure a set of
field label / field value pairs that must match what is found in the transformed
data for the enrichment to take place. This configuration can be set at the
organization level or the datapool level. When the enrichment is performed,
the fields in the `enriched_data` attribute are merged in the transformed data.
If there are enrichment fields that have the same field label as the fields in
the transformed data, the enrichment field value will override the transformed field
value.

## Upsert enrichment field match

This endpoint allow to create or update a field match configuration. The body of the
request has the following format:

```json
{
  "org_id": "ABC123",
  "data_pool_id": "DEF456",
  "field_matches": {
    "FieldLabel1": "FieldValue1",
    "FieldLabel2": "FieldValue2"
  },
  "enrichment_data": {
    "EnrichedFieldLabel1": "EnrichedValue1",
    "EnrichedFieldLabel2": "EnrichedValue2"
  }
}
```

It is possible to allow enrichment to take place regardless of the content of the transformed
data by passing an empty object for `field_matches` like so:

```json
{
  "org_id": "ABC123",
  "data_pool_id": "DEF456",
  "field_matches": {},
  "enrichment_data": {
    "EnrichedFieldLabel1": "EnrichedValue1",
    "EnrichedFieldLabel2": "EnrichedValue2"
  }
}
```

To configure the enrichment at the organization level, simply omit the `data_pool_id` attribute:

```json
{
  "org_id": "ABC123",
  "field_matches": {
    "FieldLabel1": "FieldValue1",
    "FieldLabel2": "FieldValue2"
  },
  "enrichment_data": {
    "EnrichedFieldLabel1": "EnrichedValue1",
    "EnrichedFieldLabel2": "EnrichedValue2"
  }
}
```

Here is a curl example command that will enrich, regardless of the transformed data for
all transformations in the org `ABC123`:

```shell
curl --request PUT \
    --header "content-type: application/json" \
    --header "Authorization: Bearer abc.123.def" \
    --url "https://api.glynt.ai/v6/data-pools/pRvt5/enrichments/field-match" \
    --data '{"org_id": "ABC123", "field_matches": {}, "enrichment_data": {"EnrichmentLabel": "EnrichmentValue"}}'
```

> On success, this command will return a 200.

### HTTP Request

`PUT <api_base_url>/data-pools/<data_pool_id>/enrichments/field-match`

## Delete field match enrichment

To delete a specific enrichment entry, the same attributes that were passed at the
creation, except for the `enrichment_data`, must be passed in the request body. Here
is a curl example that deletes the enrichment entry that was created in the upsert
section above.

```shell
curl --request DELETE \
    --header "content-type: application/json" \
    --header "Authorization: Bearer abc.123.def" \
    --url "https://api.glynt.ai/v6/data-pools/pRvt5/enrichments/field-match" \
    --data '{"org_id": "ABC123", "field_matches": {}'
```

Remove a field match enrichment entry

### HTTP Request

`PUT <api_base_url>/data-pools/<data_pool_id>/enrichments/field-match`
