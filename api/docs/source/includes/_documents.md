# Documents

## Documents Overview

A Document is an image, pdf, scan, etc, and it's associated meta data. The
actual content of the file is referred to as the Document content. All other
fields (such as `label`, `tags`, etc.) are that Document's metadata. These must
meet certain imaging quality metrics. Once created, most properties of a
Document resource are immutable.  See the [Change Document Properties
section](#change-document-properties) for more details.

Documents can be associated with a specific training set using the `target_training_set` field. 
This allows you to specify which model is **intended** for use at extraction time. 

**NOTE**: This is currently used only for tracking/annotation.  The system does
not automatically use this training set.

Documents can be single or multiple pages. Artifacts and results will refer to
the page numbers sequentially starting from 1.

<aside class="notice">
Document pages are unrelated to API List View Pagination.
</aside>

Uploading a Document's content can be achieved in one of two ways. It can be
included directly in the initial POST request, or it can be uploaded in a
separate PUT request after the Document instance has been created. See the
[Create a Document section](#create-a-document) for more details.

Once uploaded, the Document content can be accessed through temporary urls.
These urls can not be altered and must be used exactly as provided. Each
Document resource which has an associated file has a permanent
`file_access_url`. By retrieving this URL, a `file_temp_url` is generated and
returned to you. This `file_temp_url` may be used to directly retrieve the file
content for 1 hour.  See the [Retrieve a Document
section](#retrieve-a-document) for more details.


## Retrieve all Documents

```shell
curl --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/documents/"
```
> This command will return a 200 response with a JSON structured like this:

```json
{
  "count": 2,
  "next": null,
  "previous": null,
  "results": [
    {
      "created_at": "2019-01-16T20:24:21.467694Z",
      "updated_at": "2019-01-16T20:24:21.467694Z",
      "url": "https://api.glynt.ai/v6/data-pools/pRvt5/documents/c71d90d2/",
      "file_access_url": "https://api.glynt.ai/v6/data-pools/pRvt5/documents/c71d90d2/file/",
      "id": "c71d90d2",
      "label": "one_cool_doc.pdf",
      "tags": [],
      "glynt_tags": [],
      "content_type": "application/pdf",
      "content_md5":"4DujaMxdUy64mWOWbP6Xew==",
      "filesize": 327,
      "target_training_set": "GTS123"
        
    },
    {
      "created_at": "2019-01-16T21:21:30.467694Z",
      "updated_at": "2019-01-16T21:24:31.645855Z",
      "url": "https://api.glynt.ai/v6/data-pools/pRvt5/documents/442a2904/",
      "file_access_url": "https://api.glynt.ai/v6/data-pools/pRvt5/documents/442a2904/file/",
      "id": "442a2904",
      "label": "a_lame_doc.tiff",
      "tags": [],
      "glynt_tags": [],
      "content_type": "image/tiff",
      "content_md5":"gHtrtAskfdFDS2d11skAew==",
      "filesize": 350,
      "target_training_set": null
    }
  ]
}
```
Lists all Documents in the DataPool.

### HTTP Request
`GET <datapool_url>/documents/`

### Query Parameters

| Parameter                 | Default | Description                                                                                               |
|---------------------------|---------|-----------------------------------------------------------------------------------------------------------|
| show_related_extractions  | false   | When set to `true`, add extraction data for each returned document under the `related_extractions` field. |
| with_preview              | false   | When set to `true`, include a 1000 char text based preview of the document, if tokenized succesfully.     |

<aside class="warning">
When <code>show_related_extractions</code> and <code>extraction_batch</code> are included, the standard filters aren't used.
This custom filter excludes any document without a extraction from the results.
</aside>

I'll review and improve the clarity of your REST API documentation.

### Document Manifest Endpoint

This endpoint provides a document list that isn't directly scoped to a specific data pool.
It allows clients to filter across organizations and data pools. 
The `with_preview` flag is supported mentioned above is also supported.

```shell
curl --header "Authorization: Bearer abc.123.def" \
 --url "https://api.glynt.ai/v6/documents/"
```

**Response:** Returns a 200 status code with the following JSON structure:

```json
{
  "count": 23,
  "next": null,
  "previous": null,
  "results": [
    {
      "id": "Z6EVZ1",
      "data_pool_id": "gu6TV1",
      "data_pool_label": "Acme Electricity",
      "org_id": "PIeI22",
      "org_label": "Acme",
      "label": "sample_bill_01",
      "created_at": "2025-03-21T23:25:02.976788Z",
      "updated_at": "2025-03-21T23:25:05.246450Z",
      "content_type": "application/pdf",
      "file_extension": ".pdf",
      "inbound_path": "acme/acme-electric",
      "token_count": 5014,
      "filesize": 10384,
      "language": "en-US",
      "format_id": "72d3e4e2-af74-45f9-bd10-2b2f9c997b66",
      "is_supported": true,
      "content_md5": "1CY6EP6vV53rk+Yvr0+Q+w==",
      "duplicate_count": 7,
      "extractions": [
        {
          "id": "IRgX22",
          "created_at": "2025-03-21T23:57:45.852212Z",
          "updated_at": "2025-03-21T23:58:23.160187Z",
          "training_set": "lndl81",
          "training_set_label": "Train Set XYZ",
          "extraction_batch": "ylYLF1",
          "extraction_batch_label": "Extraction Batch",
          "status": "Success",
          "data_processing_status": "Success",
          "finished": true,
          "verified": false,
          "verified_at": null,
          "verify_expired": false
        }
      ],
      "enrichments": null,
      "pages": [
        "https://api.glynt.ai/v6/data-pools/gu6TV1/documents/Z6EVZ1/pages/1/"
      ],
      "url": "https://api.glynt.ai/v6/documents/Z6EVZ1/",
      "file_access_url": "https://api.glynt.ai/v6/data-pools/gu6TV1/documents/Z6EVZ1/file/",
      "status": "PENDING_CREATION",
      "target_training_set": "Abc123",
      "target_training_set_label": null
    }
  ]
}
```

### Document Text Content

This endpoint provides the text representation of a document. It's designed for clients that need
to examine content without requesting the full `tokenized` object.

```shell
curl --header "Authorization: Bearer abc.123.def" \
 --url "https://api.glynt.ai/v6/documents/Z6EVZ1/text"
```

**Response:**

```json
{
  "id": "Awzls1",
  "data_pool_id": "UL4312",
  "text_pages": [
     "Account Number 492-44-2929\nStatement Date: 02/01/2025\nBright Energy Solutions 262150\nACH Page 1 of 1",
     "Charges\nEnergy Usage 122.08\nPeak Demand Charge 8.08\nCity Utility Tax 5.47\nGrid Maintenance Fee 8.13\nBasic Service Fee 18.19\nTotal Charges 161.95"
  ]
}
```


## Create a Document

> To create a Document using the recommended single-call approach, POST to the
> GLYNT API to create the Document instance and upload the content in one step.
> Include the Base64-encoded document content in the `content` key:

```shell
curl --request POST \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/documents/" \
     --header "Authorization: Bearer abc.123.def" \
     --header "content-type: application/json" \
     --data '{"label":"sample_doc_name","tags":["sample_tag"],"content_type":"application/pdf","content":"6jughaFskanHuja55lakAsi---snipped for brevity---mskni8nv292wnv232v33df2k323f2=="}'
```
> On success, this command will return a 201 response with a JSON body
> structured like this. Notice that the `content_md5` will be calculated and
> displayed if not provided on upload. The Document is now created and uploaded.

```json
{
  "created_at": "2018-02-16T21:21:30.467694Z",
  "updated_at": "2018-02-16T21:21:30.467694Z",
  "url": "https://api.glynt.ai/v6/data-pools/pRvt5/documents/442a2904/",
  "file_access_url": "https://files.glynt.ai?signature=abc123def456",
  "id": "442a2904",
  "label": "sample_doc_name",
  "tags": ["sample_tag"],
  "glynt_tags": [],
  "content_type": "application/pdf",
  "content_md5":"4DujaMxdUy64mWOWbP6Xew==",
  "filesize": 454,
  "target_training_set": null
}
```
> If you wish to include a `content_md5` for increased data integrity
> guarantees, the first step is to generate the verification hash for the file
> you are going to upload.  This is achieved by first generating an MD5 binary
> and then encoding it as a Base64 string. The MD5 digest, before being encoded
> as Base64, is a binary entity - do not convert to hexidecimal representation.
>
> Below is an example of a shell command which combines these two steps:

```shell
openssl dgst -md5 -binary /path/to/some/file.pdf | openssl enc -base64
```
> This returns the Base64 encoded string to use for the `content_md5`.
> Something like the following:

```
4DujaMxdUy64mWOWbP6Xew==
```
> This value may now be included in the `content_md5` field of the POST
> request, and will be used to guarantee that the file we receive is the
> file you sent.

The recommended method of creating a Document is to simply include the Base64
encoded Document content in the POST request made to this endpoint. 

This method requires no subsequent call to upload the Document content, because
the content is included in the POST request. If you have already integrated the
legacy Document upload method, see the [Create a Document
(legacy)](#create-a-document-legacy) section, below, for that documentation.

If you wish to have stronger guarantees that the received Document is not
corrupted in transit or the file is too large for GLYNT to process,
you may optionally include the `content_md5` and `filesize` property
when creating a Document. See the Request Parameters table, below, for more
information.

The GLYNT code examples git repo includes `upload_document.py` [example
python script](https://github.com/Glynt-ai/Glynt/tree/master/python),
demonstrating this upload method.

<aside class="success">
Notice that <code>content_md5</code> must be a fully compliant Base64-encoded 128-bit MD5
digest of the file content. Do not first encode the file content in Base64 -
the file content is only encoded in Base64 to put that encoded string into the
<code>content</code> field. So calculate the <code>content_md5</code> from the
file's raw content first, then encode the file content in Base64 for inclusion
in the `content` field.  See <a href="https://www.ietf.org/rfc/rfc1864.txt">RFC
1864</a> for detail.
</aside>

<aside class="warning">
Be sure to include the <code>content</code> when making this POST request. If
you accidentally include <code>content_md5</code> but not <code>content</code>,
the system assumes you are following the legacy Document upload method, and
will return a 201 response even though the Document content has not yet been
submitted.
</aside>

The following content types for files are are supported for processing (training, extraction):

* application/pdf
* image/jpeg
* image/png
* image/tiff

Files in formats not listed above can still be uploaded. Although these
files will be accepted, they are classified as unsupported and, as such,
cannot undergo processing for either training or extraction activities.


### HTTP Request
`POST <datapool_url>/documents/`

### Request Body Parameters
Parameter | Default | Description
--------- | ------- | -----------
content_md5 | Autocalculated | The Base64-encoded 128-bit MD5 digest of file content according to [RFC 1864](https://www.ietf.org/rfc/rfc1864.txt). If provided, will be used to validate the uploaded `content`, and the POST will be rejected and not created if the provided value does not match what the API calculates from the content. Used to guarantee that there is no corruption of the document data during transit or decryption.
content_type | None | Required. The content type of the file. See the allowed content types list above.
label | A UUID | A string label for the Document. 255 character maximum. See the Labels, Tags & GLYNT Tags section. Unlike most labels in the API, this label does not need to be unique, though it is encouraged to use a unique, semantic label for every Document. Most often, this is the filename of the Document.
content | None | Required. The Base64-encoded content of the file.
tags | [] | A tags list. See the Labels, Tags & GLYNT Tags section.
target_training_set | None | Optional. The ID of the training set that is intended to be used by extractions.
inbound_path | None | Optional.  The parent folder of the document (used by automated inbound router).
filesize | None | Autocalculated if file content is provided.


## Create a Document (Legacy)

> To create a Document using the legacy creation method, the first step
> is to generate the verification hash for the file you are going to upload.
> This is achieved by first generating an MD5 binary and then encoding it as a
> Base64 string. The MD5 digest, before being encoded as Base64, is a binary
> entity - do not convert to hexidecimal representation.
>
> Below is an example of a shell command which combines these two steps:

```shell
openssl dgst -md5 -binary /path/to/some/file.pdf | openssl enc -base64
```
> This returns the Base64 encoded string to use for the `content_md5`.
> Something like the following:

```
4DujaMxdUy64mWOWbP6Xew==
```
> Next, POST to the GLYNT API to create the Document instance. Notice that the
> file `content` is not uploaded at this time and the `Content-Type` header
> still refers to the 'application/json' content type of the request, while the
> `content_type` key in the request data itself refers to the Document file
> type.

```shell
curl --request POST \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/documents/" \
     --header "Authorization: Bearer abc.123.def" \
     --header "content-type: application/json" \
     --data '{"label":"sample_doc_name","tags":["sample_tag"],"content_type":"application/pdf","content_md5":"4DujaMxdUy64mWOWbP6Xew=="}'
```
> On success, this command will return a 201 response with a JSON body
> structured like this. Notice that the unique `file_upload_url` key is
> present.

```json
{
  "created_at": "2018-02-16T21:21:30.467694Z",
  "updated_at": "2018-02-16T21:21:30.467694Z",
  "url": "https://api.glynt.ai/v6/data-pools/pRvt5/documents/442a2904/",
  "file_access_url": "",
  "id": "442a2904",
  "label": "sample_doc_name",
  "tags": ["sample_tag"],
  "glynt_tags": [],
  "content_type": "application/pdf",
  "content_md5":"4DujaMxdUy64mWOWbP6Xew==",
  "file_upload_url": "https://files.glynt.ai?signature=abc123def456",
  "target_training_set": null
}
```
> With the `file_upload_url` in hand, you can now upload the file content
> itself.  Remember: no Authorization header should be present on the second
> request because this is a presigned URL. The content-type and content-md5
> headers must now match the values provided in the json data of your initial
> POST request which reflect the file being uploaded. Notice that in this cURL
> example, we use the `--upload-file` flag, which causes cURL to include the
> document content as the body content of the request.

```shell
curl --request PUT \
     --header "content-type: application/pdf" \
     --header "content-md5: 4DujaMxdUy64mWOWbP6Xew==" \
     --upload-file  "/some/local/file" \
     --url "https://files.glynt.ai?signature=abc123def456"
```

> On success this will return a 2xx status code (exact code can vary) and may
> or may not return a response body. The Document is now created and uploaded.

The legacy method of uploading a document is outlined below. This method is not
recommended for new integrations. The documentation is retained here for
users with existing integrations.

To create a Document using the legacy method, you use the same endpoint as the
primary Document upload method. However, in the legacy method, do not include
the Document `content` in the initial call. Thus, using the legacy upload
method, `content_md5` is a mandatory field on the initial POST request.

Following this flow, a successful POST request will return a `file_upload_url`.
This URL is valid for 10 minutes, during which time you must upload the raw
Document content bytes to the `file_upload_url` as the body of a PUT request.
You must include the `Content-Type` and `Content-MD5` headers on this PUT
request, and they must match the corresponding values provided during the POST
step.

Once the 10 minute window expires, or if you uploaded the content directly in
the initial request, the content of the file can never be changed. If no
content was uploaded, then the Document instance is worthless, and can be
deleted. Because of this, it is recommended that you always upload the file
content promptly after the initial request.

<aside class="notice">
Because the <code>file_upload_url</code> is a presigned URL, no
<code>Authorization</code> header is necessary, but the
<code>Content-Type</code> and <code>Content-MD5</code> headers are required.
See the example.
</aside>

<aside class="warning">
The <code>file_upload_url</code> is only displayed once, when you make the
initial POST request - do not lose it until you've uploaded the file content!
</aside>

Our YouTube channel includes [a video tutorial](https://youtu.be/9hYO-fse0aY)
demonstrating how to upload a Document using the legacy approach using Postman.
The GLYNT code examples git repo includes the `legacy_upload_document.py` [example
python script](https://github.com/Glynt-ai/Glynt/tree/master/python), also
demonstrating the legacy upload method.

### HTTP Request
`POST <datapool_url>/documents/`

### Request Body Parameters
Parameter | Default | Description
--------- | ------- | -----------
content_md5 | None | Required in the legacy upload method. Unlike the standard Document upload method, this content_md5 will be stored - but not validated - on POST. Instead, the content_md5 will be used to validate the uploaded content during the subsequent PUT.
content_type | None | Same as standard [Create a Document](#create-a-document).
label | A UUID | Same as standard [Create a Document](#create-a-document).
content | None | May not be passed in the legacy upload method. If it is passed, the system will assume you are attempting the standard Document upload method.
tags | [] | A tags list. See the Labels, Tags & GLYNT Tags section.
target_training_set | None | Same as standard [Create a Document](#create-a-document).


## Retrieve a Document
```shell
curl --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/documents/442a2904/"
```
> If the Document exists, this command will return a 200 response with a JSON
> body structured like this:

```json
{
  "created_at": "2018-02-16T21:21:30.467694Z",
  "updated_at": "2018-02-16T21:21:30.467694Z",
  "url": "https://api.glynt.ai/v6/data-pools/pRvt5/documents/442a2904/",
  "file_access_url": "https://api.glynt.ai/v6/data-pools/pRvt5/documents/442a2904/file/",
  "id": "442a2904",
  "label": "sample_doc_name",
  "tags": ["awesome"],
  "glynt_tags": [],
  "content_type": "application/pdf",
  "content_md5":"4DujaMxdUy64mWOWbP6Xew==",
  "target_training_set": "TSB123"
}
```
Returns detailed information about a given Document.

### HTTP Request
`GET <datapool_url>/documents/<document_id>/`


## Retrieve Document Content
```shell
curl --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/documents/442a2904/file/"
```

> This returns a temporary url which can be used to access the file content
> directly.

```json
{
  "file_temp_url": "https://files.glynt.ai/442a2904?signature=123abc"
}
```

> This url may now be used to directly access the file content for 1 hour.
> Remember, no `Authorization` header is necessary because this is a presigned
> URL. Notice that you may only execute GET requests with this URL.

```shell
curl --url "https://files.glynt.ai/442a2904?signature=123abc"
```

Retrieve a temporary file url which can be used to directly access file content
for up to 1 hour. You can only read the file content with this URL - you cannot
modify or delete it.

### HTTP Request
`GET <datapool_url>/documents/<document_id>/file/`


## Change Document Properties
```shell
curl --request PATCH \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/documents/442a27b0/" \
     --header "Authorization: Bearer abc.123.def" \
     --header "content-type: application/json" \
     --data '{"tags":["sample_tag","advanced"]}'
```
> On success, this command will return a 201 response with a JSON body structured like this:

```json
{
  "created_at": "2018-02-16T21:21:30.467694Z",
  "updated_at": "2018-02-16T23:22:24.103289Z",
  "url": "https://api.glynt.ai/v6/data-pools/pRvt5/documents/442a27b0/",
  "file_access_url": "https://api.glynt.ai/v6/data-pools/pRvt5/documents/c71d9c26/file/",
  "id": "442a27b0",
  "label": "sample_doc_name",
  "tags": ["sample_tag","advanced"],
  "glynt_tags": [],
  "content_type": "application/pdf",
  "content_md5":"4DujaMxdUy64mWOWbP6Xew=="
}
```

> Here's an example of setting the target training set for a document:

```shell
curl --request PATCH \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/documents/442a27b0/" \
     --header "Authorization: Bearer abc.123.def" \
     --header "content-type: application/json" \
     --data '{"target_training_set":"abc123"}'
```

> To remove the target training set association, set it to null:

```shell
curl --request PATCH \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/documents/442a27b0/" \
     --header "Authorization: Bearer abc.123.def" \
     --header "content-type: application/json" \
     --data '{"target_training_set":null}'
```

Change the mutable properties of a Document. The mutable properties are listed
in the Request Body Parameters below.

### HTTP Request
`PATCH <datapool_url>/documents/<document_id>/`

### Request Body Parameters
Parameter | Description
--------- | -----------
label | See Create a Document request body parameters.
tags | See Create a Document request body parameters.
target_training_set | The ID of the training set that is intended to be used when this document is extracted.


## Delete a Document
```shell
curl --request DELETE \
     --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/documents/442a27b0"
```

> On success, this command will return a 204 response with no JSON body.

Removes a Document and its associated data including Extractions and
Ground Truths. It is also removed from associated Extraction Batches and
Training Sets.

<aside class="warning">
Do not delete a Document before retrieving any results in related Extractions
and Extraction Batches, since once the Document is deleted those resources will
no longer be available or complete, respectively. Similarly, do not delete a
Document if it is part of a Training Set which you wish to use to create more
Training Revisions including that Document in the future.
</aside>

It is also removed from Training Revisions, but the Training Revision will
continue to function since the underlying model has already been created,
meaning all that is lost is the ability to view which Documents were part of
the Training Revision for historical purposes.


### HTTP Request
`DELETE <datapool_url>/documents/<document_id>/`
