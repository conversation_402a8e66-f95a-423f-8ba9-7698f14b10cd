# MAT data

## MAT Overview

See [glynt-schemas](https://github.com/PaceWebApp/glynt-schemas/blob/master/doc/MASTER.md) for current MAT data definitions and explanations.

## Retrieve all MAT Rows

```shell
curl --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/organizations/dUk21/mat"
```
> This command will return a 200 response with a JSON structured like this:

```json
{
  "count": 1,
  "next": null,
  "previous": null,
  "results": [
    {
      "id": 1,
      "vendor_name": "Foo",
      "vendor_type": null,
      "normalized_name": null,
      "standardized_name": null,
      "account_number": "*********",
      "normalized_account_number": null,
      "standardized_account_number": null,
      "commodity": "electric",
      "meter_number": "0",
      "normalized_meter_number": null,
      "standardized_meter_number": null,
      "billed_usage_units": null,
      "normalized_billed_usage_units": null,
      "standardized_billed_usage_units": null,
      "meter_usage_units": null,
      "normalized_meter_usage_units": null,
      "standardized_meter_usage_units": null,
      "billed_demand_usage_units": null,
      "normalized_billed_demand_usage_units": null,
      "standardized_billed_demand_usage_units": null,
      "meter_demand_usage_units": null,
      "normalized_meter_demand_usage_units": null,
      "standardized_meter_demand_usage_units": null,
      "service_address": null,
      "standardized_service_address": null,
      "standardized_address_country": null,
      "standardized_address_state": null,
      "standardized_address_city": null,
      "standardized_address_postal_code": null,
      "standardized_address_street": null,
      "emissions_reporting_units": null,
      "emissions_conversion_factor": null,
      "data_source_frequency": null,
      "data_source_status": "included",
      "mat_key": "Foo_*********_electric_0",
      "site_id": "SITE-pRTtV1",
      "c_site_id": null,
      "c_site_name": null,
      "c_vendor_name": null,
      "c_account_number": null,
      "c_commodity": null,
      "c_meter_number": null,
      "c_extra": null,
      "created_at": "2025-05-28T21:52:05.596268Z",
      "updated_at": "2025-05-28T21:52:05.597498Z"
    }
  ]
}
```
Lists all MAT data rows in the Organization.
<aside class="notice">
This endpoint is only accessible by staff users.
</aside>

### HTTP Request
`GET <api_base_url>/organizations/<organization_id>/mat`

### Filtering
Mat rows can be filtered by any combination of `account_number`, `meter_number`, `vendor_name`, and `commodity`.

## Create a MAT Row
```shell
curl --request POST \
     --url "https://api.glynt.ai/v6/organizations/TRfc4/mat" \
     --header "Authorization: Bearer abc.123.def" \
     --header "content-type: application/json" \
     --data '{"meter_number": "*********", "account_number": "*********", "vendor_name": "VendorFOO", "commodity": "electric"}'
```
> On success, this command will return a 201 response with a JSON body structured like this:

```json
{
  "id": 1,
  "vendor_name": "VendorFOO",
  "vendor_type": null,
  "normalized_name": null,
  "standardized_name": null,
  "account_number": "*********",
  "normalized_account_number": null,
  "standardized_account_number": null,
  "commodity": "electric",
  "meter_number": "*********",
  "normalized_meter_number": null,
  "standardized_meter_number": null,
  "billed_usage_units": null,
  "normalized_billed_usage_units": null,
  "standardized_billed_usage_units": null,
  "meter_usage_units": null,
  "normalized_meter_usage_units": null,
  "standardized_meter_usage_units": null,
  "billed_demand_usage_units": null,
  "normalized_billed_demand_usage_units": null,
  "standardized_billed_demand_usage_units": null,
  "meter_demand_usage_units": null,
  "normalized_meter_demand_usage_units": null,
  "standardized_meter_demand_usage_units": null,
  "service_address": null,
  "standardized_service_address": null,
  "standardized_address_country": null,
  "standardized_address_state": null,
  "standardized_address_city": null,
  "standardized_address_postal_code": null,
  "standardized_address_street": null,
  "emissions_reporting_units": null,
  "emissions_conversion_factor": null,
  "data_source_frequency": null,
  "data_source_status": "included",
  "mat_key": "VendorFoo_*********_electric_*********",
  "site_id": "SITE-pRTtV1",
  "c_site_id": null,
  "c_site_name": null,
  "c_vendor_name": null,
  "c_account_number": null,
  "c_commodity": null,
  "c_meter_number": null,
  "c_extra": null,
  "created_at": "2025-05-28T21:52:05.596268Z",
  "updated_at": "2025-05-28T21:52:05.597498Z"
}
```

Creates a MAT entry for this organization.
<aside class="notice">
This endpoint is only accessible by staff users.
</aside>

### HTTP Request
`POST <api_base_url>/organizations/<organization_id>/mat`

### Request Body Parameters
Parameters not listed in the table are optional.  See MAT definitions in glynt-schemas.

Parameter | Default | Description
--------- | ------- | -----------
vendor_name | None | Required. A string label for the associated vendor.  A Vendor object with this name must exist unless `deep_create` is true. (see query params section)
account_number | None | Required.
commodity | None | Required. Must be a valid commodity string.
meter_number | None | Not required but is an optional part of the row unique key.
c_extras | None | Optional. This parameter holds an arbitrary dictionary for extra enrichment values not specified on the model. Should be a single-level dictionary of strings.

### Query Parameters
Parameter | Default | Description
--------- |---------| -----------
deep_create | False   | If set to True this will use the given vendor_name to create an internal global vendor entry using this name exactly how it is provided in the POST request.

## Retrieve a MAT row by ID
```shell
curl --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/organizations/dUk21/mat/12"
```
> If the row id exists, this command will return a 200 response with a JSON
> body structured like this:
> (results truncated for brevity)

```json
{
  "id": 12,
  "vendor_name": "VendorFOO",
  "vendor_type": null,
  "normalized_name": null,
  "standardized_name": null,
  "account_number": "*********",
  "normalized_account_number": null,
  "standardized_account_number": null,
  "commodity": "electric",
  "meter_number": "*********"
}
```
Returns a single mat row by the given id.

### HTTP Request
`GET <api_base_url>/organizations/<organization_id>/mat/<id>`

## Change a MAT row
```shell
curl --request PATCH \
     --url "https://api.glynt.ai/v6/organizations/hpq1F/mat/12" \
     --header "Authorization: Bearer abc.123.def" \
     --header "content-type: application/json" \
     --data '{"c_site_id": "custom_site_id_1"}'
```
> On success, this command will return a 201 response with a JSON body structured like this:
> (Results truncated for brevity.)

```json
{
  "id": 12,
  "vendor_name": "VendorFOO",
  "vendor_type": null,
  "normalized_name": null,
  "standardized_name": null,
  "account_number": "*********",
  "normalized_account_number": null,
  "standardized_account_number": null,
  "commodity": "electric",
  "meter_number": "*********",
  "c_site_id": "custom_site_id_1"
}
```
Change the mutable properties of a MAT row. All properties which can be
set at creation can be mutated. Properties which are constituents of the `mat_key`
cannot be mutated and will throw an error if attempted. These are `vendor_name`, `account_number`, 
`commodity`, `vendor_type`, and `meter_number`. Instead, the user must disable the row by setting
`data_source_status` to `excluded` and creating a new one with the corrected key values.
<aside class="notice">
This endpoint is only accessible by staff users.
</aside>

### HTTP Request
`PATCH <api_base_url>/organizations/<organization_id>/mat/<id>`
