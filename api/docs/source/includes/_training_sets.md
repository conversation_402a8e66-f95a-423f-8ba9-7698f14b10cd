# Training Sets

## Training Sets Overview

A Training Set is a collection of Documents which are brought together to form
a workspace in order to work on that collection of Documents as a group. A
Document can be a part of any number of Training Sets.

Training Sets are created by your GLYNT representative and are not editable
through the API at this time. When you provide training Documents and the list
of fields you would like extracted from them to your GLYNT representative,
the Documents are uploaded for you and Training Sets are created to extract
that data. The GLYNT AI learns to extract the data you want from the Documents
you provide.

In order to maximize accuracy, each Training Set is created to extract data
from a specific class of Documents. For example, if you provide a collection of
Documents for training which are from two different publishers, two Training
Sets will be created - one for publisher A and one for publisher B. Each Training
Set has a unique label and description, which can be used to differentiate
between the Training Sets.

If you wish to delete Training Sets, please contact your GLYNT representative.


## Retrieve all Training Sets

```shell
curl --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/training-sets/"
```
> This command will return a 200 response with a JSON body structured like this:

```json
{
  "count": 2,
  "next": null,
  "previous": null,
  "results": [
    {
      "created_at": "2019-01-16T20:24:21.467694Z",
      "updated_at": "2019-01-16T20:24:21.467694Z",
      "url": "https://api.glynt.ai/v6/data-pools/pRvt5/training-sets/3f708802/",
      "id": "3f708802",
      "label": "Electricty Company Inc.",
      "description": "Training Set for extracting data from Electrity Company Inc. invoices.",
      "documents": [
        "https://api.glynt.ai/v6/data-pools/pRvt5/documents/4de1ca72/",
        "https://api.glynt.ai/v6/data-pools/pRvt5/documents/54abcb1e/",
        "https://api.glynt.ai/v6/data-pools/pRvt5/documents/5a3fb342/",
      ],
      "glynt_tags": []
    },
    {
      "created_at": "2019-01-16T21:21:30.467694Z",
      "updated_at": "2019-01-16T21:24:31.645855Z",
      "url": "https://api.glynt.ai/v6/data-pools/pRvt5/documents/5f8d2c80/",
      "id": "5f8d2c80",
      "label": "Gas Company LLC",
      "description": "A Training Set which extracts data from Gas Company, LLC natural gas bills.",
      "documents": [
        "https://api.glynt.ai/v6/data-pools/pRvt5/documents/6b73b082/",
        "https://api.glynt.ai/v6/data-pools/pRvt5/documents/709523f2/",
        "https://api.glynt.ai/v6/data-pools/pRvt5/documents/778ba064/",
      ],
      "glynt_tags": []
    }
  ]
}
```

Lists all Training Sets in the DataPool.

### Retrieve all training sets with the quick-view parameter

```shell
curl --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/training-sets/?quick-view=true"
```
> This command will return a 200 response with a JSON body structured like this:

```json
{
  "count": 2,
  "next": null,
  "previous": null,
  "results": [
    {
      "created_at": "2019-01-16T20:24:21.467694Z",
      "updated_at": "2019-01-16T20:24:21.467694Z",
      "url": "https://api.glynt.ai/v6/data-pools/pRvt5/training-sets/3f708802/",
      "id": "3f708802",
      "label": "Electricty Company Inc.",
      "description": "Training Set for extracting data from Electrity Company Inc. invoices.",
    },
    {
      "created_at": "2019-01-16T21:21:30.467694Z",
      "updated_at": "2019-01-16T21:24:31.645855Z",
      "url": "https://api.glynt.ai/v6/data-pools/pRvt5/documents/5f8d2c80/",
      "id": "5f8d2c80",
      "label": "Gas Company LLC",
      "description": "A Training Set which extracts data from Gas Company, LLC natural gas bills.",
    }
  ]
}
```

Lists all Training Sets in the DataPool, with a reduced set of fields.


### HTTP Request
`GET <datapool_url>/training-sets/`

### Query Parameters
Parameter  | Default | Description
---------- | ------- | -----------
quick-view | None    | Optional. If set to `true`, the results do not include all fields, such as `documents` and `tags`. When using this parameter, you may also specify a much higher pagination limit (up to 2000 at the time of this writing) with the `limit` query parameter.
---------- | ------- | -----------

### Filtering
In addition to the filters specified in <a href="#filtering">Filtering</a> section, this endpoint
supports a `trained=true` filter which, when applied, restricts results to
Training Sets that have been successfully trained. Passing any value other than
`true` (case sensitive) filters out all Training Sets (returns empty results).


## Retrieve a Training Set
```shell
curl --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/training-sets/3f708802/"
```
> If the Training Set exists, this command will return a 200 response with a
> JSON body structured like this:

```json
{
  "created_at": "2019-01-16T20:24:21.467694Z",
  "updated_at": "2019-01-16T20:24:21.467694Z",
  "url": "https://api.glynt.ai/v6/data-pools/pRvt5/training-sets/3f708802/",
  "id": "3f708802",
  "label": "Electricty Company Inc.",
  "description": "Training Set for extracting data from Electrity Company Inc. invoices.",
  "documents": [
    "https://api.glynt.ai/v6/data-pools/pRvt5/documents/4de1ca72/",
    "https://api.glynt.ai/v6/data-pools/pRvt5/documents/54abcb1e/",
    "https://api.glynt.ai/v6/data-pools/pRvt5/documents/5a3fb342/",
  ],
  "glynt_tags": []
}
```

Returns detailed information about a given Training Set.

### HTTP Request
`GET <datapool_url>/training-sets/<training_set_id>/`


