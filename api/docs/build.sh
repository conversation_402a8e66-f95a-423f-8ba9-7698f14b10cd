#!/bin/bash -eu

as_template=false

while getopts ":t" opt; do
  case ${opt} in
    t ) as_template=true
      ;;
    \? ) echo "Usage: build.sh [-t]"
      ;;
  esac
done

root=`pwd`/`dirname $0`
rm -rf $root/build $root/advanced_build
mkdir $root/build $root/advanced_build

if [ -d "`pwd`/../../slate" ]; then
    # Build is executing locally
    docker build -t apidoc `pwd`/../../slate
else
    # Build is executing via ansible, so path to slate is different
    docker build -t apidoc /usr/local/lib/slate
fi

docker run -v $root/source:/slate/source -v $root/build:/slate/build apidoc \
  sh -c 'cd /slate && cp -nr source_orig/* source && exec bundle exec middleman build'
docker run -v $root/advanced_source:/slate/source -v $root/advanced_build:/slate/build apidoc \
  sh -c 'cd /slate && cp -nr source_orig/* source && exec bundle exec middleman build'
user=`whoami`
sudo chown $user:$user $root/build -R
sudo chown $user:$user $root/advanced_build -R

if [ "$as_template" = true ]; then
    # inject load static tag
    perl -p -i -e 's/<!doctype html>/{% load static %}\n/' \
        $root/build/index.html \
        $root/advanced_build/index.html
    # mutate src and href tags which need to be static aware
    perl -p -i -e 's/(href|src)="((?=stylesheets|javascripts|images|fonts)[^"]+)"/\1="{% static "\2" %}"/g' \
        $root/build/index.html \
        $root/advanced_build/index.html
fi
