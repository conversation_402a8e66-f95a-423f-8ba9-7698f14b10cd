# Data Pools

> An example of a Data Pool in advanced mode. In all other examples, the links
> to the related resource list views will be omitted for brevity.

```json
{
  "created_at": "2019-01-14T03:44:30.218991Z",
  "updated_at": "2019-01-14T03:44:30.218991Z",
  "url": "https://api.glynt.ai/v6/data-pools/hpq1F/?advanced=true",
  "id": "hpq1F",
  "label": "Production",
  "organization": "https://api.glynt.ai/v6/organizations/6R6tg/?advanced=true",
  "default_tokenizer_version": {
      "name": "1.0.0",
      "inherited": true
  },
  "corrections": "https://api.glynt.ai/v6/data-pools/hpq1F/corrections/?advanced=true",
  "documents": "https://api.glynt.ai/v6/data-pools/hpq1F/documents/?advanced=true",
  "extractions": "https://api.glynt.ai/v6/data-pools/hpq1F/extractions/?advanced=true",
  "extraction_batches": "https://api.glynt.ai/v6/data-pools/hpq1F/extraction-batches/?advanced=true",
  "fields": "https://api.glynt.ai/v6/data-pools/hpq1F/fields/?advanced=true",
  "ground_truths": "https://api.glynt.ai/v6/data-pools/hpq1F/ground-truths/?advanced=true",
  "tokenizeds": "https://api.glynt.ai/v6/data-pools/hpq1F/tokenizeds/?advanced=true",
  "training_sets": "https://api.glynt.ai/v6/data-pools/hpq1F/training-sets/?advanced=true",
  "training_revisions": "https://api.glynt.ai/v6/data-pools/hpq1F/training-revisions/?advanced=true",
  "glynt_tags": [],
  "org_label": "this data pool's parent organization's label",
  "public": false
}
```

## Data Pools Overview

In advanced mode, Data Pools gain Create, Change, and Delete (staff-only) endpoints.

In advanced mode, Data Pools gain a property which links to the parent
Organization.

In advanced mode, Data Pools gain a link to the list view of every resource
type which is dependent on Data Pools. Other than the sample to the right,
these links are omitted from all other samples in these docs for brevity.


## Create a Data Pool
```shell
curl --request POST \
     --url "https://api.glynt.ai/v6/data-pools/?advanced=true" \
     --header "Authorization: Bearer abc.123.def" \
     --header "content-type: application/json" \
     --data '{"label":"Production", "organization": "6R6tg"}'
```
> On success, this command will return a 201 response with a JSON body structured like this:

```json
{
  "created_at": "2019-01-14T03:44:30.218991Z",
  "updated_at": "2019-01-14T03:44:30.218991Z",
  "url": "https://api.glynt.ai/v6/data-pools/hpq1F/?advanced=true",
  "id": "hpq1F",
  "label": "Production",
  "organization": "https://api.glynt.ai/v6/organizations/6R6tg/?advanced=true",
  "default_tokenizer_version": {
      "name": "1.0.0",
      "inherited": true
  },
  "glynt_tags": []
}
```

Creates a Data Pool.

<aside class="success">
Creating a Data Pool does not automatically associate that Data Pool with all
the Users and/or Integrations in the Organization. Once a Data Pool is created,
you must grant access to the Data Pool to each User and Integration which
should have access.
</aside>

### HTTP Request
`POST <api_base_url>/data-pools/?advanced=true`

### Request Body Parameters
Parameter | Default | Description
--------- | ------- | -----------
label | None | Required. A string label for the Data Pool. See the Labels, Tags & GLYNT Tags section. Must be unique within an Organization.
organization | None | Required. String ID of Organization this Data Pool should be associated with.
glynt_tags | [] | A tags list. See the Labels, Tags & GLYNT Tags section.
default_tokenizer_version | None | String. Tokenizer version to use as the default for all Training Sets in this Data Pool. Can be overridden by providing a valid tokenizer version directly when creating a Training Set. This setting will also take precedence over a default set at the Organization level. See below for details about special returned value.

### Default Tokenizer Version Return Value
The `default_tokenizer_version` field returns a dictionary with the following keys:

Parameter | Default | Description
--------- | ------- | -----------
name | system default (see [tokenizer versions](#tokenizer-versions)) | If `default_tokenizer_version` is set successfully by a user, that string value is displayed. If no value for `default_tokenizer_version` was provided, or it was set to `None`, the parent Organization's default version is displayed.
inherited | True | A boolean value expressing whether the value of `name` was derived from a setting provided by a user or inherited from the parent Organization.


## Change a Data Pool

```shell
curl --request PATCH \
     --url "https://api.glynt.ai/v6/data-pools/hpq1F/?advanced=true" \
     --header "Authorization: Bearer abc.123.def" \
     --header "content-type: application/json" \
     --data '{"label":"Special Production","default_tokenizer_version":"0.0.1"}'
```

> On success, this command will return a 201 response with a JSON body structured like this:

```json
{
  "created_at": "2019-01-14T03:44:30.218991Z",
  "updated_at": "2018-02-16T23:22:24.103289Z",
  "url": "https://api.glynt.ai/v6/data-pools/hpq1F/?advanced=true",
  "id": "hpq1F",
  "label": "Special Production",
  "organization": "https://api.glynt.ai/v6/organizations/6R6tg/?advanced=true",
  "default_tokenizer_version": {
      "name": "0.0.1",
      "inherited": false
  },
  "glynt_tags": []
}
```

Change the mutable properties of a Data Pool.

### HTTP Request

`PATCH <api_base_url>/data-pools/<data_pool_id>/?advanced=true`

### Request Body Parameters
Parameter | Description
--------- | -----------
label | See Create a Data Pool request body parameters.
glynt_tags | A tags list. See the Labels, Tags & GLYNT Tags section.
default_tokenizer_version | None | See Create a Data Pool request body parameters.


## Delete a Data Pool
```shell
curl --request DELETE \
     --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/data-pools/hpq1F/?advanced=true"
```

> On success, this command will return a 204 response with no JSON body.

Removes an Data Pool and all associated data.
<aside class="notice">
This endpoint is only accessible by staff users.
</aside>

<aside class="warning">
Deleting a Data Pool deletes all data associated with that Data Pool.  This
cannot be undone.
</aside>

### HTTP Request
`DELETE <api_base_url>/data-pools/<data_pool_id>/?advanced=true`
