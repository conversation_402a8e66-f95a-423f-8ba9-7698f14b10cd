# Ground Truths

## Ground Truths Overview

Ground Truths define the true value for a Field on a Document in the context of
a Training Set. If a Field does not appear on a Document, a Ground Truth should
still be created for the field, but the `content` should be set to an empty string.

<aside class="warning">
Failure to include a Ground Truth for a Field (even an "empty" Ground Truth) will
cause Training Revisions for that Training Set to be less accurate. Creating a
Ground Truth that is incorrect, either because the value is not what appears on
the Document or because a Ground Truth exists for a Document while the
corresponding Field is in fact not on that Document will cause Training
Revisions for that Training Set to be less accurate.
</aside>

In addition to the simple string content of the Ground Truth, information about
the Tokenized tokens which constitute it can be passed via `token_data`.
Including token data will generally improve the accuracy of Training Revisions
for that Training Set. Including inaccurate information, however, will degrade
that accuracy.

> The following is a sample `token_data` property:

```json
{
  "token_data": {
    "tokens": [
      {
        "content": "$100",
        "page": 1,
        "bbox": [
          {"x": 1410, "y": 55},
          {"x": 1644, "y": 55},
          {"x": 1644, "y": 92},
          {"x": 1410, "y": 92}
        ],
        "index": 57
      }
    ]
  }
}
```

`token_data` is a JSON object. The `tokens` property is an array of objects,
each of which correspond to one of the constituent tokens of the Ground Truth.
Order does not matter. When submitting `token_data`, you must provide `tokens`,
and each token must contain `content`, `page`, and `bbox`.

`content` may be an empty string, if the token does not appear in the Tokenized.
This will trigger special behaviors by the system. The training process will be
made aware of the lack of correctly OCR'd token. This will improve the accuracy
of created Training Revisions. `index` will be null for such tokens.

<aside class="success">
If the Ground Truth is composed of multiple tokens, all tokens must be present
in <code>token_data</code> if it is passed at all - you should never provide
only <b>some</b> of the constituent tokens.

Note that the bounding box must <b>exactly</b> match the bounding box of the token as
it appears in the Tokenized.
</aside>

You can not set `index`. It is a calculated value. It changes
when the `tokenizer_version` of the parent Training Set changes, or when the
token data itself changes. `index` is the 0-indexed index into the array of
tokens for the given page. 

The most common issues with `token_data` are as follows, which will usually
cause an `advisory` to be issued on the Ground Truth object.

* The content provided does not match the content of the token found at the
  provided page and bounding box. For example, `"$100"` is instead `"Sl00"`.
  This is usually a result of an OCR issue.
* The token content is found in roughly the provided bounding box, but the
  bounding box is different enough that the system can not be sure it is the
  exact token you are trying to indicate. A small amount of difference between
  bounding boxes is allowed to account for minor bounding box "drift." This
  issue arises when the bounding box is outside of that acceptable range.
* When tokenizer versions are changed, tokens may be merged or split due to
  different OCR. This will necessitate updating the token data to reflect the
  new token count and positions.
* The indicated token cannot be found anywhere near the indicated location, or
  with the indicated content.


## Retrieve all Ground Truths

```shell
curl --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/ground-truths/?advanced=true"
```
> This command will return a 200 response with a JSON structured like this:

```json
{
  "count": 2,
  "next": null,
  "previous": null,
  "results": [
    {
      "created_at": "2019-01-16T21:21:30.467694Z",
      "updated_at": "2019-01-16T21:21:30.467694Z",
      "url": "https://api.glynt.ai/v6/data-pools/pRvt5/ground-truths/34f04172/?advanced=true",
      "id": "34f04172",
      "document": "https://api.glynt.ai/v6/data-pools/pRvt5/documents/15bedd90/?advanced=true",
      "field": "https://api.glynt.ai/v6/data-pools/pRvt5/fields/0f4f02fa/?advanced=true",
      "content": "$100.00",
      "advisories": [],
      "token_data": {
        "tokens": [
          {
            "content": "$100",
            "page": 1,
            "bbox": [
              {"x": 1410, "y": 55},
              {"x": 1644, "y": 55},
              {"x": 1644, "y": 92},
              {"x": 1410, "y": 92}
            ],
            "index": 57
          }
        ]
      }
    },
    {
      "created_at": "2019-01-16T21:22:30.001010Z",
      "updated_at": "2019-01-16T21:22:30.001010Z",
      "url": "https://api.glynt.ai/v6/data-pools/pRvt5/ground-truths/1kvcs0s/?advanced=true",
      "id": "1kvcs0s",
      "document": "https://api.glynt.ai/v6/data-pools/pRvt5/documents/fdsa99sn/?advanced=true",
      "field": "https://api.glynt.ai/v6/data-pools/pRvt5/fields/0f4f02fa/?advanced=true",
      "content": "500",
      "advisories": [],
      "token_data": {}
    }
  ]
}
```

Lists all Ground Truths. This endpoint can be invoked with filters for
Document, Field, and Training Set. Since Documents can be associated with
multiple Training Sets, just filtering by Document alone can return Ground
Truths for Fields in mutliple sets. To retrieve Ground Truths for a Document
for Fields in a single Training Set you can use this query string:
`?document=<doc_id>&training_set=<training_set_id>`.

### HTTP Request
`GET <datapool_url>/ground-truths/?advanced=true`


## Create a Ground Truth

```shell
curl --request POST \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/ground-truths/?advanced=true" \
     --header "Authorization: Bearer abc.123.def" \
     --header "content-type: application/json" \
     --data '{"field":"0f4f02fa", "document":"15bedd90","content":"$100.00"}'
```
> On success, this command will return a 201 response with a JSON body structured like this:

```json
{
  "created_at": "2019-01-16T21:21:30.467694Z",
  "updated_at": "2019-01-16T21:21:30.467694Z",
  "url": "https://api.glynt.ai/v6/data-pools/pRvt5/ground-truths/34f04172/?advanced=true",
  "id": "34f04172",
  "document": "https://api.glynt.ai/v6/data-pools/pRvt5/documents/15bedd90/?advanced=true",
  "field": "https://api.glynt.ai/v6/data-pools/pRvt5/fields/0f4f02fa/?advanced=true",
  "content": "$100.00",
  "token_data": {},
  "advisories": []
}
```

Creates a new Ground Truth instance.

### HTTP Request
`POST <datapool_url>/ground-truths/?advanced=true`

### Request Body Parameters
Parameter | Default | Description
--------- | ------- | -----------
field | None | Required. ID of Field to which this Ground Truth applies.
document | None | Required. ID of Document to which this Ground Truth applies.
content | None | Required. String of Field value. Max 255 characters.
token_data | None | Optional. JSON object of information about the tokens constituting the Ground Truth. See the introduction to this resource for details about format.



## Retrieve a Ground Truth

```shell
curl --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/ground-truths/34f04172/?advanced=true"
```
> If the Ground Truth exists, this command will return a 200 response with a
> JSON body structured like this:

```json
{
  "created_at": "2019-01-16T21:21:30.467694Z",
  "updated_at": "2019-01-16T21:21:30.467694Z",
  "url": "https://api.glynt.ai/v6/data-pools/pRvt5/ground-truths/34f04172/?advanced=true",
  "id": "34f04172",
  "document": "https://api.glynt.ai/v6/data-pools/pRvt5/documents/15bedd90/?advanced=true",
  "field": "https://api.glynt.ai/v6/data-pools/pRvt5/fields/0f4f02fa/?advanced=true",
  "content": "$100.00",
  "token_data": {
    "tokens": [
      {
        "content": "$100.00",
        "page": 1,
        "bbox": [
          {"x": 1410, "y": 55},
          {"x": 1644, "y": 55},
          {"x": 1644, "y": 92},
          {"x": 1410, "y": 92}
        ],
        "index": 57
      }
    ]
  },
  "advisories": []
}
```

Returns detailed information about a Ground Truth.

### HTTP Request
`GET <datapool_url>/ground-truths/<ground_truth_id>/?advanced=true`


## Change a Ground Truth

> Modifying the example from the Create a Ground Truth section:

```shell
curl --request PATCH \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/ground-truths/34f04172/?advanced=true" \
     --header "Authorization: Bearer abc.123.def" \
     --header "content-type: application/json" \
     --data '{"content":"500.00"}'
```
> On success, this command will return a 201 response with a JSON body structured like this:

```json
{
  "created_at": "2019-01-16T21:21:30.467694Z",
  "updated_at": "2019-01-16T23:00:00.000000Z",
  "url": "https://api.glynt.ai/v6/data-pools/pRvt5/ground-truths/34f04172/?advanced=true",
  "id": "34f04172",
  "document": "https://api.glynt.ai/v6/data-pools/pRvt5/documents/15bedd90/?advanced=true",
  "field": "https://api.glynt.ai/v6/data-pools/pRvt5/fields/0f4f02fa/?advanced=true",
  "content": "500.00",
  "token_data": {},
  "advisories": []
}
```

Modify an existing Ground Truth instance.

### HTTP Request
`PATCH <datapool_url>/ground-truths/<ground_truth_id>/?advanced=true`

### Request Body Parameters
All parameters which can be set at creation may be mutated. See Create a Ground
Truth section for details.


## Delete a Ground Truth
```shell
curl --request DELETE \
     --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/ground-truths/34f04172/?advanced=true"
```
> On success, this command will return a 204 response with no JSON body.

Removes a Ground Truth and its associated data.

### HTTP Request
`DELETE <datapool_url>/ground-truths/<ground_truth_id>/?advanced=true`


