# Corrections

## Corrections Overview

Correction resources are associated with Extractions. They allow you to
overwrite the `raw_results` of an Extraction for a given Field. Corrections
should only be used to correct the results as they appear on the Document. If
you wish to adjust the `raw_results` away from what was originally present on
the Document, then you should use [Alterations](#alterations-overview). By
using Corrections for as-present-on-document fixes and Alterations for all
other results mutations, it enables the [Copy Extraction as Training
Data](#copy-extractions-as-training-data) endpoint to include Corrections but
omit Alterations, saving time and avoiding incorporating misleading data into
the ML models.

For example, if the `Due Date` on a Document is `Jan 1`, but the raw result for
the field is `Jun 7`, a Correction could be used to set the content to the
correct `Jan 1`. An Alteration could then also be issue against this field, to
change the format of the date to `January 1st`, if that is the desired format.

This also allows a Correction to provide a result for a Field that
the Extraction failed to find entirely, or a value for a field which is not
even present in the Extraction's fields, which is useful when you notice during
review of results that a previously unseen Field is present on the Document.

A `content` of an empty string is a special value which means that the Field
does not appear on the Document. See the basic mode Extraction docs for more
information. Note that a `content` value of `null` is not supported because
once a human is correcting an Extraction, there should be no ambiguity about
whether a value is or is not on the Document.

Corrections are append-only. They may not be deleted once created. For a given
Field of a given Extraction, the Correction with the latest `created_at` time
is the active Correction. Note that even if you wish to revert to the raw
result value, the method to achieve this is to create a new Correction which
sets the value to the original result content.

Corrections store the `user` or `integration` which created it. These are
stored so that a forensic trail is available to track who created each object.
Should the User or Integration which created the Correction be deleted, both
properties will show as `null` for that Correction.

A correction can be made for a field that does not yet exist in the extracted
data.  This is called an "additional field" and will be displayed in the
extraction data under the `additional_fields` key. Additional fields can be
configured like fields in training sets by using the `data_type`, `data_type_config`,
and `glynt_tags` fields.  These values can only be set for additional fields.
A validation error will be returned if you try to set these values for fields
that were present in the training revision at extraction time.

## Retrieve all Corrections

```shell
curl --header "Authorization: Bearer abc.123.def"
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/corrections/?advanced=true" \
```
> This command will return a 200 response with a JSON structured like this:

```json
{
  "count": 2,
  "next": null,
  "previous": null,
  "results": [
    {
      "created_at": "2019-01-16T21:21:30.467694Z",
      "updated_at": "2019-01-16T21:21:30.467694Z",
      "url": "https://api.glynt.ai/v6/data-pools/pRvt5/corrections/db42e50e/?advanced=true",
      "id": "db42e50e",
      "extraction": "https://api.glynt.ai/v6/data-pools/pRvt5/extraction/d0905768/?advanced=true",
      "field": "Total Amount Due",
      "correction": {
        "content": "$200.00",
        "tokens": []
      }
    },
    {
      "created_at": "2019-01-16T21:21:31.228191Z",
      "updated_at": "2019-01-16T21:21:31.228191Z",
      "url": "https://api.glynt.ai/v6/data-pools/pRvt5/corrections/c9qwj2n/?advanced=true",
      "id": "c9qwj2n",
      "extraction": "https://api.glynt.ai/v6/data-pools/pRvt5/extraction/d0905768/?advanced=true",
      "field": "Address",
      "correction": {
        "content": "123 Main Street",
        "tokens": []
      }
    }
  ]
}
```

Lists all Corrections in the Data Pool. Usually this endpoint is called with a
filter to show only Corrections associated with a particular Extraction.

### HTTP Request
`GET <datapool_url>/corrections/?advanced=true`


## Create Corrections

```shell
curl --request POST \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/corrections/?advanced=true" \
     --header "Authorization: Bearer abc.123.def" \
     --header "content-type: application/json" \
     --data '{"extraction":"d0905768","field": "Total Amount Due","correction":{"content": "$200.00"}}'
```
> On success, this command will return a 201 response with a JSON body
> structured like this:

```json
{
  "created_at": "2019-01-16T21:21:30.467694Z",
  "updated_at": "2019-01-16T21:21:30.467694Z",
  "url": "https://api.glynt.ai/v6/data-pools/pRvt5/corrections/db42e50e/?advanced=true",
  "id": "db42e50e",
  "extraction": "https://api.glynt.ai/v6/data-pools/pRvt5/extraction/d0905768/?advanced=true",
  "field": "Total Amount Due",
  "correction": {
    "content": "$200.00",
    "tokens": []
  },
  "user": "https://api.glynt.ai/v6/users/1n21kd/?advanced=true",
  "integration": null
}
```
> Multiple Corrections can be created at once by passing an array:

```shell
curl --request POST \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/corrections/?advanced=true" \
     --header "Authorization: Bearer abc.123.def" \
     --header "content-type: application/json" \
     --data '[\
       {"extraction":"d0905768","field": "Total Amount Due","correction":{"content": "$200.00"}}, \
       {"extraction":"d0905768","field": "Due Date","correction":{"content": "1/1/2020"}} \
     ]'
```


Creates one or several Correction instances.

If creating multiple Corrections with one call, only one Correction per
Extraction/Field combination may be set in a single call. Attempting to set
multiple Corrections for the same Extraction/Field combination will result in
an error. Any errors raised by the endpoint with stop all Corrections from
being created. Errors will be returned per Correction.

<aside class="success">
Remember that Corrections should only be used to correct the results as they
appear on the Document. For all other results mutations, use Alterations.
Whenever possible, include accurate `token_data` when submitting Corrections.
</aside>

### HTTP Request
`POST <datapool_url>/corrections/?advanced=true`

### Request Body Parameters
Parameter | Default | Description
--------- |------| -----------
extraction | None | Required. ID of Extraction to which this Correction applies.
field | None | Required. String label of field for which this Correction applies. You must be very careful that this exactly matches the field as present in the Extraction's `fields` property. If it does not, the correction will not overwrite the existing field. Field names are case sensitive.
correction | None | Required. JSON object of correction content. Notice that this will completely overwrite the given field's `result` JSON, so an effort should be made to match the result format as closely as possible. See Extractions section of basic mode docs for more details.
data_type | string | Used to set the data type of the field being corrected. Only allowed if the correction is for a field that was added post extraction.
data_type_config | None | Used to set the data type config of the field being corrected. Only allowed if the correction is for a field that was added post extraction. Check [Data types](#data-types) for the valid options.
glynt_tags | None | Used to set glynt tags used by the transformer. Only allowed if the correction is for a field that was added post extraction.

## Retrieve a Correction

```shell
curl --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/corrections/db42e50e/?advanced=true"
```
> If the Correction exists, this command will return a 200 response with a
> JSON body structured like this:

```json
{
  "created_at": "2019-01-16T21:21:30.467694Z",
  "updated_at": "2019-01-16T21:21:30.467694Z",
  "url": "https://api.glynt.ai/v6/data-pools/pRvt5/corrections/db42e50e/?advanced=true",
  "id": "db42e50e",
  "extraction": "https://api.glynt.ai/v6/data-pools/pRvt5/extraction/d0905768/?advanced=true",
  "field": "Total Amount Due",
  "correction": {
    "content": "$200.00",
    "tokens": []
  },
  "user": "https://api.glynt.ai/v6/users/1n21kd/?advanced=true",
  "integration": null
}
```

Returns detailed information about a Correction.

### HTTP Request
`GET <datapool_url>/corrections/<correction_id>/?advanced=true`

