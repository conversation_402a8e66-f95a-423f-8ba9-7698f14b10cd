# Data Types

```shell
curl --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/data-types/?advanced=true"
```

> The request will return a 200 response with a JSON structured like this:

```json
[
    { "name": "amount", "options": {"decimalSymbol":[".",","]}, "defaultOptions": {"decimalSymbol":"."} },
    { "name": "date", "options": {"order": [ "DMY", "MDY", "YMD" ]}, "defaultOptions": {"order":"MDY"} },
    { "name": "number", "options": {"decimalSymbol":[".",","]}, "defaultOptions": {"decimalSymbol":"."} }
]
```

This endpoint displays a list of supported and currently active data types which are used to engage the auto-normalization service. `options` are extra arguments that can be specified during training to change the normalization behavior. Fields objects that are tagged with any of these strings will have Normalizations created for them automatically when extractions are completed or when corrections are made against them.

### HTTP Request
`GET <api_base_url>/data-types/?advanced=true`

