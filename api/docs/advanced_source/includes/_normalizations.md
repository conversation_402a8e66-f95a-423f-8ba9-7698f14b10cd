# Normalizations

## Normalizations Overview
Normalizations are automatic data manipulations that add a standardized
formatted view of a field's extracted result.

Normalizations objects are generated automatically in two instances:

* at the end of an extraction immediately after the raw results are generated
* after any Correction is issued against such raw results

Because Normalizations are generated automatically by the system, this
resource is read-only and no user is stored. Normalizations cannot be deleted.

Not all fields are normalized. Currently, the normalization process is
triggered by the presence of specific data type tags placed on fields before
they are trained. This mechanism is a prototype and will change in subsequent
releases. Contact your Glynt representative to enable normalization on your
data sets.


## Retrieve all Normalizations

```shell
curl --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/normalizations/?advanced=true"
```
> This command will return a 200 response with a JSON structured like this:

```json
{
  "count": 2,
  "next": null,
  "previous": null,
  "results": [
    {
      "id": "db42e5",
      "created_at": "2019-01-16T21:21:30.467694Z",
      "updated_at": "2019-01-16T21:21:30.467694Z",
      "url": "https://api.glynt.ai/v6/data-pools/pRvt5/normalizations/db42e50e/?advanced=true",
      "extraction": "https://api.glynt.ai/v6/data-pools/pRvt5/extractions/d0905768/?advanced=true",
      "field": "Total Amount Due",
      "content": "123.99",
      "original_value": "$123.99",
      "data_type": "amount"
    },
    {
      "created_at": "2019-01-16T21:21:31.228191Z",
      "updated_at": "2019-01-16T21:21:31.228191Z",
      "url": "https://api.glynt.ai/v6/data-pools/pRvt5/normalizations/c9qwj2n/?advanced=true",
      "id": "c9qwj2n",
      "extraction": "https://api.glynt.ai/v6/data-pools/pRvt5/extractions/d0905768/?advanced=true",
      "field": "Start Date",
      "content": "2022-03-15",
      "original_value": "3/15/2022",
      "data_type": "date"
    }
  ]
}
```

Lists all Normalizations in the Data Pool.

Available query parameter filters:

* **extraction**: the id of an extraction
* **field**: the string label of a field

### HTTP Request
`GET <datapool_url>/normalizations/?advanced=true`


## Retrieve a Normalization

```shell
curl --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/normalizations/ADv6gY/?advanced=true"
```
> This command will return a 200 response with a JSON structured like this:

```json
{
  "id": "ADv6gY",
  "created_at": "2019-01-16T21:21:30.467694Z",
  "updated_at": "2019-01-16T21:21:30.467694Z",
  "url": "https://api.glynt.ai/v6/data-pools/pRvt5/normalizations/ADv6gY/?advanced=true",
  "extraction": "https://api.glynt.ai/v6/data-pools/pRvt5/extractions/d0905768/?advanced=true",
  "field": "Total Amount Due",
  "content": "123.99",
  "original_value": "$123.99",
  "data_type": "amount"
}
```

### HTTP Request
`GET <datapool_url>/normalizations/<normalization_id>/?advanced=true`

