# Users

## Users Overview

<aside class="notice">
All User endpoints are only accessible to staff users.
</aside>

Users are individuals who may interact with the system. For automated
connections to the system, Integrations should be used instead.

Staff users may create and edit non-staff users. At this time, there
is no way to set a user's password or request a password reset email be sent
via the API. Passwords are managed entirely by Auth0. When a user is created
(via the API or otherwise), a corresponding user is created in Auth0, and an
email is sent to the user in order to set a password. If necessary, the user
may request a password reset email from the browsable API's login page.

The API allows a user's Organizations and Data Pools to be edited.

Once created, a user's email address may not be changed. Their username is the
same as their email address.

Users may not be deleted via the API at this time. Instead, they may be set to
`is_active: false`. This disables all access to that user, but allows us to
maintain a forensic trail of actions taken by a given User. If it becomes
necessary to delete a User, contact the API development team.


## Retrieve all Users

```shell
curl --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/users/?advanced=true"
```
> This command will return a 200 response with a JSON structured like this:

```json
{
  "count": 2,
  "next": null,
  "previous": null,
  "results": [
    {
      "id": "Dsk1kh",
      "created_at": "2018-02-16T21:21:30.467694Z",
      "updated_at": "2018-02-16T21:21:30.467694Z",
      "url": "https://api.glynt.ai/v6/users/Dsk1kh/?advanced=true",
      "email": "<EMAIL>",
      "is_staff": true
    },
    {
      "id": "7ga1hD",
      "created_at": "2018-02-16T21:24:20.192011Z",
      "updated_at": "2018-02-16T21:24:20.192011Z",
      "url": "https://api.glynt.ai/v6/users/7ga1hD/?advanced=true",
      "email": "<EMAIL>",
      "is_staff": false
    }
  ]
}
```

Lists all Users. Note that not all fields are present in the response. Use the
[Retrieve a User](#retrieve-a-user) endpoint to get detailed user information.

### HTTP Request
`GET <api_base_url>/users/?advanced=true`

### Filtering
In addition to the filters specified in <a href="#filtering">Filtering</a>
section, this endpoint supports an `email` filter which allows for filtering
for an exact email address.


## Create a User

```shell
curl --request POST \
     --url "https://api.glynt.ai/v6/users/?advanced=true" \
     --header "Authorization: Bearer abc.123.def" \
     --header "content-type: application/json" \
     --data '{"email":"<EMAIL>"}'
```
> On success, this command will return a 201 response with a JSON body structured like this:

```json
{
  "id": "Dsk1kh",
  "created_at": "2018-02-16T21:21:30.467694Z",
  "updated_at": "2018-02-16T21:21:30.467694Z",
  "url": "https://api.glynt.ai/v6/users/Dsk1kh/?advanced=true",
  "email": "<EMAIL>",
  "is_staff": false,
  "is_active": true,
  "data_pools": [],
  "organizations": []
}
```

Creates a new User instance.

### HTTP Request
`POST <api_base_url>/users/?advanced=true`

### Request Body Parameters
Parameter | Default | Description
--------- | ------- | -----------
email | None | Required. User's email address.
organizations | None | List of IDs of Organizations which this user has access to.
data_pools | None | List of IDs of Data Pools which this user has access to. A 400 error will be returned if you attempt to create a User with Data Pools which are not a part of one of the User's Organizations.
is_active | true | Whether the User is allowed to interact with the GLYNT system. If false, the User will be unable to retrieve tokens or otherwise interact with the system.


## Retrieve a User

```shell
curl --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/users/Dsk1kh/?advanced=true"
```
> If the User exists, this command will return a 200 response with a JSON body
> structured like this:

```json
{
  "id": "Dsk1kh",
  "created_at": "2018-02-16T21:21:30.467694Z",
  "updated_at": "2018-02-16T21:21:30.467694Z",
  "url": "https://api.glynt.ai/v6/users/Dsk1kh/?advanced=true",
  "email": "<EMAIL>",
  "is_staff": false,
  "is_active": true,
  "data_pools": [],
  "organizations": []
}
```

Returns detailed information about a User.

### HTTP Request
`GET <api_base_url>/users/<user_id>/?advanced=true`


## Change a User

> Modifying the example from the Create a User section:

```shell
curl --request PATCH \
     --url "https://api.glynt.ai/v6/users/Dsk1kh/?advanced=true" \
     --header "Authorization: Bearer abc.123.def" \
     --header "content-type: application/json" \
     --data '{"organizations": ["pRTtV1"], "data_pools": ["pRvt5", "dD314"]}'
```
> On success, this command will return a 201 response with a JSON body
> structured like this:

```json
{
  "id": "Dsk1kh",
  "created_at": "2018-02-16T21:21:30.467694Z",
  "updated_at": "2018-02-16T21:21:30.467694Z",
  "url": "https://api.glynt.ai/v6/users/Dsk1kh/?advanced=true",
  "email": "<EMAIL>",
  "is_staff": true,
  "is_active": true,
  "data_pools": [
    "https://api.glynt.ai/v6/data-pools/pRvt5/?advanced=true",
    "https://api.glynt.ai/v6/data-pools/dD314/?advanced=true",
  ],
  "organizations": [
    "https://api.glynt.ai/v6/organizations/pRTtV1/?advanced=true"
  ]
}
```

Modify an existing User instance.

### HTTP Request
`PATCH <api_base_url>/users/<user_id>/?advanced=true`

### Request Body Parameters
Only the `data_pools`, `organizations`, and `is_active` parameters may be mutated. See the
[Create a User](#create-a-user) section for details.

