# Fields

## Fields Overview

Fields are used to define the pieces of data which will be extracted from
Documents. They are defined in relation to Training Sets. Ground Truth
instances are then created for each Field for each Document in the Training
Set.

The most common issues with Fields are as follows, which will usually
cause an `advisory` to be issued on the Field object.

* When a Ground Truth associated with a Field has an advisory, the Field itself
  gets an advisory. Ignoring these will result in less accurate Training
  Revisions.
* When a Ground Truth does not exist for a Field for a Document in the Training
  Set, the Field gets an advisory. Ignoring these will result in less accurate
  Training Revisions.
* When a Field has too few documents with non-null marked Ground Truth to do a
  sufficient benchmark, an advisory will warn you of this and ask you to add
  more documents if you wish to have a reasonable benchmark. It will also warn
  if there are so few samples that there will be no benchmark for this Field,
  even if one is requested. See the [Training Revisions](#training-revisions)
  section for more information about what constitutes "too few" documents for
  benchmarking. These advisories may be safely ignored if you will not be
  running a benchmark.

Fields have a unique ordering mechanism referred to as "field order." You can
let the system manage this order, or can make manual edits yourself.  This
order will be used at various points throughout the system, for instance as the
default order of Fields when field-level reports are generated, and it can be
used for ordering when [retrieving all fields](#retrieve-all-fields).

The `field_order` property is stored as a positive integer. Within a training
set, every field will have a unique `field_order` integer. These integers are
always sequential by 1. If a `field_order` is not specified at creation, the
newly created Field will be placed at the end of the list automatically (i.e.,
its `field_order` property will automatically be the highest for that Training
Set.) If you manually set a `field_order` to one which already exists on
another Field, that old Field and all Fields after it will have their
`field_order` property incremented by one to make room for the inserted Field.

<aside class="notice">
The `metafield` property is only readable or writable with the <a
href="#es-flag">ES flag</a> set.
</aside>

<aside class="notice">
While the `metafield` property is not required to create a Field instance, it
is required when creating Training Revision for a Training Set with the Field.
400 Bad Request error will be returned if attempt to create Training Revision
for a Training Set with a Field with no metafield.
This is a feature which is due to be removed soon, but is required for now.
</aside>

## Retrieve all Fields

```shell
curl --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/fields/?advanced=true"
```
> This command will return a 200 response with a JSON structured like this:

```json
{
  "count": 2,
  "next": null,
  "previous": null,
  "results": [
    {
      "created_at": "2019-01-16T21:21:30.467694Z",
      "updated_at": "2019-01-16T21:21:30.467694Z",
      "url": "https://api.glynt.ai/v6/data-pools/pRvt5/fields/34f04172/?advanced=true",
      "id": "34f04172",
      "training_set": "https://api.glynt.ai/v6/data-pools/pRvt5/training-sets/0f4f02fa/?advanced=true",
      "label": "Total Amount Due",
      "ground_truths": [],
      "advisories": [],
      "field_order": 2,
      "tags": [],
      "glynt_tags": [],
      "data_type": "amount",
      "validation_config": {}
    },
    {
      "created_at": "2019-01-16T21:22:31.182928Z",
      "updated_at": "2019-01-16T21:22:31.182928Z",
      "url": "https://api.glynt.ai/v6/data-pools/pRvt5/fields/d1kdds7/?advanced=true",
      "id": "d1kdds7",
      "training_set": "https://api.glynt.ai/v6/data-pools/pRvt5/training-sets/0f4f02fa/?advanced=true",
      "label": "Invoice Date",
      "ground_truths": [],
      "advisories": [],
      "field_order": 1,
      "tags": [],
      "glynt_tags": [],
      "data_type": "date",
      "data_type_config": {
        "order": "YMD",
        "inherited": true
      },
      "validation_config": {}
    }
  ]
}
```
> Notice that these two fields happen to be associated with the same Training
> Set, but are not in field order. We can use the `ordering` parameter to
> instead return them in field order:
```shell
curl --header "Authorization: Bearer abc.123.def" \
     --url
     "https://api.glynt.ai/v6/data-pools/pRvt5/fields/?advanced=true&training_set=0f4f02fa&ordering=next"
```
> This command will return a 200 response with a JSON structured like this
> (results truncated for purposes of example).

```json
{
  "count": 2,
  "next": null,
  "previous": null,
  "results": [
    {
      "url": "https://api.glynt.ai/v6/data-pools/pRvt5/fields/d1kdds7/?advanced=true",
      "id": "d1kdds7",
      "training_set": "https://api.glynt.ai/v6/data-pools/pRvt5/training-sets/0f4f02fa/?advanced=true",
      "label": "Invoice Date",
      "field_order": 1,
      "tags": [],
      "glynt_tags": [],
      "data_type": "date",
      "validation_config": {}
    },
    {
      "url": "https://api.glynt.ai/v6/data-pools/pRvt5/fields/34f04172/?advanced=true",
      "id": "34f04172",
      "training_set": "https://api.glynt.ai/v6/data-pools/pRvt5/training-sets/0f4f02fa/?advanced=true",
      "label": "Total Amount Due",
      "field_order": 2,
      "tags": [],
      "glynt_tags": [],
      "data_type": "amount",
      "validation_config": {}
    }
  ]
}
```
Lists all Fields. Usually, this endpoint is invoked with a filter targetting a
specific training set.

By default, this list is ordered the same as every other list in the API (by
`created_at`, oldest first). This endpoint supports a special `ordering` query
parameter value which allow the list to instead be ordered by field order.

<aside class="success">
Notice that if a query ordered on `field_order` does not also filter on
Training Set (<code>?training_set=some_id&ordering=field_order</code>), then
the fields of various Training Sets will be interleaved, rendering the order
much less useful.
</aside>

Ordering Value | Effect
-------------- | ------
field_order | By `field_order`, ascending.

### HTTP Request
`GET <datapool_url>/fields/?advanced=true`


## Create a Field

```shell
curl --request POST \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/fields/?advanced=true" \
     --header "Authorization: Bearer abc.123.def" \
     --header "content-type: application/json" \
     --data '{"training_set":"0f4f02fa", "label":"Total Amount Due"}'
```
> On success, this command will return a 201 response with a JSON body structured like this:

```json
{
  "created_at": "2019-01-16T21:21:30.467694Z",
  "updated_at": "2019-01-16T21:21:30.467694Z",
  "url": "https://api.glynt.ai/v6/data-pools/pRvt5/fields/34f04172/?advanced=true",
  "id": "34f04172",
  "training_set": "https://api.glynt.ai/v6/data-pools/pRvt5/training-sets/0f4f02fa/?advanced=true",
  "label": "Total Amount Due",
  "ground_truths": [],
  "advisories": [],
  "field_order": 1,
  "tags": [],
  "glynt_tags": [],
  "data_type": "amount",
  "validation_config": {
    "validations": [
      {
        "name": "rule1",
        "options": {
          "option1": "value"
        }
      }
    ]
  }
}
```

Creates a new Field instance.

### HTTP Request
`POST <datapool_url>/fields/?advanced=true`

### Request Body Parameters
Parameter | Default | Description
--------- | ------- | -----------
training_set | None | Required. ID of Training Set to which this Field applies.
label | None | Required. String label for the Field. See the Labels, Tags & GLYNT Tags section. Must be unique within the Training Set.
metafield | "" | Optional. [ES flag](#es-flag) required or parameter will be ignored and default will be used.
field_order | None | Integer of Field's position in the field order for this Training Set.
tags | [] | A tags list. See the Labels, Tags & GLYNT Tags section.
glynt_tags | [] | A tags list. See the Labels, Tags & GLYNT Tags section.
data_type | "string" | Type definition for the normalization operations.
data_type_config | None | The configurations for the data type normalization operation. Check [Data types](#data-types) for the valid options. Together with the normalization configuration the property `inherited` is used to identify if the configuration was inherited from the `training-set`
validation_config | None | Optional. A json object of validations to perform on the field. The object has a single `validations` list, containing objects of shape `{'name': 'rulename', 'options': {'option': 'value'}}`


## Retrieve a Field

```shell
curl --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/fields/34f04172/?advanced=true"
```
> If the Field exists, this command will return a 200 response with a
> JSON body structured like this:

```json
{
  "created_at": "2019-01-16T21:21:30.467694Z",
  "updated_at": "2019-01-16T21:21:30.467694Z",
  "url": "https://api.glynt.ai/v6/data-pools/pRvt5/fields/34f04172/?advanced=true",
  "id": "34f04172",
  "training_set": "https://api.glynt.ai/v6/data-pools/pRvt5/training-sets/0f4f02fa/?advanced=true",
  "label": "Total Amount Due",
  "ground_truths": [],
  "advisories": [],
  "field_order": 1,
  "tags": [],
  "glynt_tags": [],
  "data_type": "string",
  "validation_config": {
    "validations": [
      {
        "name": "rule1",
        "options": {
          "option1": "value"
        }
      }
    ]
  }
}
```

Returns detailed information about a Field. If any Ground Truths are associated
with this Field, they will be linked to in the `ground_truths` property.

### HTTP Request
`GET <datapool_url>/fields/<field_id>/?advanced=true`


## Change a Field

> Modifying the example from the Create a Field section:

```shell
curl --request PATCH \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/fields/34f04172/?advanced=true" \
     --header "Authorization: Bearer abc.123.def" \
     --header "content-type: application/json" \
     --data '{"label":"Total"}'
```
> On success, this command will return a 201 response with a JSON body structured like this:

```json
{
  "created_at": "2019-01-16T21:21:30.467694Z",
  "updated_at": "2019-01-16T23:00:00.000000Z",
  "url": "https://api.glynt.ai/v6/data-pools/pRvt5/fields/34f04172/?advanced=true",
  "id": "34f04172",
  "training_set": "https://api.glynt.ai/v6/data-pools/pRvt5/training-sets/0f4f02fa/?advanced=true",
  "label": "Total",
  "ground_truths": [],
  "advisories": [],
  "field_order": 1,
  "tags": [],
  "glynt_tags": []
}
```

Modify an existing Field instance.

### HTTP Request
`PATCH <datapool_url>/fields/<field_id>/?advanced=true`

### Request Body Parameters
`label` and `metafield` may be changed. See Create a Field section for details.


## Delete a Field
```shell
curl --request DELETE \
     --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/fields/34f04172/?advanced=true"
```
> On success, this command will return a 204 response with no JSON body.

Removes a Field and its associated data.

<aside class="warning">
Remember that this will delete all Ground Truth instances associated with this
Field.
</aside>

### HTTP Request
`DELETE <datapool_url>/fields/<field_id>/?advanced=true`
