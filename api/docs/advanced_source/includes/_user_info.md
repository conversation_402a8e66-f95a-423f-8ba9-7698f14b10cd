# User Info

```shell
curl --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/user-info/?advanced=true"
```

> The request will return a 200 response with a JSON structured like this:

```json
{
    "id": "Dsk1kh",
    "created_at": "2018-02-16T21:21:30.467694Z",
    "updated_at": "2018-02-16T21:21:30.467694Z",
    "url": "https://api.glynt.ai/v6/users/Dsk1kh/?advanced=true",
    "email": "<EMAIL>",
    "is_staff": true,
    "data_pools": [
        "https://api.glynt.ai/v6/data-pools/pRvt5/?advanced=true",
        "https://api.glynt.ai/v6/data-pools/dD314/?advanced=true",
    ],
    "organizations": [
        "https://api.glynt.ai/v6/organizations/pRTtV1/?advanced=true"
    ]
}
```

> For a Machine-to-Machine integration, the request will return a 200 response
> with a JSON structured like this:

```json
{
    "created_at": "2018-02-16T21:21:30.467694Z",
    "updated_at": "2018-02-16T21:21:30.467694Z",
    "client_id": "abc123",
    "label": "Production",
    "data_pools": [
        "https://api.glynt.ai/v6/data-pools/pRvt5/?advanced=true",
        "https://api.glynt.ai/v6/data-pools/dD314/?advanced=true",
    ],
    "organization": "https://api.glynt.ai/v6/organizations/pRTtV1/?advanced=true"
}
```

This endpoint displays information about the user or machine-to-machine
integration making the request, with different information available depending
on the user or integration making the request. See the examples for more
details.

### HTTP Request
`GET <api_base_url>/user-info/?advanced=true`


