# Organizations

## Organizations Overview

An Organization is an entity with a relationship with GLYNT. Most often,
Organizations are billable entities.


## Retrieve all Organizations

```shell
curl --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/organizations/?advanced=true"
```
> This command will return a 200 response with a JSON structured like this:

```json
{
  "count": 2,
  "next": null,
  "previous": null,
  "results": [
    {
      "created_at": "2019-01-16T20:24:21.467694Z",
      "updated_at": "2019-01-16T20:24:21.467694Z",
      "url": "https://api.glynt.ai/v6/organizations/dUk21/?advanced=true",
      "id": "dUk21",
      "label": "Widgets Inc",
      "default_tokenizer_version": {
          "name": "1.0.0",
          "inherited": true
      },
      "data_pools": [
        "https://api.glynt.ai/v6/data-pools/pRvt5/?advanced=true"
        "https://api.glynt.ai/v6/data-pools/dD314/?advanced=true"
      ]
    },
    {
      "created_at": "2019-01-16T21:21:30.467694Z",
      "updated_at": "2019-01-16T21:24:31.645855Z",
      "url": "https://api.glynt.ai/v6/organizations/7731f/?advanced=true",
      "id": "7731f",
      "label": "Doodads LLC",
      "default_tokenizer_version": {
          "name": "1.0.0",
          "inherited": true
      },
      "data_pools": [
        "https://api.glynt.ai/v6/data-pools/vftpp/?advanced=true"
        "https://api.glynt.ai/v6/data-pools/HHjcz/?advanced=true"
      ]
    }
  ]
}
```
Lists all Organizations.

### HTTP Request
`GET <api_base_url>/organizations/?advanced=true`


## Create an Organization
```shell
curl --request POST \
     --url "https://api.glynt.ai/v6/organizations/?advanced=true" \
     --header "Authorization: Bearer abc.123.def" \
     --header "content-type: application/json" \
     --data '{"label":"Acme Co."}'
```
> On success, this command will return a 201 response with a JSON body structured like this:

```json
{
  "created_at": "2019-01-14T03:44:30.218991Z",
  "updated_at": "2019-01-14T03:44:30.218991Z",
  "url": "https://api.glynt.ai/v6/organizations/hpq1F/?advanced=true",
  "id": "hpq1F",
  "label": "Acme Co.",
  "default_tokenizer_version": {
      "name": "1.0.0",
      "inherited": true
  },
  "data_pools": []
}
```

Creates an Organization.
<aside class="notice">
This endpoint is only accessible by staff users.
</aside>

### HTTP Request
`POST <api_base_url>/organizations/?advanced=true`

### Request Body Parameters
Parameter | Default | Description
--------- | ------- | -----------
label | None | Required. A string label for the Organization. See the Labels, Tags & GLYNT Tags section. Must be globally unique.
default_tokenizer_version | None | A string tokenizer version used as the default for all Training Sets in this Organization. Can be overridden by setting a default at the Data Pool level. See below for details about special returned value.

### Default Tokenizer Version Return Value
The `default_tokenizer_version` field returns a dictionary with the following keys:

Parameter | Default | Description
--------- | ------- | -----------
name | system default (see [tokenizer versions](#tokenizer-versions)) | If `default_tokenizer_version` is set successfully by a user, that string value is displayed. If no value for `default_tokenizer_version` was provided, or it was set to `None`, the Glynt system default version is displayed.
inherited | True | A boolean value expressing whether the value of `name` was derived from a setting provided by a user or inherited from the system default.


## Retrieve an Organization
```shell
curl --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/organizations/dUk21/?advanced=true"
```
> If the Organization exists, this command will return a 200 response with a JSON
> body structured like this:

```json
{
  "created_at": "2019-01-16T20:24:21.467694Z",
  "updated_at": "2019-01-16T20:24:21.467694Z",
  "url": "https://api.glynt.ai/v6/organizations/dUk21/?advanced=true",
  "id": "dUk21",
  "label": "Widgets Inc",
  "default_tokenizer_version": {
      "name": "1.0.0",
      "inherited": true
  },
  "data_pools": [
    "https://api.glynt.ai/v6/data-pools/pRvt5/?advanced=true"
    "https://api.glynt.ai/v6/data-pools/dD314/?advanced=true"
  ]
}
```
Returns detailed information about a given Organization.

### HTTP Request
`GET <api_base_url>/organizations/<organization_id>/?advanced=true`


## Change an Organization
```shell
curl --request PATCH \
     --url "https://api.glynt.ai/v6/organizations/hpq1F/?advanced=true" \
     --header "Authorization: Bearer abc.123.def" \
     --header "content-type: application/json" \
     --data '{"label":"Acme Company","default_tokenizer_version":"0.0.1"}'
```
> On success, this command will return a 201 response with a JSON body structured like this:

```json
{
  "created_at": "2019-01-14T03:44:30.218991Z",
  "updated_at": "2018-02-16T23:22:24.103289Z",
  "url": "https://api.glynt.ai/v6/organizations/hpq1F/?advanced=true",
  "id": "hpq1F",
  "label": "Acme Company",
  "default_tokenizer_version": {
      "name": "0.0.1",
      "inherited": false
  },
  "data_pools": []
}
```
Change the mutable properties of an Organization. All properties which can be
set at creation can be mutated. See the Create an Organization section.
<aside class="notice">
This endpoint is only accessible by staff users.
</aside>

### HTTP Request
`PATCH <api_base_url>/organizations/<organization_id>/?advanced=true`


## Delete an Organization
```shell
curl --request DELETE \
     --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/organizations/dUk21/?advanced=true"
```

> On success, this command will return a 204 response with no JSON body.

Removes an Organization and its associated data.
<aside class="notice">
This endpoint is only accessible by staff users.
</aside>

<aside class="warning">
Deleting an Organization deletes all associated Data Pools, which deletes all
data associated with this Organization. This cannot be undone.
</aside>

### HTTP Request
`DELETE <api_base_url>/organizations/<organization_id>/?advanced=true`


