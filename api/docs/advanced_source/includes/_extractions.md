# Extractions

> Sample Extraction in advanced mode (`results` omitted for brevity)

```json
{
  "created_at": "2019-01-16T20:24:25.120938Z",
  "updated_at": "2019-01-16T20:25:59.999881Z",
  "url": "https://api.glynt.ai/v6/data-pools/pRvt5/extractions/d0923410/?advanced=true",
  "id": "d0923410",
  "uuid": "0b4bb23d-1ac3-413e-bc03-e29d9a79f45e",
  "training_set": "https://api.glynt.ai/v6/data-pools/pRvt5/training-sets/774554c8/?advanced=true",
  "training_revision": "https://api.glynt.ai/v6/data-pools/pRvt5/training-revisions/47d11524/?advanced=true",
  "extraction_batch": "https://api.glynt.ai/v6/data-pools/pRvt5/extraction-batches/d7ec28ce/?advanced=true",
  "document": "https://api.glynt.ai/v6/data-pools/pRvt5/documents/dc7f9344/?advanced=true",
  "tokenized": "https://api.glynt.ai/v6/data-pools/pRvt5/tokenized/e0bfed46/?advanced=true",
  "status": "Success",
  "verified": false,
  "verified_at": null,
  "verify_expired": false,
  "finished": true,
  "corrections": [
    "https://api.glynt.ai/v6/data-pools/pRvt5/corrections/e781eecc/?advanced=true"
  ],
  "alterations": [
    "https://api.glynt.ai/v6/data-pools/pRvt5/alterations/db42e50e/?advanced=true"
  ],
  "normalizations": [
    "https://api.glynt.ai/v6/data-pools/pRvt5/normalizations/dll500e/?advanced=true"
  ],
  "benchmark": false,
  "tags": [],
  "glynt_tags": []
}
```

## Extractions Overview

In advanced mode, Extractions display extra properties and have additional
endpoints.

In advanced mode, Extractions List endpoint will display objects created as part
of benchmarking a Training Revision.

In advanced mode, details of Extractions created as part of benchmarking a
Training Revision can also be retrieved.

In advanced mode, Extractions may be created using a Training Revision instead
of a Training Set. What actually occurs when a Training Set is passed to an
Extraction is a lookup of the latest succeeded Training Revision, and that is
what is used.

Extractions gain the `training_revision` property, which links to the Training
Revision which was used to create the Extraction.

Extractions gain the `corrections`, `normalizations`, and `alterations`
properties, which are lists of URLs linking to all of the Corrections,
Normalizations and Alterations which are associated with the Extractions. See
the [Corrections](#corrections-overview)
[Normalizations](#normalizations-overview) [Alterations](#alterations-overview)
section for more details.

Extraction `results` (even in basic mode) are actually the `raw-results` of the
Extraction with the latest Corrections, Normalizations or Alterations layered on.

Extractions gain the `alterations` property, which is a list of URLs linking to
all of the Alterations which are associated with the Extractions. See the
[Alterations](#alterations) section for more details.

Extractions gain the `/raw-results/` endpoint, which is the results without any
modification by Corrections or Alterations. When an Extraction completes, `results`
and `raw-results` will be identical. While an Extraction is in progress, all results
will be initialized to None. Once the results are available these values will be
updated with extracted results.

Extractions gain the `/corrected-results/` endpoint which is the raw results
with any Corrections applied.  The latest Correction applied to a field is
displayed. If no Correction has been made against a field then the raw result
is shown. While an Extraction is in progress, all results will be initialized
to None. Once the results are available these values will be updated with
extracted results.

Extractions gain the `/normalized-results/` endpoint which is the raw results
with any Corrections or Normalizations applied.  The latest of either
Correction or Normalization for each field is displayed. If no Correction or
Normalization have been made against a field then the raw result is displayed.

Extractions gain the `benchmark` property, which is a boolean indicating if
this Extraction was created automatically by the system as part of a Training
Revision's benchmarking process. If true, this Extraction will not be
readable/writable in basic mode.

Extractions gain the `tokenized` property, which is a link to the Tokenized
resource from which the data was extracted. If this Tokenized is ever deleted,
it becomes impossible to associate the results with the raw tokens which
compose those results, unless you retrieve another Tokenized for the Document.
This may have changed. If the Tokenized has not changed, the token page and
bounding box information will still "align" with the tokens in the Tokenized.
If it has changed, the page and bounding box information may no longer "align"
with the tokens of Tokenized.

Extractions gain an additional endpoint allowing them to be copied to a target
Training Set to be used as training data. See the [Copy Extraction as Training
Data](#copy-extraction-as-training-data) section for more information.

In advanced mode, Extraction objects reveal their internal UUID.

<aside class="success">
It is recommended that you do not delete a Tokenized associated with an
Extraction unless you are also deleting the Extraction. This will guarantee
your ability to analyze the Extraction in the context of the exact Tokenized
which was used to generate the results.
</aside>

## Create an Extraction

```shell
curl --request POST \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/extractions/?advanced=true" \
     --header "Authorization: Bearer abc.123.def" \
     --header "content-type: application/json" \
     --data '{"training_set":"89660ffa","document":"4c543d94"}'
```

> On success, this command will return a 201 response with a JSON body
> structured like this:

```json
{
  "created_at": "2018-02-17T14:54:30.699864Z",
  "updated_at": "2018-02-17T14:54:30.699864Z",
  "url": "https://api.glynt.ai/v6/data-pools/pRvt5/extractions/e923b69a/?advanced=true",
  "id": "e923b69a",
  "uuid": "0b4bb23d-1ac3-413e-bc03-e29d9a79f45e",
  "training_set": "https://api.glynt.ai/v6/data-pools/pRvt5/training-sets/774554c8/?advanced=true",
  "training_revision": "https://api.glynt.ai/v6/data-pools/pRvt5/training-revisions/639c2be0/?advanced=true",
  "extraction_batch": null,
  "document": "https://api.glynt.ai/v6/data-pools/pRvt5/documents/4c543d94/?advanced=true",
  "tokenized": "https://api.glynt.ai/v6/data-pools/pRvt5/tokenized/e0bfed46/?advanced=true",
  "status": "Pending",
  "finished": false,
  "classify": false,
  "corrections": [],
  "alterations": [],
  "normalizations": [],
  "benchmark": false,
  "tags": [],
  "glynt_tags": []
}
```

In advanced mode, the option to pass `training_revision` instead of
`training_set` is available. If both are passed, the `training_revision` must
be a child of the `training_set`, or a 400 error will be returned. If an
invalid Training Revision is passed, a 400 error will be returned (e.g., the
Revision has not completed, is failed, etc.).

### HTTP Request

`POST <datapool_url>/extractions/?advanced=true`

### Request Body Parameters

Parameter | Default | Description
--------- | ------- | -----------
training_set | None | Required if `training_revision` is not passed. ID of the Training Set whose latest successful Training Revision will power the extraction.
training_revision | None | Required if `training_set` is not passed. ID of the Training Revision which will power the extraction.
document | None | Required. ID of the Document to extract data from.
classify | False | Present for legacy reasons. The only legal value is `false`.
tags | [] | A tags list. See the Labels, Tags & GLYNT Tags section.
glynt_tags | [] | A tags list. See the Labels, Tags & GLYNT Tags section.

## Retrieve an Extraction's Normalized Results

```shell
curl --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/extractions/d0923410/normalized-results/?advanced=true"
```

> If the Extraction exists and has completed, this command will return a 200
> response with a JSON body structured like this:

Returns Extraction with the Normalized results set, which is the raw results with
the latest Corrections and Normalizations applied to each field. If no
Normalization exists the latest Correction is shown, if there is no Correction
then the raw result is given. If a Correction exists that is more recent than
a Normalization for the same field, the Correction is given. Alterations are
not included.

### HTTP Request

`GET <datapool_url>/extractions/<extraction_id>/normalized-results/?advanced=true`

### Query Parameters

Parameter  | Default | Description
---------- | ------- | -----------
quick-view | false   | When set to true, omits `fields`, `normalizations`, and `corrections` from the returned data in order to somewhat improve endpoint response time.
---------- | ------- | -----------

## Retrieve an Extraction's Corrected Results

```shell
curl --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/extractions/d0923410/corrected-results/?advanced=true"
```

Returns the Extraction with the corrected results set, which is the raw results with
the latest Correction applied to each field. If no Correction exists for a
field, then the raw result is given.  Corrected results do not contain
Normalizations or Alterations.

### HTTP Request

`GET <datapool_url>/extractions/<extraction_id>/corrected-results/?advanced=true`

## Retrieve an Extraction's Raw Results

```shell
curl --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/extractions/d0923410/raw-results/?advanced=true"
```

> If the Extraction exists and has completed, this command will return a 200
> response with a JSON body structured like this:

Returns the Extraction with the raw results set, which are the results with no
Corrections, Normalizations, or Alterations applied.

### HTTP Request

`GET <datapool_url>/extractions/<extraction_id>/raw-results/?advanced=true`

## Clean and Normalize an Extraction

```shell
curl --request POST \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/extractions/jDZAv/clean/?advanced=true" \
     --header "Authorization: Bearer abc.123.def" \
     --header "content-type: application/json"
```

> On success, this command will return a 204 response with no JSON body.

Extractions are automatically cleaned and normalized by default.
However, in the event that one needs to force a re-cleaning and re-normalization
of the extraction, this endpoint will invoke those processes as if the
extraction just completed and needs to be cleaned and normalized. All other
post-cleaning processes (e.g. transformation) will also run.

### HTTP Request

`POST <datapool_url>/extractions/<extraction_id>/clean/?advanced=true`

### Request Body Parameters

None

## Verify an Extraction

```shell
curl --request POST \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/extractions/jDZAv/verify/?advanced=true" \
     --header "Authorization: Bearer abc.123.def" \
     --header "content-type: application/json" \
     --data '{"is_verified":true}'
```

> On success, this command will return a 204 response with no JSON body.

Marks an Extraction as verified.
Extractions can be verified only when they have successfully completed all extraction and transformation phases.
Processing of such Extractions will not continue by the system until it's verified by posting a request to this endpoint.
Once that is done, the system will notify the router system, which will eventually make the extraction available for the client.

The `is_verified` is a boolean value. The request can be made multiple times to re-trigger the post-transformation operations, assuming the `is_verified` value is `true`. Re-requesting with a `is_verified` value `false` it's also allowed and will not trigger the
post-transformation processes.

### HTTP Request

`POST <datapool_url>/extractions/<extraction_id>/verify/?advanced=true`

### Request Body Parameters

Parameter | Default | Description
--------- | ------- | -----------
is_verified | None | Boolean

## Copy Extraction as Training Data

```shell
curl --request POST \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/extractions/jDZAv/copy-as-training-data/?advanced=true" \
     --header "Authorization: Bearer abc.123.def" \
     --header "content-type: application/json" \
     --data '{"training_set":"Hyjui5"}'
```

> On success, this command will return a 204 response with no JSON body.

This convenience endpoint allows a user to "copy" an Extraction's results to
a target Training Set as an additional, marked sample for that Training Set.
Remember that this will only copy the `raw-results` with Corrections applied -
Alterations will NOT be copied, because Alterations by their very nature are
changes to the results mutating them away from their on-document appearance. It
accomplishes this copy action by executing the following steps:

First, the Document of the Extraction is copied to the Traning Set. If the
Document is already associated with the Training Set, an error will be
returned.

Next, any Fields which are present on the Extraction which are not present on
the Training Set are created for the Training Set. Note that no metafield will
be set for any newly created Fields.

Finally, a Ground Truth is created for each corrected result in the Extraction.

<aside class="success">
Note that any result with a null content will be treated as an explicit
"not present" Ground Truth. This is good if true, but may degrade the
performance of the Training Model if untrue. Be careful to check the results
before copying and/or check the created Ground Truths after copying.
</aside>

### HTTP Request

`POST <datapool_url>/extractions/<extraction_id>/copy-as-training-data/?advanced=true`

### Request Body Parameters

Parameter | Default | Description
--------- | ------- | -----------
training_set | The parent Training Set of the Training Revision associated with this Extraction. | String. The ID of the target Training Set.
