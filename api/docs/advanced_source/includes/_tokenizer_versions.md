# Tokenizer Versions

```shell
curl --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/tokenizer-versions/?advanced=true"
```

> The request will return a 200 response with a JSON structured like this:

```json
{
    "default": "AWS",
    "active": ["AWS"],
    "deprecated": [],
    "obsolete": ["ABBYY11", "ABBYY12],
    "experimental": ["Google", "Azure"]
}
```

This endpoint displays four lists of tokenizer versions.

`default` is the current system-wide default version which will be used in
various places when no specific version is provided.

`active` is the list of currently available, fully supported tokenizer
versions.

`deprecated`, if not empty, will be a list of currently `active` versions which
will soon no longer be supported. It is recommended to update any resources
dependent on these versions.

`obsolete` is a list of versions which are no longer `active`, but once were.
These versions may no longer be used, and an error will be thrown if this is
tried. You must update the `tokenizer_version` of any dependent resources in
order for them to continue functioning.

`experimental` is a list of "preview" versions. They are not fully supported,
and can disappear without notice. They will not go to `deprecated` or
`obsolete`. As long as they continue to exist, they may be used as if they were
in `active`. Generally, these versions are used to test a new possible version
before promoting to `active`.

### HTTP Request
`GET <api_base_url>/tokenizer-versions/?advanced=true`


