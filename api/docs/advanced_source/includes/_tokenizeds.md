# Tokenizeds

## Tokenzieds Overview

A Tokenized representation is a structured representation of a Document.
Primarily, it is composed of an array of pages, each of which is an array of
tokens which appear on that page.

A Document will often have multiple Tokenized representations. There are
several reasons for this. In short, a tokenized representation is not perfect,
as it relies on OCR and computer vision to construct a representation of the
document. As the service which provides these Tokenized representations
(Boxcars) evolves, these representations can improve.

Training Revisions, though, use these Tokenized representations to create the
underlying machine learning models. As Boxcars evolves, those models can
degrade even as the Tokenized representations improve! This counterintuitive
behavior results because the models are static - they were trained against
Tokenized representations created at one point in time, and now those
representations are evolving, while the underlying models are not.

To counterract this, a Document can store multiple different Tokenized
representations, and the Create Tokenized endpoint supports choosing which
version of Boxcars to use for tokenization. Thus, we store the "current"
Boxcars version on a Training Set when it is created. That Boxcars version is
then used for all Extraction Batches created from that Training Set.

Unfortunately, this doesn't completely insulate us from degradation of Training
Revisions over time, because Boxcars itself uses 3rd party services, so even a
given Boxcars version can produce changing results over time. Boxcars is doing
what it can to reduce this drift over time, but it is a fact of the system
which must be remembered.

`tokenizer_version` is a property associated with every Tokenized resource. It
stores the string version of the tokenizer which was responsible for producing
the tokenized representation.

When a Tokenized resource is created, it starts a job to generate the content.
It  will inform you of the status of the job, as well as the boolean finished
status.

All possible status values are listed in the table below.

Status | Meaning
------ | -------
Pending | Tokenized has not yet started processing.
In Progress | Tokenization is in progress.
Success | Tokenization finished processing successfully. Content is ready.
Failed | Tokenization finished processing with an error. No content was returned.

Due to the numerous ways the system relies on a history of Document
tokenizations, Tokenizeds may not be deleted independently. Instead, they are
deleted automatically when its corresponding Document is deleted.

## Retrieve all Tokenizeds

```shell
curl --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/tokenizeds/?advanced=true"
```
> This command will return a 200 response with a JSON body structured like this:

```json
{
  "count": 2,
  "next": null,
  "previous": null,
  "results": [
    {
      "created_at": "2019-01-16T20:24:21.467694Z",
      "updated_at": "2019-01-16T20:25:33.333451Z",
      "url": "https://api.glynt.ai/v6/data-pools/pRvt5/tokenizeds/ad6fef58/?advanced=true",
      "id": "ad6fef58",
      "document": "https://api.glynt.ai/v6/data-pools/pRvt5/documents/b9e20780/?advanced=true", 
      "tokenizer_version": "1.0.0",
      "status": "Success",
      "finished": true
    },
    {
      "created_at": "2019-01-16T21:21:30.467694Z",
      "updated_at": "2019-01-16T21:24:31.645855Z",
      "url": "https://api.glynt.ai/v6/data-pools/pRvt5/tokenizeds/bf07475c/?advanced=true",
      "id": "bf07475c",
      "document": "https://api.glynt.ai/v6/data-pools/pRvt5/documents/bf074f90/?advanced=true", 
      "tokenizer_version": "1.0.0",
      "status": "In Progress"
      "finished": false
    }
  ]
}
```

Lists all Tokenizeds in the Data Pool. The `tokenized` property which contains
the content of the tokenization is omitted in this view.  Request the detailed
view of a Tokenized to see the content.

### HTTP Request
`GET <datapool_url>/tokenizeds/?advanced=true`


## Create a Tokenized

```shell
curl --request POST \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/tokenizeds/?advanced=true" \
     --header "Authorization: Bearer abc.123.def" \
     --header "content-type: application/json" \
     --data '{"document":"d14c0a56"}'
```
> On success, this command will return a 201 response with a JSON body structured like this:

```json
{
  "created_at": "2019-01-16T21:21:30.467694Z",
  "updated_at": "2019-01-16T21:24:31.645855Z",
  "url": "https://api.glynt.ai/v6/data-pools/pRvt5/tokenizeds/d8d073d4/?advanced=true",
  "id": "d8d073d4",
  "document": "https://api.glynt.ai/v6/data-pools/pRvt5/documents/d14c0a56/?advanced=true", 
  "tokenizer_version": "1.0.0",
  "status": "Pending",
  "finished": false
}
```

Creates a new Tokenized instance.

### HTTP Request
`POST <datapool_url>/tokenizeds/?advanced=true`

### Request Body Parameters
Parameter | Default | Description
--------- | ------- | -----------
document | None | Required. ID of Document to be Tokenized.
tokenizer_version | "default" | String. "default" or a specific tokenizer version. A 400 response will be returned if you provide an invalid tokenizer version.


## Retrieve a Tokenized

```shell
curl --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/tokenizeds/d8d073d4/?advanced=true"
```
> If the Tokenized exists, this command will return a 200 response with a
> JSON body structured like this:

```json
{
  "created_at": "2019-01-16T21:21:30.467694Z",
  "updated_at": "2019-01-16T21:24:31.645855Z",
  "url": "https://api.glynt.ai/v6/data-pools/pRvt5/tokenizeds/d8d073d4/?advanced=true",
  "id": "d8d073d4",
  "document": "https://api.glynt.ai/v6/data-pools/pRvt5/documents/d14c0a56/?advanced=true", 
  "tokenizer_version": "1.0.0",
  "status": "Success",
  "finished": true,
  "tokenized": {
    "pages": [
      {
        "page_number": 1,
        "full_page_bounding_box": [
          {"x": 0, "y": 0},
          {"x": 2550, "y": 0},
          {"x": 2550, "y": 3300},
          {"x": 0, "y": 3300}
        ],
        "tokens": [
          {
            "block_id": 1,
            "bounding_box": [
              {"x": 1410, "y": 55},
              {"x": 1644, "y": 55},
              {"x": 1644, "y": 92},
              {"x": 1410, "y": 92}
            ],
            "char_widths":[34,37,11,40,13,36,36,27],
            "content": "Hello,",
            "in_table": 0,
            "cell_num": 0,
            "row_num": 0
          },
          {
            "block_id": 1,
            "bounding_box": [
              {"x": 1663, "y": 52},
              {"x": 1861, "y": 52},
              {"x": 1861, "y": 92},
              {"x": 1663, "y": 92}
            ],
            "content": "World!"
          }
        ]
      }
    ]
  }
}
```

Returns detailed information about a Tokenized.

`tokenized` is the tokenized representation. The updatedDate and expiredDate
contain the time when the tokenized was last updated by boxcars, and 
the expiration date of the particular tokenized. This expiration date
can be used by the client to issue a refresh request (see below).

The `pages` property contains an array of all pages in the Document.
The `page_number` is an integer starting at one. The `full_page_bounding_box` 
is an array of coordinates which describe the full page's dimensionality, 
where the origin (0,0) is the upper left, y increases as you move down the page, 
and x increases as you move to the right.

Each page also contains a `tokens` property, which is an array of all tokens on
the page. The following table is a non-exhaustive list of possible properties for each
token. Certain properties are always available, and others may be present.

Property | Presence | Description
-------- | -------- | -----------
bounding_box | Always | Array of coordinates describing bounding box of token.
block_id | Optional | Tokenized will sometimes separate the content into "blocks," and assign an integer ID to those blocks. This is the ID of the block containing the token.
cell_num | Optional | ??? Something related to tables.
char_widths | Optional | Array of character widths where the first element of the array refers to the first character of the token `content`.
content | Always | String content of token.
row_num | Optional | ??? Something related to tables.

<aside class="notice">
Tokenized is derived from OCR and computer vision, so the calculated properties
are not guaranteed to be perfectly accurate.
</aside>

### HTTP Request
`GET <datapool_url>/tokenizeds/<tokenized_id>/?advanced=true`


## Retrieve and Refresh a Tokenized

```shell
curl --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/tokenizeds/d8d073d4/?advanced=true&refresh"
```
> If the request includes the query parameter `refresh` set to true, then the 
> server will check if the Tokenized exists and fresh. By default, this period 
> is one day, so any Tokenized older than one day will be generated. 
> The response will be identical to the regular tokenized retrieval.


Returns detailed information about a Tokenized that is guaranteed to be at most one day old.
See the previous section about retrieving tokenized for details.

### HTTP Request
`GET <datapool_url>/tokenizeds/<tokenized_id>/?advanced=true&refresh=true`


