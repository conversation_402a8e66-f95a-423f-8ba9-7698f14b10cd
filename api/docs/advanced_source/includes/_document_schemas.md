# Document Schemas

```shell
curl --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/document-schemas/"
```

> The request will return a 200 response with a JSON list of support document schema types.

Displays a list of supported document schema types, which can be set on a training set in the
`document_schema` property.

### HTTP Request
`GET <api_base_url>/document-schemas/`

