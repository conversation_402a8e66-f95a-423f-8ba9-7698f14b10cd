# Integrations

## Integrations Overview

<aside class="notice">
All Integration endpoints are only accessible to staff users.
</aside>

Integrations are used for machine-to-machine authentication and authorization.

Staff users may create and edit Integrations.

Integrations may not be deleted via the API at this time. Instead, they may be set to
`is_active: false`. This disables all access to that integration, but allows us to
maintain a forensic trail of actions taken by a given Integration. If it becomes
necessary to delete a Integration, contact the API development team.


## Retrieve all Integrations

```shell
curl --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/integrations/?advanced=true"
```
> This command will return a 200 response with a JSON structured like this:

```json
{
  "count": 2,
  "next": null,
  "previous": null,
  "results": [
    {
      "id": "Dsk1kh",
      "created_at": "2018-02-16T21:21:30.467694Z",
      "updated_at": "2018-02-16T21:21:30.467694Z",
      "url": "https://api.glynt.ai/v6/integrations/Dsk1kh/?advanced=true",
      "client_id": "2f6HWbImO2vAYhOfcReVXRwkyJix015z",
      "organization": "https://api.glynt.ai/v6/organizations/12kfff/?advanced=true",
      "label": "UI Integration"
    },
    {
      "id": "7ga1hD",
      "created_at": "2018-02-16T21:24:20.192011Z",
      "updated_at": "2018-02-16T21:24:20.192011Z",
      "url": "https://api.glynt.ai/v6/integrations/7ga1hD/?advanced=true",
      "client_id": "89aHAsda812bn081jdSlkLLkdi8gnBJ9",
      "organization": "https://api.glynt.ai/v6/organizations/12kfff/?advanced=true",
      "label": "Automation Server"
    }
  ]
}
```

Lists all Integrations. Note that not all fields are present in the response.
Use the [Retrieve an Integration](#retrieve-an-integration) endpoint to get
detailed Integration information.

### HTTP Request
`GET <api_base_url>/integrations/?advanced=true`

### Filtering
In addition to the filters specified in <a href="#filtering">Filtering</a>
section, this endpoint supports a `client_id` filter which allows for filtering
for an exact `client_id`.


## Create an Integration

```shell
curl --request POST \
     --url "https://api.glynt.ai/v6/integrations/?advanced=true" \
     --header "Authorization: Bearer abc.123.def" \
     --header "content-type: application/json" \
     --data '{"label": "Backup Server", "organization":"12kfff"}'
```
> On success, this command will return a 201 response with a JSON body structured like this:

```json
{
  "id": "7ga1hD",
  "created_at": "2018-02-16T21:24:20.192011Z",
  "updated_at": "2018-02-16T21:24:20.192011Z",
  "url": "https://api.glynt.ai/v6/integrations/7ga1hD/?advanced=true",
  "client_id": "89aHAsda812bn081jdSlkLLkdi8gnBJ9",
  "organization": "https://api.glynt.ai/v6/organizations/12kfff/?advanced=true",
  "label": "Backup Server",
  "data_pools": [],
  "is_active": true
}
```

Creates a new Integration instance.

<aside class="notice">
The example demonstrates that it is possible to create an Integration which
does not have access to any Data Pools, but instead only access to an
Organization. Under most circumstances an Integration will be associated with
one or more Data Pools so that it can do meaningful work.
</aside>

### HTTP Request
`POST <api_base_url>/integrations/?advanced=true`

### Request Body Parameters
Parameter | Default | Description
--------- | ------- | -----------
label | None | Required. A string label for the Integration. See the Labels, Tags & GLYNT Tags section. Must be unique within an Organization.
organization | None | Required. ID of Organization which the Integration should be associated with.
data_pools | None | List of IDs of Data Pools which this Integration has access to. A 400 error will be returned if you attempt to create an Integration with Data Pools which are not a part of one of the Integration's Organization.
is_active | true | Whether the Integration is allowed to interact with the GLYNT system. If false, the Integration will be unable to retrieve tokens or otherwise interact with the system.


## Retrieve an Integration

```shell
curl --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/integrations/7ga1hD/?advanced=true"
```
> If the Integration exists, this command will return a 200 response with a JSON body
> structured like this:

```json
{
  "id": "7ga1hD",
  "created_at": "2018-02-16T21:24:20.192011Z",
  "updated_at": "2018-02-16T21:24:20.192011Z",
  "url": "https://api.glynt.ai/v6/integrations/7ga1hD/?advanced=true",
  "client_id": "89aHAsda812bn081jdSlkLLkdi8gnBJ9",
  "organization": "https://api.glynt.ai/v6/organizations/12kfff/?advanced=true",
  "label": "Automation Server",
  "data_pools": ["https://api.glynt.ai/v6/organizations/pRvt5/?advanced=true"],
  "is_active": true
}
```

Returns detailed information about an Integration.

### HTTP Request
`GET <api_base_url>/integrations/<integration_id>/?advanced=true`


## Change an Integration

> Modifying the example from the Create an Integration section:

```shell
curl --request PATCH \
     --url "https://api.glynt.ai/v6/integrations/7ga1hD/?advanced=true" \
     --header "Authorization: Bearer abc.123.def" \
     --header "content-type: application/json" \
     --data '{"data_pools": ["pRvt5", "dD314"]}'
```
> On success, this command will return a 201 response with a JSON body
> structured like this:

```json
{
  "id": "7ga1hD",
  "created_at": "2018-02-16T21:24:20.192011Z",
  "updated_at": "2018-02-16T21:24:20.192011Z",
  "url": "https://api.glynt.ai/v6/integrations/7ga1hD/?advanced=true",
  "client_id": "89aHAsda812bn081jdSlkLLkdi8gnBJ9",
  "organization": "https://api.glynt.ai/v6/organizations/12kfff/?advanced=true",
  "label": "Automation Server",
  "data_pools": [
    "https://api.glynt.ai/v6/organizations/pRvt5/?advanced=true",
    "https://api.glynt.ai/v6/organizations/dD314/?advanced=true"
  ],
  "is_active": true
}
```

Modify an existing Integration instance.

### HTTP Request
`PATCH <api_base_url>/integrations/<integration_id>/?advanced=true`

### Request Body Parameters
All the parameters from the [Create an Integration](#create-an-integration)
section may be mutated, except for `organization`.

