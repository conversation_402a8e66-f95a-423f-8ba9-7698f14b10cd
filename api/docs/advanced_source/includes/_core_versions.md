# Core Versions

```shell
curl --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/core-versions/?advanced=true"
```

> The request will return a 200 response with a JSON structured like this:

```json
{
    "active": ["4.0.0", "4.1.0", "4.1.1"],
    "deprecated": ["4.0.0"],
    "obsolete": ["3.0.0"],
    "experimental": ["5.0.0"]
}
```

This endpoint displays four lists of core versions. Each list is ordered
oldest to most recent.

`active` is the list of currently available, fully supported core 
versions.

`deprecated`, if not empty, will be a list of currently `active` versions which
will soon no longer be supported. It is recommended to update any resources
dependent on these versions.

`obsolete` is a list of versions which are no longer `active`, but once were.
These versions may no longer be used, and an error will be thrown if this is
tried. You must update the `core_version` of any dependent resources in
order for them to continue functioning.

`experimental` is a list of "preview" versions. They are not fully supported,
and can disappear without notice. They will not go to `deprecated` or
`obsolete`. As long as they continue to exist, they may be used as if they were
in `active`. Generally, these versions are used to test a new possible version
before promoting to `active`.

### HTTP Request
`GET <api_base_url>/core-versions/?advanced=true`


