# Documents

> Example of `tokenizeds` on a Document:

```json
{
  "created_at": "2018-02-16T21:21:30.467694Z",
  "updated_at": "2018-02-16T21:21:30.467694Z",
  "url": "https://api.glynt.ai/v6/data-pools/pRvt5/documents/32f20bb8/?advanced=true",
  "file_access_url": "https://api.glynt.ai/v6/data-pools/pRvt5/documents/32f20bb8/file/?advanced=true",
  "id": "32f20bb8",
  "label": "sample_doc_name",
  "tags": [
    "sample_tag"
  ],
  "glynt_tags": [],
  "token_count": 1215,
  "language": "en",
  "format_id": "xyz-123",
  "is_supported": true,
  "content_type": "application/pdf",
  "content_md5":"4DujaMxdUy64mWOWbP6Xew==",
  "pages": [
    "https://api.glynt.ai/v6/data-pools/pRvt5/documents/490116ec/pages/1/?advanced=true"
  ],
  "tokenizeds": [
    "https://api.glynt.ai/v6/data-pools/pRvt5/tokenizeds/490116ec/?advanced=true"
  ]
}
```

## Documents Overview

In advanced mode, Documents gain the additional linked resource of Tokenizeds.
A document can have 0 or more Tokenized representations. See the
[Tokenized](#tokenized) section for more details.

Tokenizeds are returned as an array of URLs whenever a Document resource is
returned. Each url points to a related Tokenized resource.

In advanced mode, Documents gain a `pages` property which has URL links to the
pages of the Document. This array will not populate automatically. Instead, you
must explicitly [Generate Document Page Images](#generate-document-page-images).

In advanced mode, Documents also gain additional properties `token_count` (number
of tokens), `language` (detected language), `format_id` (a cluster identifier),
and `is_supported` (bool indicating if the content type is supported).

Deleting a Document removes a Document and its associated data, as explained in
the basic mode docs. This includes advanced-only data, including Tokenizeds and
Ground Truths.


## Generate Document Page Images
```shell
curl --request POST \
     --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/documents/442a2904/pages/"
```
> If the Document exists, this will return a 204 response with no JSON body,
> indicating that the request has been accepted and page images will be
> generated.

Page images for Documents do not generate by default. If they are wanted, you
must call this endpoint in order to start the generation process. The pages
will appear in the Documents `pages` property soon thereafter. Usually, this
process takes a few seconds, but depending on Document size and server load, it
may take longer. There is no way to check on the page generation process at
this time, you must simply wait for the pages to be generated.

If the Document file content is not available, an HTTP 404 status code will be
returned.

Calling this endpoint multiple times is safe. If a page generation task has
already been started, another task will not be created. The status code will be
204 regardless.

### HTTP Request
`POST <datapool_url>/documents/<document_id>/pages/`


## Retrieve Document Pages

```shell
curl --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/documents/442a2904/pages/1/"
```
> If the Document page exists, this command will return a 200 response with a JSON
> body structured like this:

```json
{
  "file_temp_url": "https://files.glynt.ai/12ndsksd?signature=123abc",
}
```
> This url may now be used to directly access the generated page file for 1 hour.
> Remember, no `Authorization` header is necessary because this is a presigned
> URL. Notice that you may only execute GET requests with this URL.

```shell
curl --url "https://files.glynt.ai/12ndsksd?signature=123abc"
```
Document pages may be retrieved individually. Pages are numbered sequentially
starting from 1.

A PNG image is generated for each page of a Document. Much like a Document uses
a two-step process for retrieving the underlying Document file, a page is
retreived by first calling this endpoint, then using a generated temporary
`file_temp_url`.


### HTTP Request
`GET <datapool_url>/documents/<document_id>/pages/<page_number>/`
