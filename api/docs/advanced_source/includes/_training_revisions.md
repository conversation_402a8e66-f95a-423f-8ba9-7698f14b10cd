# Training Revisions

## Training Revisions Overview

Training Revisions are "executions" of a Training Set. When they are created,
they gather the data associated with the Training Set and create immutable
properties to store a "snapshot" of the state of the Training Set at the time
the Training Revision was created, namely: `fields`, `tokenizer_version`,
`core_version`, `document_ids`, and `training_data`.  Then the Training
Revision job begins. When finished, the Training Revision may then be used for
Extraction Batches.

`fields` in the responses include `label`, `tags` and `glynt_tags` as they
appeared on the Training Set's Fields at the moment the Training Revision was
created.

<aside class="success">
The captured properties of a Training Set are only those required to allow the
Training Revision to run, not any semantic properties like <code>label</code>
or <code>description</code>. It is recommended that you keep a Training Set
consistent throughout its lifespan (i.e., do not start a Training Set for one
publisher, then radically alter that Training Set to instead have a completely
different set of documents for a different publisher). If for some reason that
is impossible, consider using the <code>tags</code> field of the Training
Revision to give semantic meaning to each Revision, or delete the old Training
Revisions which no longer "match" the Training Set semantically.
</aside>

Training Revisions have an option to evaluate themselves. This process is
referred to as "benchmarking." When a Training Revision is created,
benchmarking is enabled by default. If benchmarking is enabled, some of the
documents will be held back from training for each Field, and will instead be
used to evaluate the performance of the complete Training Revision. This is a
best practice for machine-learning based systems: by holding back data from
training, you are able to estimate the performance of the system on unseen
data.

Which Documents are held out for evaluation and which are used for training is
decided by the system for each field. If there are not enough documents to
create a reasonable test set for a given field, an advisory will be present on
the Training Set warning you to add more documents with samples of that field.

At this time, the minimum Training Set size is 7. Thus, a minimum of 8 samples
of a Field are required to run benchmarking for that Field at all.  Fields
that do not meet the required minimum will still be trained using any marks
that are present in the selected Training Set's Documents, but will not be
included in the benchmark reports.  If no Fields are found which meet the
minimum requirements for benchmarking, training will still proceed without
benchmarking, the `benchmark` field on the Training Revision will be set to
`false` automatically and no reports will be generated.  You can also disable
benchmarking manually by setting `benchmark` to `false` in your training request
data.

A minimum of 12 samples of each field are recommended for benchmarking to provide
reasonable estimates of performance.

Training Revisions have an automatic anomaly detection mechanism which excludes
Documents from the Training Set before training if they contain a mark for a
field which appears to be an anomaly when compared against other marks for that
same field.  The fields `accepted_document_ids` and `anomalous_document_ids`
reflect the outcome of anomaly detection.  This mechanism can be bypassed
using the `force_use_raw_data=true` flag in the training request data.


## Retrieve all Training Revisions in a Data Pool

```shell
curl --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/training-revisions/?advanced=true"
```
> This command returns a 200 response with a JSON body structured as
> follows (for sake of brevity, only a single sample is shown):

```json
{
  "count": 1,
  "next": null,
  "previous": null,
  "results": [
    {
      "created_at": "2019-01-16T20:24:21.467694Z",
      "updated_at": "2019-01-16T20:34:44.921031Z",
      "url": "https://api.glynt.ai/v6/data-pools/pRvt5/training-revisions/0e820894/?advanced=true",
      "id": "0e820894",
      "label": "Provider A Training Revision 1",
      "description": "Training Revision for Provider A.",
      "training_set": "https://api.glynt.ai/v6/data-pools/pRvt5/training-sets/17eaa030/?advanced=true", 
      "force_use_raw_data": false,
      "tokenizer_version": "1.0.0",
      "core_version": "2.0.0",
      "benchmark": false,
      "tags": [],
      "glynt_tags": []
    }
  ]
}
```

Lists all Training Revisions in the Data Pool. Several detail attributes
omitted in the list view: `training_data`, `document_ids`,
`anomalous_document_ids`, `accepted_document_ids`, `fields`, and
`training_data`. To view these, request the details of a single Training
Revision through the `url` attribute (see description below).

### HTTP Request
`GET <datapool_url>/training-revisions/?advanced=true`


## Retrieve a Training Revision

```shell
curl --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/training-revisions/90f9747e/?advanced=true"
```
> If the Training Revision exists, this command will return a 200 response with a
> JSON body structured like this:

```json
{
  "created_at": "2019-01-16T20:24:21.467694Z",
  "updated_at": "2019-01-16T20:41:55.211182Z",
  "url": "https://api.glynt.ai/v6/data-pools/pRvt5/training-revisions/90f9747e/?advanced=true",
  "id": "90f9747e",
  "label": "Provider A Training Revision",
  "description": "Training Revision for Provider A.",
  "training_set": "https://api.glynt.ai/v6/data-pools/pRvt5/training-sets/9a7c2ba4/?advanced=true", 
  "document_ids": ["1ff52e12", "261525f4"],
  "anomalous_document_ids": [],
  "accepted_document_ids": ["1ff52e12", "261525f4"],
  "force_use_raw_data": false,
  "tokenizer_version": "1.0.0",
  "core_version": "2.0.0",
  "fields": [
    {
      "label": "Total Amount Due",
      "tags": [],
      "glynt_tags": []
    }
  ],
  "status": "Success",
  "finished": true,
  "benchmark": false,
  "tags": [],
  "glynt_tags": [],
  "debug_package": "https://api.glynt.ai/v6/data-pools/pRvt5/training-revisions/90f9747e/debug-package/?advanced=true",
  "training_data": {
    "1ff52e12": {
      "tokenized_id": "rekl4S",
      "ground_truths": {
        "Total Amount Due": {
          "content": "$145.87",
          "token_data": {}
        }
      }
    },
    "261525f4": {
      "tokenized_id": "pF443g",
      "ground_truths": {
        "Total Amount Due": {
          "content": "$1.50",
          "token_data": {}
        }
      }
    }
  }
}
```

Returns detailed information about a Training Revision.

### HTTP Request
`GET <datapool_url>/training-revisions/<training_revision_id>/?advanced=true`


## Create a Training Revision

```shell
curl --request POST \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/training-revisions/?advanced=true" \
     --header "Authorization: Bearer abc.123.def" \
     --header "content-type: application/json" \
     --data '{"training_set":"23d9238a"}'
```
> On success, this command will return a 201 response with a JSON body structured like this:

```json
{
  "count": 1,
  "next": null,
  "previous": null,
  "results": [
    {
      "created_at": "2019-01-16T20:24:21.467694Z",
      "updated_at": "2019-01-16T20:24:21.467694Z",
      "url": "https://api.glynt.ai/v6/data-pools/pRvt5/training-revisions/0e820894/?advanced=true",
      "id": "0e820894",
      "label": "Provider A Training Revision",
      "description": "Training Revision for provider A.",
      "training_set": "https://api.glynt.ai/v6/data-pools/pRvt5/training-sets/23d9238a/?advanced=true", 
      "document_ids": ["1ff52e12", "261525f4"],
      "tokenizer_version": "1.0.0",
      "core_version": "2.0.0",
      "fields": [
        {
	  "label": "Total Amount Due",
	  "tags": [],
	  "glynt_tags": []
	}
      ],
      "force_use_raw_data": false,
      "anomalous_document_ids": [],
      "accepted_document_ids": ["1ff52e12", "261525f4"],
      "status": "Pending",
      "finished": false,
      "benchmark": false,
      "tags": [],
      "glynt_tags": [],
      "debug_package": "https://api.glynt.ai/v6/data-pools/pRvt5/training-revisions/0e820894/debug-package/?advanced=true",
      "training_data": {
        "1ff52e12": {
          "tokenized_id": "rekl4S",
          "ground_truths": {
            "Total Amount Due": {
              "content": "$145.87",
              "token_data": {}
            }
          }
        },
        "261525f4": {
          "tokenized_id": "pF443g",
          "ground_truths": {
            "Total Amount Due": {
              "content": "$1.50",
              "token_data": {}
            }
          }
        }
      }
    }
  ]
}
```

Creates a new Training Revision instance.

### HTTP Request
`POST <datapool_url>/training-revisions/`

### Request Body Parameters
Parameter | Default | Description
--------- | ------- | -----------
training_set | None | Required. ID of the Training Set to create a Training Revision for.
label | Varies | String label for the Training Revision. If one is not provided, a uuid will be generated. See the Labels, Tags & GLYNT Tags section.
description | None | String. A description of the Training Revision.
core_version | "latest" | String. "latest" or a specific core version.
force_use_raw_data | false | Boolean. If true, documents and ground truths will not pass through any anomaly detection, and machine learning will be executed against the raw input data of the Training Revision.
tags | None | A tags list. See the Labels, Tags & GLYNT Tags section.
glynt_tags | None | A tags list. See Labels, Tags & GLYNT Tags section.
benchmark | true | Boolean. If true, benchmarking will be performed.


## Change a Training Revision

> Modifying the example from the Create a Training Revision section:

```shell
curl --request PATCH \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/training-revisions/0e820894/?advanced=true" \
     --header "Authorization: Bearer abc.123.def" \
     --header "content-type: application/json" \
     --data '{"tags": ["customer_identifier: 7"]}'
```
> On success, this command will return a 201 response with a JSON body structured like this:

```json
{
  "created_at": "2019-01-16T20:24:21.467694Z",
  "updated_at": "2019-01-16T20:25:44.182838Z",
  "url": "https://api.glynt.ai/v6/data-pools/pRvt5/training-revisions/0e820894/?advanced=true",
  "id": "0e820894",
  "label": "Provider A Training Revision",
  "description": "Training Revision for Provider A.",
  "training_set": "https://api.glynt.ai/v6/data-pools/pRvt5/training-sets/17eaa030/?advanced=true", 
  "document_ids": ["1ff52e12", "261525f4"],
  "anomalous_document_ids": [],
  "accepted_document_ids": ["1ff52e12", "261525f4"],
  "force_use_raw_data": false,
  "tokenizer_version": "1.0.0",
  "core_version": "2.0.0",
  "fields": [
    {
      "label": "Total Amount Due",
      "tags": [],
      "glynt_tags": []
    }
  ],
  "status": "Pending",
  "finished": false,
  "benchmark": true,
  "tags": ["customer_identifier: 7"],
  "glynt_tags": [],
  "training_data": {
    "1ff52e12": {
      "tokenized_id": "rekl4S",
      "ground_truths": {
        "Total Amount Due": {
          "content": "$145.87",
          "token_data": {}
        }
      }
    },
    "261525f4": {
      "tokenized_id": "pF443g",
      "ground_truths": {
        "Total Amount Due": {
          "content": "$1.50",
          "token_data": {}
        }
      }
    }
  }
}
```

Modify an existing Training Revision instance.

### HTTP Request
`PATCH <datapool_url>/training-revisions/<training_revision_id>/?advanced=true`

### Request Body Parameters
Only the `label`, `description`, `tags` and `glynt_tags` properties may be changed.


## Delete a Training Revision
```shell
curl --request DELETE \
     --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/training-revisions/0e820894/?advanced=true"
```
> On success, this command will return a 204 response with no JSON body.

Removes a Training Revision and its associated data. Note that this will not
delete Extractions or Extraction Batches related to this Training Revision.

Removes a Training Revision.

This delete will NOT cascade to any related Extractions or Extraction Batches.
Those related resources will instead need to be deleted directly.

<aside class="success">
If you wish to Delete the Extractions and/or Extraction Batches associated with
a Training Revision, consider deleting them before you delete the Training
Revision. This makes it easy to query those resources using the
`training_revision` filter to be sure that all Extractions and Extraction
Batches related to the Training Revision are in fact deleted. Once the Training
Revision is deleted, there is no way to query the API to find those which had
once been associated with a particular deleted Training Revision.
</aside>

### HTTP Request
`DELETE <datapool_url>/training-revisions/<training_revision_id>/?advanced=true`


## Retrieve Benchmark Data
```shell
curl --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/training-revisions/0e820894/benchmark/?advanced=true"
```
> If the Training Revision exists, this command will return a 200 response with
> a JSON body structured like this. Notice that for this example the train and
> test sets are smaller than they would normally be for good system
> performance - this is done only for brevity of the example.

```json
{
  "summary": {
    "ml_accuracy": 1.0,
    "ml_precision": 1.0,
    "ml_recall": 1.0,
    "ml_f1": 1.0
  },
  "fields": {
    "Account Number": {
      "training_document_ids": ["1c30dcd0", "1fe34bce"],
      "test_document_ids": ["2506c02c"],
      "ml_accuracy": 1.0,
      "ml_precision": 1.0,
      "ml_recall": 1.0,
      "ml_f1": 1.0,
      "test_constituents": {
        "2506c02c": {
          "expected": "abc123",
          "extracted": "abc123",
          "outcome": "correct"
        }
      }
    }
  }
}
```

Returns details about the benchmark results for this Training Revision.

If a benchmark has not run (either not yet, or because one was not requested at
Training Revision creation), this will return `{}`.

`summary` aggregates the field-level statistics into a collection of summary
statistics for the entire Training Revision.

`fields` is an object containing detailed statistics for each field, as well as
a breakdown of which document ids were part of the training set and which were
part of the test set for that field.

If a field has less examples than the minimum training set size (see the
[Training Revisions](#training-revisions) introduction for more information),
then the test set will be empty even if a benchmark is requested. In that case,
the field will have a value of `{}`, and it will be excluded from the `summary`
statistics.

Each field has a `test_constituents` object which shows exactly what the
`expected` value was, `extracted` value was, and what the resulting `outcome`
is when comparing those two values.

There are four possible outcome values for the `test_constituents`:

* **correct**: A value was extracted, and it was the expected value.
* **incorrect**: A vaue was extracted, but it was not the expected value.
* **missing**: No value was extracted, but a value was expected.
* **not present**: No value was extracted, and no value was expected.

This endpoint can be requested as a prepared CSV report if the `format=csv`
query parameter is passed. The default report includes the summary statistics,
as well as the statistics for each field. If the `test_constituents` report
type is requested via the `report_type` query parameter, the CSV report will
instead include the details of the `test_constituents`.

### HTTP Request
`GET <datapool_url>/training-revisions/<training_revision_id>/benchmark/?advanced=true`

### Query Parameters
Parameter | Default | Description
--------- | ------- | -----------
format | json | Possible values: csv, json. If csv is requested, the response content-type will be `text/csv` and the raw CSV data will be returned.
report_type | stats | Possible values: stats, test_constituents. This query parameter is ignored unless the format is `csv`.


## Retrieve Training Revision Debug Package

```shell
curl --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/training-revisions/639c2x/debug-package/?advanced=true" \
```
> On success, this command will return a 200 response with a bz2 compressed
> tarball as the body of the response. The content-type of the response will be
> `application/x-tar`.

This staff-only endpoint allows a staff user to download a bz2 compressed
tarball. This package contains a single directory with the name
`<api-environment>_<training_revision_id>_debug_pkg`. This directory
contains one file for each Tokenized in the Training Revision. It also includes
a `payload.json` which is the data that would be sent to core to run this
Training Revision. It also includes a `dev_payload.json` which is a modified
version of `payload.json` which includes relative paths to the tokenizeds and
output destinations. This package, and the `dev_payload.json` in particular,
are intended to be downloaded by core developers and used via the
`dev_interface` to allow them to run training revisions through core's full
machinery in their local dev environments.

If any Documents which were used to create the Training Revision have been
deleted, or if any Tokenizeds that are needed for running the Training Revision
are no longer present, this endpoint will instead return a 400 error with a
detailed message explaining why the system cannot create a debug package for
this Training Revision. The solution to this varies with the error. In the case
of Tokenizeds, you can manually request them to be created for all Documents
for the revision's `tokenizer_version`. If Documents have been deleted, the only
choice is to create a new Training Revision from the parent Training Set (which
may need to have the Documents restored there, as well, before running).

This strict policy of only creating debug packages for Training Revisions which
we can reconstruct as accurately as possible is a defense mechanism to protect
core devs from downloading incomplete and/or "different" debug packages,
leading to pain attempting to reconcile the debug package's local performance
with the actual Training Revision's performance.

### HTTP Request
`GET <datapool_url>/training-revisions/<training_revision_id>/debug-package/?advanced=true`
