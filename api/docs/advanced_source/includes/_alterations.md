# Alterations

## Alterations Overview

Alteration resources are associated with Extractions. They allow you to
overwrite the `raw_results` of an Extraction for a given Field. Alterations
should only be used to alter the results to a value different from how it
appears on the Document. If you wish to correct the results to what appears on
the Document, instead use [Corrections](#corrections-overview). See the
Corrections overview for a more detailed explanation of when to use Corrections
vs Alterations, and why this distinction is important.

Alterations may be used to introduce Fields which are not present on the Document.

Alterations are append-only. They may not be deleted once created. For a given
Field of a given Extraction, the Alteration with the latest `created_at` time
is the active Alteration. Note that even if you wish to revert to the raw
result value, the method to achieve this is to create a new Alteration which
sets the value to the original result content.

For now, Alterations allow two forms of "blank" entry: an empty string, or
`null`. These are allowed so that users can coerce results to look how they
like. There is effectively no meaning to these empty values, so they are
interchangeable, though they will be stored and displayed faithfully. This is a
temporary solution to a larger problem around how we store Corrections and
Alterations, and the overall data pipeline. Expect to see this change in the
future.

Alterations always take precedence over Corrections when an Extraction's
`results` are being compiled for display, even if the Alteration was created
before the most recent Correction.

Alterations store the `user` or `integration` which created it. These are
stored so that a forensic trail is available to track who created each object.
Should the User or Integration which created the Alteration be deleted, both
properties will show as `null` for that Alteration.


## Retrieve all Alterations

```shell
curl --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/alterations/?advanced=true"
```
> This command will return a 200 response with a JSON structured like this:

```json
{
  "count": 2,
  "next": null,
  "previous": null,
  "results": [
    {
      "created_at": "2019-01-16T21:21:30.467694Z",
      "updated_at": "2019-01-16T21:21:30.467694Z",
      "url": "https://api.glynt.ai/v6/data-pools/pRvt5/alterations/db42e50e/?advanced=true",
      "id": "db42e50e",
      "extraction": "https://api.glynt.ai/v6/data-pools/pRvt5/extraction/d0905768/?advanced=true",
      "field": "Total Amount Due",
      "content": "$200.00"
    },
    {
      "created_at": "2019-01-16T21:21:31.228191Z",
      "updated_at": "2019-01-16T21:21:31.228191Z",
      "url": "https://api.glynt.ai/v6/data-pools/pRvt5/alterations/c9qwj2n/?advanced=true",
      "id": "c9qwj2n",
      "extraction": "https://api.glynt.ai/v6/data-pools/pRvt5/extraction/d0905768/?advanced=true",
      "field": "Address",
      "content": "123 Main Street"
    }
  ]
}
```

Lists all Alterations in the Data Pool. Usually this endpoint is called with a
filter to show only Alterations associated with a particular Extraction.

### HTTP Request
`GET <datapool_url>/alterations/?advanced=true`


## Create Alterations

```shell
curl --request POST \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/alterations/?advanced=true" \
     --header "Authorization: Bearer abc.123.def" \
     --header "content-type: application/json" \
     --data '{"extraction":"d0905768","field": "Total Amount Due","content": "$200.00"}'
```
> On success, this command will return a 201 response with a JSON body
> structured like this:

```json
{
  "created_at": "2019-01-16T21:21:30.467694Z",
  "updated_at": "2019-01-16T21:21:30.467694Z",
  "url": "https://api.glynt.ai/v6/data-pools/pRvt5/alterations/db42e50e/?advanced=true",
  "id": "db42e50e",
  "extraction": "https://api.glynt.ai/v6/data-pools/pRvt5/extraction/d0905768/?advanced=true",
  "field": "Total Amount Due",
  "content": "$200.00",
  "user": "https://api.glynt.ai/v6/users/1n21kd/?advanced=true",
  "integration": null
}
```
> Multiple Alterations can be created at once by passing an array:

```shell
curl --request POST \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/alterations/?advanced=true" \
     --header "Authorization: Bearer abc.123.def" \
     --header "content-type: application/json" \
     --data '[\
       {"extraction":"d0905768","field": "Total Amount Due","content": "$200.00"}, \
       {"extraction":"d0905768","field": "Due Date","content": "1/1/2020"} \
     ]'
```


Creates one or several Alteration instances.

Only one Alteration per Extraction/Field combination may be set in a single
call. Attempting to set multiple Alterations for any Extraction/Field
combination will result in an error. Any errors raised by the endpoint will
stop all Alterations from being created. Errors will be returned per Alteration.

<aside class="success">
Remember that Alterations should only be used to alter the results away from
how they appear on the Document. To correct a result to match how it appears on
the Document, use Corrections.
</aside>

### HTTP Request
`POST <datapool_url>/alterations/?advanced=true`

### Request Body Parameters
Parameter | Default | Description
--------- | ------- | -----------
extraction | None | Required. ID of Extraction to which this Alteration applies.
field | None | Required. String label of field for which this Alteration applies. You must be very careful that this exactly matches the field as present in the Extraction's `fields` property. If it does not, the Alteration will not overwrite the existing field. Field names are case sensitive.
content | None | Required. String value of this Alteration. To pass an "empty" result, use `null` or an empty string.


## Retrieve an Alteration

```shell
curl --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/alterations/db42e50e/?advanced=true"
```
> If the Alteration exists, this command will return a 200 response with a
> JSON body structured like this:

```json
{
  "created_at": "2019-01-16T21:21:30.467694Z",
  "updated_at": "2019-01-16T21:21:30.467694Z",
  "url": "https://api.glynt.ai/v6/data-pools/pRvt5/alterations/db42e50e/?advanced=true",
  "id": "db42e50e",
  "extraction": "https://api.glynt.ai/v6/data-pools/pRvt5/extraction/d0905768/?advanced=true",
  "field": "Total Amount Due",
  "content": "$200.00",
  "user": "https://api.glynt.ai/v6/users/1n21kd/?advanced=true",
  "integration": null
}
```

Returns detailed information about an Alteration.

### HTTP Request
`GET <datapool_url>/alterations/<alteration_id>/?advanced=true`

