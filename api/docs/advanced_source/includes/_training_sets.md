# Training Sets

> An example Training Set as viewed in advanced mode:

```json
{
  "created_at": "2019-01-16T20:24:21.467694Z",
  "updated_at": "2019-01-16T20:25:44.281802Z",
  "url": "https://api.glynt.ai/v6/data-pools/pRvt5/training-sets/3eb5e46c/?advanced=true",
  "id": "3eb5e46c",
  "label": "Electricty Company Inc.",
  "description": "Training Set for extracting data from Electrity Company Inc. invoices.",
  "documents": [
    "https://api.glynt.ai/v6/data-pools/pRvt5/documents/484b12ea/?advanced=true",
    "https://api.glynt.ai/v6/data-pools/pRvt5/documents/4d77e5a4/?advanced=true",
    "https://api.glynt.ai/v6/data-pools/pRvt5/documents/5110512e/?advanced=true",
  ],
  "tokenizer_version": "1.0.0",
  "tags": [],
  "glynt_tags": [],
  "fields": [
    "https://api.glynt.ai/v6/data-pools/pRvt5/fields/61c2a9ae/?advanced=true",
    "https://api.glynt.ai/v6/data-pools/pRvt5/fields/6639574e/?advanced=true"
  ],
  "training_revisions": [
    "https://api.glynt.ai/v6/data-pools/pRvt5/training_revisions/6d495bc4/?advanced=true",
  ],
  "advisories": [],
  "training_requested": false,
  "data_type_config": {
    "date": {
      "order": "DMY"
    },
    "amount": {
      "decimalSymbol": "."
    }
  },
  "using_global_data_type_config": false,
  "document_schema": "UtilityBill"
}
```

## Training Sets Overview

Training Sets are significantly expanded in advanced mode, which adds
properties for `tokenizer_version`, `fields`, `training_revisions`,
`training_requested`, `data_type_config` and `advisories`. In addition, advanced mode allows for the
creation and deletion of Training Sets.

In advanced mode, TrainingSets gain the `/request-training/` endpoint.

In advanced mode, TrainingSets gain the `/field-distribution/` endpoint.

Common causes for advisories on Training Sets are as follows:

* When a Field associated with the Training Set has an advisory, the Training
  Set itself gets an advisory.

## Create a Training Set

```shell
curl --request POST \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/training-sets/?advanced=true" \
     --header "Authorization: Bearer abc.123.def" \
     --header "content-type: application/json" \
     --data '{"label":"Widgets Anonymous", "documents":["88349200","8f207d7c","92eeb4a0"]}'
```

> On success, this command will return a 201 response with a JSON body structured like this:

```json
{
  "created_at": "2019-01-16T20:24:21.467694Z",
  "updated_at": "2019-01-16T20:24:21.467694Z",
  "url": "https://api.glynt.ai/v6/data-pools/pRvt5/training-sets/c38518fc/?advanced=true",
  "id": "c38518fc",
  "label": "Widgets Anonymous",
  "description": "",
  "documents": [
    "https://api.glynt.ai/v6/data-pools/pRvt5/documents/88349200/?advanced=true",
    "https://api.glynt.ai/v6/data-pools/pRvt5/documents/8f207d7c/?advanced=true",
    "https://api.glynt.ai/v6/data-pools/pRvt5/documents/92eeb4a0/?advanced=true"
  ],
  "tokenizer_version": "1.0.0",
  "tags": [],
  "glynt_tags": [],
  "fields": [],
  "training_revisions": [],
  "training_requested": false,
  "advisories": [],
  "data_type_config": {
    "date": {
      "order": "YMD"
    }
  },
  "document_schema": "UtilityBill"
}
```

Creates a new Training Set instance.  

As a convenience, a Tokenized will be created for each Document in the set,
as needed, when a Training Set is created.

### HTTP Request

`POST <datapool_url>/training-sets/?advanced=true`

### Request Body Parameters

Parameter | Default | Description
--------- | ------- | -----------
label | None | Required. String label for the Training Set. See the Labels, Tags, and GLYNT Tags section. Must be unique within the Data Pool.
description | None | Verbose description of Training Set. Max of 255 characters.
documents | None | List of Document IDs to associate with the Training Set. Maximum of 50.
tokenizer_version | "default" | String. "default" or tokenizer version to use. Setting to null or "default" will defer first to the default setting on the Data Pool. If no default is found it then defers to the default setting on the Organization. Finally, if there are no defaults on either Data Pool or Organization, the system default version is used.
tags | None | A tags list. See the Labels, Tags & GLYNT Tags section.
glynt_tags | None | A tags list. See Labels, Tags & GLYNT Tags section.
data_type_config | None | The configurations for the fields with data_type other than string defined. The configuration propagation happens in two scenarios for the fields with data_type matching the configuration: when they are created; when this property is updated, the fields' configurations are overridden. Check [Data types](#data-types) for the valid options
using_global_data_type_config | False | Read only property that indicates when all the fields have the configurations defined by the `data_type_config`

## Change a Training Set

> Modifying the example from the Create a Training Set section:

```shell
curl --request PATCH \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/training-sets/c38518fc/?advanced=true" \
     --header "Authorization: Bearer abc.123.def" \
     --header "content-type: application/json" \
     --data '{"label":"A New Label"}'
```

> On success, this command will return a 201 response with a JSON body structured like this:

```json
{
  "created_at": "2019-01-16T20:24:21.467694Z",
  "updated_at": "2019-01-16T21:10:22.383282Z",
  "url": "https://api.glynt.ai/v6/data-pools/pRvt5/training-sets/c38518fc/?advanced=true",
  "id": "c38518fc",
  "label": "A New Label",
  "description": "",
  "documents": [
    "https://api.glynt.ai/v6/data-pools/pRvt5/documents/88349200/?advanced=true",
    "https://api.glynt.ai/v6/data-pools/pRvt5/documents/8f207d7c/?advanced=true",
    "https://api.glynt.ai/v6/data-pools/pRvt5/documents/92eeb4a0/?advanced=true",
  ],
  "tokenizer_version": "1.0.0",
  "tags": [],
  "glynt_tags": [],
  "fields": [],
  "training_revisions": [],
  "advisories": [],
  "training_requested": false,
  "data_type_config": {
    "date": {
      "order": "YMD"
    }
  }
}
```

Modifies an existing Training Set instance.

As a convenience, a Tokenized will be created for each Document in the set,
as needed, when a Training Set's related `documents` or `tokenizer_version`
is changed.

### HTTP Request

`PATCH <datapool_url>/training-sets/<training_set_id>/?advanced=true`

### Request Body Parameters

All properties which can be passed at creation can be passed during
modification. See the Create a Training Set section fore details.
Additionally, `training_requested` property can also be passed during
modification. It should be a boolean value if provided.

<aside class="success">
Note that changing the tokenizer version may cause many new Ground Truth
<code>token_data</code> advisories on the related Ground Truth instances.
</aside>

## Delete a Training Set

```shell
curl --request DELETE \
     --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/training-sets/e13374ac/?advanced=true"
```

> On success, this command will return a 204 response with no JSON body.

Removes a Training Set.

This delete will cascade down to any associated [Field](#field) and [Ground
Truth](#ground-truth) resources, deleting those as well.

This delete will NOT cascade to any related Training Revisions or Documents.
Those related resources will instead need to be deleted directly.

<aside class="success">
If you wish to Delete the Documents and/or Training Revisions associated with a
Training Set, consider deleting them before you delete the Training Set. This
makes it easy to query those resources using the `training_set` filter to check
that all related objects are deleted. Once the Training Set is deleted, there
is no way to then query Documents or Training Revisions to find those which had
once been associated with a particular deleted Training Set.
</aside>

### HTTP Request

`DELETE <datapool_url>/training-sets/<training_set_id>/?advanced=true`

## Request Training

```shell
curl --request GET \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/training-sets/c38518fc/request-training/?advanced=true" \
     --header "Authorization: Bearer abc.123.def"
```

> On success, this command will return a 200 response with a JSON body of the
TrainingSet with `training_requested` property set to `true`.

Request training for the training set.

### HTTP Request

`GET <datapool_url>/training-sets/<training_set_id>/request-training/?advanced=true`

## Retrieve Training Set Field Distribution Information

```shell
curl --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/training-sets/639c2x/field-distribution/?advanced=true" \
```

> On success, this command will return a 201 response with a JSON body
> structured like this:

```json
{
  "training_set_id": "daknf2",
  "training_set_label": "Sample Training Set",
  "document_count": 12,
  "fields": {
    "Account Number": {
      "responses": 2,
      "response_rate": 100
    },
    "Line Item One": {
      "responses": 1,
      "response_rate": 50
    }
  },
  "documents": {
    "hg8rr1": {
      "label": "Sample Document 1",
      "marking_url": "http://ui.glynt.ai/data-pools/Dnais5/mark/639c2x/document/hg8rr1",
      "fields": {
        "Account Number": {
          "content": "abc123",
          "tags": ["Sample Tag 1", "Sample Tag 2"],
          "glynt_tags": []
        }
      }
    },
    "nQkaL1": {
      "label": "Sample Document 2",
      "marking_url": "http://ui.glynt.ai/data-pools/Dnais5/mark/639c2x/document/nQkaL1",
      "fields": {
        "Account Number": {
          "content": "def456",
          "tags": ["Sample Tag 1", "Sample Tag 2"],
          "glynt_tags": []
        },
        "Line Item One": {
          "content": "$100",
          "tags": [],
          "glynt_tags": ["Sample Glynt Tag"]
        }
      }
    }
  }
}
```

Returns information about the distribution of ground truth marks in a training set.

### HTTP Request

`GET <datapool_url>/training-sets/<training_set_id>/field-distribution/?advanced=true`

### Query Parameters

Parameter | Default | Description
--------- | ------- | -----------
format | json | Possible values: csv, json. If csv is requested, the response content-type will be `text/csv` and the raw CSV data will be returned.
report_type | stats | Possible values: long, wide, stats. This query parameter is ignored unless the format is `csv`. `stats` returns a summary view of the data. `long` returns a field-centric view of the data. `wide` returns a document-centric view of the data. `long` report is only availabe for download by staff users.

<aside class="notice">
NOTE: JSON reports may be integrated against and will remain backwards compatible. Keys may be added
to the JSON report in the future. CSV reports are intended for human use and may change without notice.
</aside>

## Import fields from another training-set

```shell
curl --request POST \
     --url "https://api.glynt.ai/v6/import-field-list/?advanced=true" \
     --header "Authorization: Bearer abc.123.def" \
     --header "Content-Type: application/json" \
     --data '{"source_training_set_id": "998518fc", "dest_training_set_id": "6655105ve"}'
```

> On success, this command will return a 200 response with JSON body of the TrainingSet with

``` json
{
  "imported_fields": 22,
  "from": {"id": "998518fc", "name": "training set 1"},
  "to": {"id": "c38518fc", "name": "training set 2"},
}
```

Import all fields from another training-set in the same datapool

### HTTP Request

`POST v6/import-field-list/?advanced=true`

### Request Body Parameters

Parameter | Default | Description
--------- | ------- | -----------
source_training_set_id | None | training-set id from which the fields
will be copied from
dest_training_set_id | None | training-set id from which the fields
will be copied to

## List training set from data-pool

```shell
curl --request GET \
     --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/data-pools/<data_pool_id>/public-training-set-list/?advanced=true"
```

This endpoint can list training sets like the main #retrieve-all-training-sets operations.
But this endpoint has visibility of training sets on public data-pools

> Look for the basic documentation of #retrieve-all-training-sets for the response structure.

### HTTP Request

`GET v6/data-pools/<datapool_id>/public-training-set-list/advanced=true`

## Generate pages for documents

```shell
curl --request POST \
     --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/training-sets/998518fc/pages/?advanced=true"
```

> On success, this command will return a 204 response with no JSON body.

This endpoint generates the images for all pages of the documents in the training set.

The behavior of this endpoint is inherited from the [Generate pages](#generate-document-page-images)
with the exception that 404 it's returned only if the training_set doesn't exist.

<aside class="info">
Check the documentation of <a href='#generate-document-page-images'>page generation</a> for more details.
</aside>

### HTTP Request

`POST v6/data-pools/<datapool_id>/training-sets/<training_set_id>/pages/?advanced=true`

## Tokenize all documents in the training set

```shell
curl --request POST \
     --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/training-sets/998518fc/refresh-tokenizeds/?advanced=true"
```

> On success, this command will return a 204 response with no JSON body.

This endpoint will attempt to tokenize all documents in this training set, with the current tokenizer specified for that training set.

The behavior it's inherited from the [retrieve and refresh endpoint](#retrieve-and-refresh-a-tokenized).
This endpoint will attempt to execute the operations of [retrieve and refresh](#retrieve-and-refresh-a-tokenized) with the query parameter `refresh`
on all tokenized of in the training set with the current `tokenizer_version`.

### HTTP Request

`POST v6/data-pools/<datapool_id>/training-sets/<training_set_id>/refresh-tokenizeds/?advanced=true`
