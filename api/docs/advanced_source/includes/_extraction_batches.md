# Extraction Batches

## Extraction Batches Overview

In advanced mode, Extraction Batches gain the following functionality.

Extraction Batches may be created using a Training Revision instead of a
Training Set. See the Extraction section for more details.

Extraction Batches gain the benchmark property, which is a boolean indicating
if this Extraction Batch was created automatically by the system as part of a
Training Revision's benchmarking process. If true, this Extraction Batch will
not be readable/writable in basic mode, and all child Extractions will have
this flag set as well.

The Extraction Batch detail view gains the `extractions_by_training_set`
property. See the Retrieve an Extraction Batch section for more information.

Extraction Batches reveal their internal UUID. Note that this is different from
the UUID generated for the default label value.

Advanced note about distribution reports: the fields as they appear on the csv
reports reflect the ordering of the fields as set on the training set used to
power the extractions.

## Create an Extraction Batch

```shell
curl --request POST \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/extraction-batches/?advanced=true" \
     --header "Authorization: Bearer abc.123.def" \
     --header "content-type: application/json" \
     --data '{"training_revision":"639c2be0","documents":["69c439cc","69c43c74","7188eb44"]}'
```

> On success, this command will return a 201 response with a JSON body
> structured like this:

```json
{
  "created_at": "2018-02-17T14:54:30.699864Z",
  "updated_at": "2018-02-17T14:54:30.699864Z",
  "url": "https://api.glynt.ai/v6/data-pools/pRvt5/extraction-batches/639c2be0/?advanced=true",
  "id": "639c2be0",
  "uuid": "921526c3-e5da-4af0-b462-e6734203c6bd",
  "label": "December 2018 Invoices",
  "training_set": "https://api.glynt.ai/v6/data-pools/pRvt5/training-sets/774554c8/?advanced=true",
  "training_revision": "https://api.glynt.ai/v6/data-pools/pRvt5/training-revisions/639c2be0/?advanced=true",
  "documents": [
    "https://api.glynt.ai/v6/data-pools/pRvt5/documents/69c439cc/?advanced=true",
    "https://api.glynt.ai/v6/data-pools/pRvt5/documents/69c43c74/?advanced=true",
    "https://api.glynt.ai/v6/data-pools/pRvt5/documents/7188eb44/?advanced=true",
  ],
  "extractions": [],
  "status": "Pending",
  "finished": false,
  "classify": false,
  "verified": {"is_verified": false, "updated_at": null, "expired": false},
  "tags": [],
  "glynt_tags": [],
  "benchmark": false
}
```

In advanced mode the option to pass `training_revision` instead of
`training_set` is available. See the [Create an
Extraction](#create-an-extraction-batch) section for more details.

### HTTP Request

`POST <datapool_url>/extraction-batches/?advanced=true`

### Request Body Parameters

Parameter | Default | Description
--------- | ------- | -----------
documents | None | Required. List of Document IDs to extract data from. Minimum of 1, maximum of 1000 Document IDs.
label | A UUID | A string label for the Extraction Batch. See the Labels, Tags & GLYNT Tags section. Must be unique within a Data Pool.
training_set | None | Required if `training_revision` is not passed. ID of the Training Set whose latest successful Training Revision will power the extractions.
training_revision | None | Required if `training_set` is not passed. ID of the Training Revision which will power the extractions.
classify | False | Present for legacy reasons. The only legal value is `false`.
tags | [] | A tags list. See the Labels, Tags & GLYNT Tags section.
glynt_tags | [] | A tags list. See the Labels, Tags & GLYNT Tags section.

### HTTP Request

`GET <datapool_url>/extraction-batches/`

## Retrieve an Extraction Batch

```shell
curl --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/extraction-batches/23d69fec/?advanced=true"
```

> If the Extraction Batch exists, this command will return a 200 response with
> a JSON body structured like this:

```json
{
  "created_at": "2019-01-16T20:24:21.467694Z",
  "updated_at": "2019-01-16T20:34:00.100103Z",
  "url": "https://api.glynt.ai/v6/data-pools/pRvt5/extraction-batches/23d69fec/?advanced=true",
  "id": "23d69fec",
  "label": "December 2018 Invoices",
  "training_set": null,
  "documents": [
    "https://api.glynt.ai/v6/data-pools/pRvt5/documents/4c543d94/?advanced=true",
    "https://api.glynt.ai/v6/data-pools/pRvt5/documents/56d03d54/?advanced=true",
    "https://api.glynt.ai/v6/data-pools/pRvt5/documents/5d2fb5da/?advanced=true",
  ],
  "extractions": [
    "https://api.glynt.ai/v6/data-pools/pRvt5/extractions/60a8332c/?advanced=true",
    "https://api.glynt.ai/v6/data-pools/pRvt5/extractions/6dd93e88/?advanced=true",
    "https://api.glynt.ai/v6/data-pools/pRvt5/extractions/73847cc6/?advanced=true",
  ],
  "extractions_by_training_set": {
    "https://api.glynt.ai/v6/data-pools/pRvt5/training_sets/21nefD/?advanced=true": [
      "https://api.glynt.ai/v6/data-pools/pRvt5/extractions/60a8332c/?advanced=true",
      "https://api.glynt.ai/v6/data-pools/pRvt5/extractions/6dd93e88/?advanced=true",
    ],
    "None": [
      "https://api.glynt.ai/v6/data-pools/pRvt5/extractions/73847cc6/?advanced=true",
    ]
  },
  "status": "Success",
  "finished": true,
  "verified": {"is_verified": false, "updated_at": null, "expired": false},
  "tags": [],
  "glynt_tags": []
}
```

In advanced mode, the `extractions_by_training_set` property is returned when
retrieving an Extraction Batch. This property maps each Extraction to the
Training Set which was used to execute the Extraction. It is most useful when
the Extraction Batch was created without a Training Set or Revision, where each
Extraction went through the classification process. The special `"None"` group
is all Extractions which have no associated Training Set. This could happen,
for example, if the Training Set was deleted, or if the classification process
failed to find a suitable Training Set for that Extraction.

### HTTP Request

`GET <datapool_url>/extraction-batches/<extraction_batch_id>/`

## Clean and Normalize an Extraction Batch

```shell
curl --request POST \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/extraction-batches/jDZAv/clean/?advanced=true" \
     --header "Authorization: Bearer abc.123.def" \
     --header "content-type: application/json"
```

> On success, this command will return a 204 response with no JSON body.

Extractions are automatically cleaned and normalized by default.
However, in the event that one needs to force a re-cleaning and re-normalization
of the batch, this endpoint will invoke those processes as if the
batch just completed and needs to be cleaned and normalized. All other
post-cleaning processes (e.g. transformation) will also run.

### HTTP Request

`POST <datapool_url>/extraction-batches/<extraction_id>/clean/?advanced=true`

## Verify an Extraction Batch

```shell
curl --request POST \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/extraction-batches/jDZAv/verify/?advanced=true" \
     --header "Authorization: Bearer abc.123.def" \
     --header "content-type: application/json" \
     --data '{"is_verified":true}'
```

> On success, this command will return a 204 response with no JSON body.

Marks all extractions in the batch as verified.
This endpoint has an equivalent behavior to the [verify extraction](#verify-an-extraction) marking all extractions in the batch.

### HTTP Request

`POST <datapool_url>/extraction-batches/<extraction_batch_id>/verify/?advanced=true`

### Request Body Parameters

Parameter | Default | Description
--------- | ------- | -----------
is_verified | None | Boolean

## Generate pages for documents

```shell
curl --request POST \
     --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/data-pools/pRvt5/extraction-batches/jDZAv/pages/?advanced=true"
```

> On success, this command will return a 204 response with no JSON body.

This endpoint generates the images for all pages of the documents in the extraction batch.

The behavior of this endpoint is inherited from the [Generate pages](#generate-document-page-images)
with the exception that 404 it's returned only if the extraction batch doesn't exist.

<aside class="info">
Check the documentation of <a href='#generate-document-page-images'>page generation</a> for more details.
</aside>

### HTTP Request

`POST v6/data-pools/<datapool_id>/extraction-batches/<training_set_id>/pages/?advanced=true`
