---
title: GLYNT API - Advanced Mode

language_tabs: # must be one of https://git.io/vQNgJ
  - shell

toc_footers:
  - <a href='https://github.com/lord/slate'>Documentation Powered by Slate</a>

includes:
  - user_info
  - users
  - integrations
  - organizations
  - data_pools
  - documents
  - tokenizeds
  - tokenizer_versions
  - training_sets
  - fields
  - ground_truths
  - core_versions
  - training_revisions
  - extractions
  - extraction_batches
  - corrections
  - normalizations
  - alterations
  - data_types
  - document_schemas

search: true
---
<aside class="notice">
This is prerelease documentation for a service in preview release. It is
subject to change.
</aside>

# Definitions

* **Correction**: Improved data which corrects errors in the raw results
  returned by an Extraction. 
* **Normalization**: A system-generated presentation of the raw result that
  shows the result in a standard form.
* **Field**: A label "key" for identifying data - for example "Account Number"
  or "Total Amount Due". Note that this has nothing to do with how the value is
  found - it is simply a label to give semantic meaning to some piece of data
  which appears on Documents. Fields are defined on a Training Set, and are
  used to give meaning to the extracted data from Documents.
* **Ground Truth**: In the context of GLYNT, a Ground Truth resource is created
  for each field on each Document in a Training Set. It is the true value for
  the field as it appears on the Document.
* **Tokenized**: Sometimes called a "tokenized representation", a Tokenized
  resource is a structured representation of a Document. Most importantly, it
  contains an array of pages, each of which is an array of the Tokens which
  appear on that page.
* **Training Revision**: An "execution" of a Training Set. A Training Set may
  have multiple Training Revisions. A Training Revision is what is passed to an
  Extraction Batch in order to configure what data is extracted from the
  Documents in the Batch.

# Advanced Mode Introduction

This is the internal half of the GLYNT API documentation, and is intended to
supplement the primary, public GLYNT API documentation. It assumes the reader
has access to and an understanding of the primary documentation.

> Querying Data Pools in advanced mode:

```shell
curl "https://api.glynt.ai/api/v6/data-pools/?advanced=true"
```

The GLYNT API, invoked as described in the public documentation, is actually a
subset of the capabilities of the full GLYNT API. Advanced mode is activated by
including the `advanced=true` query parameter to API requests.

<aside class="warning">
There is nothing to stop an external end user from invoking the API in advanced
mode. This is extremely important to remember - there is nothing "secure" about
the advanced mode. It is ONLY a mechanism to hide functionality from our
end users so that they are not confused by the complicated functionality of the
API, or so that we can release half-baked functionality which our first-party
tools can use on behalf of the user while we finalize the interface.
</aside>

If a user tinkers around and discovers advanced mode, we don't necessarily
mind. The party-line is to tell end users who inquire about advanced mode that
it is intended for internal use only, that they should not utilize that
functionality, that the functionlity can change or disappear at any time, and
that they will be responsible if they break something using advanced mode.

The rest of this documentation explains differences between advanced and basic
API operation. This includes both expanded endpoints and properties for basic
Resources, as well as advanced-only Resource types.

## ES Flag

> Querying a Ground Truth with the ES flag set:

```shell
curl "https://api.glynt.ai/api/v6/data-pools/ght28f/ground-truths/2nkka1/?advanced=true&es=1"
```

The ES flag can only be set in advanced mode, and only by a staff user. If
passed in basic mode and/or by a non-staff user, it is silently ignored. When
`es=1` is passed in the query parameters, some resources will return extra
properties and sometimes allow those extra properties to be edited. The
detailed documentation for each resource will specify any such properties.


# Format Query Parameter

Some endpoints allow a `format=<desired_format>` query parameter to be passed.
For example, some data endpoints allow CSV views of the data, which would be
requested with `format=csv`.  The default in all cases is `json`. Supported
alternate formats are specified by the endpoints that support them.


# Advisories

Some resources have an `advisories` property, which will be an empty list if no
issues are detected with the resource. If there are detected issues,
`advisories` will be populated with a list of strings which indicate the
issue(s) and, when possible, a suggested course of action.

You cannot alter `advisories` except by rectifying the issue they have
detected.


