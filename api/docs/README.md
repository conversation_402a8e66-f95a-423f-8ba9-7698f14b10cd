The "public docs" are in the `source` directory. They are the publicly visible
docs, which are automatically compiled and hosted with the API.

The "advanced mode docs" are in the `advanced_source` directory. These document
the additional functionality which is enabled when viewing the API with the
`advanced` flag set, and how to set that flag. They are also compiled and
hosted with the API, at a different url than the public docs.

To edit the docs, edit the files in the corresponding `source` directory.

To use the development apidocs server(s) or to build the docs, you will need
Docker installed. Then, you can run `./server.sh` or `./advanced_server.sh` and
visit localhost:4567 or localhost:4568 respectively to see a live build of the
docs.

To build a static version of the docs for sharing, use `./build.sh`, which will
build both `source` directories into the corresponding `build` directories.
`build.sh` may prompt for your password in order to set the ownership of the
files in the build directory to your user.  This is required, otherwise it will
not be possible to build again on your machine without first emptying out the
build directories. This is a hack, and we'll improve the docker image in the
future so that this becomes unnecessary.

If the `-t` flag is passed to `build.sh`, then the built html will be a django
template instead of standalong html. This is used by the app deployment scripts
to create the hosted docs.
