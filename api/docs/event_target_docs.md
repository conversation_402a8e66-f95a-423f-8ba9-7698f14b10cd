# Events

Events track the actions taken by the Users and M2M Integrations within an
organization. These are used primarily for billing purposes. They are created
automatically by the system, and are read-only via the API.

Some properties of Events are common across all Event types. They are described
below:

### Common Event Properties
Parameter | Description
--------- | -----------
url | URL to detail view of the Event.
id | ID of the Event.
organization | Link to the Organization to which this Event belongs.
data_pool_id | ID of Data Pool to which this Event belongs. Will not be present if the Event is not specific to a Data Pool.
created_at | Datetime of when the Event occurred.
user_id | ID of the User which initiated the event. Will not be present if not applicable
integration_id | ID of the Integration which initiated the event. Will not be present if not applicable. Note that this is NOT the client_id of the Integration.
type | Type of the event (e.g. `extraction`).

Each Event type has its own additional details. The event types and their
unique properties are listed below:

### Extraction Event
type: `extraction`

Parameter | Description
--------- | -----------
extraction_id | ID of the Extraction which caused this Event.
n_pages | Number of pages processed to service this Extraction.
n_extracted_fields | Number of Fields which had data successfully extracted.


## Retrieve all Events

```shell
curl --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/organizations/gaTT6/events/?advanced=true"
```
> This command will return a 200 response with a JSON structured like this:

```json
{
  "count": 2,
  "next": null,
  "previous": null,
  "results": [
    {
      "created_at": "2019-01-16T21:21:30.467694Z",
      "url": "https://api.glynt.ai/v6/organizations/gaTT6/events/34f04172/?advanced=true",
      "id": "34f04172",
      "organization": "https://api.glynt.ai/v6/organizations/gaTT6/?advanced=true",
      "data_pool_id": "hakn88",
      "user_id": "2ksnn2",
      "type": "extraction",
      "extraction_id": "1ns00",
      "n_pages": 2,
      "n_extracted_fields": 10
    },
    {
      "created_at": "2019-01-16T21:21:30.928222Z",
      "url": "https://api.glynt.ai/v6/organizations/gaTT6/events/d9djksns/?advanced=true",
      "id": "d9djksns",
      "organization": "https://api.glynt.ai/v6/organizations/gaTT6/?advanced=true",
      "data_pool_id": "hakn88",
      "user_id": "2ksnn2",
      "type": "extraction",
      "extraction_id": "AA211",
      "n_pages": 2,
      "n_extracted_fields": 11
    }
  ]
}
```

Lists all Events for an Organization.

### HTTP Request
`GET <organization_url>/events/?advanced=true`


## Retrieve an Event

```shell
curl --header "Authorization: Bearer abc.123.def" \
     --url "https://api.glynt.ai/v6/organizations/gaTT6/events/34f04172/?advanced=true"
```
> If the Event exists, this command will return a 200 response with a JSON
> structured like this:

```json
{
  "created_at": "2019-01-16T21:21:30.467694Z",
  "url": "https://api.glynt.ai/v6/organizations/gaTT6/events/34f04172/?advanced=true",
  "id": "34f04172",
  "organization": "https://api.glynt.ai/v6/organizations/gaTT6/?advanced=true",
  "data_pool_id": "hakn88",
  "user_id": "2ksnn2",
  "type": "extraction",
  "extraction_id": "1ns00",
  "n_pages": 2,
  "n_extracted_fields": 10
}
```

Returns detailed information about an Event.

### HTTP Request
`GET <organization_url>/events/<event_id>/?advanced=true`


