# Shared Infrastructure Provisioning Scripts

This directory contains the provisioning scripts responsible for creating the
shared aspects of the GLYNT system.

This script creates an AWS Virtual Private Cloud (VPC), which all Glynt projects
should deploy to.

The RDS database instance is placed in a private subnet configured via
its security group to only accept incoming traffic from instances which are a
part of the database client security group which this provisioner makes
available, and other projects should attach to their db-accessible instances
(see terraform). 

The public subnet also includes a NAT instance which is a gateway for the RDS
instance to access the internet for things like automatic updates.  This does
not allow incoming traffic to the database. 

The RDS database instance is configured (via terraform) to create automatic
point-in-time backups with a retention period of 14 days.  Backups can be
restore to the nearest second.  To restore from a backup, simply navigate to the
RDS section of the aws console, select the target database instance, click on
`Actions -> "Restore to point in time"` and follow the wizard.  RDS is also
configured to maintain a cross-zone replica which is used for automatic failover
if one of the follow occurs:

- An Availability Zone outage
- The primary DB instance fails
- The DB instance's server type is changed
- The operating system of the DB instance is undergoing software patching

## DNS name resolution

There is DNS resolution in the [VPC](https://github.com/PaceWebApp/glynt-api/blob/master/__provision__/terraform/modules/network/main.tf)
of each environment (sandbox, stage and, production). The top-level DNS zone is
`private.glynt.ai`. And the format of each records is:

```text
[server-name].[environment].private.glynt.ai
```

This zone is a [AWS private hosted DNS zone](https://docs.aws.amazon.com/Route53/latest/DeveloperGuide/hosted-zones-private.html). So the records are not resolvable
from the internet.

The DNS zone is created by the [common infrastructure](https://github.com/PaceWebApp/glynt-api/tree/master/__provision__/terraform/modules/dns). The rest of
the applications (boxcars, api, core) make use of this zone to add, update or remove records.

The [shared mysql database](https://github.com/PaceWebApp/glynt-api/blob/master/__provision__/terraform/modules/storage/main.tf) is the only
server using a `CNAME` instead o an `A` record because the RDS IP addresses
and instances are dynamic. So the `CNAME` points to the `A` record provided by AWS.

* shared-db.sandbox.private.glynt.ai
* shared-db.stage.private.glynt.ai
* shared-db.production.private.glynt.ai

### How to refresh services DNS records

If for some reason any of the servers change its IP address, run the following commands:

1) Go to the terraform folder:

```bash
$ cd /path/to/the/project/and/corresponding/terraform/environment/folder/
```

2) List all planned changes and filter all the DNS updates:

```bash
$ terraform plan |grep route53 |grep -E "update|created"
  # module.dns.aws_route53_record.servers["core-access-node.sandbox.private.glynt.ai"] will be created
  # module.dns.aws_route53_record.servers["core-transfer-node.sandbox.private.glynt.ai"] will be created
```

3) Apply the changes:

To apply the changes copy al the resources and use as a parameter for `apply`. All the double quotes (`"`) must
be escaped using backslack (`\`).

```bash
$ terraform apply \
  -target module.dns.aws_route53_record.servers[\"core-access-node.sandbox.private.glynt.ai\"]
  -target module.dns.aws_route53_record.servers[\"core-transfer-node.sandbox.private.glynt.ai\"]
```

## Auth0

Auth0 is our auth provider. Currently Auth0 is mostly provisioned manually when
an environment is first set up. Although Auth0 is an infrastructure concern,
the majority of the documentation around it still resides in the `/api/`
project README. This should be moved to here instead, to better reflect the
shared nature of Auth0. For now, refer to the documentation in the `/api/`
project for most information.

The one element of Auth0 which is automatically provisioned by the
infrastructure pipeline is a small redis server which allows inbound
connections from the Auth0 Outbound IP list:
https://auth0.com/docs/security/allowlist-ip-addresses. 
