terraform {
  backend "s3" {
    bucket  = "tfstate.baja.wattzon"
    key     = "baja/glynt-infrastructure/stage/main.tfstate"
    region  = "us-west-2"
    encrypt = true
  }
  required_version = ">=0.12.21"
  required_providers {
    archive = {
      source  = "hashicorp/archive"
      version = "~> 2.1"
    }
    aws = {
      source  = "hashicorp/aws"
      version = "~> 2.70.0"
    }
    cloudflare = {
      source  = "cloudflare/cloudflare"
      version = "~> 2.18.0"
    }
    auth0 = {
      source  = "auth0/auth0"
      version = "~> 0.14"
    }
  }
}

variable "environment" {
  default = "stage"
}

variable "domain" {
  default = "private.glynt.ai"
}

locals {
  private_dns_zone = "${var.environment}.${var.domain}"
}

variable "ebs_snapshot_schedule" {
  default = "0300;15;US/Pacific;wed,sat"
}

variable "uptime_schedule_tagname" {
  default = "uptime_schedule"
}

# vvvvv from env vvvvv

variable "aws_region" {
  type = string
}

variable "db_root_username" {
  type = string
}

variable "db_root_password" {
  type = string
}

variable "auth0_domain" {
  type = string
}

variable "auth0_client_id" {
  type = string
}

variable "auth0_client_secret" {
  type = string
}

# unused, but still present in CI config. See GL-6108
variable "auth0_redis_password" {
  type = string
}

# ^^^^^^ from env ^^^^^

provider "aws" {
  region  = var.aws_region
  version = "~> 2.0"
}

provider "auth0" {
  domain        = var.auth0_domain
  client_id     = var.auth0_client_id
  client_secret = var.auth0_client_secret
}

module "public_dns" {
  source      = "../../modules/public_dns"
  environment = var.environment
  dns_records = []
}

module "identity" {
  source      = "../../modules/identity"
  environment = var.environment
}

module "network" {
  source                  = "../../modules/network"
  environment             = var.environment
  subnet_region           = var.aws_region
  ssh_public_key          = "${file("files/key.pub")}"
  uptime_schedule_tagname = var.uptime_schedule_tagname
  public_subnet_route_table_id = "rtb-04cea4a7fcd34b4c4"
  nat_gateway_route_table_id = "rtb-0876ec6af0154e467"
  router_subnet_route_table_id = "rtb-0498431fba29ca127"
  nat_eip = "eipalloc-039153f5cb63eca14"
}

module "kms_keys" {
  source        = "../../modules/kms_keys"
  kms_users     = ["<EMAIL>", "<EMAIL>", "<EMAIL>"]
  kms_key_alias = "${var.environment}-encrypt-slack-webhook-url"
}

module "vpc_flow_logs" {
  source         = "../../modules/vpc_flow_logs"
  retention_days = 14
  vpc_id         = module.network.vpc_id
  environment    = var.environment
}

module "storage" {
  source                   = "../../modules/storage"
  environment              = var.environment
  aws_region               = var.aws_region
  db_instance_class        = "db.m5.xlarge"
  db_iops                  = 3000
  db_server_sg_id          = module.network.db_server_sg_id
  db_username              = var.db_root_username
  db_password              = var.db_root_password
  db_allocated_storage     = 200
  db_max_allocated_storage = 500
  db_subnet_group_name     = module.network.db_subnet_group_name
  uptime_schedule_tagname  = var.uptime_schedule_tagname
  iam_policy_arn = "arn:aws:iam::986171576906:policy/manage_shared_db-stage"
}

module "auth0" {
  source              = "../../modules/auth0"
  environment         = var.environment
  auth0_domain        = var.auth0_domain
  auth0_client_id     = var.auth0_client_id
  auth0_client_secret = var.auth0_client_secret
}

module "dns" {
  source           = "../../modules/dns"
  environment      = var.environment
  vpc_id           = module.network.vpc_id
  private_dns_zone = local.private_dns_zone
  dns_records = list(
    {
      name  = "shared-db.${local.private_dns_zone}"
      value = module.storage.db_address
      type  = "CNAME"
    }
  )
}

module "alerts" {
  source                    = "../../modules/alerts"
  environment               = var.environment
  alarm_check_rate          = "3 minutes"
  slack_channel             = "glynt_monitoring_${var.environment}"
  shared_db_identifier      = module.storage.shared_db_identifier
  kms_encrypted_webhook_url = "AQICAHg5R1pTjxu7M3226OwljW/mqdTCBy/E7wCK+3e7q4BbSQE9Fvl5aXSMMgs3ppkoxoTaAAAApzCBpAYJKoZIhvcNAQcGoIGWMIGTAgEAMIGNBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDLB06jxCXvsozRrSfAIBEIBg8XPb/H64UHHNlcA2vR9+vLCCPb91PffuG+iwrEFwhEX/JgbATQBZFLKI+nD33WIAjMZJa+saLZPfcUxQiJJoktzHeJEIZbDUy2/+JC92VgDX7UGRLn5Q/lLLL2AWs6T0"
  describe_cloudwatch_alarms_policy = "arn:aws:iam::986171576906:policy/glynt_stage_describe_cloudwatch_alarms"
  describe_ec2_instance_policy = "arn:aws:iam::986171576906:policy/glynt_stage_describe_ec2_instance"
  kms_allow_decrypt_policy = "arn:aws:iam::986171576906:policy/glynt_stage_kms_allow_decrypt"
}
