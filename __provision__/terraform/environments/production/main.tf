terraform {
  backend "s3" {
    bucket  = "tfstate.bacalar.wattzon"
    key     = "bacalar/glynt-infrastructure/production/main.tfstate"
    region  = "us-west-2"
    encrypt = true
  }
  required_version = ">=0.12.21"
  required_providers {
    archive = {
      source  = "hashicorp/archive"
      version = "~> 2.1"
    }
    aws = {
      source  = "hashicorp/aws"
      version = "~> 2.70.0"
    }
    cloudflare = {
      source  = "cloudflare/cloudflare"
      version = "~> 2.18.0"
    }
    auth0 = {
      source  = "auth0/auth0"
      version = "~> 0.14"
    }
  }
}

variable "environment" {
  default = "production"
}

variable "domain" {
  default = "private.glynt.ai"
}

locals {
  private_dns_zone = "${var.environment}.${var.domain}"
}

variable "ebs_snapshot_schedule" {
  default = "0300;15;US/Pacific;wed,sat"
}

# vvvvv from env vvvvv

variable "aws_region" {
  type = string
}

variable "db_root_username" {
  type = string
}

variable "db_root_password" {
  type = string
}

variable "auth0_domain" {
  type = string
}

variable "auth0_client_id" {
  type = string
}

variable "auth0_client_secret" {
  type = string
}

# unused, but still present in CI config. See GL-6108
variable "auth0_redis_password" {
  type = string
}

# ^^^^^^ from env ^^^^^

provider "aws" {
  region  = var.aws_region
  version = "~> 2.0"
}

provider "aws" {
  region  = "us-west-2"
  alias   = "shared"
  version = "~> 2.0"
}

provider "auth0" {
  domain        = var.auth0_domain
  client_id     = var.auth0_client_id
  client_secret = var.auth0_client_secret
}

module "public_dns" {
  source      = "../../modules/public_dns"
  environment = var.environment
  dns_records = []
}

module "identity" {
  source      = "../../modules/identity"
  environment = var.environment
}

########################
##### GLYNT GLOBAL #####
########################
# The following block is global to an AWS account. Thus,  the following block
# should not be run in the same AWS account by different environments (for
# example, in both sandbox and stage if they are deployed to the same AWS
# account.)

module "identity-test-runner" {
  source                = "../../modules/identity-test-runner/"
  environment           = var.environment
  tests_bucket_name     = "secrets.bacalar.wattzon"
  tests_bucket_username = "test-runner"
}

module "ecr_user" {
  source = "../../modules/ecr_user/"
}

module "s3_shared_bucket" {
  source = "../../modules/s3_shared_bucket/"
  providers = {
    aws = aws.shared
  }
}
module "identity-policy" {
  source = "../../modules/identity-policy"
}

provider "aws" {
  region  = "us-east-1"
  version = "~> 2.0"
  alias   = "us-east-1"
}

module "root_login_notification" {
  source                  = "../../modules/root_login_notification"
  environment             = var.environment
  providers               = { aws = aws.us-east-1 }
  slack_encrypted_webhook = "AQICAHgQrY7u2zPWDBiYnxDAORvkVgxC3ber9GS1ZlsvdZJD1gG+ykFDUwW2quNzNSPjByE4AAAAsTCBrgYJKoZIhvcNAQcGoIGgMIGdAgEAMIGXBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDLm0iYjQ25ZFSr7fiAIBEIBqSE4rQpBt6Fm8t6YnzIJrgACp2oIIONGKBw+Ju14XpyieOoY156yhZuX1A8Nb7McKEXd9mPw7MZfzDAPpGwFAPlypa20ZOweEx1VrAJldkp42rqOovVGrLjuLxnXpljSVFqEf4d5s+MjqDg=="
  slack_channel           = "security"
  kms_key_alias           = "encrypt-slack-webhook-url"
  webhook_kms_key_arn     = module.kms_keys.webhook_kms_key_arn
}

module "support" {
  source = "../../modules/support"
}

module "guardduty" {
  source                                   = "../../modules/guardduty"
  cloudwatch_action_notifier_function_name = module.alerts.cloudwatch_action_notifier_function_name
  cloudwatch_action_notifier_arn           = module.alerts.cloudwatch_action_notifier_arn
}

#########################################
##### END OF GLOBAL PER AWS ACCOUNT #####
#########################################

module "network" {
  source         = "../../modules/network"
  environment    = var.environment
  subnet_region  = var.aws_region
  ssh_public_key = "${file("files/key.pub")}"
  public_subnet_route_table_id = "rtb-00980c397507a3705"
  nat_gateway_route_table_id = "rtb-0762aebc0d9ad1b92"
  router_subnet_route_table_id = "rtb-03b8d6b1b69d73f01"
  nat_eip = "eipalloc-085735dde760d1eb3"
}


module "kms_keys" {
  source        = "../../modules/kms_keys"
  kms_users     = ["<EMAIL>", "<EMAIL>", "<EMAIL>"]
  kms_key_alias = "${var.environment}-encrypt-slack-webhook-url"
}

module "vpc_flow_logs" {
  source         = "../../modules/vpc_flow_logs"
  retention_days = 14
  vpc_id         = module.network.vpc_id
  environment    = var.environment
}

module "storage" {
  source                   = "../../modules/storage"
  environment              = var.environment
  aws_region               = var.aws_region
  db_instance_class        = "db.m5.xlarge"
  db_iops                  = 12000
  db_server_sg_id          = module.network.db_server_sg_id
  db_username              = var.db_root_username
  db_password              = var.db_root_password
  db_allocated_storage     = 1000
  db_max_allocated_storage = 1000
  db_subnet_group_name     = module.network.db_subnet_group_name
  iam_policy_arn = "arn:aws:iam::700095865679:policy/manage_shared_db-production"
}

module "auth0" {
  source              = "../../modules/auth0"
  environment         = var.environment
  auth0_domain        = var.auth0_domain
  auth0_client_id     = var.auth0_client_id
  auth0_client_secret = var.auth0_client_secret
}

module "dns" {
  source           = "../../modules/dns"
  environment      = var.environment
  vpc_id           = module.network.vpc_id
  private_dns_zone = local.private_dns_zone
  dns_records = list(
    {
      name  = "shared-db.${local.private_dns_zone}"
      value = module.storage.db_address
      type  = "CNAME"
    }
  )
}

module "alerts" {
  source                    = "../../modules/alerts"
  environment               = var.environment
  alarm_check_rate          = "3 minutes"
  slack_channel             = "monitoring"
  shared_db_identifier      = module.storage.shared_db_identifier
  kms_encrypted_webhook_url = "AQICAHhrANIDpk2fDbqE7IBHhJLEQDx9YR3RiYvhbEbhxNT2pQG7QgTB0bwI0F3ugWVboPRhAAAApzCBpAYJKoZIhvcNAQcGoIGWMIGTAgEAMIGNBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDChd2N1Wz/3xCPlqjAIBEIBgD+j0W6XFYIPTHyzoDh07GIKkiNWJFB/LPYMkTODKQtYAK87CD1heP1Mt/Rmle/KjLxg9DLrLAH1WUCS4br1x85fUzo7l2P6gNtGdedKMKHlSJgPDDm2x8s/z/BtWGl/2"
  describe_cloudwatch_alarms_policy = "arn:aws:iam::700095865679:policy/glynt_production_describe_cloudwatch_alarms"
  describe_ec2_instance_policy = "arn:aws:iam::700095865679:policy/glynt_production_describe_ec2_instance"
  kms_allow_decrypt_policy = "arn:aws:iam::700095865679:policy/glynt_production_kms_allow_decrypt"
}
