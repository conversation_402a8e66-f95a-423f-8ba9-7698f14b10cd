terraform {
  backend "s3" {
    bucket  = "tfstate.baja.wattzon"
    key     = "baja/glynt-infrastructure/sandbox/main.tfstate"
    region  = "us-west-2"
    encrypt = true
  }
  required_version = ">=0.12.21"
  required_providers {
    archive = {
      source  = "hashicorp/archive"
      version = "~> 2.1"
    }
    aws = {
      source  = "hashicorp/aws"
      version = "~> 2.70.0"
    }
    cloudflare = {
      source  = "cloudflare/cloudflare"
      version = "~> 2.18.0"
    }
    auth0 = {
      source  = "auth0/auth0"
      version = "~> 0.14"
    }
  }
}

variable "environment" {
  default = "sandbox"
}

variable "domain" {
  default = "private.glynt.ai"
}

locals {
  private_dns_zone = "${var.environment}.${var.domain}"
}

variable "ebs_snapshot_schedule" {
  default = "0300;15;US/Pacific;wed,sat"
}

variable "uptime_schedule_tagname" {
  default = "uptime_schedule"
}

# vvvvv from env vvvvv

variable "aws_region" {
  type = string
}

variable "db_root_username" {
  type = string
}

variable "db_root_password" {
  type = string
}

variable "auth0_domain" {
  type = string
}

variable "auth0_client_id" {
  type = string
}

variable "auth0_client_secret" {
  type = string
}

# unused, but still present in CI config. See GL-6108
variable "auth0_redis_password" {
  type = string
}

# ^^^^^^ from env ^^^^^

provider "aws" {
  region  = var.aws_region
  version = "~> 2.0"
}

provider "auth0" {
  domain        = var.auth0_domain
  client_id     = var.auth0_client_id
  client_secret = var.auth0_client_secret
}

module "public_dns" {
  source      = "../../modules/public_dns"
  environment = var.environment
  dns_records = []
}

module "identity" {
  source      = "../../modules/identity"
  environment = var.environment
}

##################################
##### GLOBAL PER AWS ACCOUNT #####
##################################
# The following block is global to an AWS account. Thus,  the following block
# should not be run in the same AWS account by different environments (for
# example, in both sandbox and stage if they are deployed to the same AWS
# account.)

module "identity-policy" {
  source = "../../modules/identity-policy"
}

provider "aws" {
  region  = "us-east-1"
  version = "~> 2.0"
  alias   = "us-east-1"
}

module "root_login_notification" {
  source                  = "../../modules/root_login_notification"
  environment             = var.environment
  providers               = { aws = aws.us-east-1 }
  slack_encrypted_webhook = "AQICAHjQw4oErQxL+2ME1uaRJ3DoQ3j8S/SIkUxnN/ov76rOzAHgruih7etJKewmh7N8IR8hAAAAsTCBrgYJKoZIhvcNAQcGoIGgMIGdAgEAMIGXBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDKG3gRWGpgNqJj1LmgIBEIBqfMLx4WexQKWbQP6amxHJugWs6cA5v+CgFO3JgS/EX+yt3iecyGYP/c1MTdb8sD7gFMUHync46pOs7XuhxseS1/bwlbcKxtISN/NXU/giFBMZvgHfCC7wnyI84rRlFWs+txiLvYPFRsEsig=="
  slack_channel           = "security"
  kms_key_alias           = "${var.environment}-encrypt-slack-webhook-url"
  webhook_kms_key_arn     = module.kms_keys.webhook_kms_key_arn
}

module "support" {
  source = "../../modules/support"
}

module "guardduty" {
  source                                   = "../../modules/guardduty"
  cloudwatch_action_notifier_function_name = module.alerts.cloudwatch_action_notifier_function_name
  cloudwatch_action_notifier_arn           = module.alerts.cloudwatch_action_notifier_arn
}

#########################################
##### END OF GLOBAL PER AWS ACCOUNT #####
#########################################

module "network" {
  source                       = "../../modules/network"
  environment                  = var.environment
  subnet_region                = var.aws_region
  ssh_public_key               = "${file("files/key.pub")}"
  uptime_schedule_tagname      = var.uptime_schedule_tagname
  public_subnet_route_table_id = "rtb-0cd9e25fdee4f4f8d"
  nat_gateway_route_table_id   = "rtb-0c91e11c24622f519"
  router_subnet_route_table_id = "rtb-0d7bd50924b0025b3"
  nat_eip                      = "eipalloc-0cefda25b163de49f"
}

module "kms_keys" {
  source        = "../../modules/kms_keys"
  kms_users     = ["<EMAIL>", "<EMAIL>", "<EMAIL>"]
  kms_key_alias = "${var.environment}-encrypt-slack-webhook-url"
}

module "vpc_flow_logs" {
  source         = "../../modules/vpc_flow_logs"
  retention_days = 14
  vpc_id         = module.network.vpc_id
  environment    = var.environment
}

module "storage" {
  source                   = "../../modules/storage"
  environment              = var.environment
  aws_region               = var.aws_region
  db_instance_class        = "db.m5.xlarge"
  db_iops                  = 3000
  db_server_sg_id          = module.network.db_server_sg_id
  db_username              = var.db_root_username
  db_password              = var.db_root_password
  db_allocated_storage     = 100
  db_max_allocated_storage = 500
  db_subnet_group_name     = module.network.db_subnet_group_name
  uptime_schedule_tagname  = var.uptime_schedule_tagname
  iam_policy_arn           = "arn:aws:iam::986171576906:policy/manage_shared_db-sandbox"
}

module "auth0" {
  source              = "../../modules/auth0"
  environment         = var.environment
  auth0_domain        = var.auth0_domain
  auth0_client_id     = var.auth0_client_id
  auth0_client_secret = var.auth0_client_secret
}

module "dns" {
  source           = "../../modules/dns"
  environment      = var.environment
  vpc_id           = module.network.vpc_id
  private_dns_zone = local.private_dns_zone
  dns_records = list(
    {
      name  = "shared-db.${local.private_dns_zone}"
      value = module.storage.db_address
      type  = "CNAME"
    }
  )
}

module "alerts" {
  source                            = "../../modules/alerts"
  environment                       = var.environment
  alarm_check_rate                  = "3 minutes"
  slack_channel                     = "glynt_monitoring_${var.environment}"
  shared_db_identifier              = module.storage.shared_db_identifier
  kms_encrypted_webhook_url         = "AQICAHjQw4oErQxL+2ME1uaRJ3DoQ3j8S/SIkUxnN/ov76rOzAEMNth9ZFqKXWZaopJiHRD0AAAApzCBpAYJKoZIhvcNAQcGoIGWMIGTAgEAMIGNBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDEtEGSadBWISX+FQCAIBEIBgM+9dQE1IbRZ9+yTczTYmIb1p76GF28ZUuz3YaLf2JKjnlqvPtGHxG0TANNdozAgVWLUBgzeyKVSUzOacbIOBXFJjnsNT5DvAotALEOrNdYM2hxcarSRxV7EQgTjrc7JR"
  describe_cloudwatch_alarms_policy = "arn:aws:iam::986171576906:policy/glynt_sandbox_describe_cloudwatch_alarms"
  describe_ec2_instance_policy      = "arn:aws:iam::986171576906:policy/glynt_sandbox_describe_ec2_instance"
  kms_allow_decrypt_policy          = "arn:aws:iam::986171576906:policy/glynt_sandbox_kms_allow_decrypt"
}
