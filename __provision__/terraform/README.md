# Terraform

The Infrastructure provisioning uses Terraform 0.12.21.

To manually run terraform, first ensure you have the aws CLI installed and
configured to use your credentials for interacting with the environment of your
choice. Numerous permissions are required to run successfully. The
`ops_dev_<env>` IAM group should have all needed permissions and will exist
once this provisioner has run once. Adding that group to your user is the
recommended way to add the permissions you need. Also ensure you have terraform
version 0.12.21 installed on your system.

1) Copy the `terraform/template.tfvars` at
`terraform/environments/<environment_name>/terraform.tfvars`.

2) Fill out the newly created file with the needed values.

3) `cd` to `/terraform/environments/<env_to_deploy>`.

4) Run `terraform init -upgrade`.

5) Run `terraform plan`. Check that the plan will do what you expect.

6) Run `terraform apply`.

Note that, during CI, these secrets are instead gathered up by concourse via
files in S3 prior to running terraform. See the CI configuration for details.

This terraform configuration does not assign IAM users to any IAM groups it
creates. You must manually add users to the appropriate groups. All glynt staff
who have a presence on the AWS account should be added to the `glynt_staff_<env>`
groups. Developers who manage this infrastructure should be added to the
`ops_dev_<env>` groups.

The storage module uses `apply_immediately` to restart the database if needed.
Thus, be sure that any services which use the database will not be affected by
a restart and potential temporary database unavailability. This setting will
not automatically restart the database if AWS-created database maintenance
items are queued. If such maintenance items exist, they should be manually
applied during one of our safe deploy windows, otherwise AWS will automatically
apply it during an RDS maintenance window. See the [RDS maintenance AWS
docs](https://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/USER_UpgradeDBInstance.Maintenance.html)
for more information. In theory, AWS will notify us well ahead of time by email
if they schedule any such maintenance items.
