resource "aws_iam_group_policy" "manage_own_credentials" {
  name  = "manage_own_credentials_${var.environment}"
  group = aws_iam_group.glynt_staff.id

  policy = <<EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "AllowViewAccountInfo",
            "Effect": "Allow",
            "Action": [
                "iam:GetAccountPasswordPolicy",
                "iam:GetAccountSummary"
            ],
            "Resource": "*"
        },
        {
            "Sid": "AllowManageOwnPasswords",
            "Effect": "Allow",
            "Action": [
                "iam:ChangePassword",
                "iam:GetUser"
            ],
            "Resource": "arn:aws:iam::*:user/$${aws:username}"
        },
        {
            "Sid": "AllowManageOwnAccessKeys",
            "Effect": "Allow",
            "Action": [
                "iam:CreateAccessKey",
                "iam:DeleteAccessKey",
                "iam:ListAccessKeys",
                "iam:UpdateAccessKey"
            ],
            "Resource": "arn:aws:iam::*:user/$${aws:username}"
        },
        {
            "Sid": "AllowManageOwnSigningCertificates",
            "Effect": "Allow",
            "Action": [
                "iam:DeleteSigningCertificate",
                "iam:ListSigningCertificates",
                "iam:UpdateSigningCertificate",
                "iam:UploadSigningCertificate"
            ],
            "Resource": "arn:aws:iam::*:user/$${aws:username}"
        },
        {
            "Sid": "AllowManageOwnSSHPublicKeys",
            "Effect": "Allow",
            "Action": [
                "iam:DeleteSSHPublicKey",
                "iam:GetSSHPublicKey",
                "iam:ListSSHPublicKeys",
                "iam:UpdateSSHPublicKey",
                "iam:UploadSSHPublicKey"
            ],
            "Resource": "arn:aws:iam::*:user/$${aws:username}"
        },
        {
            "Sid": "AllowManageOwnGitCredentials",
            "Effect": "Allow",
            "Action": [
                "iam:CreateServiceSpecificCredential",
                "iam:DeleteServiceSpecificCredential",
                "iam:ListServiceSpecificCredentials",
                "iam:ResetServiceSpecificCredential",
                "iam:UpdateServiceSpecificCredential"
            ],
            "Resource": "arn:aws:iam::*:user/$${aws:username}"
        },
        {
            "Sid": "AllowManageOwnVirtualMFADevice",
            "Effect": "Allow",
            "Action": [
                "iam:CreateVirtualMFADevice",
                "iam:DeleteVirtualMFADevice"
            ],
            "Resource": "arn:aws:iam::*:mfa/$${aws:username}"
        },
        {
            "Sid": "AllowManageOwnUserMFA",
            "Effect": "Allow",
            "Action": [
                "iam:DeactivateMFADevice",
                "iam:EnableMFADevice",
                "iam:ListMFADevices",
                "iam:ResyncMFADevice"
            ],
            "Resource": "arn:aws:iam::*:user/$${aws:username}"
        }
    ]
}
EOF
}

resource "aws_iam_group" "glynt_staff" {
  name = "glynt_staff_${var.environment}"
}

resource "aws_iam_group" "ops_dev" {
  name = "ops_dev_${var.environment}"
}

resource "aws_iam_group" "transformer_dev" {
  name = "transformer_dev_${var.environment}"
}

resource "aws_iam_group_policy_attachment" "ops_dev_amazon_ec2_full_access" {
  group      = aws_iam_group.ops_dev.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonEC2FullAccess"
}

resource "aws_iam_group_policy_attachment" "ops_dev_amazon_s3_full_access" {
  group      = aws_iam_group.ops_dev.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonS3FullAccess"
}

resource "aws_iam_group_policy_attachment" "ops_dev_cloudwatch_full_access" {
  group      = aws_iam_group.ops_dev.name
  policy_arn = "arn:aws:iam::aws:policy/CloudWatchFullAccess"
}

resource "aws_iam_policy" "transformer_dev_permissions" {
  name        = "${var.environment}-transformer-dev-permissions"
  description = "Gives minimum permissions needed for transformers devs to access logs."

  policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "logs:DescribeLogGroups",
      "Effect": "Allow",
      "Resource": "arn:aws:logs:*:*:log-group::log-stream:"
    },
    {
      "Action": [
        "logs:DescribeLogStreams",
        "logs:FilterLogEvents"
      ],
      "Effect": "Allow",
      "Resource": "arn:aws:logs:*:*:log-group:/glynt/api/${var.environment}/job_servers/celery:log-stream:"
    },
    {
      "Action": "logs:GetLogEvents",
      "Effect": "Allow",
      "Resource": "arn:aws:logs:*:*:log-group:/glynt/api/${var.environment}/job_servers/celery:log-stream:transformer*"
    }
  ]
}
EOF
}

resource "aws_iam_policy" "breaker_box_permissions" {
  name        = "${var.environment}-breaker-box-permissions"
  description = "Gives permissions needed for devs to use the breaker box tool to turn on and off environments and enable/disable the scheduler CloudFormation stack."

  policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": [
       "rds:StartDBInstance",
       "rds:StopDBInstance"
      ],
      "Effect": "Allow",
      "Resource": "*"
    },
    {
      "Action": [
       "ec2:StartInstances",
       "ec2:StopInstances"
      ],
      "Effect": "Allow",
      "Resource": "*"
    },
    {
	"Effect": "Allow",
	"Action": [
	    "cloudformation:*"
	],
	"Resource": "*"
    },
    {
	"Effect": "Allow",
	"Action": "events:PutRule",
	"Resource": "*"
    },
    {
        "Effect": "Allow",
        "Action": [
          "lambda:GetFunctionConfiguration",
          "lambda:GetFunction"
        ],
        "Resource": "*"
    }
  ]
}
EOF
}

resource "aws_iam_group_policy_attachment" "minimum_breaker_box_permissions" {
  group      = aws_iam_group.ops_dev.name
  policy_arn = aws_iam_policy.breaker_box_permissions.arn
}

resource "aws_iam_group_policy_attachment" "transformers_dev_permissions" {
  group      = aws_iam_group.transformer_dev.name
  policy_arn = aws_iam_policy.transformer_dev_permissions.arn
}
