/**
@param {object} client - information about the client
@param {string} client.name - name of client
@param {string} client.id - client id
@param {string} client.tenant - Auth0 tenant name
@param {object} client.metadata - client metadata
@param {array|undefined} scope - array of strings representing the scope claim or undefined
@param {string} audience - token's audience claim
@param {object} context - additional authorization context
@param {object} context.webtask - webtask context
@param {function} cb - function (error, accessTokenClaims)
*/
module.exports = function(client, scope, audience, context, cb) {
  var access_token = {};
  access_token.scope = scope; // do not remove this line

  // Only rate limit the API
  if (audience !== 'glynt-public-api-${environment}') {
    cb(null, access_token);
    return;
  }
 
  let redis     = require('redis');
  let redisClient    = redis.createClient({
    port      : 6379,
    host      : '${auth0_redis_dns_name}',
    auth_pass : '${auth0_redis_password}',
    no_ready_check: true
  });

  // If there are any problems with the redisClient, just fall back to
  // returning a token.
  redisClient.on('error', function(err) {
    console.log(err);
    cb(null, access_token);
    return;
  });
 
  // Rate limits are 2 per 5 minutes and 6 per 12 hours.
  var shortTimePeriod = 1000 * 60 * 5; //  minutes
  var shortRateLimit = 2;
  var longTimePeriod = 1000 * 60 * 60 * 12; // hours
  var longRateLimit = 6;
 
  redisClient.lrange(client.id, 0, -1, function(err, clientHistory) {
      if (err) {
        console.log(err);
        throw err;
      } else {     
        if (clientHistory === null) clientHistory = [];
        var now = new Date().getTime();
        var requestsInShortTimePeriod = 0;
        var requestsInLongTimePeriod = 0;
        var outdatedIndex = -1;
        for (var i = 0; i < clientHistory.length; i++) {
            if (now - clientHistory[i] < shortTimePeriod) {
              requestsInShortTimePeriod++;
              requestsInLongTimePeriod++;
            } else if (now - clientHistory[i] < longTimePeriod) {
              requestsInLongTimePeriod++
            } else {
              outdatedIndex = i;
            }
        }
         
        if (requestsInShortTimePeriod >= shortRateLimit || requestsInLongTimePeriod >= longRateLimit) {
          cb('Rate limit reached');
        } else {
          cb(null, access_token); // no error, return a token to the caller

          // add the successful token response to the client history in redis
          redisClient.rpush(client.id, now, function(error) {
            if (error) {
              console.log('Error writing to Redis: ' + error);
              throw error;
            }
          });

          // delete any events outside the long time period
          if (outdatedIndex >= 0) {
            redisClient.ltrim(client.id, outdatedIndex + 1, -1, function(error) {
              if (error) {
                console.log('Error trimming list: ' + error);
                throw error;
              }
            }); 
          }
        };
         
      } // if err
  });
 
 
};
