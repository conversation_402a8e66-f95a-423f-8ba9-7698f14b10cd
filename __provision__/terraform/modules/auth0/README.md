# Glynt Auth0 Terraform Configuration

This module is responsible for setting up the Auth0 components of our common
infrastructure.

Currently, that means only standing up the Auth0 Redis server, which is the
small, dedicated server responsible for running the internet-accessible redis
instance which the Auth0 Client Credentials Hook uses to manage the Client
Credential token rate limit per Auth0 Application. See the corresponding Auth0
Ansible role to see how this Redis server and the Auth0 hook are setup.

