variable "sns_topic_name" {
  type    = string
  default = "root-login"
}

variable "environment" {
  type = string
}

variable "slack_channel" {
  type = string
}

variable "slack_user" {
  type    = string
  default = "lambda_notification"
}

variable "kms_key_alias" {
  type    = string
  default = "encrypt-slack-webhook-url"
}

variable "webhook_kms_key_arn" {
  type = string
}

variable "slack_encrypted_webhook" {
  type = string
}
