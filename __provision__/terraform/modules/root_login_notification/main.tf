# sns topic configuration

data "aws_caller_identity" "current" {}

data "aws_region" "current" {}

resource "aws_sns_topic" "main" {
  name = var.sns_topic_name
}

resource "aws_cloudwatch_event_rule" "main" {
  name          = "iam-root-login"
  description   = "Successful login with root account"
  event_pattern = file("${path.module}/sns-topic/event-pattern.json")
}

resource "aws_cloudwatch_event_target" "main" {
  rule      = aws_cloudwatch_event_rule.main.name
  target_id = "send-to-sns"
  arn       = aws_sns_topic.main.arn
}

resource "aws_sns_topic_policy" "default" {
  arn = aws_sns_topic.main.arn
  policy = templatefile("${path.module}/sns-topic/sns-topic-access-policy.json",
    {
      sns_topic_arn = aws_sns_topic.main.arn
  })

  depends_on = [data.aws_lambda_function.function]
}

# lambda configuration

locals {
  lambda_function_name = "notify_root_login"
}

resource "aws_iam_role" "lambda_role" {
  name               = "lambda_role"
  path               = "/"
  assume_role_policy = file("${path.module}/lambda/assume-role-policy.json")
}

resource "aws_iam_policy" "lambda_execution" {
  name        = "lambda_execution"
  path        = "/"
  description = "Lambda execution role policy"
  policy = templatefile("${path.module}/lambda/execution-role-policy.json",
    {
      sns_topic_arn  = aws_sns_topic.main.arn,
      aws_region     = data.aws_region.current.name,
      aws_account_id = data.aws_caller_identity.current.account_id,
      kms_key_arn    = var.webhook_kms_key_arn,
      function_name  = local.lambda_function_name
    }
  )
}

resource "aws_iam_role_policy_attachment" "policy_attachment" {
  role       = aws_iam_role.lambda_role.name
  policy_arn = aws_iam_policy.lambda_execution.arn
}

# This is changing to a data block, resource has been imported into the glynt-infrastructure repo
/*
data "archive_file" "lambda_package" {
  type = "zip"
  source {
    content  = file("${path.module}/lambda/lambda_function.py")
    filename = "lambda_function.py"
  }
  output_path = "${local.lambda_function_name}.zip"
}

resource "aws_lambda_function" "function" {
  filename         = data.archive_file.lambda_package.output_path
  function_name    = local.lambda_function_name
  description      = "notifies on slack if root authenticates against the AWS console"
  role             = aws_iam_role.lambda_role.arn
  handler          = "lambda_function.lambda_handler"
  runtime          = "python3.8"
  source_code_hash = data.archive_file.lambda_package.output_base64sha256
  environment {
    variables = {
      SLACK_CHANNEL           = var.slack_channel,
      SLACK_USER              = var.slack_user,
      SLACK_ENCRYPTED_WEBHOOK = var.slack_encrypted_webhook
    }
  }
}
*/

data "aws_lambda_function" "function" {
  function_name    = local.lambda_function_name
}


resource "aws_lambda_permission" "with_sns" {
  statement_id  = "AllowExecutionFromSNS"
  action        = "lambda:InvokeFunction"
  function_name = data.aws_lambda_function.function.function_name
  principal     = "sns.amazonaws.com"
  source_arn    = aws_sns_topic.main.arn
}

resource "aws_sns_topic_subscription" "lamba_root_login_events" {
  topic_arn = aws_sns_topic.main.arn
  protocol  = "lambda"
  endpoint  = data.aws_lambda_function.function.arn
}
