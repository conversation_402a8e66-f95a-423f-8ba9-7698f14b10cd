#!/usr/bin/env python3 

import json
import logging
import urllib.request 
import os
import boto3
from base64 import b64decode

slack_user = os.environ['SLACK_USER']
slack_channel = os.environ['SLACK_CHANNEL']
slack_webhook_url = os.environ['SLACK_ENCRYPTED_WEBHOOK']
slack_webhook_url = b64decode(slack_webhook_url)
slack_webhook_url = boto3.client('kms').decrypt( CiphertextBlob=slack_webhook_url )['Plaintext'].decode('utf-8')

logger = logging.getLogger()
logger.setLevel(logging.INFO)

def lambda_handler(event, context):
  logger.debug(json.dumps(event, indent=2))
  
  sns_message = json.loads(event["Records"][0]["Sns"]["Message"])
  account   = sns_message["account"]
  time      = sns_message["detail"]["eventTime"].replace(" ", "")
  source_ip = sns_message["detail"]["sourceIPAddress"]

  slack_message = "\n".join(("*Root authentication detected*.",
                             "*AWS account:* `{}`",
                             "*Time:* `{}`",
                             "*Source IP:* `{}`"))
  slack_message = slack_message.format(account, time, source_ip)

  data = {      'text': slack_message, 
            'username': slack_user,
          'icon_emoji': ':robot_face:'  }
  data = json.dumps(data).encode('utf-8')
  req = urllib.request.Request(slack_webhook_url, 
                               data=data)
  req.add_header('Content-Type', 'application/json; charset=utf-8')
  
  response = urllib.request.urlopen(req)
  logger.debug(response)

