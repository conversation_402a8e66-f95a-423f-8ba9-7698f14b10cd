{"Version": "2012-10-17", "Statement": [{"Sid": "CreateLogGroup", "Effect": "Allow", "Action": ["logs:CreateLogGroup"], "Resource": ["arn:aws:logs:${aws_region}:${aws_account_id}:*"]}, {"Sid": "CreateEvents", "Effect": "Allow", "Action": ["logs:PutLogEvents", "logs:CreateLogStream"], "Resource": ["arn:aws:logs:${aws_region}:${aws_account_id}:log-group:/aws/lambda/${function_name}:log-stream:*"]}, {"Sid": "Decrypt", "Effect": "Allow", "Action": ["kms:Decrypt"], "Resource": "${kms_key_arn}"}]}