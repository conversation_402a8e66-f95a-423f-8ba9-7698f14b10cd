# root_login_notification module

- [root_login_notification module](#root_login_notification-module)
  - [How the module works](#how-the-module-works)
  - [Module requirements](#module-requirements)
  - [How to use the module](#how-to-use-the-module)
  - [How to's](#how-tos)
    - [How to encrypt a webhook using the AWS CLI](#how-to-encrypt-a-webhook-using-the-aws-cli)
    - [How to decrypt a webhook using the AWS CLI](#how-to-decrypt-a-webhook-using-the-aws-cli)

This module sends notifications to Slack on every successful login authentication.
As this module monitors a global event, it is included only once per AWS account:

| AWS Account | Terraform Environment | Included |
|-------------|-----------------------|----------|
| baja        | sandbox               | Yes      |
| baja        | stage                 | No       |
| bacalar     | production            | Yes      |

## How the module works

The module works the following way:

- Creates an event rule to detect the root login event.
- Creates an SNS Topic to notify a successful root login event.
- Creates a Lambda function to send the slack notification.
- Subscribes the Lambda to the SNS Topic.
- The Lambda function gets triggered.
- The chosen slack channel receives a notification.

## Module requirements

The module assumes that the following components are available:

- [Slack Incoming Webhook][2]: It uses the webhook identified as `Posts to #devops as lambda_notification` in the [Slack configuration interface][3].
- KMS key: It uses the Customer managed key named (All the environments use `encrypt-slack-webhook-url`).
- Encrypted webhook URL: The `slack_encrypted_webhook` module parameter expects the webhook  URL encrypted with the KMS key.

## How to use the module

The root authentication event is [logged in us-east-1][1]. Because of this we need to create an alias for the aws provider. This alias will have the same credentials, but a different region:

```hcl
provider "aws" {
  alias  = "us-east-1"
  profile = var.aws_profile
  region = "us-east-1"
}

module "root_login_notification" {
  source       = "../../modules/root_login_notification"
  environment  = var.environment
  providers    = { aws = aws.us-east-1 }
  slack_encrypted_webhook = "AQICAHh9lT49ZSFr3rnRMku+8kYrNNSqVW8kYzjzMiw/H7+DwAGxwCIv0qx7o3C0eozTfZsIAAAAcDBuBgkqhkiG9w0BBwagYTBfAgEAMFoGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMbN4IzFpn9DtRq5CEAgEQgC1C/k4ixLFWWuYp7vJXu8PeOS9FCx+j5aPu1dPNiqqNzUwFI+NqvU48DmiQxG0=
}
```

## How to's

### How to encrypt a webhook using the AWS CLI

```bash
$ KEY_ID="alias/encrypt-slack-webhook-url"
$ TEXT_TO_ENCRYPT="https://hooks.slack.com/services/*********/*********/*********"
$ aws kms encrypt --key-id ${KEY_ID} --plaintext fileb://<(echo -n "${TEXT_TO_ENCRYPT}") --query CiphertextBlob --output text > encrypted_text.txt
$ cat encrypted_text.txt
AQICAHh9lT49ZSFr3rnRMku+8kYrNNSqVW8kYzjzMiw/H7+DwAGxwCIv0qx7o3C0eozTfZsIAAAAcDBuBgkqhkiG9w0BBwagYTBfAgEAMFoGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMbN4IzFpn9DtRq5CEAgEQgC1C/k4ixLFWWuYp7vJXu8PeOS9FCx+j5aPu1dPNiqqNzUwFI+NqvU48DmiQxG0=
```

### How to decrypt a webhook using the AWS CLI

```bash
$ KEY_ID="alias/encrypt-slack-webhook-url"
$ aws kms decrypt --ciphertext-blob fileb://<( cat encrypted_text.txt | base64 --decode ) --query Plaintext --output text | base64 --decode
https://hooks.slack.com/services/*********/*********/*********
```

[1]: https://aws.amazon.com/blogs/mt/monitor-and-notify-on-aws-account-root-user-activity/
[2]: https://api.slack.com/messaging/webhooks
[3]: https://wattzonteam.slack.com/apps/manage/custom-integrations
