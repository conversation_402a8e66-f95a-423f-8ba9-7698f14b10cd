{"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Principal": {"AWS": ["arn:aws:iam::986171576906:role/boxcars_development_role", "arn:aws:iam::986171576906:role/boxcars_sandbox_role", "arn:aws:iam::986171576906:role/boxcars_stage_role", "arn:aws:iam::700095865679:role/boxcars_production_role"]}, "Action": ["s3:GetObject", "s3:GetObjectVersionTagging", "s3:GetObjectTagging", "s3:GetObjectVersion"], "Resource": "arn:aws:s3:::glynt.shared/*"}, {"Effect": "Allow", "Principal": {"AWS": ["arn:aws:iam::986171576906:role/boxcars_development_role", "arn:aws:iam::986171576906:role/boxcars_sandbox_role", "arn:aws:iam::986171576906:role/boxcars_stage_role", "arn:aws:iam::700095865679:role/boxcars_production_role"]}, "Action": "s3:ListBucket", "Resource": "arn:aws:s3:::glynt.shared"}, {"Effect": "<PERSON><PERSON>", "Principal": "*", "Action": "*", "Resource": ["arn:aws:s3:::glynt.shared", "arn:aws:s3:::glynt.shared/*"], "Condition": {"Bool": {"aws:SecureTransport": "false"}}}]}