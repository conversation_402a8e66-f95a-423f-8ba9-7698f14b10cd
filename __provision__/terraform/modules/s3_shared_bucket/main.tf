data "aws_canonical_user_id" "current" {}

resource "aws_s3_bucket" "glynt_shared" {
  bucket        = "glynt.shared"
  region        = "us-west-2"
  force_destroy = false
  versioning {
    enabled = true
  }
  policy = file("${path.module}/assets/cross_account_access_policy.json")

  logging {
    target_bucket = "logging.bacalar.wattzon"
    target_prefix = "shared/"
  }

  server_side_encryption_configuration {
    rule {
      apply_server_side_encryption_by_default {
        sse_algorithm = "AES256"
      }
    }
  }

}

resource "aws_s3_bucket_public_access_block" "glynt_shared" {
  bucket                  = aws_s3_bucket.glynt_shared.id
  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

