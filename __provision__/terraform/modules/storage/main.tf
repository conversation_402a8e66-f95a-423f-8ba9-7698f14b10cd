data "aws_caller_identity" "current" {}

data "aws_db_instance" "mysql" {
  db_instance_identifier = "glynt-shared-${var.environment}"
}

data "aws_iam_policy" "amazon_rds_read_only_access" {
  arn = "arn:aws:iam::aws:policy/AmazonRDSReadOnlyAccess"
}

data "aws_iam_policy" "manage_shared_db" {
  arn = var.iam_policy_arn
}

data "aws_iam_group" "manage_glynt_shared_db" {
  group_name = "manage_glynt_shared_db-${var.environment}"
}
