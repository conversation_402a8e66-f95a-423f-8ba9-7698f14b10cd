variable "environment" {
  type = string
}

variable "aws_region" {
  type = string
}

variable "db_server_sg_id" {
  type = string
}

variable "db_allocated_storage" {
  type = string
}

variable "db_max_allocated_storage" {
  type = string
}

variable "db_password" {
  type = string
}

variable "db_username" {
  type = string
}

variable "db_instance_class" {
  type = string
}

variable "db_iops" {
  type = number
}

variable "db_subnet_group_name" {
  type = string
}

variable "uptime_schedule_tagname" {
  type    = string
  default = "uptime_schedule"
}

variable "iam_policy_arn" {
  type    = string
}

