{"Version": "2012-10-17", "Statement": [{"Sid": "ReadPermissions", "Effect": "Allow", "Action": ["ec2:Describe*", "rds:DescribeDBEngineVersions", "rds:DescribeExportTasks", "rds:DescribeEngineDefaultClusterParameters", "rds:DescribeOrderableDBInstanceOptions", "rds:DescribeSourceRegions", "rds:DescribeEngineDefaultParameters", "rds:DescribeCertificates", "rds:DescribeEventCategories", "rds:DescribeAccountAttributes", "rds:DescribeEvents", "rds:DescribeReservedDBInstancesOfferings"], "Resource": "*"}, {"Sid": "UpdatePermissions", "Effect": "Allow", "Action": ["rds:CreateDBParameterGroup", "rds:ModifyOptionGroup", "rds:ModifyDBParameterGroup", "rds:Describe*", "rds:CreateDBSnapshot", "rds:RestoreDBInstanceFromDBSnapshot", "rds:RebootDBInstance", "rds:List*", "rds:DownloadDBLogFilePortion", "rds:ModifyDBInstance", "rds:DeleteDBSnapshot", "rds:RestoreDBInstanceToPointInTime"], "Resource": ["arn:aws:rds:${aws_region}:${aws_account_id}:target-group:*", "arn:aws:rds:${aws_region}:${aws_account_id}:snapshot:*", "arn:aws:rds:${aws_region}:${aws_account_id}:cluster-snapshot:*", "arn:aws:rds:${aws_region}:${aws_account_id}:es:*", "arn:aws:rds:${aws_region}:${aws_account_id}:subgrp:*", "arn:aws:rds:${aws_region}:${aws_account_id}:secgrp:*", "arn:aws:rds:${aws_region}:${aws_account_id}:cluster-pg:${db_parameter_group_name}", "arn:aws:rds:${aws_region}:${aws_account_id}:pg:${db_parameter_group_name}", "arn:aws:rds:${aws_region}:${aws_account_id}:db-proxy:*", "arn:aws:rds:${aws_region}:${aws_account_id}:ri:*", "arn:aws:rds:${aws_region}:${aws_account_id}:cluster-endpoint:*", "arn:aws:rds:${aws_region}:${aws_account_id}:db:${db_name}", "arn:aws:rds:${aws_region}:${aws_account_id}:og:*", "arn:aws:rds:${aws_region}:${aws_account_id}:cluster:${db_name}", "arn:aws:rds::${aws_account_id}:global-cluster:*"]}]}