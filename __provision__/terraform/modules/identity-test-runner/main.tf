resource "aws_iam_user" "iam_tests_user" {
  name = var.tests_bucket_username
}

resource "aws_iam_policy" "iam-policy-tests-user" {
  name        = "S3-access-tests-user"
  path        = "/"
  description = "Allows read only access to env files required to run tests"

  policy = <<EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "VisualEditor0",
            "Effect": "Allow",
            "Action": [
                "s3:GetObject",
                "s3:PutObjectVersionAcl",
                "s3:ListBucketVersions",
                "s3:GetBucketVersioning",
                "s3:GetObjectVersion"
            ],
            "Resource": [
                "arn:aws:s3:::${var.tests_bucket_name}/test/system/*",
                "arn:aws:s3:::${var.tests_bucket_name}"
            ]
        }
    ]
}
EOF
}

resource "aws_iam_group" "group" {
  name = "tests-runner-users"
}

resource "aws_iam_group_policy_attachment" "tests-group-attach-policy" {
  group      = aws_iam_group.group.id
  policy_arn = aws_iam_policy.iam-policy-tests-user.arn
}

resource "aws_iam_group_membership" "tests-group-attach-user" {
  name  = "tests-group-membership"
  users = [aws_iam_user.iam_tests_user.name]
  group = aws_iam_group.group.name
}
