variable "environment" {
  type = string
}

variable "subnet_region" {
  type = string
}

variable "ssh_public_key" {
  type = string
}

variable "uptime_schedule_tagname" {
  type    = string
  default = "uptime_schedule"
}

variable "public_subnet_route_table_id" {
  type = string
}

variable "nat_gateway_route_table_id" {
  type = string
}

variable "router_subnet_route_table_id" {
  type = string
}

variable "nat_eip" {
  type = string
}
