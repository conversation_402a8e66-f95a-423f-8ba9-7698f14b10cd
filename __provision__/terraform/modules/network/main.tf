data "aws_region" "current" {}


resource "aws_key_pair" "ssh_key" {
  key_name   = "glynt-infrastructure-${var.environment}"
  public_key = var.ssh_public_key
}


data "aws_vpc" "default" {
  default    = true
  filter {
    name   = "tag:Name"
    values = ["Default VPC"]
  }
}

data "aws_security_group" "default_vpc" {
   filter {
    name   = "vpc-id"
    values = [data.aws_vpc.default.id]
  }
   filter {
    name   = "group-name"
    values = ["default"]
  }
}


data "aws_vpc" "glynt_vpc" {
  filter {
    name   = "tag:Name"
    values = ["glynt-${var.environment}-vpc"]
  }
}


data "aws_vpc_endpoint" "dynamodb" {
  vpc_id       = data.aws_vpc.glynt_vpc.id
  service_name = "com.amazonaws.${data.aws_region.current.name}.dynamodb"
}



data "aws_internet_gateway" "default" {
  filter {
    name   = "attachment.vpc-id"
    values = [data.aws_vpc.glynt_vpc.id]
  }
}

data "aws_eip" "nat_gateway" {
  id = var.nat_eip
}

data "aws_nat_gateway" "nat_gw" {
  subnet_id = data.aws_subnet.nat_subnet.id
}

# PRIVATE SUBNETS

data "aws_subnet" "private_subnet_1" {
  cidr_block = "**********/24"
}

data "aws_subnet" "private_subnet_2" {
  cidr_block = "**********/24"
}


data "aws_db_instance" "glynt_api_db" {
  db_instance_identifier = "glynt-shared-${var.environment}"
}

# PUB SUBNET 

data "aws_subnet" "public_subnet" {
  cidr_block = "********/24"
}

data "aws_subnet" "public_subnet_c" {
  cidr_block = "********/24"
}

data "aws_subnet" "nat_subnet" {
  cidr_block = "********/24"
}


data "aws_subnet" "router_subnet" {
  cidr_block = "********/24"
}

data "aws_subnet" "router_subnet1" {
  cidr_block = "********/24"
}

data "aws_route_table" "public_subnet" {
  route_table_id  = var.public_subnet_route_table_id
}


data "aws_route_table" "nat_gateway" {
  route_table_id = "${var.nat_gateway_route_table_id}"
}

data "aws_route_table" "router_subnet" {
  route_table_id = var.router_subnet_route_table_id
}

# Security Groups

data "aws_security_group" "db_server" {
  name = "Glynt ${var.environment} db server"
}

data "aws_security_group" "db_client" {
  name = "Glynt ${var.environment} db client"
}
