resource "aws_guardduty_detector" "guard_duty" {
  enable = true
}

resource "aws_cloudwatch_event_rule" "guard_duty_finding" {
  name          = "guard_duty_findings"
  description   = "Triggers notification via Lambda for GuardDuty findings of severity level medium or higher."
  event_pattern = <<PATTERN
{
  "source": [
    "aws.guardduty"
  ],
  "detail-type": [
    "GuardDuty Finding"
  ],
  "detail": {
    "severity": [
      5,
      8
    ]
  }
}
PATTERN
}

resource "aws_cloudwatch_event_target" "guard_duty_target" {
  rule      = aws_cloudwatch_event_rule.guard_duty_finding.name
  target_id = "TriggerLambda"
  arn       = var.cloudwatch_action_notifier_arn
}

resource "aws_lambda_permission" "allow_guard_duty_event_trigger" {
  statement_id  = "AllowExecutionFromGuardDutyEvent"
  action        = "lambda:InvokeFunction"
  function_name = var.cloudwatch_action_notifier_function_name
  principal     = "events.amazonaws.com"
  source_arn    = aws_cloudwatch_event_rule.guard_duty_finding.arn
}
