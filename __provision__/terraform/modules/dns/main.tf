resource "aws_route53_zone" "private" {
  name = var.private_dns_zone

  vpc {
    vpc_id = var.vpc_id
  }

  tags = {
    Name = var.private_dns_zone
  }
}

locals {
  dns_records_map = { for i in var.dns_records : i.name => i }
}

resource "aws_route53_record" "servers" {
  for_each = local.dns_records_map
  name     = each.value.name
  zone_id  = aws_route53_zone.private.zone_id
  records  = [each.value.value]
  type     = each.value.type
  ttl      = "30"
  lifecycle {
    create_before_destroy = true
  }
}
