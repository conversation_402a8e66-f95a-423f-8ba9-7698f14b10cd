data "aws_caller_identity" "current" {}

data "aws_iam_user" "kms_users" {
  count     = length(var.kms_users)
  user_name = var.kms_users[count.index]
}

data "aws_iam_role" "kms_roles" {
  count = length(var.kms_roles)
  name  = var.kms_roles[count.index]
}

locals {
  users           = data.aws_iam_user.kms_users[*].arn
  roles           = data.aws_iam_role.kms_roles[*].arn
  users_and_roles = join(",", [for i in concat(local.users, local.roles) : "\"${i}\""])
}

resource "aws_kms_key" "slack" {
  policy = templatefile("${path.module}/kms_access_policy.json", {
    kms_users_and_roles = local.users_and_roles,
    aws_account_id      = data.aws_caller_identity.current.account_id
  })
  description         = "Key to encrypt the slack webhook url for use in the Lambda function which sends slack alerts for Cloudwatch alarms."
  enable_key_rotation = true
}

resource "aws_kms_alias" "slack" {
  name          = "alias/${var.kms_key_alias}"
  target_key_id = aws_kms_key.slack.key_id
}
