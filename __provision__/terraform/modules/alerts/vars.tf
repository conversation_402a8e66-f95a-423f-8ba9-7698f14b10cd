variable "environment" {
  type = string
}

variable "kms_encrypted_webhook_url" {
  type = string
}

variable "alarm_check_rate" {
  type = string
}

variable "shared_db_identifier" {
  type = string
}

variable "slack_channel" {
  type = string
}

variable "kms_allow_decrypt_policy" {
  type = string
}

variable "describe_ec2_instance_policy" {
  type = string
}

variable "describe_cloudwatch_alarms_policy" {
  type = string
}

#-------------------------------------------------------------------------------------------------------------
# Configuration parameters to restrict CloudWatch alarm notifications based on:
# environment name, day of week and time of the day

variable "scheduled_environments" {
  type        = list(string)
  default     = ["sandbox", "stage"]
  description = "environments under schedule restrictions"
}

variable "timezone" {
  type    = string
  default = "US/Pacific"
}

variable "weekday_start" {
  type        = number
  default     = 1
  description = "ISO 8601 weekday as a decimal number where 1 is Monday"
}

variable "weekday_end" {
  type        = number
  default     = 5
  description = "ISO 8601 weekday as a decimal number where 1 is Monday"
}

variable "time_start" {
  type        = string
  default     = "05:30:00"
  description = "Time formatted as '%H:%M:%S' (https://docs.python.org/3.7/library/datetime.html#strftime-strptime-behavior)"
}

variable "time_end" {
  type        = string
  default     = "22:00:00"
  description = "Time formatted as '%H:%M:%S' (https://docs.python.org/3.7/library/datetime.html#strftime-strptime-behavior)"
}
#-------------------------------------------------------------------------------------------------------------
