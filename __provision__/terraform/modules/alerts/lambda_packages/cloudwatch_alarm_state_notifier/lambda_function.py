import boto3
import logging
import json
import os
from dateutil import tz
import datetime_utils

from base64 import b64decode
from urllib.request import Request, urlopen
from urllib.error import URLError, HTTPError

logger = logging.getLogger()
logger.setLevel(logging.INFO)

# The base-64 encoded, encrypted key (CiphertextBlob) stored in the kmsEncryptedHookUrl environment variable
ENCRYPTED_HOOK_URL = os.environ['kmsEncryptedHookUrl']
# The Slack channel to send a message to stored in the slackChannel environment variable
SLACK_CHANNEL = os.environ['slackChannel']

ENVIRONMENT = os.environ['environment']
SCHEDULED_ENVIRONMENTS = os.environ['scheduled_environments'].split(" ")
WEEKDAY_START = int(os.environ['weekday_start'])
WEEKDAY_END = int(os.environ['weekday_end'])
TIME_START = os.environ['time_start']
TIME_END = os.environ['time_end']
TIMEZONE = tz.gettz(os.environ['timezone'])

HOOK_URL = "https://" + boto3.client('kms').decrypt(
    CiphertextBlob=b64decode(ENCRYPTED_HOOK_URL)
)['Plaintext'].decode('utf-8')


COLOR_MAP = {'ALARM': 'danger', 'OK': 'good'}


def lambda_handler(event, context):
    """This function is triggered by an sns topic used by alarms to send
    notifications when they return to an OK state.
    Configure this function with these lambda environment variables:
        kmsEncryptedHookUrl (string): url used to post messages to slack via their
            webhook api. Must be manually configured before deployment. See
            glynt/README.md for encryption instructions.
        slackChannel (string): the name of the slack channel to post to
        environment (string): name of the current environment.
        scheduled_environments (string): name of the environments under schedule restrictions.
        weekday_start (string): First day of the week. ISO 8601 weekday as a decimal number where 1 is Monday
        weekday_end (string): Last day of the week. ISO 8601 weekday as a decimal number where 1 is Monday
        time_start (string): Time formatted as "%H:%M:%S" (https://docs.python.org/3.7/library/datetime.html#strftime-strptime-behavior)
        time_end (string): Time formatted as "%H:%M:%S" (https://docs.python.org/3.7/library/datetime.html#strftime-strptime-behavior)
        timezone (string): Timezone name
    """

    if (ENVIRONMENT in SCHEDULED_ENVIRONMENTS) and \
            (datetime_utils.nowIsOnSchedule(WEEKDAY_START, WEEKDAY_END, TIME_START, TIME_END, TIMEZONE) is False):
        logger.info("Notification suppressed by schedule.")
        return
    logger.info(event)
    slack_message = build_slack_message(event)
    send_slack_message(slack_message)


def build_slack_message(event):
    alarm_message = json.loads(event['Records'][0]['Sns']['Message'])
    region = event['Records'][0]['Sns']['TopicArn'].split(":")[3]
    alarm_name = alarm_message['AlarmName']
    alarm_description = alarm_message['AlarmDescription']
    state = alarm_message['NewStateValue']
    return message_from_template(alarm_name, alarm_description, region, state)


def message_from_template(alarm_name, alarm_description, region, state):
    return {
        "channel": SLACK_CHANNEL,
        "text": f"<!here> *{alarm_name}* - {state}",
        "attachments": [
            {
                "color": COLOR_MAP[state],
                "fields": [
                    {
                        "title": "Alarm Description",
                        "value": alarm_description,
                        "short": False
                    },
                    {
                        "title": "Message",
                        "value": f"Alarm has entered the {state} state.",
                        "short": True
                    },
                    {
                        "title": "Link to Alarm",
                        "value": (
                            "https://console.aws.amazon.com/"
                            f"cloudwatch/home?region={region}"
                            f"#alarmsV2:alarm/{alarm_name.replace(' ', '+')}"
                        ),
                        "short": False
                    }
                ]
            }
        ]
    }


def send_slack_message(slack_message):
    logger.info("Sending Slack message to channel '{}'".format(
        SLACK_CHANNEL)
    )
    logger.info(slack_message)

    req = Request(HOOK_URL, json.dumps(slack_message).encode('utf-8'))
    try:
        response = urlopen(req)
    except HTTPError as e:
        logger.error("Request failed: %d %s", e.code, e.reason)
    except URLError as e:
        logger.error("Server connection failed: %s", e.reason)
    response.read()
    logger.info("Message posted to {}".format(SLACK_CHANNEL))
