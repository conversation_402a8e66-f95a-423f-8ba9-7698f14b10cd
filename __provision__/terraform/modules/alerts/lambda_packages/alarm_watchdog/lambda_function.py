import boto3
import logging
import json
import os
from dateutil import tz
import datetime_utils

from base64 import b64decode
from urllib.request import Request, urlopen
from urllib.error import URLError, HTTPError

logger = logging.getLogger()
logger.setLevel(logging.INFO)

# The base-64 encoded, encrypted key (CiphertextBlob) stored in the kmsEncryptedHookUrl environment variable
ENCRYPTED_HOOK_URL = os.environ['kmsEncryptedHookUrl']
# The Slack channel to send a message to stored in the slackChannel environment variable
SLACK_CHANNEL = os.environ['slackChannel']

ENVIRONMENT = os.environ['environment']
SCHEDULED_ENVIRONMENTS = os.environ['scheduled_environments'].split(" ")
WEEKDAY_START = int(os.environ['weekday_start'])
WEEKDAY_END = int(os.environ['weekday_end'])
TIME_START = os.environ['time_start']
TIME_END = os.environ['time_end']
TIMEZONE = tz.gettz(os.environ['timezone'])

HOOK_URL = "https://" + boto3.client('kms').decrypt(
    CiphertextBlob=b64decode(ENCRYPTED_HOOK_URL)
)['Plaintext'].decode('utf-8')


def lambda_handler(event, context):
    """This function is intended to be triggered periodically by a scheduled
    cloudwatch action. It collects a list of cloudwatch alarms which are in the
    ALARM state and sends slack notifications about those alarms. The periodic
    action will continually trigger this function but it will only send
    notifications when it finds triggered alarms, thus, when alarms return to
    an OK status, the notifications stop.

    Configure this function with these lambda environment variables:
        kmsEncryptedHookUrl (string): url used to post messages to slack via their
            webhook api. Must be manually configured before deployment. See
            glynt/README.md for encryption instructions.
        slackChannel (string): the name of the slack channel to post to.
        environment (string): name of the current environment.
        scheduled_environments (string): name of the environments under schedule restrictions.
        weekday_start (string): First day of the week. ISO 8601 weekday as a decimal number where 1 is Monday
        weekday_end (string): Last day of the week. ISO 8601 weekday as a decimal number where 1 is Monday
        time_start (string): Time formatted as "%H:%M:%S" (https://docs.python.org/3.7/library/datetime.html#strftime-strptime-behavior)
        time_end (string): Time formatted as "%H:%M:%S" (https://docs.python.org/3.7/library/datetime.html#strftime-strptime-behavior)
        timezone (string): Timezone name
    """

    if (ENVIRONMENT in SCHEDULED_ENVIRONMENTS) and \
            (datetime_utils.nowIsOnSchedule(WEEKDAY_START, WEEKDAY_END, TIME_START, TIME_END, TIMEZONE) is False):
        logger.info("Notification suppressed by schedule.")
        return

    logger.info("Getting list of cloudwatch alarms in the ALARM state")
    alarm_list = boto3.client('cloudwatch').describe_alarms(StateValue='ALARM')

    logger.info(alarm_list)

    for alarm in alarm_list['MetricAlarms']:
        if is_autoscale_alarm(alarm):
            continue

        alarm_name = alarm['AlarmName']
        alarm_description = alarm['AlarmDescription']
        metric_name = get_metric_name_from_nested_alarm_dict(alarm, alarm_name)

        if "skip_nagging_notifier" in alarm_description:
            return

        state = alarm['StateValue']
        region = alarm['AlarmArn'].split(":")[3]

        slack_message = {
            "channel": SLACK_CHANNEL,
            "text": "<!channel> " + "*" + alarm_name + "*" + " - IN ALARM",
            "attachments": [
                {
                    "color": "danger",
                    "fields": [
                        {"title": "Alarm Description",
                            "value": alarm_description, "short": False},
                        {
                            "title": "Alarm Reason",
                            "value": alarm["StateReason"],
                            "short": False
                        },
                        {"title": "Current State",
                            "value": state, "short": True},
                        {"title": "Metric",
                            "value": metric_name, "short": True},
                        {
                            "title": "Link to Alarm",
                            "value": "https://console.aws.amazon.com/cloudwatch/home?region="
                            + region + "#alarmsV2:alarm/" +
                            alarm_name.replace(' ', '+'),
                            "short": False
                        }
                    ]
                }
            ]
        }

        logger.info("Sending Slack message to channel '{}' for alarm: {}".format(
            SLACK_CHANNEL, alarm)
        )
        req = Request(HOOK_URL, json.dumps(slack_message).encode('utf-8'))
        try:
            response = urlopen(req)
        except HTTPError as e:
            logger.error("Request failed: %d %s", e.code, e.reason)
        except URLError as e:
            logger.error("Server connection failed: %s", e.reason)
        response.read()
        logger.info("Message posted to %s", slack_message['channel'])


def is_autoscale_alarm(alarm):
    return 'autoscaling' in ''.join(alarm['AlarmActions'])


def get_metric_name_from_nested_alarm_dict(alarm_dct, metric_alarm_name):
    if 'MetricName' in alarm_dct:
        return alarm_dct['MetricName']
    else:
        return metric_alarm_name

