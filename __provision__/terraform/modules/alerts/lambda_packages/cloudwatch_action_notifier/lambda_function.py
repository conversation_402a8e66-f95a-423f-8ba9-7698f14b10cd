import boto3
import logging
import json
import os
from dateutil import tz
import datetime_utils

from base64 import b64decode
from urllib.request import Request, urlopen
from urllib.error import URLError, HTTPError

logger = logging.getLogger()
logger.setLevel(logging.INFO)

ENCRYPTED_HOOK_URL = os.environ['kmsEncryptedHookUrl']
SLACK_CHANNEL = os.environ['slackChannel']

ENVIRONMENT = os.environ['environment']
SCHEDULED_ENVIRONMENTS = os.environ['scheduled_environments'].split(" ")
WEEKDAY_START = int(os.environ['weekday_start'])
WEEKDAY_END = int(os.environ['weekday_end'])
TIME_START = os.environ['time_start']
TIME_END = os.environ['time_end']
TIMEZONE = tz.gettz(os.environ['timezone'])

HOOK_URL = "https://" + boto3.client('kms').decrypt(
    CiphertextBlob=b64decode(ENCRYPTED_HOOK_URL)
)['Plaintext'].decode('utf-8')


def lambda_handler(event, context):
    """This method is intended to be triggered by cloudwatch events which track
    the state changes of ec2 instances. Each glynt component should configure a
    cloudwatch action in it's deployment scripts which send a trigger event to
    this lambda function whenever one of it's vital ec2 instances changes state.

    This method parses the event json and sends a slack notification to the
    configured channel with details about the state change.  This is intended
    to alert Glynt employees of unexpected downtimes or restarts or any other
    instance state changes that should be tracked like periodic rebuilds.

    Also used to report rds events.

    Configure this function with these lambda environment variables:
        kmsEncryptedHookUrl (string): url used to post messages to slack via their
            webhook api. Must be manually configured before deployment. See
            glynt/README.md for encryption instructions.
        slackChannel (string): the name of the slack channel to post to.
        environment (string): name of the current environment.
        scheduled_environments (string): name of the environments under schedule restrictions.
        weekday_start (string): First day of the week. ISO 8601 weekday as a decimal number where 1 is Monday
        weekday_end (string): Last day of the week. ISO 8601 weekday as a decimal number where 1 is Monday
        time_start (string): Time formatted as "%H:%M:%S" (https://docs.python.org/3.7/library/datetime.html#strftime-strptime-behavior)
        time_end (string): Time formatted as "%H:%M:%S" (https://docs.python.org/3.7/library/datetime.html#strftime-strptime-behavior)
        timezone (string): Timezone name
    """

    if (ENVIRONMENT in SCHEDULED_ENVIRONMENTS) and \
            (datetime_utils.nowIsOnSchedule(WEEKDAY_START, WEEKDAY_END, TIME_START, TIME_END, TIMEZONE) is False):
        logger.info("Notification suppressed by schedule.")
        return
    logger.info(event)

    if event['source'] == 'aws.rds':
        if 'backup' in event['detail']['EventCategories']:
            return
        slack_message = build_rds_event_notification(event)
    elif event['source'] == 'aws.ec2':
        slack_message = build_ec2_state_change_message(event)
    elif event['source'] == 'aws.guardduty':
        slack_message = build_guard_duty_finding_notification(event)
    else:
        logger.warning("Function recieved an message from an unknown event")
        return

    logger.info("Posting alert to '{}' slack channel".format(SLACK_CHANNEL))
    post_message(slack_message)
    logger.info("Message posted to {}".format(SLACK_CHANNEL))


def build_guard_duty_finding_notification(event):
    logger.info(f"Guard Duty finding detected: {event}")
    slack_message = {
        "channel": SLACK_CHANNEL,
        "text": f"<!channel> Guard Duty has reported a finding with severity of {event['detail']['severity']}/10.\n" +
                f"details: {event['detail']['title']}"
    }
    return slack_message


def build_ec2_state_change_message(event):
    logger.info("EC2 state change action detected: " + str(event))
    expression_emojis = {'running': ":money_with_wings:",
                         'shutting-down': ":fire:", 'stopping': ":octagonal_sign:"}
    chan_mention = {'running': "<!here>",
                    'shutting-down': "<!channel>", 'stopping': "<!channel>"}
    instance_id = event['detail']['instance-id']
    state = event['detail']['state']
    instance_name = [tag['Value'] for tag in boto3.resource('ec2').
                     Instance(instance_id).tags if tag['Key'] == 'Name'][0]
    instance_url = "https://console.aws.amazon.com/ec2/v2/home" \
                   "?region={}#Instances:search={};sort=instanceType".format(
                       event['region'], instance_id)

    slack_message = {
        "channel": SLACK_CHANNEL,
        "text": "{}\nInstance {} state has changed to *{}* ^^^\n".replace(
            '^', expression_emojis[state]).format(chan_mention[state], instance_name, state) +
        "Link to instance: *{}*".format(instance_url)
    }
    return slack_message


def build_rds_event_notification(event):
    logger.info("RDS event detected, sending notification")
    instance_name = event['detail']['SourceArn'].split(':')[-1]
    event_message = event['detail']['Message']
    slack_message = {
        "channel": SLACK_CHANNEL,
        "text": ":triangular_flag_on_post: RDS event detected: [Instance: {} - Message: {}]".format(
            instance_name, event_message
        )
    }
    return slack_message


def post_message(slack_message):
    req = Request(HOOK_URL, json.dumps(slack_message).encode('utf-8'))
    try:
        response = urlopen(req)
    except HTTPError as e:
        logger.error("Request failed: %d %s", e.code, e.reason)
    except URLError as e:
        logger.error("Server connection failed: %s", e.reason)
    response.read()
