from datetime import datetime, <PERSON><PERSON><PERSON>

def toDatetime(weekday, time, timezone):
    """Converts a weekday and time into a datetime object

    Args:
        weekday (int): ISO 8601 weekday as a decimal number where 1 is Monday
        time (string): Time formatted as "%H:%M:%S" (https://docs.python.org/3.7/library/datetime.html#strftime-strptime-behavior)
        timezone (dateutil.tz): dateutil tz object

    Returns:
        datetime.datetime: datetime.datetime object
    """

    now = datetime.now(tz=timezone)
    tmp_time = [int(i) for i in time.split(":")]
    datetimeObject = datetime.now(tz=timezone).replace(hour=tmp_time[0],
                                                                minute=tmp_time[1],
                                                                second=tmp_time[2],
                                                                microsecond=0)
    today = int(now.strftime("%u"))
    diff_days = today - weekday
    datetimeObject = datetimeObject - timedelta(days=diff_days)

    return datetimeObject


def nowIsOnSchedule(weekday_start, weekday_end, time_start, time_end, timezone):
    """Checks if current date and time are between two provided datetime objects range.

    Args:
        weekday_start (int): First day of the week. ISO 8601 weekday as a decimal number where 1 is Monday
        weekday_end (int): Last day of the week. ISO 8601 weekday as a decimal number where 1 is Monday
        time_start (string): Time formatted as "%H:%M:%S" (https://docs.python.org/3.7/library/datetime.html#strftime-strptime-behavior)
        time_end (string): Time formatted as "%H:%M:%S" (https://docs.python.org/3.7/library/datetime.html#strftime-strptime-behavior)
        timezone (dateutil.tz): dateutil tz object

    Returns:
        bool: True if current time and date are in the provided weekday and time range
    """
    now = datetime.now(tz=timezone)
    number_days = weekday_end - weekday_start
    day_start = toDatetime(weekday_start, time_start, timezone)
    day_end = toDatetime(weekday_start, time_end, timezone)

    for i in range(0, number_days+1):
        start = day_start + timedelta(days=i)
        end = day_end + timedelta(days=i)
        if start.strftime("%u") == now.strftime("%u"):
            return (now >= start) and (now <= end)
    return False
