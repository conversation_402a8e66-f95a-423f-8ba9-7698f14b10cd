# Lambda

data "aws_iam_policy" "kms_allow_decrypt" {
  arn = var.kms_allow_decrypt_policy
}

data "aws_iam_policy" "describe_instance" {
  arn = var.describe_ec2_instance_policy
}

data "aws_iam_policy" "describe_alarms" {
  arn = var.describe_cloudwatch_alarms_policy
}

data "aws_iam_role" "lambda_exec" {
  name = "glynt_${var.environment}_lambda_function_execution_role"
}

data "aws_lambda_function" "cloudwatch_action_notifier" {
  function_name    = "${var.environment}_cloudwatch_action_notifier"
}

data "aws_cloudwatch_log_group" "action_notifier_log" {
  name              = "/aws/lambda/${var.environment}_cloudwatch_action_notifier"
}

data "aws_lambda_function" "cloudwatch_alarm_watchdog" {
  function_name    = "${var.environment}_cloudwatch_alarm_notifier"
}

data "aws_cloudwatch_log_group" "alarm_notifier_log" {
  name = "/aws/lambda/${var.environment}_cloudwatch_alarm_notifier"
}

data "aws_lambda_function" "alarm_state_change_notifier" {
  function_name    = "${var.environment}_cloudwatch_alarm_state_change_notifier"
}

data "aws_cloudwatch_log_group" "alarm_ok_notifier_log" {
  name = "/aws/lambda/${var.environment}_cloudwatch_alarm_ok_notifier"
}

# SNS notification lambda hookup

data "aws_sns_topic" "alarm_state_change" {
  name = "${var.environment}-trigger-notification-when-alarm-state-change"
}
