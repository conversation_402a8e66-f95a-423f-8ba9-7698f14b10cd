resource "aws_flow_log" "flow_logs" {
  traffic_type         = "ALL"
  log_destination_type = "cloud-watch-logs"
  vpc_id               = var.vpc_id
  log_destination      = aws_cloudwatch_log_group.vpc_flow_logs.arn
  iam_role_arn         = aws_iam_role.cloudwatch_logs.arn
}

resource "aws_cloudwatch_log_group" "vpc_flow_logs" {
  name              = "flow_logs-${var.environment}"
  retention_in_days = var.retention_days
}

resource "aws_iam_role" "cloudwatch_logs" {
  name               = "cloudwatch_logs-${var.environment}"
  path               = "/"
  assume_role_policy = file("${path.module}/assume-role-policy.json")
}

resource "aws_iam_role_policy" "cloudwatch_logs_access" {
  name   = "allow-access-to-cw-logs-${var.environment}"
  role   = aws_iam_role.cloudwatch_logs.id
  policy = file("${path.module}/manage-log-groups-policy.json")
}
