data "aws_caller_identity" "current" {}

resource "aws_iam_policy" "ecr_access" {
  name        = "concourse_ecr_download"
  path        = "/"
  description = "Allows user to download images from ECR in us-west-1"
  policy = templatefile("${path.module}/templates/ecr-access-policy.json",
    {
      aws_region     = var.aws_region,
      aws_account_id = data.aws_caller_identity.current.account_id
    }
  )
}

resource "aws_iam_group" "concourse_ecr_download" {
  name = "concourse_ecr_download"
}

resource "aws_iam_group_policy_attachment" "concourse_ecr_download" {
  group      = aws_iam_group.concourse_ecr_download.name
  policy_arn = aws_iam_policy.ecr_access.arn
}

resource "aws_iam_user" "concourse_ecr" {
  name = "concourse_ecr"
  path = "/"

  tags = {
    Name = "concourse_ecr"
  }
}

resource "aws_iam_user_group_membership" "concourse_ecr_download" {
  user = aws_iam_user.concourse_ecr.name

  groups = [
    aws_iam_group.concourse_ecr_download.name
  ]
}
