{"Version": "2012-10-17", "Statement": [{"Sid": "ReadOnlyECR", "Effect": "Allow", "Action": ["ecr:GetDownloadUrlForLayer", "ecr:BatchGetImage", "ecr:DescribeImages", "ecr:DescribeRepositories", "ecr:ListTagsForResource", "ecr:ListImages"], "Resource": "arn:aws:ecr:${aws_region}:${aws_account_id}:repository/*"}, {"Sid": "GetAuthorizationToken", "Effect": "Allow", "Action": "ecr:GetAuthorizationToken", "Resource": "*"}]}