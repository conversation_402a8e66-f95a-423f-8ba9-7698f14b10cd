#!/usr/bin/env sh

# Description: Iterates over all the terraform dirs, runs terraform fmt and
#              generates a report.

EXECUTION_DIR="$(pwd)"
EXIT_CODE=0
REPORT_FILE="${EXECUTION_DIR}/output.txt"
TF_FILE="${EXECUTION_DIR}/delete_me.txt"
GLYNT_REPO_DIR="pull-request-terraform"

# list of terraform directories in the repo
set -- "__provision__/terraform" \
       "api/__provision__/terraform" \
       "boxcars/__provision__/terraform" \
       "common/terraform"

cat /dev/null > "${REPORT_FILE}"

for dir in "$@"; do
  cd "${GLYNT_REPO_DIR}/${dir}" || exit 1
  cat /dev/null > "${TF_FILE}"
  echo "[INFO] ===== executing 'terraform fmt' on '${dir}' directory =====" >> "${REPORT_FILE}"
  terraform fmt -check -recursive >"${TF_FILE}" 2>&1
  TF_EXIT_CODE=$?
  if [ $TF_EXIT_CODE -eq 0 ]; then
    echo "[INFO] Code is properly formatted. No changes required." >> "${REPORT_FILE}"
  elif [ $TF_EXIT_CODE -eq 2 ]; then
    echo "[ERROR] Code has compilation errors:" >> "${REPORT_FILE}"
    cat "${TF_FILE}" >> "${REPORT_FILE}"
    EXIT_CODE=1
  elif [ $TF_EXIT_CODE -eq 3 ]; then
    echo "[WARN] Files requiring changes:" >> "${REPORT_FILE}"
    nl "${TF_FILE}" >> "${REPORT_FILE}"
    EXIT_CODE=1
  else
    echo "[ERROR] Unknown error." >> "${REPORT_FILE}"
    cat "${TF_FILE}" >> "${REPORT_FILE}"
    EXIT_CODE=1
  fi
  cd "${EXECUTION_DIR}" || exit 1
done

cat "${REPORT_FILE}"

exit $EXIT_CODE

