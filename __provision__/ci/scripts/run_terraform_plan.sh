#!/usr/bin/env bash

set -o nounset

# Description: Iterates over all the terraform dirs, runs terraform plan and
#              generates a report.

EXIT_CODE=0
EXECUTION_DIR="$(pwd)"
TF_FILE="${EXECUTION_DIR}/delete_me.txt"
GLYNT_REPO_DIR="pull-request-terraform"

# Loads required environment variables
source "aws_credentials_${ENVIRONMENT}/aws_credentials.sh"
source "shared_env_${ENVIRONMENT}/${ENVIRONMENT}_shared_env.sh"
source "terraform_env_${ENVIRONMENT}/${ENVIRONMENT}_terraform_env.sh"
export TF_VAR_db_root_password="${DB_ROOT_PASSWORD}"
export TF_VAR_db_root_username="${DB_ROOT_USERNAME}"
export TF_VAR_auth0_domain="${AUTH0_DOMAIN}"
export TF_VAR_auth0_client_id="${AUTH0_CLIENT_ID}"
export TF_VAR_auth0_client_secret="${AUTH0_CLIENT_SECRET}"
export TF_VAR_auth0_redis_password="${AUTH0_REDIS_PASSWORD}"
export TF_VAR_aws_region="${AWS_REGION}"

readarray -td '' directories < <(find . -type d -name "terraform" -not -path '*/common/terraform*' -print0)

for dir in "${directories[@]}"; do
  cd "${dir}/environments/${ENVIRONMENT}" || exit 1
  cat /dev/null > "${TF_FILE}"
  echo "[INFO] ===== executing 'terraform init' on '${dir}' directory =====" | tee
  terraform init -input=false -no-color >>"${TF_FILE}" 2>&1
  TF_EXIT_CODE=$?

  if [ $TF_EXIT_CODE -ne 0 ]; then
    echo "[ERROR] 'terraform init' ran with errors"
    cat "${TF_FILE}"
    TF_EXIT_CODE=1
  else
    echo "[INFO] 'terraform init' ran successfully"
    echo "[INFO] ===== executing 'terraform plan' on '${dir}' directory =====" | tee
    terraform plan -input=false -no-color  >>"${TF_FILE}" 2>&1
    TF_EXIT_CODE=$?
    if [ $TF_EXIT_CODE -eq 0 ]; then
      echo "[INFO] Code has no errors."
    elif [ $TF_EXIT_CODE -eq 1 ]; then
      echo "[ERROR] Code has compilation errors:"
      cat "${TF_FILE}"
      EXIT_CODE=1
    else
      echo "[ERROR] Unknown error."
      cat "${TF_FILE}"
      EXIT_CODE=1
    fi
    cd "${EXECUTION_DIR}" || exit 1
    echo -e "\n\n\n"
  fi
done

exit $EXIT_CODE
