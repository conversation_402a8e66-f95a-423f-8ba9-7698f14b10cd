#!/bin/sh

APP_ENV=$1

# Setting EC2 credentials
. aws_credentials/aws_credentials.sh

. shared_env/${APP_ENV}_shared_env.sh
export TF_VAR_aws_region=${AWS_REGION}
export TF_VAR_db_root_username=${DB_ROOT_USERNAME}
export TF_VAR_db_root_password=${DB_ROOT_PASSWORD}
export TF_VAR_auth0_domain="${AUTH0_DOMAIN}"
export TF_VAR_auth0_client_id="${AUTH0_CLIENT_ID}"
export TF_VAR_auth0_client_secret="${AUTH0_CLIENT_SECRET}"
export TF_VAR_auth0_redis_password="${AUTH0_REDIS_PASSWORD}"

# Running terraform with all needed parameters
cd source_code/__provision__/terraform/environments/${APP_ENV} && \
	terraform init -upgrade && \
	terraform plan -out=current_tfplan && \
	echo "Applying plan in 30 seconds..." && \
	sleep 30 && \
	terraform apply current_tfplan && \
	rm current_tfplan

