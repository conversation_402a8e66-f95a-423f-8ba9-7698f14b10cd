---
#
# Description: AWS Access Key ID and Secret Access Key credentials
#
# These should be the keys for the "automation" user that will connect to AWS
# to retrieve the secrets from the secrets bucket.
#
# LastPass' location: Shared-Glynt-Infrastructure folder.
#                     "Baja Terraform user AWS Access Key" note.
#
aws_access_key_sandbox:
aws_secret_key_sandbox:


#
# Description: AWS Access Key ID and Secret Access Key credentials
#
# These should be the keys for the "automation" user that will connect to AWS
# to retrieve the secrets from the secrets bucket.
#
# LastPass' location: Shared-Glynt-Infrastructure folder.
#                     "Baja Terraform user AWS Access Key" note.
#
aws_access_key_stage:
aws_secret_key_stage:


#
# Description: AWS Access Key ID and Secret Access Key credentials
#
# These should be the keys for the "automation" user that will connect to AWS
# to retrieve the secrets from the secrets bucket.
#
# LastPass' location: Shared-Glynt-Infrastructure folder.
#                     "Bacalar Terraform user AWS Access Key" note.
#
aws_access_key_production:
aws_secret_key_production:


#
# Description: GitHub access token used to download source code from GitHub.
#
# LastPass' location: Shared-Glynt-Infrastructure folder,
#                     "GlyntBot Github Access Token" note.
#
github_access_token: 


#
# Description: AWS Access Key ID and Secret Access Key of the "concourse_ecr".
#              Used to download docker images from ECR in bacalar.
#
# LastPass' location: Shared-Glynt-Infrastructure folder,
#                     "bacalar - concourse_ecr" note.
aws_access_key_ecr_downloads_production:
aws_secret_key_ecr_downloads_production:
