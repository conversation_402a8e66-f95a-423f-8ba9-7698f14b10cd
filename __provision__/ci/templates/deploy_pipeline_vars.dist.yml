---
#
# Description: AWS region to deploy the app into
#
aws_region:


#
# Description: AWS Access Key ID and Secret Access Key credentials
#
# These should be the keys for the "automation" user that will connect to AWS
# to retrieve the secrets from the secrets bucket.
#
# LastPass' location: Shared-Glynt-Infrastructure folder.
#                     "<env> Terraform user AWS Access Key" note.
#
aws_access_key:
aws_secret_key:


#
# Description: GitHub access key used to download source code from GitHub.
#
# LastPass' location: Shared-Glynt-Infrastructure folder,
#                     "<env> 'glynt-api' Git Deploy Key" note.
#
# Comments:
#
# - Copy private key here. Paste the key indented as shown in the
#   <paste_key_here> prompt, below. The "|" character tells yaml to read the
#   indented lines below as multi-line text.
#
# - WARNING: be sure to remove any blank lines from the key when pasted. If
#   any blank lines are erroneously injected, the key gets misread during YAML
#   parsing.
#
git_key: |
  <paste_key_here>


#
# Description: AWS Access Key ID and Secret Access Key of the "concourse_ecr".
#              Used to download docker images from ECR in bacalar.
#
# LastPass' location: Shared-Glynt-Infrastructure folder,
#                     "bacalar - concourse_ecr" note.
aws_access_key_ecr_downloads_production:
aws_secret_key_ecr_downloads_production:
