FROM ubuntu:18.04

#
# This image is used to run ansible commands on the concourse pipeline.
#
# It provides AWS CLI v2 and ansible. There is a separate Dockerfile for
# Terraform (Dockerfile-terraform-custom, also in this directory).

# Installs latest version of AWS CLI v2
RUN apt-get -y update && \
    apt-get -y upgrade && \
    apt-get -y install unzip curl ca-certificates --no-install-recommends && \
    curl -s -o /tmp/awscliv2.zip "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" && \
    unzip /tmp/awscliv2.zip -d /opt/ && \
    /opt/aws/install && \
    rm /tmp/awscliv2.zip && \
    apt-get -y clean  && \
    apt-get -y autoremove --yes

RUN apt-get -y install locales openssh-client rsync python3.8 python3.8-venv python3-pip && \
    locale-gen en_US.UTF-8

ENV LANG=en_US.UTF-8
ENV LC_ALL=en_US.UTF-8

RUN python3.8 -m venv venv && \
    . venv/bin/activate && \
    pip install -q --upgrade pip && \
    pip install -q boto3==1.22.13 && \
    pip install -q ansible==5.7.1
