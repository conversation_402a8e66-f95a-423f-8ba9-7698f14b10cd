---
platform: linux

image_resource:
  type: docker-image
  source: 
    repository: 700095865679.dkr.ecr.us-west-1.amazonaws.com/glynt/terraform-custom
    aws_access_key_id: ((aws_access_key_ecr_downloads_production))
    aws_secret_access_key:  ((aws_secret_key_ecr_downloads_production))
    tag: "0.12.21"

inputs:
  - name: pull-request-terraform
  - name: aws_credentials_sandbox
  - name: shared_env_sandbox
  - name: terraform_env_sandbox
params:
  ENVIRONMENT: "((terraform_environment))"

run:
  path: /bin/bash
  args:
    - -xce
    - |
      ./pull-request-terraform/__provision__/ci/scripts/run_terraform_plan.sh
