---
platform: linux

image_resource:
  type: docker-image
  source:
    repository: 700095865679.dkr.ecr.us-west-1.amazonaws.com/glynt/python
    aws_access_key_id: ((aws_access_key_ecr_downloads_production))
    aws_secret_access_key:  ((aws_secret_key_ecr_downloads_production))
    tag: 3.6

inputs:
  - name: pull-request-common

run:
  path: pull-request-common/__provision__/ci/scripts/run_normalization_data_validation.sh
