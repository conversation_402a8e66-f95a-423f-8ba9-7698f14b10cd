---
platform: linux

image_resource:
  type: docker-image
  source:
    repository: amazon/aws-cli
    tag: "latest"

inputs:
  - name: codeartifact_env

outputs:
  - name: private-pypi-url

run:
  path: /bin/sh
  args:
   - -ce
   - |
     . codeartifact_env/codeartifact_credentials.sh
     AWS_ACCOUNT="************"
     AWS_REGION="us-east-1"
     CA_DOMAIN="glynt-artifacts"
     CA_REPOSITORY="glynt-private-pypi"
     TOKEN=$(aws codeartifact get-authorization-token --domain ${CA_DOMAIN} --domain-owner ${AWS_ACCOUNT} --region ${AWS_REGION} --query authorizationToken --output text)
     URL="https://aws:${TOKEN}@${CA_DOMAIN}-${AWS_ACCOUNT}.d.codeartifact.${AWS_REGION}.amazonaws.com/pypi/${CA_REPOSITORY}/simple"
     echo "export PIP_INDEX_URL=${URL}" > private-pypi-url/private_pypi_url.sh
