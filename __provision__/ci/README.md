# Continuous Integration Pipelines

The pipelines in this folder are Glynt-wide.

All credentials and secrets needed to deploy this pipeline are stored in the
`Shared-Glynt-infrastructure` and `Shared-Glynt-common` folders of LastPass.

## `test_pull_requests` pipeline

The `test_pull_requests` pipeline is responsible for running all automated dev
tests for all projects whenever a Pull Request is made against the repo, and
publishing the status of those checks to github.

To read and write to the repo, this pipeline use a github access token of the
GlyntBot github user **VERIFY that this is still true**. This pipeline is hosted on the Bacalar AWS account, on
the `ci` team.

Whenever a new project is added or new tests are added to an existing project
which requires a new entrypoint/invocation, the pipelines in this directory
will need to be updated, along with any needed tasks or scripts. Remember that
changes to the pipeline needs to be pushed to the Concourse server using the
`fly` cli.

## `deploy` pipelines

The "deploy" pipelines are responsible for deploying the shared infrastructure
that the other Glynt projects rely on. They are deployed to baja and bacalar as
appropriate.

As a reminder, to push a pipeline, follow these steps:

  1. `cd` to the `ci` directory.
  2. Create a `vars/<some_pipeline>.yml` from an approrpiate `*.dist.yml` from
     `/templates/` and populate with secrets.
  3. Ensure that the necessary secrets are present in s3. See the pipeline
     files themselves for a list of expected secrets. Use another environments
     secrets files as an example.
  4. Using `fly`, log in and setup a target for the correct concourse instance and team.
     If you get an 'Unknown Certificate Authority' error, add `-k` to the login command.
  5. Execute the following, being sure to use the exact same `pipeline_name` if
     you are updating an existing pipeline.
  
  ```bash
  fly -t <target> set-pipeline \
  -p <pipeline_name> \
  -c pipelines/<the_pipeline>.yml \
  --load-vars-from vars/<the_vars_file>.yml
  ```

## Custom terraform's docker image for PRs checks

The [test_api_pull_requests](https://bacalar-ci.wattzon.com/teams/ci/pipelines/test_api_pull_requests)
pipeline uses a custom terraform
image based on the [terraform official image](https://hub.docker.com/r/hashicorp/terraform/)

This applies also to the [test_core_pull_requests](https://bacalar-ci.wattzon.com/teams/ci/pipelines/test_core_pull_requests) pipeline
that lives in the glynt-core repo.

### Create the image and upload it to ECR

Follow these steps:

1. Create the docker image:

   ```bash
   docker build -f Dockerfile-terraform-custom -t terraform-custom:<terraform version> .
   ```

2. Authenticate docker against the ECR repo

   ```bash
   aws ecr get-login-password --region us-west-1 | \
   docker login --username AWS --password-stdin 700095865679.dkr.ecr.us-west-1.amazonaws.com
   ```

3. Tag the built image:

   ```bash
   docker tag terraform-bash:<terraform version> 700095865679.dkr.ecr.us-west-1.amazonaws.com/glynt/terraform-custom:<terraform version>
   ```

4. Push the image to ECR:

   ```bash
   docker push 700095865679.dkr.ecr.us-west-1.amazonaws.com/glynt/terraform-custom:<terraform version>
   ```

### Update the image version

If the terraform version in use changes, update the Dockerfile `Dockerfile-terraform-custom` by changing the
terraform version number, then build and upload the new version to ECR.
