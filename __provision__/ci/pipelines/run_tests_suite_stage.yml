---
resources:
  - name: awscli_public_key
    type: s3
    source:
      bucket: secrets.baja.wattzon
      versioned_file: infrastructure/awscliv2.pub
      region_name: us-west-2
      access_key_id: ((aws_access_key))
      secret_access_key: ((aws_secret_key))

jobs:
- name: run_tests
  plan:
  - get: awscli_public_key
  - config:
      container_limits: {}
      image_resource:
        source:
          repository: public.ecr.aws/e0y5m1g8/glynt-tox
          tag: latest
        type: docker-image
      platform: linux
      inputs:
        - name: awscli_public_key
      run:
        args:
        - -exc
        - |
          apt-get update -q && apt-get install -y sudo curl unzip gpg
          gpg --import awscli_public_key/awscliv2.pub 
          curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
          curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip.sig" -o "awscliv2.sig"
          gpg --verify awscliv2.sig awscliv2.zip         
          unzip awscliv2.zip
          sudo ./aws/install
          aws events put-events --region us-west-2 --entries '[{"Source": "concourse", "DetailType": "Run Test Suite", "Detail": "{}", "EventBusName": "default"}]' --no-cli-pager
        path: /bin/sh
    task: run_tests_suite
    vars:
      access_key_id: ((aws_access_key))
      secret_access_key: ((aws_secret_key))
