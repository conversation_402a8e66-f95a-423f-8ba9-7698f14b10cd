---
resource_types:
  - name: pull-request
    type: docker-image
    source: 
      repository: 700095865679.dkr.ecr.us-west-1.amazonaws.com/teliaoss/github-pr-resource
      aws_access_key_id: ((aws_access_key_ecr_downloads_production))
      aws_secret_access_key:  ((aws_secret_key_ecr_downloads_production))
      tag: v0.21.0

resources:

  - name: aws_credentials_sandbox
    type: s3
    source:
      bucket: secrets.baja.wattzon
      versioned_file: aws_credentials.sh
      region_name: us-west-2
      access_key_id: "((aws_access_key_sandbox))"
      secret_access_key: "((aws_secret_key_sandbox))"

  - name: shared_env_sandbox
    type: s3
    source:
      bucket: secrets.baja.wattzon
      versioned_file: sandbox_shared_env.sh
      region_name: us-west-2
      access_key_id: "((aws_access_key_sandbox))"
      secret_access_key: "((aws_secret_key_sandbox))"

  - name: terraform_env_sandbox
    type: s3
    source:
      bucket: secrets.baja.wattzon
      versioned_file: core/sandbox_terraform_env.sh 
      region_name: us-west-2
      access_key_id: "((aws_access_key_sandbox))"
      secret_access_key: "((aws_secret_key_sandbox))"

  - name: aws_credentials_stage
    type: s3
    source:
      bucket: secrets.baja.wattzon
      versioned_file: aws_credentials.sh
      region_name: us-west-2
      access_key_id: "((aws_access_key_stage))"
      secret_access_key: "((aws_secret_key_stage))"

  - name: shared_env_stage
    type: s3
    source:
      bucket: secrets.baja.wattzon
      versioned_file: stage_shared_env.sh
      region_name: us-west-2
      access_key_id: "((aws_access_key_stage))"
      secret_access_key: "((aws_secret_key_stage))"

  - name: terraform_env_stage
    type: s3
    source:
      bucket: secrets.baja.wattzon
      versioned_file: core/stage_terraform_env.sh 
      region_name: us-west-2
      access_key_id: "((aws_access_key_stage))"
      secret_access_key: "((aws_secret_key_stage))"

  - name: aws_credentials_production
    type: s3
    source:
      bucket: secrets.bacalar.wattzon
      versioned_file: aws_credentials.sh
      region_name: us-west-2
      access_key_id: "((aws_access_key_production))"
      secret_access_key: "((aws_secret_key_production))"

  - name: shared_env_production
    type: s3
    source:
      bucket: secrets.bacalar.wattzon
      versioned_file: production_shared_env.sh
      region_name: us-west-2
      access_key_id: "((aws_access_key_production))"
      secret_access_key: "((aws_secret_key_production))"

  - name: terraform_env_production
    type: s3
    source:
      bucket: secrets.bacalar.wattzon
      versioned_file: core/production_terraform_env.sh 
      region_name: us-west-2
      access_key_id: "((aws_access_key_production))"
      secret_access_key: "((aws_secret_key_production))"

  - name: codeartifact_env
    type: s3
    source:
      bucket: secrets.bacalar.wattzon
      versioned_file: codeartifact_credentials.sh
      region_name: us-west-2
      access_key_id: "((aws_access_key_production))"
      secret_access_key: "((aws_secret_key_production))"

  - name: pull-request-system-tests
    type: pull-request
    source:
      repository: PaceWebApp/glynt-api
      paths: ["test/system", "common/"]
      access_token: ((github_access_token))

  - name: pull-request-ansible-var-vault
    type: pull-request
    source:
      repository: PaceWebApp/glynt-api
      paths: ["tools/ansible-var-vault", "common/"]
      access_token: ((github_access_token))

  - name: pull-request-boxcars
    type: pull-request
    source:
      repository: PaceWebApp/glynt-api
      paths: ["boxcars/", "common/"]
      access_token: ((github_access_token))

  - name: pull-request-common
    type: pull-request
    source:
      repository: PaceWebApp/glynt-api
      paths: ["common/"]
      access_token: ((github_access_token))

  - name: pull-request-terraform
    type: pull-request
    source:
      repository: PaceWebApp/glynt-api
      paths:
        - "__provision__/terraform"
        - "api/__provision__/terraform"
        - "boxcars/__provision__/terraform"
        - "common/terraform"
      access_token: ((github_access_token))

jobs:
  - name: test_terraform_fmt
    plan:
    - get: pull-request-terraform
      trigger: true
      version: every
    - put: pull-request-terraform
      params:
        path: pull-request-terraform
        context: test_terraform_fmt
        status: pending
    - task: terraform_fmt
      file: pull-request-terraform/__provision__/ci/tasks/test_terraform_fmt.yml
      on_failure:
        put: pull-request-terraform
        params:
          path: pull-request-terraform
          context: test_terraform_fmt
          status: failure
      vars:
        aws_access_key_ecr_downloads_production: ((aws_access_key_ecr_downloads_production))
        aws_secret_key_ecr_downloads_production: ((aws_secret_key_ecr_downloads_production))
    - put: pull-request-terraform
      params:
        path: pull-request-terraform
        context: test_terraform_fmt
        status: success

  - name: test_terraform_plan_sandbox
    plan:
    - get: aws_credentials_sandbox
    - get: shared_env_sandbox
    - get: terraform_env_sandbox
    - get: pull-request-terraform
      trigger: true
      version: every
    - put: pull-request-terraform
      params:
        path: pull-request-terraform
        context: test_terraform_plan_sandbox
        status: pending
    - task: terraform_plan_sandbox
      file: pull-request-terraform/__provision__/ci/tasks/test_terraform_plan_sandbox.yml
      on_failure:
        put: pull-request-terraform
        params:
          path: pull-request-terraform
          context: test_terraform_plan_sandbox
          status: failure
      vars:
        aws_access_key: ((aws_access_key_sandbox))
        aws_secret_key: ((aws_secret_key_sandbox))
        aws_access_key_ecr_downloads_production: ((aws_access_key_ecr_downloads_production))
        aws_secret_key_ecr_downloads_production: ((aws_secret_key_ecr_downloads_production))
        terraform_environment: sandbox
    - put: pull-request-terraform
      params:
        path: pull-request-terraform
        context: test_terraform_plan_sandbox
        status: success

  - name: test_terraform_plan_stage
    plan:
    - get: aws_credentials_stage
    - get: shared_env_stage
    - get: terraform_env_stage
    - get: pull-request-terraform
      trigger: true
      version: every
    - put: pull-request-terraform
      params:
        path: pull-request-terraform
        context: test_terraform_plan_stage
        status: pending
    - task: terraform_plan_stage
      file: pull-request-terraform/__provision__/ci/tasks/test_terraform_plan_stage.yml
      on_failure:
        put: pull-request-terraform
        params:
          path: pull-request-terraform
          context: test_terraform_plan_stage
          status: failure
      vars:
        aws_access_key: ((aws_access_key_stage))
        aws_secret_key: ((aws_secret_key_stage))
        aws_access_key_ecr_downloads_production: ((aws_access_key_ecr_downloads_production))
        aws_secret_key_ecr_downloads_production: ((aws_secret_key_ecr_downloads_production))
        terraform_environment: stage
    - put: pull-request-terraform
      params:
        path: pull-request-terraform
        context: test_terraform_plan_stage
        status: success

  - name: test_terraform_plan_production
    plan:
    - get: aws_credentials_production
    - get: shared_env_production
    - get: terraform_env_production
    - get: pull-request-terraform
      trigger: true
      version: every
    - put: pull-request-terraform
      params:
        path: pull-request-terraform
        context: test_terraform_plan_production
        status: pending
    - task: terraform_plan_production
      file: pull-request-terraform/__provision__/ci/tasks/test_terraform_plan_production.yml
      on_failure:
        put: pull-request-terraform
        params:
          path: pull-request-terraform
          context: test_terraform_plan_production
          status: failure
      vars:
        aws_access_key: ((aws_access_key_production))
        aws_secret_key: ((aws_secret_key_production))
        aws_access_key_ecr_downloads_production: ((aws_access_key_ecr_downloads_production))
        aws_secret_key_ecr_downloads_production: ((aws_secret_key_ecr_downloads_production))
        terraform_environment: production
    - put: pull-request-terraform
      params:
        path: pull-request-terraform
        context: test_terraform_plan_production
        status: success

  - name: test_boxcars
    plan:
    - get: codeartifact_env
    - get: pull-request-boxcars
      trigger: true
      version: every
    - put: pull-request-boxcars
      params:
        path: pull-request-boxcars
        context: test_boxcars
        status: pending
    - task: make-private-pypi-url
      file: pull-request-boxcars/__provision__/ci/tasks/make_private_pypi_url.yml
    - task: unit-test
      file: pull-request-boxcars/__provision__/ci/tasks/test_boxcars.yml
      on_failure:
        put: pull-request-boxcars
        params:
          path: pull-request-boxcars
          context: test_boxcars
          status: failure
      vars:
        aws_access_key_ecr_downloads_production: ((aws_access_key_ecr_downloads_production))
        aws_secret_key_ecr_downloads_production: ((aws_secret_key_ecr_downloads_production))
    - put: pull-request-boxcars
      params:
        path: pull-request-boxcars
        context: test_boxcars
        status: success

  - name: test_django_auth0
    plan:
    - get: pull-request-common
      trigger: true
      version: every
    - put: pull-request-common
      params:
        path: pull-request-common
        context: test_django_auth0
        status: pending
    - task: unit-test
      file: pull-request-common/__provision__/ci/tasks/test_django_auth0.yml
      on_failure:
        put: pull-request-common
        params:
          path: pull-request-common
          context: test_django_auth0
          status: failure
      vars:
        aws_access_key_ecr_downloads_production: ((aws_access_key_ecr_downloads_production))
        aws_secret_key_ecr_downloads_production: ((aws_secret_key_ecr_downloads_production))
    - put: pull-request-common
      params:
        path: pull-request-common
        context: test_django_auth0
        status: success

  - name: test_obfuscate
    plan:
    - get: pull-request-common
      trigger: true
      version: every
    - put: pull-request-common
      params:
        path: pull-request-common
        context: test_obfuscate
        status: pending
    - task: unit-test
      file: pull-request-common/__provision__/ci/tasks/test_obfuscate.yml
      on_failure:
        put: pull-request-common
        params:
          path: pull-request-common
          context: test_obfuscate
          status: failure
      vars:
        aws_access_key_ecr_downloads_production: ((aws_access_key_ecr_downloads_production))
        aws_secret_key_ecr_downloads_production: ((aws_secret_key_ecr_downloads_production))
    - put: pull-request-common
      params:
        path: pull-request-common
        context: test_obfuscate
        status: success

  - name: test_parameter_store_client
    plan:
    - get: pull-request-common
      trigger: true
      version: every
    - put: pull-request-common
      params:
        path: pull-request-common
        context: test_parameter_store_client
        status: pending
    - task: unit-test
      file: pull-request-common/__provision__/ci/tasks/test_parameter_store_client.yml
      on_failure:
        put: pull-request-common
        params:
          path: pull-request-common
          context: test_parameter_store_client
          status: failure
      vars:
        aws_access_key_ecr_downloads_production: ((aws_access_key_ecr_downloads_production))
        aws_secret_key_ecr_downloads_production: ((aws_secret_key_ecr_downloads_production))
    - put: pull-request-common
      params:
        path: pull-request-common
        context: test_parameter_store_client
        status: success

  - name: test_mailer
    plan:
    - get: pull-request-common
      trigger: true
      version: every
    - put: pull-request-common
      params:
        path: pull-request-common
        context: test_mailer
        status: pending
    - task: unit-test
      file: pull-request-common/__provision__/ci/tasks/test_mailer.yml
      on_failure:
        put: pull-request-common
        params:
          path: pull-request-common
          context: test_mailer
          status: failure
      vars:
        aws_access_key_ecr_downloads_production: ((aws_access_key_ecr_downloads_production))
        aws_secret_key_ecr_downloads_production: ((aws_secret_key_ecr_downloads_production))
    - put: pull-request-common
      params:
        path: pull-request-common
        context: test_mailer
        status: success

  - name: test_django_maintenance_mode
    plan:
    - get: pull-request-common
      trigger: true
      version: every
    - put: pull-request-common
      params:
        path: pull-request-common
        context: test_django_maintenance_mode
        status: pending
    - task: unit-test
      file: pull-request-common/__provision__/ci/tasks/test_django_maintenance_mode.yml
      on_failure:
        put: pull-request-common
        params:
          path: pull-request-common
          context: test_django_maintenance_mode
          status: failure
      vars:
        aws_access_key_ecr_downloads_production: ((aws_access_key_ecr_downloads_production))
        aws_secret_key_ecr_downloads_production: ((aws_secret_key_ecr_downloads_production))
    - put: pull-request-common
      params:
        path: pull-request-common
        context: test_django_maintenance_mode
        status: success

  - name: test_django_slack_client
    plan:
    - get: pull-request-common
      trigger: true
      version: every
    - put: pull-request-common
      params:
        path: pull-request-common
        context: test_django_slack_client
        status: pending
    - task: unit-test
      file: pull-request-common/__provision__/ci/tasks/test_django_slack_client.yml
      on_failure:
        put: pull-request-common
        params:
          path: pull-request-common
          context: test_django_slack_client
          status: failure
      vars:
        aws_access_key_ecr_downloads_production: ((aws_access_key_ecr_downloads_production))
        aws_secret_key_ecr_downloads_production: ((aws_secret_key_ecr_downloads_production))
    - put: pull-request-common
      params:
        path: pull-request-common
        context: test_django_slack_client
        status: success

  - name: test_api_client
    plan:
    - get: pull-request-common
      trigger: true
      version: every
    - put: pull-request-common
      params:
        path: pull-request-common
        context: test_api_client
        status: pending
    - task: unit-test
      file: pull-request-common/__provision__/ci/tasks/test_api_client.yml
      on_failure:
        put: pull-request-common
        params:
          path: pull-request-common
          context: test_api_client
          status: failure
      vars:
        aws_access_key_ecr_downloads_production: ((aws_access_key_ecr_downloads_production))
        aws_secret_key_ecr_downloads_production: ((aws_secret_key_ecr_downloads_production))
    - put: pull-request-common
      params:
        path: pull-request-common
        context: test_api_client
        status: success

  - name: test_ansible_var_vault
    plan:
    - get: pull-request-ansible-var-vault
      trigger: true
      version: every
    - put: pull-request-ansible-var-vault
      params:
        path: pull-request-ansible-var-vault
        context: test_ansible_var_vault
        status: pending
    - task: unit-test
      file: pull-request-ansible-var-vault/__provision__/ci/tasks/test_ansible_var_vault.yml
      on_failure:
        put: pull-request-ansible-var-vault
        params:
          path: pull-request-ansible-var-vault
          context: test_ansible_var_vault
          status: failure
      vars:
        aws_access_key_ecr_downloads_production: ((aws_access_key_ecr_downloads_production))
        aws_secret_key_ecr_downloads_production: ((aws_secret_key_ecr_downloads_production))
    - put: pull-request-ansible-var-vault
      params:
        path: pull-request-ansible-var-vault
        context: test_ansible_var_vault
        status: success

  - name: test_system_tests
    plan:
    - get: pull-request-system-tests
      trigger: true
      version: every
    - put: pull-request-system-tests
      params:
        path: pull-request-system-tests
        context: test_system_tests
        status: pending
    - task: unit-test
      file: pull-request-system-tests/__provision__/ci/tasks/test_system_tests.yml
      on_failure:
        put: pull-request-system-tests
        params:
          path: pull-request-system-tests
          context: test_system_tests
          status: failure
      vars:
        aws_access_key_ecr_downloads_production: ((aws_access_key_ecr_downloads_production))
        aws_secret_key_ecr_downloads_production: ((aws_secret_key_ecr_downloads_production))
    - put: pull-request-system-tests
      params:
        path: pull-request-system-tests
        context: test_system_tests
        status: success

  - name: test_normalization_service
    plan:
    - get: pull-request-common
      trigger: true
      version: every
    - put: pull-request-common
      params:
        path: pull-request-common
        context: test_normalization_service
        status: pending
    - task: unit-test
      file: pull-request-common/__provision__/ci/tasks/test_normalization_service.yml
      on_failure:
        put: pull-request-common
        params:
          path: pull-request-common
          context: test_normalization_service
          status: failure
      vars:
        aws_access_key_ecr_downloads_production: ((aws_access_key_ecr_downloads_production))
        aws_secret_key_ecr_downloads_production: ((aws_secret_key_ecr_downloads_production))
    - put: pull-request-common
      params:
        path: pull-request-common
        context: test_normalization_service
        status: success

  - name: normalization_data_validation
    plan:
      - get: pull-request-common
        trigger: true
        version: every
      - task: data-validation-test
        config:
          platform: linux
          image_resource:
            type: docker-image
            source:
              repository: 700095865679.dkr.ecr.us-west-1.amazonaws.com/glynt/python
              aws_access_key_id: ((aws_access_key_ecr_downloads_production))
              aws_secret_access_key:  ((aws_secret_key_ecr_downloads_production))
              tag: 3.6
          inputs:
            - name: pull-request-common
          run:
            path: pull-request-common/__provision__/ci/scripts/run_normalization_data_validation.sh
        on_failure:
          put: pull-request-common
          params:
            path: pull-request-common
            context: normalization_data_validation
            status: failure
      - put: pull-request-common
        params:
          path: pull-request-common
          context: normalization_data_validation
          status: success
