#!/usr/bin/env python3
"""
Debug script to test CoC field configuration and identify 500 errors
"""

import os
import requests
import json
from typing import Dict, Any

# Configuration
API_BASE_URL = os.getenv('API_BASE_URL', 'https://api-sandbox.glynt.ai/v6')
USERNAME = os.getenv('USERNAME', 'your_username')
PASSWORD = os.getenv('PASSWORD', 'your_password')
DATA_POOL_ID = os.getenv('DATA_POOL_ID', 'your_data_pool_id')

def get_access_token():
    """Get access token for API authentication"""
    auth_url = f"{API_BASE_URL}/auth/login/"
    auth_data = {"username": USERNAME, "password": PASSWORD}
    
    try:
        response = requests.post(auth_url, json=auth_data)
        response.raise_for_status()
        return response.json().get('access_token')
    except Exception as e:
        print(f"❌ Failed to get access token: {e}")
        return None

def make_api_request(method: str, endpoint: str, token: str, data: Dict[str, Any] = None):
    """Make API request with error handling"""
    url = f"{API_BASE_URL}/data-pools/{DATA_POOL_ID}/{endpoint}"
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    try:
        if method == 'POST':
            response = requests.post(url, headers=headers, json=data)
        elif method == 'GET':
            response = requests.get(url, headers=headers)
        else:
            raise ValueError(f"Unsupported method: {method}")
        
        print(f"📡 {method} {url}")
        print(f"📊 Status: {response.status_code}")
        
        if response.status_code >= 400:
            print(f"❌ Error Response: {response.text}")
            return None
            
        return response.json()
        
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return None

def test_coc_field_configurations():
    """Test different CoC field configurations to identify issues"""
    
    access_token = get_access_token()
    if not access_token:
        return
    
    print("🧪 Testing CoC Field Configurations")
    print("=" * 50)
    
    # Test configurations
    test_configs = [
        {
            "name": "Minimal Config",
            "config": {
                "label": "Debug Test 1 - Minimal",
                "output_format": "json",
                "delivery_data_grouping_strategy": "ALL_IN_ONE",
                "s3_bucket": "glynt-sandbox-packager",
                "s3_prefix": "debug-test-1",
                "region": "us-west-2"
            }
        },
        {
            "name": "Basic CoC Config",
            "config": {
                "label": "Debug Test 2 - Basic CoC",
                "output_format": "json",
                "delivery_data_grouping_strategy": "ALL_IN_ONE",
                "s3_bucket": "glynt-sandbox-packager",
                "s3_prefix": "debug-test-2",
                "region": "us-west-2",
                "append_coc_fields_to_data": True,
                "coc_field_config": ["SourceFileID", "SourceFileOriginalName"]
            }
        },
        {
            "name": "Full CoC Config",
            "config": {
                "label": "Debug Test 3 - Full CoC",
                "output_format": "json",
                "delivery_data_grouping_strategy": "ALL_IN_ONE",
                "s3_bucket": "glynt-sandbox-packager",
                "s3_prefix": "debug-test-3",
                "region": "us-west-2",
                "append_coc_fields_to_data": True,
                "coc_field_config": [
                    "ChainOfCustodyStartDate",
                    "ChainOfCustodyEndDate",
                    "SourceFileReceivedAt",
                    "SourceFileID",
                    "SourceFileOriginalName",
                    "SourceFileOriginalPath",
                    "SourceFileFinalName",
                    "SourceFileStatus",
                    "ExclusionReason",
                    "ParentFileID",
                    "RelationshipToParent",
                    "PackageID",
                    "DeliveredAt"
                ],
                "coc_report_fields_config": [
                    "ChainOfCustodyStartDate",
                    "ChainOfCustodyEndDate",
                    "SourceFileReceivedAt",
                    "SourceFileID",
                    "SourceFileOriginalName",
                    "SourceFileOriginalPath",
                    "SourceFileFinalName",
                    "SourceFileStatus",
                    "ExclusionReason",
                    "ParentFileID",
                    "RelationshipToParent",
                    "PackageID",
                    "DeliveredAt",
                    "SourceFileNumPages",
                    "SourceFileDetectedLanguage",
                    "SourceFileSize"
                ]
            }
        }
    ]
    
    created_packagers = []
    
    for i, test_config in enumerate(test_configs):
        print(f"\n--- Test {i+1}: {test_config['name']} ---")
        
        config = test_config['config']
        print(f"📋 Config: {json.dumps(config, indent=2)}")
        
        # Try to create packager
        response = make_api_request('POST', 'packagers/', access_token, data=config)
        
        if response and 'id' in response:
            packager_id = response['id']
            created_packagers.append(packager_id)
            print(f"✅ Successfully created packager: {packager_id}")
            
            # Verify the configuration was saved correctly
            verify_response = make_api_request('GET', f'packagers/{packager_id}/', access_token)
            if verify_response:
                print(f"✅ Packager configuration verified")
                if 'coc_field_config' in verify_response:
                    print(f"   CoC fields: {len(verify_response.get('coc_field_config', []))} configured")
                if 'coc_report_fields_config' in verify_response:
                    print(f"   CoC report fields: {len(verify_response.get('coc_report_fields_config', []))} configured")
            else:
                print(f"⚠️ Could not verify packager configuration")
        else:
            print(f"❌ Failed to create packager for {test_config['name']}")
    
    # Cleanup
    print(f"\n🧹 Cleaning up {len(created_packagers)} test packagers...")
    for packager_id in created_packagers:
        delete_response = make_api_request('DELETE', f'packagers/{packager_id}/', access_token)
        if delete_response is not None:  # DELETE returns None on success
            print(f"✅ Deleted packager {packager_id}")
        else:
            print(f"⚠️ Could not delete packager {packager_id}")

if __name__ == "__main__":
    test_coc_field_configurations()
