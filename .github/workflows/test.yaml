# TODO: as the common packages get moved out of this repo, remove these tests and eventually
# retire the test-component.yaml version

on:
  pull_request:
    paths-ignore:
      - CHANGELOG.md

jobs:
  test-parameter-store-client:
    uses: PaceWebApp/glynt-api/.github/workflows/test-component.yaml@master
    secrets: inherit
    with:
      python-version: 3.8
      working-directory: common/python/parameter-store-client
      executor-image: ubuntu-24.04

  test-api:
    uses: PaceWebApp/glynt-reusable-workflows/.github/workflows/test-pr.yaml@master
    secrets: inherit
    with:
      python-version: 3.8
      working-directory: api
      install-system-deps: true
      test-command-line: docker compose -f docker-test-mysql-db.yml up -d && poetry run pytest -q -n 4
      timeout-minutes: 60

  test-boxcars:
    uses: PaceWebApp/glynt-api/.github/workflows/test-component.yaml@master
    secrets: inherit
    with:
      python-version: 3.8
      working-directory: boxcars
      executor-image: ubuntu-24.04

  test-ansible-vault:
    uses: PaceWebApp/glynt-api/.github/workflows/test-component.yaml@master
    secrets: inherit
    with:
      python-version: 3.8
      working-directory: tools/ansible-var-vault
      executor-image: ubuntu-24.04
