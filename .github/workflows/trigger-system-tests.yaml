name: Trigger System Tests Pipeline

on:
  workflow_dispatch: # Allows manual triggering from the Actions tab

permissions:
  id-token: write # Required to allow calling workflow to pass OIDC token to reusable workflow
  contents: read

jobs:
  call_trigger_workflow:
    name: Trigger System Tests CodePipeline
    # Ensure the glynt-api repo settings allow calling reusable workflows from PaceWebApp/glynt-reusable-workflow
    uses: PaceWebApp/glynt-reusable-workflows/.github/workflows/trigger-aws-codepipeline.yaml@master
    with:
      pipeline_name: 'glynt-system-tests-pipeline' # The specific pipeline to trigger
      aws_region: ${{ secrets.AWS_REGION }} # Use secret defined in glynt-api repo
    secrets:
      AWS_ROLE_TO_ASSUME: ${{ secrets.AWS_ROLE_TO_ASSUME }} # Pass the secret from glynt-api repo to the reusable workflow