on:
  workflow_call:
    inputs:
      executor-image:
        required: true
        type: string
      python-version:
        required: true
        type: string
      working-directory:
        required: true
        type: string
      install-system-deps:
        required: false
        type: boolean
        default: false
      test-command-line:
        required: false
        type: string
        default: python runtests.py --nolint

jobs:
  test-component:
    runs-on: ${{ inputs.executor-image }}
    defaults:
      run:
        working-directory: ${{ inputs.working-directory }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Python ${{ inputs.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ inputs.python-version }}
          cache: pip

      # TODO: cache me or go Docker
      - name: Install system dependencies
        if: ${{ inputs.install-system-deps }}
        run: |
          sudo apt-get update
          sudo apt-get install -y --no-install-recommends libmysqlclient-dev poppler-utils file libmagickwand-dev

      - name: Install dependencies
        run: |
          pip install --upgrade pip
          pip install wheel
          pip install -r requirements.txt

      - name: Lint the code
        run:
          python runtests.py --lintonly

      - name: Run the tests
        run:
          ${{ inputs.test-command-line }}
