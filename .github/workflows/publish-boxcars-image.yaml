on:
  workflow_dispatch:
    inputs:
      image-version:
        required: true
        type: string
        description: image version (docker tag) to publish; will publish glynt-boxcars:<image-version>

jobs:
  build-and-push:
    name: 'Build and publish boxcars image'
    uses: PaceWebApp/glynt-reusable-workflows/.github/workflows/publish-docker-image.yaml@master
    secrets: inherit
    with:
      image-name: 'glynt-boxcars'
      image-version: ${{ github.event.inputs.image-version || github.ref_name }}
      working-directory: 'boxcars'
