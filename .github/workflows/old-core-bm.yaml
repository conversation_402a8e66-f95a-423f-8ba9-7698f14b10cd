name: run core benchmark test

on:
  workflow_dispatch:

defaults:
  run:
    shell: bash

jobs:
  test:
    runs-on: ubuntu-20.04

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Python 3.8
        uses: actions/setup-python@v4
        with:
          python-version: 3.8

      - name: Setup glynt credentials
        run: |
          echo SANDBOX_BASE_URL=https://api-sandbox.glynt.ai/v6 > tools/api-client-tools/.env
          echo SANDBOX_USERNAME=<EMAIL> >> tools/api-client-tools/.env
          echo SANDBOX_PASSWORD=${{secrets.TEST_DUMMY_STAFF_SANDBOX_PASSWORD}} >> tools/api-client-tools/.env
          
      - name: Install requirements
        run: |
          cd tools/api-client-tools/
          pip install -r requirements.txt
          

      - name: Run the core benchmark test
        run: |
          cd tools/api-client-tools/
          python3 -m core_version_benchmark.core_version_benchmarker_2 run

