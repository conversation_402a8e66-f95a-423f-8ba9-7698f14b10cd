Timestamp,Step,Status,Details
2025-06-10T02:20:12.045351,<PERSON><PERSON><PERSON> Start,Completed,
2025-06-10T02:20:13.652076,Get Access Token,Completed,
2025-06-10T02:20:20.037384,Data Pool Cleanup - Delete extractionbatchdata,Completed,Successfully deleted all 1 extractionbatchdata
2025-06-10T02:20:21.322449,Data Pool Cleanup - Delete extractionbatches,Completed,Successfully deleted all 1 extractionbatches
2025-06-10T02:20:22.648144,Data Pool Cleanup - Delete packages,Completed,Successfully deleted all 1 packages
2025-06-10T02:20:24.370307,Data Pool Cleanup - Delete packagers,Completed,Successfully deleted all 1 packagers
2025-06-10T02:20:28.022247,Data Pool Cleanup - Delete extractions,Completed,Successfully deleted all 6 extractions
2025-06-10T02:20:30.865553,Data Pool Cleanup - Delete documents,Completed,Successfully deleted all 6 documents
2025-06-10T02:20:33.365986,Data Pool Cleanup - Delete documentdata,Completed,Successfully deleted all 6 documentdata
2025-06-10T02:20:33.366308,Data Pool Cleanup,Completed,
2025-06-10T02:20:33.366654,Packager Lifecycle Test,Skipped by user,
2025-06-10T02:20:33.366880,Main Packager Creation,Skipped by user,
2025-06-10T02:20:33.367080,Upload Documents,Skipped by user,
2025-06-10T02:20:33.367271,Document Processing Status Check,Skipped by user,
2025-06-10T02:20:33.367458,Create Extraction Batch,Skipped by user,
2025-06-10T02:20:33.367691,Simulate Content Duplication,Skipped by user,
2025-06-10T02:20:33.367913,Verify Extraction Batch,Skipped by user,
2025-06-10T02:20:33.368097,Change Packager Schedule to CRON,Skipped by user,
2025-06-10T02:20:33.368334,Validate CRON Package,Skipped by user,
2025-06-10T02:20:33.368611,Test Package Reuse,Skipped by user,
2025-06-10T02:20:33.368844,Test Delete Package and Re-run CRON,Skipped by user,
2025-06-10T02:20:33.369058,Simulate Package,Skipped by user,
2025-06-10T02:20:33.369345,Create Package,Skipped by user,
2025-06-10T02:20:33.369687,Trigger Delivery,Skipped by user,
2025-06-10T02:20:33.369912,Flag Variation Testing,Skipped by user,
2025-06-10T02:20:33.370103,Generate CoC Report,Skipped by user,
2025-06-10T02:20:33.370268,Script End,Completed,
