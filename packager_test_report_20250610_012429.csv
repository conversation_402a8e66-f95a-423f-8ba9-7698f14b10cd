Timestamp,Step,Status,Details
2025-06-10T01:24:29.603918,<PERSON><PERSON><PERSON> Start,Completed,
2025-06-10T01:24:37.322232,Get Access Token,Completed,
2025-06-10T01:24:46.267868,Data Pool Cleanup - Delete extractionbatchdata,Completed,Successfully deleted all 1 extractionbatchdata
2025-06-10T01:24:48.169278,Data Pool Cleanup - Delete extractionbatches,Completed,Successfully deleted all 1 extractionbatches
2025-06-10T01:24:49.835806,Data Pool Cleanup - Delete packages,Completed,Successfully deleted all 1 packages
2025-06-10T01:24:57.247373,Data Pool Cleanup - Delete packagers,Completed,Successfully deleted all 1 packagers
2025-06-10T01:25:01.391223,Data Pool Cleanup - Delete extractions,Completed,Successfully deleted all 6 extractions
2025-06-10T01:25:04.903220,Data Pool Cleanup - Delete documents,Completed,Successfully deleted all 6 documents
2025-06-10T01:25:09.888542,Data Pool Cleanup - Delete documentdata,Completed,Successfully deleted all 6 documentdata
2025-06-10T01:25:09.888829,Data Pool Cleanup,Completed,
2025-06-10T01:25:12.300913,Packager Lifecycle Test,Completed,Created packager PNv4D1 with 12/12 validations passed. Verified packager exists with 12/12 field validations passed. Deleted temp packager PNv4D1. Verified temp packager PNv4D1 is deleted. 
2025-06-10T01:25:12.658313,Main Packager Creation,Completed,Created main packager ID: Xu4W32
2025-06-10T01:25:32.323237,Enhanced Document Upload,Completed,Uploaded 6/6 documents with 96/96 validations passed
2025-06-10T01:25:34.263691,Document Processing Status Check,Completed,All 6 documents still processing. 
2025-06-10T01:26:16.724568,Create Extraction Batch,Completed,Created extraction batch ID: cGUzl1. Batch processing completed. 
2025-06-10T01:26:19.955267,Simulate Content Duplication,Completed,Created identical content in 3 documents for duplicate testing. Created 3 corrections for orig_doc_1.pdf. Created 3 corrections for origin_doc_2.pdf. Created 3 corrections for orig_doc_3.pdf. 
2025-06-10T01:26:42.085519,Verify Extraction Batch,Completed,"Verified extraction batch cGUzl1 on attempt 1. Found 4 duplicate relationships (0 MD5, 0 label, 4 content). "
2025-06-10T01:27:03.651916,Change Packager Schedule to CRON,Completed,Changed packager Xu4W32 schedule to CRON. CRON schedule successfully created package JFu881 after 20s. Package processing completed. CRON schedule working correctly. 
2025-06-10T01:27:04.867696,Validate CRON Package,Completed, 4 duplicate relationships found. CRON package validation: 3/3 checks passed.
2025-06-10T01:27:04.867988,Test Package Reuse,Skipped by user,
2025-06-10T01:27:04.868238,Test Delete Package and Re-run CRON,Skipped by user,
2025-06-10T01:27:05.200718,Reset Packager to MANUAL,Completed,Packager schedule set to MANUAL
2025-06-10T01:27:09.369669,Simulate Package,Completed,"Simulation successful: 1 data items, 0 errors. "
2025-06-10T01:27:14.809675,Create Package,Partial Success,"Using CRON package ID: JFu881.  4 duplicate relationships found (0 MD5, 4 content). Package validation: 5/13 checks passed."
2025-06-10T01:27:14.809993,Trigger Delivery,Skipped by user,
2025-06-10T01:27:17.028726,Flag Variation Testing,Partial Success,Only 0/3 flag variation tests passed. 
2025-06-10T01:27:17.344264,Generate CoC Report,Completed,CoC report response received. 
2025-06-10T01:27:17.344573,Script End,Completed,
