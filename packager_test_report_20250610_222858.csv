Timestamp,Step,Status,Details
2025-06-10T22:28:58.372071,<PERSON><PERSON><PERSON> Start,Completed,
2025-06-10T22:29:02.102622,Get Access Token,Completed,
2025-06-10T22:29:13.018955,Data Pool Cleanup - Delete extractionbatchdata,Completed,Successfully deleted all 1 extractionbatchdata
2025-06-10T22:29:18.719303,Data Pool Cleanup - Delete extractionbatches,Completed,Successfully deleted all 1 extractionbatches
2025-06-10T22:29:30.500874,Data Pool Cleanup - Delete packages,Completed,Successfully deleted all 1 packages
2025-06-10T22:29:33.216570,Data Pool Cleanup - Delete packagers,Completed,Successfully deleted all 1 packagers
2025-06-10T22:29:37.610606,Data Pool Cleanup - Delete extractions,Completed,Successfully deleted all 6 extractions
2025-06-10T22:29:50.927153,Data Pool Cleanup - Delete documents,Completed,Successfully deleted all 10 documents
2025-06-10T22:29:56.176923,Data Pool Cleanup - Delete documentdata,Completed,Successfully deleted all 10 documentdata
2025-06-10T22:29:56.177255,Data Pool Cleanup,Completed,
2025-06-10T22:29:58.586713,Packager Lifecycle Test,Completed,Created packager Y4xTK1 with 12/12 validations passed. Verified packager exists with 12/12 field validations passed. Deleted temp packager Y4xTK1. Verified temp packager Y4xTK1 is deleted. 
2025-06-10T22:29:58.912436,Main Packager Creation,Completed,Created main packager ID: gb6vA2
2025-06-10T22:30:18.985753,Enhanced Document Upload,Completed,Uploaded 6/6 documents with 96/96 validations passed
2025-06-10T22:30:21.197056,Document Processing Status Check,Completed,All 6 documents still processing. 
2025-06-10T22:30:53.732426,Create Extraction Batch,Completed,Created extraction batch ID: YE2OR1. Batch processing completed. 
2025-06-10T22:30:56.988057,Simulate Content Duplication,Completed,Created identical content in 3 documents for duplicate testing. Created 3 corrections for orig_doc_1.pdf. Created 3 corrections for origin_doc_2.pdf. Created 3 corrections for orig_doc_3.pdf. 
2025-06-10T22:31:19.065252,Verify Extraction Batch,Completed,"Verified extraction batch YE2OR1 on attempt 1. Found 4 duplicate relationships (0 MD5, 0 label, 4 content). "
2025-06-10T22:32:01.314585,Change Packager Schedule to CRON,Completed,Changed packager gb6vA2 schedule to CRON. CRON schedule successfully created package kF9Ku1 after 40s. Package processing completed. CRON schedule working correctly. 
2025-06-10T22:32:02.562000,Validate CRON Package,Completed, 4 duplicate relationships found. CRON package validation: 3/3 checks passed.
2025-06-10T22:32:02.562270,Test Package Reuse,Skipped by user,
2025-06-10T22:32:02.562702,Test Delete Package and Re-run CRON,Skipped by user,
2025-06-10T22:32:02.911933,Reset Packager to MANUAL,Completed,Packager schedule set to MANUAL
2025-06-10T22:32:06.410619,Simulate Package,Completed,"Simulation successful: 1 data items, 0 errors. "
2025-06-10T22:32:10.819163,Duplicate Filtering Test,Completed,All 3 duplicate filtering tests passed. 
2025-06-10T22:32:16.355568,Create Package,Partial Success,"Using CRON package ID: kF9Ku1.  4 duplicate relationships found (0 MD5, 4 content). Package validation: 18/19 checks passed."
2025-06-10T22:32:16.355931,Trigger Delivery,Skipped by user,
2025-06-10T22:32:18.664316,Flag Variation Testing,Partial Success,Only 0/3 flag variation tests passed. 
2025-06-10T22:32:20.900358,Debug API Issues,Completed,All 4 debug tests passed
2025-06-10T22:32:35.849433,Incremental vs Cumulative Delivery Testing,Completed,All 0 delivery scenario tests passed
2025-06-10T22:32:36.176853,Generate CoC Report,Completed,CoC report generated with 10 entries. 
2025-06-10T22:32:36.177137,Script End,Completed,
