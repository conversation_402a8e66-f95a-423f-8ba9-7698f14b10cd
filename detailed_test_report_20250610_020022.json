{"test_run_info": {"timestamp": "2025-06-10T02:00:22.148762", "total_tests": 143, "duration_seconds": 177.94965624809265, "api_calls": 56, "api_errors": 6}, "summary": {"passed": 137, "failed": 6, "partial": 0, "errors": 0, "skipped": 0}, "test_results": [{"test_name": "Packager Config - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "9TqnJ1"}}, {"test_name": "Packager Config - label", "status": "PASSED", "message": "Required field 'label' exists", "expected": null, "actual": null, "details": {"field_value": "Enhanced Test Script Packager"}}, {"test_name": "Packager Config - packager_schedule_type", "status": "PASSED", "message": "Required field 'packager_schedule_type' exists", "expected": null, "actual": null, "details": {"field_value": "MANUAL"}}, {"test_name": "Packager Config - document_status_filter", "status": "PASSED", "message": "Required field 'document_status_filter' exists", "expected": null, "actual": null, "details": {"field_value": ["VERIFIED"]}}, {"test_name": "Packager Config - eb_status_filter", "status": "PASSED", "message": "Required field 'eb_status_filter' exists", "expected": null, "actual": null, "details": {"field_value": ["VERIFIED"]}}, {"test_name": "Packager Config - output_format", "status": "PASSED", "message": "Required field 'output_format' exists", "expected": null, "actual": null, "details": {"field_value": "json"}}, {"test_name": "Packager Config - delivery_data_grouping_strategy", "status": "PASSED", "message": "Required field 'delivery_data_grouping_strategy' exists", "expected": null, "actual": null, "details": {"field_value": "ALL_IN_ONE"}}, {"test_name": "Packager Config - Label", "status": "PASSED", "message": "label has correct value: Enhanced Test Script Packager", "expected": "Enhanced Test Script Packager", "actual": "Enhanced Test Script Packager", "details": {}}, {"test_name": "Packager Config - Schedule Type", "status": "PASSED", "message": "packager_schedule_type has correct value: MANUAL", "expected": "MANUAL", "actual": "MANUAL", "details": {}}, {"test_name": "Packager Config - Output Format", "status": "PASSED", "message": "output_format has correct value: json", "expected": "json", "actual": "json", "details": {}}, {"test_name": "Packager Config - Document Status Filter", "status": "PASSED", "message": "Document status filter correct: ['VERIFIED']", "expected": ["VERIFIED"], "actual": ["VERIFIED"], "details": {}}, {"test_name": "Packager Config - Deduplication Fields", "status": "PASSED", "message": "Deduplication fields correct: []", "expected": [], "actual": [], "details": {}}, {"test_name": "Packager Config - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "9TqnJ1"}}, {"test_name": "Packager Config - label", "status": "PASSED", "message": "Required field 'label' exists", "expected": null, "actual": null, "details": {"field_value": "Enhanced Test Script Packager"}}, {"test_name": "Packager Config - packager_schedule_type", "status": "PASSED", "message": "Required field 'packager_schedule_type' exists", "expected": null, "actual": null, "details": {"field_value": "MANUAL"}}, {"test_name": "Packager Config - document_status_filter", "status": "PASSED", "message": "Required field 'document_status_filter' exists", "expected": null, "actual": null, "details": {"field_value": ["VERIFIED"]}}, {"test_name": "Packager Config - eb_status_filter", "status": "PASSED", "message": "Required field 'eb_status_filter' exists", "expected": null, "actual": null, "details": {"field_value": ["VERIFIED"]}}, {"test_name": "Packager Config - output_format", "status": "PASSED", "message": "Required field 'output_format' exists", "expected": null, "actual": null, "details": {"field_value": "json"}}, {"test_name": "Packager Config - delivery_data_grouping_strategy", "status": "PASSED", "message": "Required field 'delivery_data_grouping_strategy' exists", "expected": null, "actual": null, "details": {"field_value": "ALL_IN_ONE"}}, {"test_name": "Packager Config - Label", "status": "PASSED", "message": "label has correct value: Enhanced Test Script Packager", "expected": "Enhanced Test Script Packager", "actual": "Enhanced Test Script Packager", "details": {}}, {"test_name": "Packager Config - Schedule Type", "status": "PASSED", "message": "packager_schedule_type has correct value: MANUAL", "expected": "MANUAL", "actual": "MANUAL", "details": {}}, {"test_name": "Packager Config - Output Format", "status": "PASSED", "message": "output_format has correct value: json", "expected": "json", "actual": "json", "details": {}}, {"test_name": "Packager Config - Document Status Filter", "status": "PASSED", "message": "Document status filter correct: ['VERIFIED']", "expected": ["VERIFIED"], "actual": ["VERIFIED"], "details": {}}, {"test_name": "Packager Config - Deduplication Fields", "status": "PASSED", "message": "Deduplication fields correct: []", "expected": [], "actual": [], "details": {}}, {"test_name": "Invalid Config - packager_schedule_type", "status": "PASSED", "message": "Invalid configuration properly rejected for field: packager_schedule_type", "expected": null, "actual": null, "details": {}}, {"test_name": "Invalid Config - output_format", "status": "PASSED", "message": "Invalid configuration properly rejected for field: output_format", "expected": null, "actual": null, "details": {}}, {"test_name": "Invalid Config - label", "status": "FAILED", "message": "Invalid configuration was accepted (should have been rejected): label", "expected": null, "actual": null, "details": {"created_packager_id": "iEQ1k1"}}, {"test_name": "Document Upload - COPY_doc2_dupe1.pdf - Success", "status": "PASSED", "message": "Document uploaded successfully with ID: nWasS1", "expected": null, "actual": null, "details": {"document_id": "nWasS1", "file_name": "COPY_doc2_dupe1.pdf"}}, {"test_name": "Document Upload - COPY_doc2_dupe1.pdf - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "nWasS1"}}, {"test_name": "Document Upload - COPY_doc2_dupe1.pdf - label", "status": "PASSED", "message": "Required field 'label' exists", "expected": null, "actual": null, "details": {"field_value": "COPY_doc2_dupe1.pdf"}}, {"test_name": "Document Upload - COPY_doc2_dupe1.pdf - content_type", "status": "PASSED", "message": "Required field 'content_type' exists", "expected": null, "actual": null, "details": {"field_value": "application/pdf"}}, {"test_name": "Document Upload - COPY_doc2_dupe1.pdf - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-10T06:57:37.883207Z"}}, {"test_name": "Document Upload - COPY_doc2_dupe1.pdf - Label", "status": "PASSED", "message": "label has correct value: COPY_doc2_dupe1.pdf", "expected": "COPY_doc2_dupe1.pdf", "actual": "COPY_doc2_dupe1.pdf", "details": {}}, {"test_name": "Document Upload - COPY_doc2_dupe1.pdf - Content Type", "status": "PASSED", "message": "content_type has correct value: application/pdf", "expected": "application/pdf", "actual": "application/pdf", "details": {}}, {"test_name": "DocumentData for nWasS1 - Existence", "status": "PASSED", "message": "DocumentData found for document nWasS1", "expected": null, "actual": null, "details": {"doc_data_id": "jmhBc1"}}, {"test_name": "DocumentData for nWasS1 - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "jmhBc1"}}, {"test_name": "DocumentData for nWasS1 - document", "status": "PASSED", "message": "Required field 'document' exists", "expected": null, "actual": null, "details": {"field_value": "https://api-sandbox.glynt.ai/v6/data-pools/Fj8uL1/documents/nWasS1/"}}, {"test_name": "DocumentData for nWasS1 - status", "status": "PASSED", "message": "Required field 'status' exists", "expected": null, "actual": null, "details": {"field_value": "PENDING_CREATION"}}, {"test_name": "DocumentData for nWasS1 - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-10T06:57:37.895851Z"}}, {"test_name": "DocumentData for nWasS1 - updated_at", "status": "PASSED", "message": "Required field 'updated_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-10T06:57:37.895893Z"}}, {"test_name": "DocumentData for nWasS1 - ID Type", "status": "PASSED", "message": "Field 'id' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for nWasS1 - Status Type", "status": "PASSED", "message": "Field 'status' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for nWasS1 - Status Valid", "status": "PASSED", "message": "DocumentData has valid status: PENDING_CREATION", "expected": null, "actual": null, "details": {}}, {"test_name": "Document Upload - COPY_doc3_dupe1.pdf - Success", "status": "PASSED", "message": "Document uploaded successfully with ID: MIA6t1", "expected": null, "actual": null, "details": {"document_id": "MIA6t1", "file_name": "COPY_doc3_dupe1.pdf"}}, {"test_name": "Document Upload - COPY_doc3_dupe1.pdf - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "MIA6t1"}}, {"test_name": "Document Upload - COPY_doc3_dupe1.pdf - label", "status": "PASSED", "message": "Required field 'label' exists", "expected": null, "actual": null, "details": {"field_value": "COPY_doc3_dupe1.pdf"}}, {"test_name": "Document Upload - COPY_doc3_dupe1.pdf - content_type", "status": "PASSED", "message": "Required field 'content_type' exists", "expected": null, "actual": null, "details": {"field_value": "application/pdf"}}, {"test_name": "Document Upload - COPY_doc3_dupe1.pdf - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-10T06:57:42.989389Z"}}, {"test_name": "Document Upload - COPY_doc3_dupe1.pdf - Label", "status": "PASSED", "message": "label has correct value: COPY_doc3_dupe1.pdf", "expected": "COPY_doc3_dupe1.pdf", "actual": "COPY_doc3_dupe1.pdf", "details": {}}, {"test_name": "Document Upload - COPY_doc3_dupe1.pdf - Content Type", "status": "PASSED", "message": "content_type has correct value: application/pdf", "expected": "application/pdf", "actual": "application/pdf", "details": {}}, {"test_name": "DocumentData for MIA6t1 - Existence", "status": "PASSED", "message": "DocumentData found for document MIA6t1", "expected": null, "actual": null, "details": {"doc_data_id": "IYHP22"}}, {"test_name": "DocumentData for MIA6t1 - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "IYHP22"}}, {"test_name": "DocumentData for MIA6t1 - document", "status": "PASSED", "message": "Required field 'document' exists", "expected": null, "actual": null, "details": {"field_value": "https://api-sandbox.glynt.ai/v6/data-pools/Fj8uL1/documents/MIA6t1/"}}, {"test_name": "DocumentData for MIA6t1 - status", "status": "PASSED", "message": "Required field 'status' exists", "expected": null, "actual": null, "details": {"field_value": "PENDING_CREATION"}}, {"test_name": "DocumentData for MIA6t1 - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-10T06:57:43.002838Z"}}, {"test_name": "DocumentData for MIA6t1 - updated_at", "status": "PASSED", "message": "Required field 'updated_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-10T06:57:43.002881Z"}}, {"test_name": "DocumentData for MIA6t1 - ID Type", "status": "PASSED", "message": "Field 'id' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for MIA6t1 - Status Type", "status": "PASSED", "message": "Field 'status' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for MIA6t1 - Status Valid", "status": "PASSED", "message": "DocumentData has valid status: PENDING_CREATION", "expected": null, "actual": null, "details": {}}, {"test_name": "Document Upload - COPY_doc3_dupe2.pdf - Success", "status": "PASSED", "message": "Document uploaded successfully with ID: fNqdD1", "expected": null, "actual": null, "details": {"document_id": "fNqdD1", "file_name": "COPY_doc3_dupe2.pdf"}}, {"test_name": "Document Upload - COPY_doc3_dupe2.pdf - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "fNqdD1"}}, {"test_name": "Document Upload - COPY_doc3_dupe2.pdf - label", "status": "PASSED", "message": "Required field 'label' exists", "expected": null, "actual": null, "details": {"field_value": "COPY_doc3_dupe2.pdf"}}, {"test_name": "Document Upload - COPY_doc3_dupe2.pdf - content_type", "status": "PASSED", "message": "Required field 'content_type' exists", "expected": null, "actual": null, "details": {"field_value": "application/pdf"}}, {"test_name": "Document Upload - COPY_doc3_dupe2.pdf - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-10T06:57:46.456858Z"}}, {"test_name": "Document Upload - COPY_doc3_dupe2.pdf - Label", "status": "PASSED", "message": "label has correct value: COPY_doc3_dupe2.pdf", "expected": "COPY_doc3_dupe2.pdf", "actual": "COPY_doc3_dupe2.pdf", "details": {}}, {"test_name": "Document Upload - COPY_doc3_dupe2.pdf - Content Type", "status": "PASSED", "message": "content_type has correct value: application/pdf", "expected": "application/pdf", "actual": "application/pdf", "details": {}}, {"test_name": "DocumentData for fNqdD1 - Existence", "status": "PASSED", "message": "DocumentData found for document fNqdD1", "expected": null, "actual": null, "details": {"doc_data_id": "bdxwM1"}}, {"test_name": "DocumentData for fNqdD1 - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "bdxwM1"}}, {"test_name": "DocumentData for fNqdD1 - document", "status": "PASSED", "message": "Required field 'document' exists", "expected": null, "actual": null, "details": {"field_value": "https://api-sandbox.glynt.ai/v6/data-pools/Fj8uL1/documents/fNqdD1/"}}, {"test_name": "DocumentData for fNqdD1 - status", "status": "PASSED", "message": "Required field 'status' exists", "expected": null, "actual": null, "details": {"field_value": "PENDING_CREATION"}}, {"test_name": "DocumentData for fNqdD1 - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-10T06:57:46.478254Z"}}, {"test_name": "DocumentData for fNqdD1 - updated_at", "status": "PASSED", "message": "Required field 'updated_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-10T06:57:46.478318Z"}}, {"test_name": "DocumentData for fNqdD1 - ID Type", "status": "PASSED", "message": "Field 'id' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for fNqdD1 - Status Type", "status": "PASSED", "message": "Field 'status' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for fNqdD1 - Status Valid", "status": "PASSED", "message": "DocumentData has valid status: PENDING_CREATION", "expected": null, "actual": null, "details": {}}, {"test_name": "Document Upload - orig_doc_1.pdf - Success", "status": "PASSED", "message": "Document uploaded successfully with ID: E9Qrd1", "expected": null, "actual": null, "details": {"document_id": "E9Qrd1", "file_name": "orig_doc_1.pdf"}}, {"test_name": "Document Upload - orig_doc_1.pdf - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "E9Qrd1"}}, {"test_name": "Document Upload - orig_doc_1.pdf - label", "status": "PASSED", "message": "Required field 'label' exists", "expected": null, "actual": null, "details": {"field_value": "orig_doc_1.pdf"}}, {"test_name": "Document Upload - orig_doc_1.pdf - content_type", "status": "PASSED", "message": "Required field 'content_type' exists", "expected": null, "actual": null, "details": {"field_value": "application/pdf"}}, {"test_name": "Document Upload - orig_doc_1.pdf - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-10T06:57:49.629382Z"}}, {"test_name": "Document Upload - orig_doc_1.pdf - Label", "status": "PASSED", "message": "label has correct value: orig_doc_1.pdf", "expected": "orig_doc_1.pdf", "actual": "orig_doc_1.pdf", "details": {}}, {"test_name": "Document Upload - orig_doc_1.pdf - Content Type", "status": "PASSED", "message": "content_type has correct value: application/pdf", "expected": "application/pdf", "actual": "application/pdf", "details": {}}, {"test_name": "DocumentData for E9Qrd1 - Existence", "status": "PASSED", "message": "DocumentData found for document E9Qrd1", "expected": null, "actual": null, "details": {"doc_data_id": "APXAn1"}}, {"test_name": "DocumentData for E9Qrd1 - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "APXAn1"}}, {"test_name": "DocumentData for E9Qrd1 - document", "status": "PASSED", "message": "Required field 'document' exists", "expected": null, "actual": null, "details": {"field_value": "https://api-sandbox.glynt.ai/v6/data-pools/Fj8uL1/documents/E9Qrd1/"}}, {"test_name": "DocumentData for E9Qrd1 - status", "status": "PASSED", "message": "Required field 'status' exists", "expected": null, "actual": null, "details": {"field_value": "PENDING_CREATION"}}, {"test_name": "DocumentData for E9Qrd1 - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-10T06:57:49.646752Z"}}, {"test_name": "DocumentData for E9Qrd1 - updated_at", "status": "PASSED", "message": "Required field 'updated_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-10T06:57:49.646797Z"}}, {"test_name": "DocumentData for E9Qrd1 - ID Type", "status": "PASSED", "message": "Field 'id' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for E9Qrd1 - Status Type", "status": "PASSED", "message": "Field 'status' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for E9Qrd1 - Status Valid", "status": "PASSED", "message": "DocumentData has valid status: PENDING_CREATION", "expected": null, "actual": null, "details": {}}, {"test_name": "Document Upload - origin_doc_2.pdf - Success", "status": "PASSED", "message": "Document uploaded successfully with ID: nuz442", "expected": null, "actual": null, "details": {"document_id": "nuz442", "file_name": "origin_doc_2.pdf"}}, {"test_name": "Document Upload - origin_doc_2.pdf - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "nuz442"}}, {"test_name": "Document Upload - origin_doc_2.pdf - label", "status": "PASSED", "message": "Required field 'label' exists", "expected": null, "actual": null, "details": {"field_value": "origin_doc_2.pdf"}}, {"test_name": "Document Upload - origin_doc_2.pdf - content_type", "status": "PASSED", "message": "Required field 'content_type' exists", "expected": null, "actual": null, "details": {"field_value": "application/pdf"}}, {"test_name": "Document Upload - origin_doc_2.pdf - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-10T06:57:53.103385Z"}}, {"test_name": "Document Upload - origin_doc_2.pdf - Label", "status": "PASSED", "message": "label has correct value: origin_doc_2.pdf", "expected": "origin_doc_2.pdf", "actual": "origin_doc_2.pdf", "details": {}}, {"test_name": "Document Upload - origin_doc_2.pdf - Content Type", "status": "PASSED", "message": "content_type has correct value: application/pdf", "expected": "application/pdf", "actual": "application/pdf", "details": {}}, {"test_name": "DocumentData for nuz442 - Existence", "status": "PASSED", "message": "DocumentData found for document nuz442", "expected": null, "actual": null, "details": {"doc_data_id": "TUDi71"}}, {"test_name": "DocumentData for nuz442 - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "TUDi71"}}, {"test_name": "DocumentData for nuz442 - document", "status": "PASSED", "message": "Required field 'document' exists", "expected": null, "actual": null, "details": {"field_value": "https://api-sandbox.glynt.ai/v6/data-pools/Fj8uL1/documents/nuz442/"}}, {"test_name": "DocumentData for nuz442 - status", "status": "PASSED", "message": "Required field 'status' exists", "expected": null, "actual": null, "details": {"field_value": "PENDING_CREATION"}}, {"test_name": "DocumentData for nuz442 - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-10T06:57:53.117688Z"}}, {"test_name": "DocumentData for nuz442 - updated_at", "status": "PASSED", "message": "Required field 'updated_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-10T06:57:53.117744Z"}}, {"test_name": "DocumentData for nuz442 - ID Type", "status": "PASSED", "message": "Field 'id' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for nuz442 - Status Type", "status": "PASSED", "message": "Field 'status' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for nuz442 - Status Valid", "status": "PASSED", "message": "DocumentData has valid status: PENDING_CREATION", "expected": null, "actual": null, "details": {}}, {"test_name": "Document Upload - orig_doc_3.pdf - Success", "status": "PASSED", "message": "Document uploaded successfully with ID: 60gcO1", "expected": null, "actual": null, "details": {"document_id": "60gcO1", "file_name": "orig_doc_3.pdf"}}, {"test_name": "Document Upload - orig_doc_3.pdf - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "60gcO1"}}, {"test_name": "Document Upload - orig_doc_3.pdf - label", "status": "PASSED", "message": "Required field 'label' exists", "expected": null, "actual": null, "details": {"field_value": "orig_doc_3.pdf"}}, {"test_name": "Document Upload - orig_doc_3.pdf - content_type", "status": "PASSED", "message": "Required field 'content_type' exists", "expected": null, "actual": null, "details": {"field_value": "application/pdf"}}, {"test_name": "Document Upload - orig_doc_3.pdf - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-10T06:57:56.423391Z"}}, {"test_name": "Document Upload - orig_doc_3.pdf - Label", "status": "PASSED", "message": "label has correct value: orig_doc_3.pdf", "expected": "orig_doc_3.pdf", "actual": "orig_doc_3.pdf", "details": {}}, {"test_name": "Document Upload - orig_doc_3.pdf - Content Type", "status": "PASSED", "message": "content_type has correct value: application/pdf", "expected": "application/pdf", "actual": "application/pdf", "details": {}}, {"test_name": "DocumentData for 60gcO1 - Existence", "status": "PASSED", "message": "DocumentData found for document 60gcO1", "expected": null, "actual": null, "details": {"doc_data_id": "2GnvX1"}}, {"test_name": "DocumentData for 60gcO1 - id", "status": "PASSED", "message": "Required field 'id' exists", "expected": null, "actual": null, "details": {"field_value": "2GnvX1"}}, {"test_name": "DocumentData for 60gcO1 - document", "status": "PASSED", "message": "Required field 'document' exists", "expected": null, "actual": null, "details": {"field_value": "https://api-sandbox.glynt.ai/v6/data-pools/Fj8uL1/documents/60gcO1/"}}, {"test_name": "DocumentData for 60gcO1 - status", "status": "PASSED", "message": "Required field 'status' exists", "expected": null, "actual": null, "details": {"field_value": "PENDING_CREATION"}}, {"test_name": "DocumentData for 60gcO1 - created_at", "status": "PASSED", "message": "Required field 'created_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-10T06:57:56.444871Z"}}, {"test_name": "DocumentData for 60gcO1 - updated_at", "status": "PASSED", "message": "Required field 'updated_at' exists", "expected": null, "actual": null, "details": {"field_value": "2025-06-10T06:57:56.444923Z"}}, {"test_name": "DocumentData for 60gcO1 - ID Type", "status": "PASSED", "message": "Field 'id' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for 60gcO1 - Status Type", "status": "PASSED", "message": "Field 'status' has correct type: str", "expected": "str", "actual": "str", "details": {}}, {"test_name": "DocumentData for 60gcO1 - Status Valid", "status": "PASSED", "message": "DocumentData has valid status: PENDING_CREATION", "expected": null, "actual": null, "details": {}}, {"test_name": "Package Rm3ay1 Entries - Entry Count", "status": "PASSED", "message": "Found 6 package entries", "expected": null, "actual": null, "details": {"entry_count": 6}}, {"test_name": "Package Rm3ay1 Entries - Duplicate Exclusion", "status": "PASSED", "message": "Duplicate exclusion working: 4 documents excluded as duplicates", "expected": null, "actual": null, "details": {"excluded_count": 4}}, {"test_name": "Package Rm3ay1 Config Compliance - Configuration Recorded", "status": "PASSED", "message": "Package configuration recorded for compliance verification", "expected": null, "actual": null, "details": {"include_source_files": true, "include_relationship_details": true, "output_format": "json", "grouping_strategy": "ALL_IN_ONE", "dedup_fields": ["CustomerName", "<PERSON><PERSON><PERSON><PERSON>", "StatementDate"]}}, {"test_name": "Package Rm3ay1 Entries - Entry Count", "status": "PASSED", "message": "Found 6 package entries", "expected": null, "actual": null, "details": {"entry_count": 6}}, {"test_name": "Package Rm3ay1 Entries - Duplicate Exclusion", "status": "PASSED", "message": "Duplicate exclusion working: 4 documents excluded as duplicates", "expected": null, "actual": null, "details": {"excluded_count": 4}}, {"test_name": "DocumentData Fields - source_file_id", "status": "PASSED", "message": "All documents have source_file_id populated", "expected": null, "actual": null, "details": {}}, {"test_name": "DocumentData Fields - source_file_name", "status": "PASSED", "message": "All documents have source_file_name populated", "expected": null, "actual": null, "details": {}}, {"test_name": "DocumentData Fields - source_file_md5", "status": "PASSED", "message": "All documents have source_file_md5 populated", "expected": null, "actual": null, "details": {}}, {"test_name": "DocumentData Fields - received_at", "status": "PASSED", "message": "All documents have received_at populated", "expected": null, "actual": null, "details": {}}, {"test_name": "DocumentData Fields - source_file_num_pages", "status": "PASSED", "message": "All documents have source_file_num_pages populated", "expected": null, "actual": null, "details": {}}, {"test_name": "DocumentData Fields - source_file_size", "status": "PASSED", "message": "All documents have source_file_size populated", "expected": null, "actual": null, "details": {}}, {"test_name": "DocumentData Fields - source_file_detected_language", "status": "FAILED", "message": "No documents have source_file_detected_language populated", "expected": null, "actual": null, "details": {}}, {"test_name": "DocumentData Fields - canonical_document_data", "status": "PASSED", "message": "4 documents have canonical_document_data populated (indicates duplicate relationships)", "expected": null, "actual": null, "details": {}}, {"test_name": "Package Rm3ay1 Config Compliance - Configuration Recorded", "status": "PASSED", "message": "Package configuration recorded for compliance verification", "expected": null, "actual": null, "details": {"include_source_files": true, "include_relationship_details": true, "output_format": "json", "grouping_strategy": "ALL_IN_ONE", "dedup_fields": ["CustomerName", "<PERSON><PERSON><PERSON><PERSON>", "StatementDate"]}}, {"test_name": "Package Rm3ay1 Verification - Status Updated", "status": "PASSED", "message": "Package status successfully updated from PROCESSING_SUCCESSFUL to VERIFIED", "expected": null, "actual": null, "details": {"initial_status": "PROCESSING_SUCCESSFUL", "final_status": "VERIFIED"}}, {"test_name": "Package Rm3ay1 Final Delivery - Preview Count", "status": "FAILED", "message": "Preview shows 0 documents, expected 1", "expected": null, "actual": null, "details": {"preview_count": 0, "expected": 1}}, {"test_name": "Package Rm3ay1 Final Delivery - Download Available", "status": "PASSED", "message": "Package download endpoint is accessible", "expected": null, "actual": null, "details": {"download_available": true}}, {"test_name": "Flag Variations - Full CoC + Duplicates ON + Relationships ON Creation", "status": "FAILED", "message": "Failed to create test packager for Full CoC + Duplicates ON + Relationships ON", "expected": null, "actual": null, "details": {}}, {"test_name": "Flag Variations - Essential CoC + Duplicates OFF + Relationships ON Creation", "status": "FAILED", "message": "Failed to create test packager for Essential CoC + Duplicates OFF + Relationships ON", "expected": null, "actual": null, "details": {}}, {"test_name": "Flag Variations - No CoC + All Flags OFF Creation", "status": "FAILED", "message": "Failed to create test packager for No CoC + All Flags OFF", "expected": null, "actual": null, "details": {}}]}